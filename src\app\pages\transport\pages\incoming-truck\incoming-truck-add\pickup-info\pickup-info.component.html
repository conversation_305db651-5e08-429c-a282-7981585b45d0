<div class="pickup-info">
  <form [formGroup]="driverScheduleAddress">
    <section>
      <div class="row m-t-10">
        <div class="col-md-6 col-12">
          <label class="required">Address</label>
          <input
            class="form-control"
            type="text"
            ngx-google-places-autocomplete
            ngx-gp-autocomplete
            [options]="options"
            (onAddressChange)="handleAddressChange($event)"
            placeholder="Enter address"
            formControlName="streetAddress"
          />
          <app-error-messages [control]="driverScheduleAddress.controls.streetAddress"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label class="required">City</label>
          <input class="form-control" type="text" placeholder="Enter city name" formControlName="city" />
          <app-error-messages [control]="driverScheduleAddress.controls.city"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">State</label>
          <input class="form-control" type="text" placeholder="Enter state name" formControlName="state" />
          <app-error-messages [control]="driverScheduleAddress.controls.state"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-5 col-10">
          <label class="required">Zip code</label>
          <input class="form-control" type="text" placeholder="Enter zip code" formControlName="zipcode" />
          <app-error-messages [control]="driverScheduleAddress.controls.zipcode"></app-error-messages>
        </div>
        <div class="col-1 d-flex align-items-end">
          <div
            class="add-shop map-icon"
            *ngIf="
              driverScheduleAddress.get('streetAddress')?.value ||
              driverScheduleAddress.get('city')?.value ||
              driverScheduleAddress.get('state')?.value ||
              driverScheduleAddress.get('zipcode')?.value
            "
          >
            <button class="btn btn-primary" type="button" (click)="toggleGoogleMapPopUp()">
              <fa-icon [icon]="faIcons.faLocationDot"></fa-icon>
            </button>
          </div>

          <p-sidebar
            [closeOnEscape]="false"
            [dismissible]="false"
            [(visible)]="showGoogleMapSideBar"
            position="right"
            (onHide)="showGoogleMapSideBar = false"
            [blockScroll]="true"
            [showCloseIcon]="false"
            styleClass="p-sidebar-md"
            [baseZIndex]="10000"
            appendTo="body"
          >
            <app-google-map (onClose)="toggleGoogleMapPopUp()" *ngIf="showGoogleMapSideBar" [addressGroup]="driverScheduleAddress.value" [address]="fullAddress"> </app-google-map>
          </p-sidebar>
        </div>
        <div class="row mt-0">
          <div class="col-lg-3 col-md-6 col-12">
            <label class="required">Status</label>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [disabled]="isViewMode ? true : false"
              [options]="inventoryStatuses"
              [(ngModel)]="status"
              (onChange)="onStatusChange()"
              [ngModelOptions]="{ standalone: true }"
              optionLabel="name"
              [showClear]="true"
              optionValue="value"
              [filter]="true"
              filterBy="name"
              placeholder="Select status"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage"></ng-container>
              </ng-template>
            </p-dropdown>
            <small class="text-danger status-error" *ngIf="isStatusError"> This field is required </small>
          </div>
        </div>
        <div class="row mt-0">
          <div class="col-12">
            <label>Notes</label>
            <textarea
              placeholder="Enter notes"
              rows="6"
              [disabled]="isViewMode ? true : false"
              [(ngModel)]="pickupNotes"
              (ngModelChange)="onPickupNotesChange()"
              [ngModelOptions]="{ standalone: true }"
            ></textarea>
          </div>
        </div>
      </div>
    </section>
  </form>
</div>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
