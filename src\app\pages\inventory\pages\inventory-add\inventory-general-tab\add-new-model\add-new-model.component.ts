import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';

@Component({
  selector: 'app-add-new-model',
  templateUrl: './add-new-model.component.html',
  styleUrls: ['./add-new-model.component.scss']
})
export class AddNewModelComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Model';
  hasDataBeenModified = false;
  modeFormGroup!: FormGroup;

  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() makeId!: number | null;
  @Input() categoryId!: number | null;
  @Input() makes: IdNameModel[] = [];
  constructor(private readonly formBuilder: FormBuilder,
    private readonly inventoryService: InventoryService,
    private readonly toasterService: AppToasterService,
  ) { super(); }

  ngOnInit(): void {
    this.initializeFormGroup();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.pipelineConfig?.currentValue) {
      this.isLoading = true;
    }
  }

  private initializeFormGroup(): void {
    this.modeFormGroup = this.formBuilder.group({
      name: new FormControl(null, Validators.required),
      makeId: new FormControl(this.makeId, Validators.required),
    });
  }

  get makeParams(): IdNameModel {
    return {
      ...this.modeFormGroup.value,
    };
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  onSubmit(close = true): void {
    if (this.modeFormGroup.invalid) {
      this.modeFormGroup.markAllAsTouched();
      return;
    }
    this.save(close);
  }

  save(close = true): void {
    this.inventoryService.add(this.makeParams, API_URL_UTIL.inventory.models).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.modelAddSuccess);
      if (close) {
        this.onClose.emit(true);
      } else {
        this.modeFormGroup.reset();
      }
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

}
