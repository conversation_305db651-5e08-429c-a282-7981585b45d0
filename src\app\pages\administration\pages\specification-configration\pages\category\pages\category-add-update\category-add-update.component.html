<div class="modal-title close">
  <h4 class="header-title">{{ pageTitle }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
</div>

<form [formGroup]="categoryEditFormGroup" (ngSubmit)="onUpdateCategory()" *ngIf="selectedCategory?.id">
  <div class="content category-form">
    <div class="form-group">
      <label for="name" class="required">Name</label>
      <input formControlName="name" type="text" class="form-control" placeholder="Enter category name" />
      <app-error-messages [control]="categoryEditFormGroup?.controls?.name"></app-error-messages>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>

<form [formGroup]="categoryAddFormGroup" (ngSubmit)="onAddCategory()" *ngIf="!selectedCategory?.id">
  <div class="content category-form">
    <ng-container formArrayName="category">
      <label class="mb-2 required">Name</label>
      <ng-container *ngFor="let model of categoryFormArray.controls; let i = index">
        <div class="row model-group" [formGroupName]="i">
          <div class="col-8">
            <input [pKeyFilter]="blockSpecial" class="form-control" type="text" placeholder="Enter Name" formControlName="name" />
            <small class="text-danger error-msg" *ngIf="categoryFormArray.controls[i].get('name')?.touched || categoryFormArray.controls[i].get('name')?.dirty">
              <span *ngIf="categoryFormArray.controls[i].get('name')?.hasError('required')"> This field is required </span>
              <span *ngIf="categoryFormArray.controls[i].get('name')?.errors?.maxlength"> Maximum 50 characters are allowed </span>
            </small>
          </div>
          <div class="col-4">
            <div class="add-model">
              <img *ngIf="i || categoryFormArray.value.length !== 1" [src]="constants.staticImages.icons.deleteIcon" alt="" class="delete-shop" (click)="onDeleteCategory(i)" />
              <button class="btn btn-primary" id="addShopBtn" type="button" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAddNewCategory()" *ngIf="isAddNewCategoryVisible(i)">
                <span class="hide-label"> Add Category</span>
              </button>
            </div>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>
