<div class="modal-title">
  <h4>Change Password</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>

<form (ngSubmit)="onSubmit()">
  <div class="content">
    <section [formGroup]="passwordFormGroup">
      <div class="row">
        <div class="col-12">
          <label>New Password</label>
          <input formControlName="newPassword" type="password" appPasswordEye class="form-control password" placeholder="Enter your new password" />
          <app-error-messages [control]="passwordFormGroup.controls?.newPassword"></app-error-messages>
        </div>
        <div class="col-12 mt-4">
          <label>Confirm Password</label>
          <input formControlName="confirmPassword" type="password" appPasswordEye class="form-control password" placeholder="Confirm your new password" />
          <app-error-messages [control]="passwordFormGroup.controls?.confirmPassword"></app-error-messages>
        </div>
      </div>
    </section>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" [disabled]="passwordFormGroup.invalid" appShowLoaderOnApiCall>Save</button>
  </div>
</form>
