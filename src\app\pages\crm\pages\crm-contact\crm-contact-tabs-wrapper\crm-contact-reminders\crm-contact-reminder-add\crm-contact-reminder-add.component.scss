.schedule-on-content {
  margin: 10px 0;
}

textarea {
  height: auto !important;
}

.mr-5 {
  margin-right: 5px;
}

.history {
  font-size: 13px;

  .title {
    color: var(--active-color);
    font-size: 16px !important;
    font-weight: 600 !important;
    letter-spacing: 0;
    line-height: 25px !important;
    text-transform: uppercase;
    border-bottom: 1px solid #e3e3e3;
    margin: 10px;
    padding-bottom: 10px;
  }

  .content-wrapper {
    padding: 10px;
  }

  .content-wrapper:last-child {
    border-bottom: none;
  }
  .history-bold-text {
    font-weight: 600;
  }
}

.model-body {
  height: calc(100vh - 114px);
  overflow-y: auto;

  .content {
    max-height: unset;
  }
}
