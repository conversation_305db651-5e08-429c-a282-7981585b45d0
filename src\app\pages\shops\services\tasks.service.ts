import { DatePipe } from "@angular/common";
import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Observable } from "rxjs";
import { TaskListItem } from "../models";

@Injectable({ providedIn: 'root' })
export class TaskService extends BaseCrudService {

  constructor(private readonly datePipe: DatePipe, protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  getBaseAPIPath(): string {
    return API_URL_UTIL.tasks.root;
  }

  fromServerModel(json: TaskListItem): TaskListItem {
    if (!json) {
      return new TaskListItem();
    }
    return new TaskListItem(json);
  }

  getTaskPriorities(): Observable<{}> {
    return this.httpClient.get<{}>(`${this.getFullAPIUrl()}/${API_URL_UTIL.tasks.priorities}`);
  }

  deleteTaskAttachment(attachmentId: number): Observable<void> {
    return this.httpClient.delete<void>(`${API_URL_UTIL.tasks.attachments}/${attachmentId}`);
  }

  archivedTask(taskId: string): Observable<void> {
    return this.httpClient.patch<void>(`${API_URL_UTIL.tasks.archived}/${taskId}`, '');
  }

  getAllTask(): Observable<Array<TaskListItem>> {
    return this.httpClient.get<Array<TaskListItem>>(`${this.getFullAPIUrl()}/${API_URL_UTIL.tasks.allTasks}`);
  }
}
