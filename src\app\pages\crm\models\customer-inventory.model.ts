import { AssociationsUnits } from "@pages/inventory/models";
import { IdNameModel } from "src/app/@shared/models";
import { Specifications } from './customer-lead-quotation.model';

export enum CustomerInventory {
  INVENTORY_MATCHED = 0,
  QUOTATION = 1,
  TASK = 2
}

export interface InventoryPreferenceDetails {
  id: string;
  year: number;
  unitType: string;
  engineMake: string;
  engineModel: string;
  engineHp: string;
  transmissionType: string;
  notes: string;
  make: IdNameModel;
  unitModel: IdNameModel;
  designation: IdNameModel;
  status: string;
  crmContact: CrmContact;
  deleted: boolean;
  createdDate: string;
  location: string;
  maxYear: number;
  minYear: number;
}

export interface CrmContact {
  id: string;
  company: string;
  contactName: string;
  email: string;
  phoneWork: string;
  streetAddress: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  creator: IdNameModel
}

export enum QuotationKey {
  UNIT_TYPE = 'generalInformation.unitType.name',
  UNIT_MODEL = 'generalInformation.unitModel.id',
  YEAR = 'generalInformation.year',
  MAKE = 'generalInformation.make.id',
  DESIGNATION = 'generalInformation.designation.id',
  UNIT_STATUS_NAME = 'generalInformation.unitStatus.name',
  PREVIOUSLY_OWNED = 'previousOwner.previousOwnerContact.id',
  CUSTOMER_LEAD = 'crmContact.id'
}

export interface QuotationResponse {
  generalInformation: GeneralInformation;
  id: number | null;
  matchingPercentage: number | null;
  retailAskingPrice: number | null;
  totalProjectedInvestment: number | null;
  unitImages: UnitImages;
  unitQuotationAssociationDTOS: Array<AssociationsUnits>;
  isExisting: boolean;
}

export interface GeneralInformation {
  id: number;
  make: IdNameModel;
  stockNumber: string;
  unitModel: UnitModel;
  unitStatus: IdNameModel;
  unitType: UnitType;
  vin: string;
  year: number;
}

export interface UnitModel {
  id: number;
  makeId: number | null;
  name: string;
}

export interface UnitType {
  id: number;
  name: string;
  parentUnitTypeId: number | null;
}

export interface UnitImages {
  componentConditionId: number | null;
  detail: string;
  displayPicture: boolean;
  fullUrl: string;
  id: number | null;
  photoType: string;
  unitId: number | null;
  url: string;
}

export interface QuotationsParams {
  quotePrice: string;
  customerLeadId: string;
  unitId: string;
  sendEmail: string;
}

export interface PreviouslyOwnedParams {
  archived: boolean,
  createdBy: IdNameModel,
  createdDate: string,
  generalInformation: GeneralInformation,
  id: string,
  unitImages: UnitImages
}

export enum UnitStaus {
  AVAILABLE = 'Available'
}

export interface InventoryMatchedParams {
  dealerId?: number;
  unitTypeId: number;
  makeIds: number[];
  unitModelIds: number[];
  designationId?: number;
  categoryId: number;
  specifications?: Array<string>;
}

export interface InventoryMatchingParams {
  totalRetailPrice: number;
  totalInvestmentCost: number;
  totalQuotePrice: number;
  customerLeadId: string;
  sendEmail: boolean;
  quotationUnitDTOS: Array<QuotationUnitDTOS>;
  email?:string
}

export interface QuotationUnitDTOS {
  includedInQuotation?: boolean;
  quotePrice?: number;
  unitId: number;
  specifications: Specifications[]
}
