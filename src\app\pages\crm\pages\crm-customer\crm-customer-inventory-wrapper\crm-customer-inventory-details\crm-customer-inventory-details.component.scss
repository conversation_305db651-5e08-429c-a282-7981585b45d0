.inventory-matching-wrapper {
  height: calc(100vh - 115px);
  overflow-y: auto;

  ::ng-deep .p-card {
    margin: 15px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);

    .p-card-content {
      padding: 0;
    }

    .p-card-body {
      padding: 10px;
    }
  }

  .gray {
    color: var(--form-placeholder-color) !important;
  }

  .inventory-quote-wrapper {
    .qoute-details {
      display: flex;

      div {
        margin-right: 5px;
      }

      .qoute-details-info {
        border-right: 1px solid #e3e3e3;
      }

      .qoute-details-info:last-child {
        border-right: none;
      }

      .model-detail-label {
        color: var(--form-placeholder-color) !important;
      }

      .model-detail-info {
        font-weight: 500;
      }
    }
  }

  .inventory-action-label {
    margin: 20px;
    font-weight: 500;
  }

  .customer-inventory-matched {
    .inventory-content-wrapper {
      .inventory-content {
        display: flex;

        .inventory-info-wrapper {
          width: calc(100% - 190px);
          margin-right: 10px;

          .inventory-info-content {
            display: flex;
            justify-content: space-between;

            .gray {
              font-size: 15px;
            }

            .black {
              color: var(--text-color);
              font-weight: 500;
            }
          }
        }

        .select-inventory-checkbox {
          width: 30px;
        }

        .inventory-image {
          width: 110px;
          display: flex;
          align-items: center;

          img {
            width: 95px;
            height: 70px;
          }
        }

        .inventory-image-without-checkbox {
          width: 140px;

          img {
            width: 125px;
            height: 90px;
          }
        }

        .percent-matching {
          width: 50px;

          .rectangle {
            box-sizing: border-box;
            height: 20px;
            width: 49px;
            border: 1px solid var(--active-color);
            border-radius: 10px;
            top: 0px;
            position: relative;
            color: var(--active-color);
            font-family: Poppins;
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 0;
            line-height: 21px;
            text-align: center;
          }
        }
      }

      .quote {
        .form-control {
          margin-bottom: 15px;
        }
      }

      .error {
        position: relative;
        top: -15px;
      }
    }
  }
}

.modal-title {
  align-items: center;

  .model-content {
    display: flex;
    flex-direction: column;
  }

  fa-icon {
    height: fit-content;
    line-height: 27px;
  }
}

.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
}

.bold-text {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

.modal-title * {
  line-height: 22px;
}

@media only screen and (max-width: 500px) {
  .inventory-info-content {
    flex-direction: column;
  }
}
