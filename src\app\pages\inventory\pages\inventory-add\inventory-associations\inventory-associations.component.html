<div class="row">
  <div class="col-lg-3 col-12" *ngIf="(isEditMode || isViewMode)">
    <div class="card primary-inventories">
      <div class="title">
        <h4>Primary Inventory</h4>
      </div>
      <div class="card-body">
        <div>
          <img [src]="getPrimaryInventoryImage()" alt="" />
        </div>
        <div class="model-name">
          {{ inventoryInfo?.generalInformation?.year }}
          {{ inventoryInfo?.generalInformation?.make?.name }}
          {{ inventoryInfo?.generalInformation?.unitModel?.name }}
        </div>
        <div class="model-details">
          <div>
            <ng-container [ngTemplateOutlet]="modelDetails" [ngTemplateOutletContext]="{ generalInfo: inventoryInfo?.generalInformation?.stockNumber, label: 'Stock#' }">
            </ng-container>
          </div>
          <div>
            <ng-container [ngTemplateOutlet]="modelDetails" [ngTemplateOutletContext]="{ generalInfo: inventoryInfo?.generalInformation?.vin, label: 'Vin' }"> </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-lg-9 col-12" [ngClass]="(isEditMode || isViewMode) ? 'col-9' : 'col-12'">
    <div [ngClass]="{ 'card association-card': (isEditMode || isViewMode) }">
      <div class="title" *ngIf="(isEditMode || isViewMode)">
        <h4>Associated Inventory</h4>
        <!-- Comment out for now but will may be used later -->
        <!-- <div class="d-flex align-items-center justify-content-end h-100" *ngIf="!isViewMode">
          <button class="btn btn-primary left" type="button" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAdd()" *appHasPermission="[permissionActions.CREATE_INVENTORY]">
            <span class="show-label">Add New Inventory</span>
          </button>
        </div> -->
      </div>
      <div [ngClass]="{ 'card-body': (isEditMode || isViewMode) }">
        <div class="row" *ngIf="!isViewMode">
          <div class="col-lg-4 col-12">
            <label>#Stock</label>
            <p-dropdown
              appPreventClearFilter
              [options]="stockList"
              [(ngModel)]="selectedType"
              [filter]="true"
              filterBy="stockNumber"
              (onFilter)="searchStock($event)"
              optionLabel="stockNumber"
              appendTo="body"
              placeholder="Select #Stock to associate"
              (ngModelChange)="onUnitSelect($event)"
              [disabled]="!selectedCategoryId"
              [virtualScroll]="true"
              [itemSize]="30"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.association, data: stockList }"></ng-container>
              </ng-template>
              <ng-template pTemplate="selectedItem">
                <div *ngIf="selectedType">
                  {{ selectedType.stockNumber }}
                  <span *ngFor="let unit of selectedType?.unitAssociationChildDTO"> , {{ unit.stockNumber }} </span>
                </div>
              </ng-template>
              <ng-template let-units pTemplate="item">
                {{ units.stockNumber }}
                <span *ngFor="let unit of units?.unitAssociationChildDTO"> , {{ unit.stockNumber }} </span>
              </ng-template>
              <ng-template pTemplate="footer">
                <p *ngIf="!isLastPage" class="load-more" (click)="onLoadMore()">Load More <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loadMoreIcon"></fa-icon></p>
              </ng-template>
            </p-dropdown>
          </div>
        </div>
        <div class="row associated-units">
          <ng-container *ngIf="!isLoading; else loader">
            <ng-container *ngIf="associatedUnits?.length || selectedUnits?.length; else noUnitAssociated">
              <div class="col-12 col-md-6 col-lg-4" *ngFor="let associatedUnit of associatedUnits">
                <div class="card units">
                  <div class="content">
                    <div class="associated-unit">
                      <div class="model-name">
                        <span>
                          {{ associatedUnit?.yearMakeModel }}
                        </span>
                        <img
                          [src]="constants.staticImages.icons.linkOff"
                          class="link-off"
                          *ngIf="!isViewMode"
                          tooltipPosition="left"
                          [pTooltip]="'Remove association'"
                          (click)="onRemoveAssociation(associatedUnit, $event)"
                          alt=""
                        />
                      </div>
                      <div class="stockNumber">
                        <ng-container [ngTemplateOutlet]="modelDetails" [ngTemplateOutletContext]="{ generalInfo: associatedUnit.stockNumber, label: '#Stock' }"> </ng-container>
                        <div class="mt-2">
                          <span class="model-detail-info">
                            <p-checkbox
                              [disabled]="isViewMode"
                              label="Display on web"
                              [(ngModel)]="associatedUnit.internetOption.displayOnWeb"
                              [binary]="true"
                              (onChange)="updateDisplayOnWeb($event.checked, associatedUnit.id)"
                            >
                            </p-checkbox>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-6 col-lg-4" *ngFor="let selectedUnit of selectedUnits; let index = index">
                <div class="card units">
                  <div class="content">
                    <div class="associated-unit">
                      <div class="model-name">
                        <span>
                          {{ selectedUnit?.yearMakeModel }}
                        </span>
                        <img
                          [src]="constants.staticImages.icons.linkOff"
                          class="link-off"
                          *ngIf="!isViewMode"
                          tooltipPosition="left"
                          [pTooltip]="'Remove association'"
                          (click)="onRemoveAssociation(selectedUnit, $event, true)"
                          alt=""
                        />
                      </div>
                      <div class="stockNumber">
                        <ng-container [ngTemplateOutlet]="modelDetails" [ngTemplateOutletContext]="{ generalInfo: selectedUnit.stockNumber, label: '#Stock' }"> </ng-container>
                        <div class="mt-2">
                          <span class="model-detail-info">
                            <p-checkbox
                              [readonly]="isViewMode"
                              label="Display on web"
                              [(ngModel)]="selectedUnit.internetOption.displayOnWeb"
                              [binary]="true"
                              (onChange)="updateDisplayOnWeb($event.checked, selectedUnit.id)"
                            >
                            </p-checkbox>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-template #noUnitAssociated>
              <div class="m-4 d-flex justify-content-center" *ngIf="isEditMode || isViewMode">No association found</div>
            </ng-template>
          </ng-container>

          <ng-template #loader>
            <div class="m-4 d-flex justify-content-center">
              <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="history-main-wrapper" *ngIf="(isEditMode || isViewMode)">
  <p-accordion class="nested-accordion" [multiple]="true">
    <p-accordionTab [(selected)]="accordionTabs.history">
      <ng-template pTemplate="header">
        <div class="accordion-header" [ngClass]="{ active: accordionTabs.history }">
          <span>History</span>
          <em class="pi" [ngClass]="accordionTabs.history ? 'pi-angle-up' : 'pi-angle-down'"></em>
        </div>
      </ng-template>
      <ng-template pTemplate="content">
        <ng-container *ngIf="associationHistory?.length; else noHistoryFound">
          <div *ngFor="let history of associationHistory" class="history-content-wrapper">
            <div class="user-info">
              <span class="history-bold-text">
                {{ history.user.name }}
              </span>

              <span class="stockNumber"> made changes - {{ history.eventDate | date: constants.fullDateFormat }} </span>
            </div>
            <div class="history-details">
              <div class="history-detail-content">
                <div class="history-bold-text history-title">Action</div>
                <div class="value stockNumber">
                  {{ history.eventType.replace('_', ' ') | titlecase }}
                </div>
              </div>
              <div class="history-detail-content">
                <div class="history-bold-text history-title">Primary Inventory</div>
                <div class="value stockNumber">#Stock: {{ inventoryInfo?.generalInformation?.stockNumber }}</div>
              </div>
              <div class="history-detail-content">
                <div class="history-bold-text history-title">Associated Inventory</div>
                <div class="value stockNumber">#Stock: {{ getAssociatedUnitName(history, inventoryInfo?.generalInformation?.stockNumber) }}</div>
              </div>
            </div>
          </div>
        </ng-container>
        <ng-template #noHistoryFound>
          <div class="m-4 d-flex justify-content-center">No history found</div>
        </ng-template>
      </ng-template>
    </p-accordionTab>
  </p-accordion>
</div>

<ng-template #modelDetails let-generalInfo="generalInfo" let-label="label">
  <span class="model-detail-label"> {{ label }} : </span>
  <span class="model-detail-info">
    {{ generalInfo ? generalInfo : 'NA' }}
  </span>
</ng-template>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  [baseZIndex]="10000000"
  appendTo="body"
>
  <app-inventory-add-wrapper
    (onClose)="onAddEditPopupClose($event)"
    *ngIf="showCreateModal"
    [showSoldTabs]="false"
    [showHoldTabs]="false"
    [showAssociation]="false"
    [inventoryInfo]="null"
    [inventoryIncomingInfo]="null"
    [activeIndexes]="0"
    [isEditMode]="false"
    [isViewMode]="false"
    (addedUnitId)="onAddUnitId($event)"
    [categoriesToShow]="categoriesToShow"
  >
  </app-inventory-add-wrapper>
</p-sidebar>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
