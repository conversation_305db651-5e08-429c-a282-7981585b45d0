import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { ColumnDropdownModule } from '@pages/common-table-column/column-dropdown.module';
import { ShopsModule } from '@pages/shops/shops.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { UiSwitchModule } from 'ngx-ui-switch';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { MenuModule } from 'primeng/menu';
import { MessageModule } from 'primeng/message';
import { MultiSelectModule } from 'primeng/multiselect';
import { ProgressBarModule } from 'primeng/progressbar';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmContactModule } from '../crm-contact/crm-contact.module';
import { CrmTaskAddModule } from './crm-task-add/crm-task-add.module';
import { CrmTaskListComponent } from './crm-task-list/crm-task-list.component';
import { CRMTaskRoutingModule } from './crm-task-routing.module';
import { CrmTaskComponent } from './crm-task.component';


@NgModule({
  declarations: [
    CrmTaskComponent,
    CrmTaskListComponent
  ],
  imports: [
    CommonModule,
    CRMTaskRoutingModule,
    AccordionModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    ProgressBarModule,
    CalendarModule,
    TabViewModule,
    TooltipModule,
    CheckboxModule,
    DividerModule,
    MessageModule,
    ShopsModule,
    CrmContactModule,
    CrmTaskAddModule,
    ColumnDropdownModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    MenuModule,
    ConfirmDialogModule,
  ],
  providers: [ConfirmationService, MessageService],
  exports: []

})
export class CrmTaskModule { }
