import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { UnitTypeAddUpdateComponent } from './pages/unit-type-add-update/unit-type-add-update.component';
import { UnitTypeRoutingModule } from './unit-type-routing.module';
import { UnitTypeComponent } from './unit-type.component';

@NgModule({
  declarations: [
    UnitTypeComponent,
    UnitTypeAddUpdateComponent
  ],
  imports: [
    CommonModule,
    UnitTypeRoutingModule,
    DirectivesModule,
    SharedComponentsModule,
    SidebarModule,
    TableModule,
    FontAwesomeModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    FormsModule,
    DropdownModule
  ],
  providers: [
    ConfirmationService,
    MessageService
  ],
})
export class UnitTypeModule { }
