import { IdNameModel } from "src/app/@shared/models";
import { GeneralInformation } from "./inventory.model";

export class SaleUnitCreateParam {
  sellType!: string;
  invoiceDate!: string;
  sellPrice!: number;
  unitId!: number;
  salesPersonId!: number;
  locationId!: number;
  id!: number;
  buyerInformationId!: number;
  quotationId!: string;
  titleProcessed!: boolean;
  paymentCompleted!: boolean;
  deliveredOrPickup!: boolean;
}

export const SaleTypeList = [
  { value: 'RETAILER', name: 'Retail Sale' },
  { value: 'WHOLESALER', name: 'Whole Sale' },
  { value: 'DEALER_TRADE', name: 'Dealer Trade' },
]

export class CompanyNameCreateParam {
  treeOperator!: string;
  values: Values[] = [];
  vendorId!: number;
}

export class Values {
  key!: string;
  value!: string;
  dataType!: string;
  operator!: string;
}

export class SalesInformation {
  id!: number;
  sellType!: string;
  invoiceDate!: string;
  sellPrice!: string;
  buyerInformation!: BuyerInformation;
  unit!: Unit;
  salesPerson!: SalesPerson;
  location!: IdNameModel;
  dealerLocation!: string;
  quotationId!: string;
  titleProcessed!: boolean;
  paymentCompleted!: boolean;
  deliveredOrPickup!: boolean;
}

export class inventoryHoldDetails {
  id!: number;
  buyerInformation!: BuyerInformation;
  salesPerson!: SalesPerson;
  depositDate!: string;
  onHoldUntil!: string;
  depositAmount!: number;
  notes!: string;
  unit!: Unit;
}

export interface BuyerInformation {
  id: number,
  company: string,
  contactName: string,
  name: string;
  email: string,
  phoneWork: number,
  streetAddress: string,
  city: string,
  state: string,
  zipcode: string,
  country: string,
  creator: IdNameModel,
  firstName: string,
  lastName: string

}

export class SalesPerson {
  id!: number;
  name!: string;
  email!: string
  phoneNumber!: number

}
export class Unit {
  id!: number;
  archived!: boolean;
  generalInformation!: GeneralInformation;
}

export class CompanyListItem {
  id!: number;
  city!: string;
  company!: string;
  creator!: IdNameModel;
  email!: string;
  firstName!: string;
  lastName!: string;
  phoneMobile!: number;
  phoneWork!: number;
  state!: string;

  constructor(json?: CompanyListItem) {
    if (json) {
      Object.assign(this, json);
    }
  }
}

export class SalesPersonDetails {
  id!: number;
  email!: string;
  phoneNumber!: number;
  name!: string
}
