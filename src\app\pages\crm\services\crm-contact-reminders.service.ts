import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { Observable } from 'rxjs';
import { CrmContactReminders } from '../models/crm-contact-reminder.model';

@Injectable({
  providedIn: 'root'
})
export class CrmContactRemindersService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.crm.reminders;
  }

  getReminders(params: any, endpoint?: string): Observable<Array<CrmContactReminders>> {
    return this.httpClient.post<Array<CrmContactReminders>>(`${this.getFullAPIUrl(endpoint)}`, { ...params });
  }

  getReminderHistory(params: any, endpoint?: string): Observable<Array<CrmContactReminders>> {
    return this.httpClient.get<Array<CrmContactReminders>>(`${endpoint}/${params}`);
  }
}
