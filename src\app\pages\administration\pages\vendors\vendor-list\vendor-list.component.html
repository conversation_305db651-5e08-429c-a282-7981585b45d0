<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left"
      (click)="showCreateModal = true"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      *appHasPermission="[permissionActions.CREATE_VENDORS]"
    >
      <span class="show-label">Add New Vendor</span>
    </button>
  </div>
</app-page-header>

<div class="card">
  <div class="tabs">
    <tabset #profileTabs>
      <tab heading="Active" #activeTab="tab" (selectTab)="onTabChanged($event)">
        <ng-container [ngTemplateOutlet]="activeTabTemplate"></ng-container>
      </tab>

      <tab heading="Archived" #archivedTab="tab" (selectTab)="onTabChanged($event)">
        <ng-container [ngTemplateOutlet]="activeTabTemplate"></ng-container>
      </tab>
    </tabset>
  </div>
</div>

<ng-template #activeTabTemplate>
  <div class="card tabs pipeline-list">
    <div class="tab-content">
      <p-table
        class="no-column-selection"
        [columns]="selectedColumns"
        [value]="vendors"
        responsiveLayout="scroll"
        sortMode="single"
        [customSort]="true"
        [lazy]="true"
        [reorderableColumns]="true"
        (onLazyLoad)="onSortChange($event, getAll.bind(this))"
        [sortField]="'name'"
        [rowHover]="true"
        [loading]="isLoading"
        styleClass="p-datatable-gridlines"
        [resizableColumns]="true"
        columnResizeMode="expand"
      >
        <ng-template pTemplate="header" let-columns>
          <tr>
            <ng-container *ngFor="let col of columns">
              <th pResizableColumn [pSortableColumn]="col.sortable ? col?.sortKey || col.field : ''" pReorderableColumn *ngIf="col.reorderable" [ngClass]="col.class">
                {{ col.header }} <p-sortIcon [field]="col.sortKey || col.field" *ngIf="col.sortable"></p-sortIcon>
              </th>
              <th pResizableColumn [pSortableColumn]="col.sortable ? col?.sortKey || col.field : ''" *ngIf="!col.reorderable" [ngClass]="col.class">
                {{ col.header }} <p-sortIcon [field]="col.sortKey || col.field" *ngIf="col.sortable"></p-sortIcon>
              </th>
            </ng-container>
            <th pResizableColumn class="small-col" *appHasPermission="[permissionActions.UPDATE_VENDORS]">Active</th>
            <th pResizableColumn class="small-col">Actions</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-vendor let-columns="columns">
          <tr>
            <ng-container *ngFor="let col of columns">
              <td *ngIf="!col.isATemplate">
                {{ vendor[col.field] }}
              </td>
              <td *ngIf="col.field === 'phoneNumber'">{{ vendor?.phoneNumber | phone }}</td>
            </ng-container>
            <td *appHasPermission="[permissionActions.UPDATE_VENDORS]" class="actions">
              <div class="actions-content">
                <ui-switch [(ngModel)]="vendor.isActive" [loading]="selectedVendor?.id === vendor.id && isArchiveInProgress" (change)="onArchive(vendor, $event)">
                  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedVendor?.id === vendor.id"></fa-icon>
                </ui-switch>
              </div>
            </td>
            <td class="actions">
              <div class="actions-content">
                <img [src]="isActiveTab ? constants.staticImages.icons.expenseContract : ''" (click)="onViewExpensesList(vendor)" alt="" />
                <img
                  [src]="isActiveTab ? constants.staticImages.icons.edit : constants.staticImages.icons.viewIcon"
                  (click)="onEdit(vendor, isActiveTab ? false : true)"
                  alt=""
                  *appHasPermission="[permissionActions.UPDATE_VENDORS]"
                />
              </div>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <td [colSpan]="cols.length + 1" class="no-data">No data to display</td>
        </ng-template>
      </p-table>
      <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
    </div>
  </div>
</ng-template>

<p-sidebar [(visible)]="showCreateModal" [fullScreen]="true" (onHide)="showCreateModal = false" [blockScroll]="true" [transitionOptions]="modalTransition" [showCloseIcon]="false">
  <app-vendor-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal" [vendorInfo]="selectedVendor" [isViewMode]="isViewMode"></app-vendor-add>
</p-sidebar>

<p-sidebar
  [(visible)]="showExpensesCreateModal"
  [fullScreen]="true"
  (onHide)="showExpensesCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-vendor-expenses-list (onClose)="onAddEditExpensePopupClose($event)" *ngIf="showExpensesCreateModal" [vendorExpenseInfo]="selectedVendorExpenses"></app-vendor-expenses-list>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>

<p-confirmPopup appClickOutside (clickOutside)="onBackdropClick($event)"></p-confirmPopup>
