<form class="search-vehicle">
  <section>
    <div class="title">
      <h4>Search vehicle to auto populate information</h4>
    </div>
    <div class="row mx-0">
      <div class="col-xl-2 col-lg-3 col-md-4 col-12 radio-wrapper"
        [ngClass]="{'active-border': selectedSearchOption === searchOptions.VIN}">
        <p-radioButton name="searchVehicle" [inputId]="searchOptions.VIN.toString()" [value]="searchOptions.VIN"
          [(ngModel)]="selectedSearchOption" label="VIN"></p-radioButton>
      </div>
      <div class="col-xl-2 col-lg-3 col-md-4 col-12 radio-wrapper ms-md-2"
        [ngClass]="{'active-border': selectedSearchOption === searchOptions.MAKE_MODEL_YEAR}">
        <p-radioButton name="searchVehicle" [inputId]="searchOptions.MAKE_MODEL_YEAR.toString()"
          [value]="searchOptions.MAKE_MODEL_YEAR" [(ngModel)]="selectedSearchOption" label="Make, Model or Year">
        </p-radioButton>
      </div>
    </div>
  </section>

  <section>
    <div class="title">
      <h4>Vehicle Information</h4>
    </div>
    <ng-container [ngSwitch]="selectedSearchOption">
      <ng-container *ngSwitchCase="searchOptions.VIN">
        <ng-container [ngTemplateOutlet]="vinNumberTemplate"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="searchOptions.MAKE_MODEL_YEAR">
        <ng-container [ngTemplateOutlet]="makeModelYearTemplate"></ng-container>
      </ng-container>
    </ng-container>
  </section>

  <section *ngIf="searchResults?.length">
    <div class="title">
      <h4>{{searchResults?.length}} result(s)</h4>
    </div>
    <div class="row">
      <div class="col-xl-2 col-md-4 col-12" *ngFor="let result of searchResults">
        <div class="result-wrapper" [ngClass]="{'active-border': selectedVehicle?.id === result.id}"
          (click)="onSelectVehicle(result)">
          <p class="result-title">{{result?.name}}</p>
          <div class="d-flex result-detail">
            <span>{{result?.year}}</span>
            <span>{{result?.make}}</span>
            <span>{{result?.model}}</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</form>

<ng-template #vinNumberTemplate>
  <form class="row" [formGroup]="vinSearchFormGroup" (ngSubmit)="onSearch()">
    <div class="col-lg-3 col-md-4 col-12">
      <label class="required">VIN Number</label>
      <input class="form-control" type="text" placeholder="Enter VIN number" formControlName="vinNumber">
      <app-error-messages [control]="vinSearchFormGroup.controls?.vinNumber"></app-error-messages>
    </div>
    <div class="col-md-2 col-12 search-action">
      <button class="btn btn-primary" type="submit">Search</button>
    </div>
  </form>
</ng-template>

<ng-template #makeModelYearTemplate>
  <form class="row" [formGroup]="unitSearchFormGroup" (ngSubmit)="onSearch()">
    <div class="col-md-2.5 col-6">
      <label>Make</label>
      <p-dropdown appendTo="body" [options]="makes" formControlName="make" optionLabel="name" [showClear]="true"
        optionValue="id" [filter]="true" filterBy="name" placeholder="Select make">
        <ng-template pTemplate="empty">
          <ng-container [ngTemplateOutlet]="emptyMessage"
            [ngTemplateOutletContext]="{loader: loaders.makes, data: makes}"></ng-container>
        </ng-template>
        <ng-template let-item pTemplate="item">
          <span>{{item.name}}</span>
        </ng-template>
      </p-dropdown>
    </div>
    <div class="col-md-2.5 col-6">
      <label>Model</label>
      <p-dropdown appendTo="body" [options]="models" formControlName="make" optionLabel="name" [showClear]="true"
        optionValue="id" [filter]="true" filterBy="name" placeholder="Select make">
        <ng-template pTemplate="empty">
          <ng-container [ngTemplateOutlet]="emptyMessage"
            [ngTemplateOutletContext]="{loader: loaders.models, data: models}"></ng-container>
        </ng-template>
        <ng-template let-item pTemplate="item">
          <span>{{item.name}}</span>
        </ng-template>
      </p-dropdown>
    </div>
    <div class="col-md-2.5 col-6">
      <label>Category</label>
      <p-dropdown appendTo="body" [options]="categories" formControlName="make" optionLabel="name" [showClear]="true"
        optionValue="id" [filter]="true" filterBy="name" placeholder="Select make">
        <ng-template pTemplate="empty">
          <ng-container [ngTemplateOutlet]="emptyMessage"
            [ngTemplateOutletContext]="{loader: loaders.categories, data: categories}"></ng-container>
        </ng-template>
        <ng-template let-item pTemplate="item">
          <span>{{item.name}}</span>
        </ng-template>
      </p-dropdown>
    </div>
    <div class="col-md-2.5 col-6">
      <label>Year</label>
      <p-dropdown appendTo="body" [options]="years" formControlName="make" optionLabel="name" [showClear]="true"
        optionValue="id" [filter]="true" filterBy="name" placeholder="Select make">
        <ng-template pTemplate="empty">
          <ng-container [ngTemplateOutlet]="emptyMessage"
            [ngTemplateOutletContext]="{loader: loaders.years, data: years}"></ng-container>
        </ng-template>
        <ng-template let-item pTemplate="item">
          <span>{{item.name}}</span>
        </ng-template>
      </p-dropdown>
    </div>
    <div class="col-md-1 col-12 search-action">
      <button class="btn btn-primary" type="submit">Search</button>
    </div>
  </form>
</ng-template>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
