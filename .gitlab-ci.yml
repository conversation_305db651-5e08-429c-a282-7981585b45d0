stages:
  - analysis
  - install_dependency_and_build
  - deploying

analysis:
  stage: analysis
  environment:
    name: $CI_COMMIT_REF_NAME
  tags:
    - analysis
  script:
    - echo "Running Code Analysis"
    - java -version
    - SONAR_TOKEN=$(echo $SONAR_TOKEN_BASE64 | base64 -d)
    - /opt/sonar-scanner/bin/sonar-scanner -X -Dsonar.host.url=$SONAR_HOST -Dsonar.login=$SONAR_TOKEN -Dsonar.projectName=$CI_PROJECT_NAME -Dsonar.projectKey=$CI_PROJECT_NAME -Dsonar.sources=. -Dsonar.exclusions=node_modules/** -Dsonar.qualitygate.wait=true
  when: manual

install_dependency_and_build:
  stage: install_dependency_and_build
  image: node:18
  environment:
    name: $CI_COMMIT_REF_NAME
  script:
    - echo "Installing Dependencies"
    - yarn install
    - echo "Running Distribution"
    - yarn build --configuration=dev
  artifacts:
    paths:
      - "dist/"
    expire_in: "1 day"
  only:
    - develop
    - stage
    - master
  when: manual

deploying:
  stage: deploying
  image: alpine:latest
  environment:
    name: $CI_COMMIT_REF_NAME
  script:
    - echo "Deploying"
    - apk add --no-cache zip && apk add --no-cache aws-cli && apk add --no-cache openssh
    - zip -r dist.zip dist
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $AWS_REGION
    - aws s3 cp dist.zip s3://$S3_BUCKET_NAME/$PROJECT_NAME/$CI_COMMIT_REF_NAME/dist.zip
    - echo "$skeye" > skeye.pem
    - chmod 400 skeye.pem
    - ssh -o StrictHostKeyChecking=no -i skeye.pem ec2-user@$EC2_HOST "mkdir -p skeye-dev"
    - scp -o StrictHostKeyChecking=no -i skeye.pem dist.zip ec2-user@$EC2_HOST:/home/<USER>/skeye-dev/
    - ssh -o StrictHostKeyChecking=no -i skeye.pem ec2-user@$EC2_HOST "sudo yum install -y epel-release && sudo yum install -y bsdtar"
    - ssh -o StrictHostKeyChecking=no -i skeye.pem ec2-user@$EC2_HOST "sudo find /var/www/html -mindepth 1 -delete && sudo bsdtar -xkf /home/<USER>/skeye-dev/dist.zip -C /var/www/html --strip-components=1"
    - rm -f skeye.pem
    - rm -f dist.zip
  only:
    - develop
    - stage
    - master
  when: manual
