import * as child from 'child_process';
import { writeFileSync } from 'fs';
import { promisify } from 'util';
const exec = promisify(child.exec);

async function createVersionsFile(filename: string) {
  const revision = (await exec('git rev-parse --short HEAD')).stdout.toString().trim();
  const branch = (await exec('git rev-parse --abbrev-ref HEAD')).stdout.toString().trim();

  console.log(`version: '${process.env.npm_package_version}', revision: '${revision}', branch: '${branch}'`);

  const content = `
// this file is automatically generated by git.version.ts script
export const versions = {
  version: '${process.env.npm_package_version}',
  revision: '${revision}',
  branch: '${branch}',
  buildDateTime: '${new Date().toLocaleString()} UTC'
};
  `;

  writeFileSync(filename, content, { encoding: 'utf8' });
}

const filePath = 'src/environments/versions.ts';

if (!filePath) {
  throw 'please provide valid versions file path';
}

createVersionsFile(filePath);
