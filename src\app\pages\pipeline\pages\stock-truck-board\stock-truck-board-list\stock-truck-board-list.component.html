<app-page-header [pageTitle]="pageTitle">
  <div class="top-header" headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="excelDownloadMenu.toggle($event)"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
    <p-menu #excelDownloadMenu [model]="excelDownloadMenuItems" [popup]="true"> </p-menu>
    <button class="btn btn-primary left" (click)="onAdd()" *appHasPermission="[permissionActions.CREATE_STOCK_PIPELINE]" [appImageIconSrc]="constants.staticImages.icons.addNew">
      <span class="show-label">Add New Stock Truck</span>
    </button>
    <button class="btn btn-primary left column-btn show-label" (click)="toggleFilterSidebar()">
      <span class="show-label">Columns</span>
      <fa-icon [icon]="faIcons.faCaretDown"></fa-icon>
    </button>
  </div>
</app-page-header>

<div class="card tabs stock-truck-list">
  <div class="tab-content">
    <p-table
      [columns]="selectedColumns"
      [value]="stockTruckBoard"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="paginationConfig.predicate"
      [rowHover]="true"
      [loading]="isLoading"
      styleClass="p-datatable-gridlines"
      [resizableColumns]="true"
      columnResizeMode="expand"
    >
      <ng-template pTemplate="header" let-columns>
        <!-- heading row -->
        <tr>
          <ng-container *ngFor="let col of columns">
            <th
              pResizableColumn
              pReorderableColumn
              [pReorderableColumnDisabled]="true"
              *ngIf="col.disable && col.name === 'Stock'"
              [pSortableColumn]="col?.shortingKey"
              scope="col"
            >
              Stock <p-sortIcon [field]="col?.shortingKey"> </p-sortIcon>
            </th>
            <th pResizableColumn [pSortableColumn]="col?.shortingKey" [pSortableColumnDisabled]="!col.shorting" pReorderableColumn *ngIf="!col.disable" pResizableColumn>
              {{ col.name }}
              <span *ngIf="col.name !== this.hideFieldForSearch">
                <p-sortIcon [field]="col.shortingKey || col.field" *ngIf="col.shorting"></p-sortIcon>
              </span>
            </th>
            <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" *ngIf="col.disable && col.name === 'Action'">
              {{ col.name }}
            </th>
          </ng-container>
        </tr>
        <!-- Column search row -->
        <tr class="inventory-search-tr">
          <ng-container *ngFor="let col of columns">
            <th
              pResizableColumn
              class="small-col"
              *ngIf="col.disable && col.name === 'Stock' && col.key !== 'pipeline' && col.type !== 'DATE' && col.key !== 'pipelineType'"
              scope="col"
              pResizableColumn
            >
              <span class="search-input"><input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" /></span>
            </th>
            <th pResizableColumn *ngIf="!col.disable" pResizableColumn>
              <span class="search-input" *ngIf="col.type === 'DROP_DOWN'">
                <p-dropdown
                  [options]="statusesCopy"
                  [(ngModel)]="col.value"
                  (onChange)="tableSearchByColumn($event, col)"
                  class="w-190 stock-truck"
                  optionLabel="name"
                  optionValue="name"
                  [filter]="true"
                  filterBy="name"
                  [showClear]="col.value ? true : false"
                  placeholder="Select status"
                  appendTo="body"
                >
                  <ng-template pTemplate="selectedItem">
                    <div class="country-item country-item-value" *ngIf="col.value">
                      <div>{{ col.value }}</div>
                    </div>
                  </ng-template>
                  <ng-template let-status pTemplate="item">
                    <div class="country-item">
                      <div>{{ status.name }}</div>
                    </div>
                  </ng-template>
                </p-dropdown>
              </span>
              <span
                class="search-input"
                *ngIf="col.type !== 'DROP_DOWN' && col.key !== 'pipeline' && col.type !== 'DATE' && col.key !== 'pipelineType' && col.name !== this.hideFieldForSearch"
              >
                <input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="col.type === 'DATE'">
                <p-calendar
                  appendTo="body"
                  [showIcon]="true"
                  [showButtonBar]="true"
                  [readonlyInput]="true"
                  inputId="startDateIcon"
                  (onSelect)="tableSearchByColumn($event, col)"
                  (onClearClick)="clearDate()"
                  [(ngModel)]="col.value"
                ></p-calendar>
              </span>
            </th>
            <th pResizableColumn class="small-col" *ngIf="col.disable && col.name === 'Action'" pResizableColumn>
              <button type="button" class="btn btn-primary btn-sm reset-btn" (click)="clearSearchInput()">Reset filters</button>
            </th>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-stockData let-columns="columns" let-rowData let-rowIndex="rowIndex">
        <tr>
          <ng-container *ngFor="let col of columns">
            <td class="view-inventory" *ngIf="col.name === 'Stock' && col.disable">
              {{ stockData?.stockNumber }}
            </td>
            <ng-container *ngIf="col.key && col.key !== null && !col.disable && col.type !== 'DROP_DOWN'">
              <td>
                <span *ngIf="col.key !== 'pipeline' && col.key !== 'taskPercentage' && col.type !== 'DATE'">
                  {{ getEvaluatedExpression(col.key, stockData) }}
                </span>
                <span *ngIf="col.type === 'DATE'">
                  {{ getEvaluatedExpression(col.key, stockData) | date: constants.dateFormat }}
                </span>
                <span *ngIf="col.key === 'pipeline'">
                  <p-timeline [value]="rowData.phases" layout="horizontal">
                    <ng-template pTemplate="marker" let-pipeline>
                      <span [class]="pipeline?.taskStatus" appPipelineStatus>{{ pipeline?.sequenceNumber }}</span>
                    </ng-template>
                    <ng-template pTemplate="content" let-pipeline>
                      <span class="text-pre-wrap">{{ pipeline?.shop?.name }}</span
                      ><span class="initials" pTooltip="{{ pipeline?.assignee?.firstName }} {{ pipeline?.assignee?.lastName }}" tooltipPosition="bottom">{{
                        utils.getInitials(pipeline?.assignee?.firstName, pipeline?.assignee?.lastName) | uppercase
                      }}</span>
                    </ng-template>
                  </p-timeline>
                </span>
                <span *ngIf="col.key === 'taskPercentage'">
                  <div class="timeline">
                    <p-progressBar [style]="{ height: '5px' }" [class]="getTaskProgressClass(rowData)" [value]="getTaskProgress(rowData)" [showValue]="false"></p-progressBar>
                    <div class="footer">
                      <span class="date"
                        ><em class="pi pi-clock"></em>{{ rowData?.timeLine?.startTime | date: constants.monthAndDateFormat }} -
                        {{ rowData?.timeLine?.endTime | date: constants.monthAndDateFormat }}</span
                      >
                      <span class="value" [ngClass]="getTaskProgressClass(rowData)">{{ getTaskProgress(rowData) }}%</span>
                    </div>
                  </div>
                </span>
              </td>
            </ng-container>
            <td *ngIf="col.type === 'DROP_DOWN'">
              <p-dropdown
                class="w-190 stock-truck"
                appendTo="body"
                [options]="statusList"
                [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_STOCK_PIPELINE])"
                (click)="findStatusIndex(rowIndex)"
                optionLabel="name"
                [(ngModel)]="rowData.status"
                optionValue="value"
                (onChange)="changeStatus(rowData?.status, rowData?.id)"
              >
                {{ getStatusName(rowData?.status) }}
              </p-dropdown>
            </td>
            <td *ngIf="col.name === 'Action' && col.disable">
              <div class="actions-content">
                <img [src]="constants.staticImages.icons.edit" (click)="onEdit(rowData)" alt="" *appHasPermission="[permissionActions.UPDATE_STOCK_PIPELINE]" />
                <img [src]="constants.staticImages.icons.deleteIcon" (click)="onDelete(rowData, $event)" alt="" *appHasPermission="[permissionActions.DELETE_STOCK_PIPELINE]" />
              </div>
            </td>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </div>
</div>

<p-sidebar
  class="pipeline-config"
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-stock-truck-board-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal" [stockTruckInfo]="selectedStockTruckBoard"></app-stock-truck-board-add>
</p-sidebar>
<p-sidebar
  [(visible)]="showColumnModal"
  position="right"
  (onHide)="showColumnModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-column-dropdown
    (onClose)="toggleColumnSidebar()"
    *ngIf="showColumnModal"
    [isModelVisible]="showColumnModal"
    [privateFilter]="defaultColumnsArray"
    [filterParams]="getFilterSaveParams()"
    [columnsList]="dropDownColumnList"
    (onSubmitCheck)="callFilterApiAgain()"
  >
  </app-column-dropdown>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>
<p-confirmPopup *ngIf="showConfirmPopup"></p-confirmPopup>
