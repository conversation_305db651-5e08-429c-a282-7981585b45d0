import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { CommonModule, TitleCasePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { AddNewMakeModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-make/add-new-make.module';
import { AddNewModelModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-model/add-new-model.module';
import { AddNewUnitTypeModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-unit-type/add-new-unit-type.module';
import { GoogleMapModule } from '@sharedComponents/google-map/google-map.component.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { AccordionModule } from 'primeng/accordion';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmContactCustomerAddModule } from '../crm-contact-customer-add/crm-contact-customer-add.module';
import { CrmCustomerInventoryWrapperModule } from '../crm-customer-inventory-wrapper/crm-customer-inventory-wrapper.module';
import { CrmCustomerAddComponent } from './crm-customer-add.component';


@NgModule({
  declarations: [CrmCustomerAddComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeIconsModule,
    AccordionModule,
    DropdownModule,
    CardModule,
    SidebarModule,
    CrmCustomerInventoryWrapperModule,
    AddNewMakeModule,
    AddNewModelModule,
    AddNewUnitTypeModule,
    CrmContactCustomerAddModule,
    GoogleMapModule,
    NgxGpAutocompleteModule,
    CalendarModule,
    InputNumberModule,
    MultiSelectModule
  ],
  exports: [CrmCustomerAddComponent],
  providers: [TitleCasePipe, {
    provide: Loader,
    useValue: new Loader({
      apiKey: environment.googleMapApiKey,
      libraries: ['places']
    })
  }]
})

export class CrmCustomerAddModule { }
