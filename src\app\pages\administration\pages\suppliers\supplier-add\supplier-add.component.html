<div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
  <h4 class="header-title">{{ title }}</h4>
  <span *ngIf="isEditMode" class="created-by">
    <span class="bold-text">#{{ supplierInfo?.name }}</span>
    Created By <span class="bold-text">{{ supplierInfo?.createdBy?.name }}</span> on
    {{ supplierInfo?.createdDate | date : constants.fullDateFormat }}
  </span>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>

<form [formGroup]="supplierFormGroup" (ngSubmit)="onSubmit()">
  <div class="content">
    <section class="card p-3">
      <div class="title">
        <h4>Supplier Details</h4>
      </div>
      <div class="row">
        <div class="col-lg-4 col-md-6 col-12">
          <label class="required">Name</label>
          <input class="form-control" type="text" placeholder="Enter supplier name" formControlName="name" />
          <app-error-messages [control]="supplierFormGroup.controls.name"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>Email</label>
          <input class="form-control" type="email" placeholder="Enter supplier email" formControlName="email" />
          <app-error-messages [control]="supplierFormGroup.controls.email"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>Phone number</label>
          <input class="form-control" type="text" [mask]="contactNoFormat" placeholder="Enter phone number" formControlName="phoneNumber" />
          <app-error-messages [control]="supplierFormGroup.controls.phoneNumber"></app-error-messages>
          <span class="text-danger f-s-12" *ngIf="supplierFormGroup.controls.phoneNumber.errors?.mask">Please enter valid phone number</span>
        </div>
      </div>
    </section>
    <section formGroupName="contactPerson" class="card p-3">
      <div class="title">
        <h4>Contact Person Details</h4>
      </div>
      <div class="row">
        <div class="col-lg-4 col-md-6 col-12">
          <label>First Name</label>
          <input class="form-control" type="text" placeholder="Enter contact person first name" formControlName="firstName" />
          <app-error-messages [control]="contactPersonFormGroup.controls.firstName"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>Last Name</label>
          <input class="form-control" type="text" placeholder="Enter contact person last name" formControlName="lastName" />
          <app-error-messages [control]="contactPersonFormGroup.controls.lastName"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>Email</label>
          <input class="form-control" type="email" placeholder="Enter contact person email" formControlName="email" />
          <app-error-messages [control]="contactPersonFormGroup.controls.email"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>Phone number</label>
          <input class="form-control" type="text" [mask]="contactNoFormat" placeholder="Enter phone number" formControlName="phoneNumber" />
          <app-error-messages [control]="contactPersonFormGroup.controls.phoneNumber"></app-error-messages>
          <span class="text-danger f-s-12" *ngIf="contactPersonFormGroup.controls.phoneNumber.errors?.mask">Please enter valid phone number</span>
        </div>
      </div>
    </section>
    <section formGroupName="address" class="card p-3">
      <div class="title">
        <h4>Address Details</h4>
      </div>
      <div class="row">
        <div class="col-md-6 col-12">
          <label>Address</label>
          <input
            class="form-control"
            type="text"
            ngx-gp-autocomplete
            ngx-google-places-autocomplete
            [options]="options"
            (onAddressChange)="handleAddressChange($event)"
            placeholder="Enter address"
            formControlName="streetAddress"
          />
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>City</label>
          <input class="form-control" type="text" placeholder="Enter city name" formControlName="city" />
          <app-error-messages [control]="addressFormGroup.controls.city"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>State</label>
          <input class="form-control" type="text" placeholder="Enter state name" formControlName="state" />
          <app-error-messages [control]="addressFormGroup.controls.state"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-5 col-10">
          <label>Zip code</label>
          <input class="form-control" type="text" placeholder="Enter zip code" formControlName="zipcode" />
          <app-error-messages [control]="addressFormGroup.controls.zipcode"></app-error-messages>
        </div>
        <div class="col-1">
          <div
            class="map-icon"
            *ngIf="
              addressFormGroup.get('streetAddress')?.value || addressFormGroup.get('city')?.value || addressFormGroup.get('state')?.value || addressFormGroup.get('zipcode')?.value
            "
          >
            <button class="btn btn-primary" type="button" (click)="toggleGoogleMapPopUp()">
              <fa-icon [icon]="faIcons.faLocationDot"></fa-icon>
            </button>
          </div>
        </div>
        <p-sidebar
          [closeOnEscape]="false"
          [dismissible]="false"
          [(visible)]="showGoogleMapSideBar"
          position="right"
          (onHide)="showGoogleMapSideBar = false"
          [blockScroll]="true"
          [showCloseIcon]="false"
          styleClass="p-sidebar-md"
          [baseZIndex]="10000"
          appendTo="body"
        >
          <app-google-map (onClose)="toggleGoogleMapPopUp()" *ngIf="showGoogleMapSideBar" [addressGroup]="addressFormGroup.value" [address]="fullAddress"> </app-google-map>
        </p-sidebar>
      </div>
    </section>
    <section class="card p-3">
      <div class="title">
        <h4>Additional Details</h4>
      </div>
      <div class="row">
        <div class="col-12">
          <textarea placeholder="Enter additional notes" rows="3" formControlName="additionalDetail"></textarea>
        </div>
      </div>
    </section>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" *ngIf="!isViewMode" appShowLoaderOnApiCall>Save</button>
    <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!isEditMode" appShowLoaderOnApiCall>Save & Add New</button>
  </div>
</form>
