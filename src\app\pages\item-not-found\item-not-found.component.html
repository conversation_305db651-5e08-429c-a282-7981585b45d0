<div class="d-flex flex-column align-items-center justify-content-center h-full not-found-container">
  <div class="grid mb-5 rounded-full bg-gradient-to-br from-gray-900 to-indigo-300 w-28 h-28 place-items-center">
    <img [src]="isDarkMode ? constants.staticImages.icons.white404Icon : constants.staticImages.icons.black404Icon" alt="" />
  </div>
  <h1 class="mb-5 text-3xl" [ngClass]="{ 'text-white': isDarkMode }">{{ message }}</h1>
  <a class="bg-blue" [routerLink]="['/']"> Take me somewhere nice </a>
</div>
