@import "/src/assets/scss/variables";
@import "/src/assets/scss/theme/mixins";

.inventory-matching-wrapper {
  height: calc(100vh - 115px);
  overflow-y: auto;
  padding: 20px;

  p-checkbox {
    margin: 5px 15px;
  }

  p-checkbox:first-child {
    margin-left: 0;
  }

  p-checkbox:last-child {
    margin-right: 0;
  }

  .add-duplicate {
    @include flex-end;
    margin-bottom: 10px;
  }

  .search-input {
    input {
      min-width: 200px;
      height: 36px !important;
    }

    p-calendar.p-inputwrapper {
      span.p-calendar-w-btn {
        height: 36px !important;
      }
    }
  }
}

.contact-vendor {
  p-dropdown {
    min-width: 200px !important;
    height: 36px !important;
  }

  ::ng-deep .p-dropdown {
    height: 36px !important;

    .p-inputtext {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }
  }
}

.primary-inventories {
  img {
    height: 185px;
    width: 100%;
  }

  .model-name {
    margin-top: 10px;
  }

  .model-details {
    margin-top: 10px;
  }
}

.model-details {
  font-weight: 500;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 15px;

  div {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 9px;

    .model-detail-label {
      color: #939393;
      margin-right: 2px;
    }
  }
}

.title {
  border-bottom: 1px solid #e3e3e3;
  margin-bottom: 10px;

  h4 {
    margin: 12px;
    font-size: 16px !important;
    color: var(--active-color);
    font-weight: 600 !important;
    letter-spacing: 0;
    line-height: 25px !important;
    text-transform: uppercase;
  }
}
