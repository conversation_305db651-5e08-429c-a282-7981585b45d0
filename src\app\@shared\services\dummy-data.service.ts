export class DummyService {

  static randomString(length = 6): string {
    let text = "";
    const possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    for (let i = 0; i < length; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }

  static randomPriority(): string {
    const priorities = ['HIGHEST', 'HIGH', 'MEDIUM', 'LOW', 'MINOR'];
    return priorities[Math.floor(Math.random() * priorities.length)];
  }

  static randomNumber(length = 6): number {
    return String(Math.random()).substring(2, length + 2) as unknown as number;
  }

  static randomEmail(): string {
    return `${DummyService.randomString()}@${DummyService.randomString()}.com`;
  }

  static randomDataList<ReturnType>(dataGeneratorFunction: Function, listSize = 5): ReturnType[] {
    const list: ReturnType[] = [];
    for (let i = 0; i < listSize; i++) {
      list.push(dataGeneratorFunction());
    }
    return list;
  }
}
