import { Utils } from "src/app/@shared/services";

export interface Shop {
  id: number;
  name: string;
  defaultUserId: number;
  defaultUserName: string;
}

export interface ShopParam {
  name: string;
}

export class ShopUser {
  id!: number;
  name!: string;
  firstName!: string;
  lastName!: string;

  get fullName(): string {
    return Utils.getFullName(this.firstName, this.lastName);
  }

  constructor(user: ShopUser) {
    Object.assign(this, user);
  }
}
