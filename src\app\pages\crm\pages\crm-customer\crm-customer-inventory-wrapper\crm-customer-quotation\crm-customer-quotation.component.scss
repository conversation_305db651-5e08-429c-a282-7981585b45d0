@import "src/assets/scss/variables";

.btn {
  width: 100px;
  padding: 0 20px !important;
}

::ng-deep .crm-quotation {
  .p-datatable .p-datatable-thead > tr > th:last-child {
    text-align: center;
  }
}

.action {
  text-align: center !important;
  justify-content: center;
}

.quotationAccepted {
  font-size: 13px;
  color: $green-color;
}

.quotationRejected {
  font-size: 13px;
  color: var(--danger-color);
}

.green-highlight {
  color: $green-color;
}

.m-l-10 {
  margin-left: 10px;
}

.view-reminder {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
  z-index: 1;
}

td.empty {
  display: flex;
  width: 100%;
  justify-content: center;
}
