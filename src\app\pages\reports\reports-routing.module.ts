import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionForChildGuard } from '@core/guards';
import { ROUTER_UTILS } from '@core/utils';
import { PermissionActions } from 'src/app/@shared/models';

const routes: Routes = [
  {
    path: ROUTER_UTILS.config.reporting.crm.root,
    loadChildren: async () => (await import('./pages/crm-reports/crm-reports.module')).CrmReportsModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [
        PermissionActions.VIEW_DAILY_SALES_REPORT,
        PermissionActions.VIEW_ACTIVITY_REPORT,
        PermissionActions.VIEW_INVENTORY_SALES_REPORT
      ]
    }
  },
  {
    path: ROUTER_UTILS.config.reporting.inventoryReport.root,
    loadChildren: async () => (await import('./pages/inventory-reports/inventory-reports.module')).InventoryReportsModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [
        PermissionActions.VIEW_INVENTORY_SALES_REPORT,
        PermissionActions.VIEW_PROFITABILITY_REPORT,
      ]
    }
  },
  {
    path: ROUTER_UTILS.config.reporting.vendorsReport,
    loadChildren: async () => (await import('./pages/vendor-reports/vendor-reports.module')).VendorReportsModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [
        PermissionActions.VIEW_VENDORS_REPORT,
      ]
    }
  },
  {
    path: ROUTER_UTILS.config.reporting.supplierReport,
    loadChildren: async () => (await import('./pages/supplier-reports/supplier-reports.module')).SupplierReportsModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [
        PermissionActions.VIEW_SUPPLIERS_REPORT,
      ]
    }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReportsRoutingModule { }
