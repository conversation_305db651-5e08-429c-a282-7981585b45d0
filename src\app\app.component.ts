import { Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { CommonService, SplashScreenService } from '@core/services';
import { AuthService } from '@pages/auth/services/auth.service';
import 'firebase/messaging';
import { setTheme } from 'ngx-bootstrap/utils';
import { Observable } from 'rxjs';
import { Constants } from './@shared/constants/app.constants';
import { LocalStorageService, StorageItem } from './@shared/services/local-storage.service';
import { THEMES, ThemeService } from './@shared/services/theme.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
})
export class AppComponent implements OnInit {
  isLoggedIn$!: Observable<boolean>;
  isUIBlocked = false;
  message!: string;
  isPublicPage = false;

  constructor(
    private readonly authService: AuthService,

    private readonly themeService: ThemeService,
    private readonly splashScreenService: SplashScreenService,
    private readonly router: Router,
    private readonly commonService: CommonService,
    private readonly localStorageStorageService: LocalStorageService
  ) {
    setTheme('bs5');
  }

  ngOnInit(): void {
    const storedTheme = this.localStorageStorageService.getItem(StorageItem.Theme);
    this.isLoggedIn$ = this.authService.isLoggedIn$;
    this.setTheme(storedTheme as
      THEMES);
    this.initializeRouterEventListener();
    this.initializeBlockUIListener();
  }

  initializeRouterEventListener() {
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.splashScreenService.hide();
        this.isPublicPage = event.url.includes('public-inventory') || event.url.includes('redirect-to-authorized-page');
        window.scrollTo(0, 0);
        setTimeout(() => {
          document.body.classList.add('page-loaded');
        }, Constants.splashScreenTimeout);
      }
    });
  }

  setTheme(theme: THEMES): void {
    if (theme === THEMES.DARK) {
      this.themeService.changeTheme(true);
    } else {
      this.themeService.changeTheme(false);
    }
  }

  private initializeBlockUIListener() {
    this.commonService.blockUI$.subscribe(blockUI => {
      this.isUIBlocked = blockUI;
    });
    this.commonService.message$.subscribe(message => {
      this.message = message;
    })
  }

}
