import { Injectable, Injector } from "@angular/core";

export class AccordionGroup<T> {
  title: string | undefined;
  customClass?: string;
  content: any;
  contentType: AccordionGroupContentType = AccordionGroupContentType.COMPONENT;
  isOpen?: boolean;
  injector: Injector;
  data?: T;

  constructor(groupData: { content: any, contentType?: AccordionGroupContentType, title: string, customClass?: string, isOpen?: boolean, injector?: Injector, data?: T }) {
    this.content = groupData.content;
    this.contentType = groupData.contentType || AccordionGroupContentType.COMPONENT;
    this.title = groupData.title;
    this.customClass = groupData.customClass;
    this.isOpen = groupData.isOpen;
    this.injector = groupData.injector || Injector.NULL;
    this.data = groupData.data;
  }
}

export enum AccordionGroupContentType {
  COMPONENT,
  TEMPLATE,
  INTERPOLATION,
}

@Injectable()
export class AccordionData<T> {
  data!: T;
}
