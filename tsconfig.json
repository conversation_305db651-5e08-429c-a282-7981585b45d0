/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": false,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "es2020",
    "module": "es2020",
    "types": ["node"],
    "lib": [
      "es2018",
      "dom"
    ],    
    "paths": {
      "@core/*": ["src/app/@core/*"],
      "@shell/*": ["src/app/@shell/*"],
      "@constants/*": ["src/app/@shared/constants"],
      "@sharedModules/*": ["src/app/@shared/modules"],
      "@sharedComponents/*": ["src/app/@shared/components/*"],
      "@pages/*": ["src/app/pages/*"],
      "@env/*": ["src/environments/*"]
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true,
    "fullTemplateTypeCheck": true
  }
}
