import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CrmContactListItem } from '@pages/administration/models/crm.model';
import { QuotationCustomerLeadKey, QuotationCustomerListFilter, QuotationCustomerListItem, QuotationFilterParams, QuotationStatus } from '@pages/crm/models/customer-lead-quotation.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { takeUntil } from 'rxjs';
import { DataType, OperatorType } from 'src/app/@shared/models';

@Component({
  selector: 'app-crm-contact-quotation',
  templateUrl: './crm-contact-quotation.component.html',
  styleUrls: ['./crm-contact-quotation.component.scss']
})
export class CrmContactQuotationComponent extends BaseComponent implements OnInit, OnChanges {
  isLoading = false;
  quotationList = [];
  quotationValue = '1';
  filterParams: QuotationCustomerListFilter = new QuotationCustomerListFilter();
  quotationListFormGroup!: FormGroup;
  quotationStatus = QuotationStatus;
  @Input() crmContactInfo!: CrmContactListItem | null;
  @Input() quotationCustomerList!: QuotationCustomerListItem[] | any;
  @Output() onQuotationAccepted: EventEmitter<boolean> = new EventEmitter<boolean>();
  showRejectModel = false;
  selectedQuotationId!: string;

  showCreateModal = false;
  customerLeadId!: string;
  quotationId!: string;

  showInventoryDetails = false;
  selectedInventoryId!: number | null;
  selectedInventoryAbbreviation!: string | null;

  constructor(private readonly crmService: CrmService,
    private readonly formBuilder: FormBuilder,
    private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef) { super(); }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getQuotationDetails();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.quotationCustomerList?.currentValue) {
      this.setQuotationDetails();
    }
  }

  initializeFormGroup() {
    this.quotationListFormGroup = this.formBuilder.group({
      quotation: this.formBuilder.array([])
    });
  }

  get quatationListFormArray(): FormArray {
    return this.quotationListFormGroup.get('quotation') as FormArray;
  }

  get getQuotationRequest(): QuotationFilterParams {
    return {
      values: [{
        dataType: DataType.LONG,
        key: QuotationCustomerLeadKey.CRM_CUSTOMER,
        operator: OperatorType.EQUAL,
        value: this.crmContactInfo?.id ? this.crmContactInfo?.id : ''
      }]
    };
  }

  private getQuotationDetails(): void {
    this.crmService.add<QuotationFilterParams>(this.getQuotationRequest, API_URL_UTIL.admin.crm.quatationFilter).pipe(takeUntil(this.destroy$)).subscribe({
      next: (quotation) => {
        this.quotationCustomerList = quotation.content;
        this.setQuotationDetails();
        this.cdf.detectChanges();
      }
    });
  }

  private setQuotationDetails(): void {
    this.quatationListFormArray.clear();
    if (this.quotationCustomerList) {
      for (const quotation of this.quotationCustomerList) {
        this.quatationListFormArray.push(this.formBuilder.group({
          quotationId: quotation.id,
          customerLeadId: quotation.customerLeadId,
          stock: quotation?.quotationUnitResponseDTOList[0]?.stockNumber,
          leadDescription: quotation.leadDescription,
          company: quotation.company,
          dateQuotated: quotation.quotationDate,
          quotedBy: quotation.createdBy.name,
          imageUrl: quotation.unit?.unitImages ? quotation.unit.unitImages.url : null,
          year: quotation.unit?.generalInformation?.year,
          make: quotation.unit?.generalInformation?.make?.name,
          model: quotation.unit?.generalInformation?.unitModel?.name,
          quotedPrice: quotation.totalQuotePrice,
          retailPrice: quotation.totalRetailPrice,
          quotationStatus: quotation.quotationStatus,
          quotationUnitResponseDTOList: quotation?.quotationUnitResponseDTOList
        }));
      }
    }
  }

  get acceptedParams() {
    return {
      quotationStatus: "ACCEPTED"
    }
  }

  onAccepted(selectedQuotationId: string): void {
    const endpoint = API_URL_UTIL.admin.crm.acceptQuotation.replace(':quotationId', selectedQuotationId);
    this.crmService.patch(this.acceptedParams, endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.quotationAccepted);
        this.getQuotationDetails();
        this.setQuotationDetails();
      }
    });
  }

  onReject(id: string): void {
    this.showRejectModel = true;
    this.selectedQuotationId = id;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showRejectModel = false;
    if (refreshList) {
      this.getQuotationDetails();
      this.cdf.detectChanges();
    }
  }

  onCloseModal(): void {
    this.showCreateModal = false;
  }

  onSaveQuotation() {
    this.getQuotationDetails();
    this.onQuotationAccepted.emit(true);
  }

  showQuotationDetails(data: any): void {
    this.customerLeadId = data.customerLeadId;
    this.quotationId = data.quotationId;
    this.showCreateModal = true;
  }

  openInventoryDetailsModal(data: any): void {
    this.showInventoryDetails = true;
    this.selectedInventoryId = data.quotationUnitResponseDTOList.unitId;
    this.selectedInventoryAbbreviation = data.quotationUnitResponseDTOList.abbreviation;
  }

  onCancel(): void {
    this.showInventoryDetails = false;
    this.selectedInventoryId = null;
    this.selectedInventoryAbbreviation = null;
  }
}
