import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { VendorsModule } from '@pages/administration/pages/vendors/vendors.module';
import { CrmContactAddModule } from '@pages/crm/pages/crm-contact/crm-contact-add/crm-contact-add.module';
import { SoldTruckBoardModule } from '@pages/pipeline/pages/sold-truck-board/sold-truck-board.module';
import { StockTruckBoardModule } from '@pages/pipeline/pages/stock-truck-board/stock-truck-board.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { TimelineModule } from 'primeng/timeline';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { AddNewMakeModule } from '../add-new-make/add-new-make.module';
import { AddNewModelModule } from '../add-new-model/add-new-model.module';
@NgModule({
  // declarations: [InventoryGeneralInfoComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeIconsModule,
    AccordionModule,
    DropdownModule,
    CardModule,
    SidebarModule,
    CheckboxModule,
    ButtonModule,
    MultiSelectModule,
    TooltipModule,
    AddNewMakeModule,
    AddNewModelModule,
    TimelineModule,
    VendorsModule,
    StockTruckBoardModule,
    SoldTruckBoardModule,
    CrmContactAddModule,
  ],
  providers: [ConfirmationService],
})
export class InventoryGeneralInfoModule { }
