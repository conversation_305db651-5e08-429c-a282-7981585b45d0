import { QuotationFilterParams } from "./customer-lead-quotation.model";

export interface CrmContactReminders {
  id: number;
  notes: string;
  reminderTime: string;
  reminderStatus: string;
  deleted: boolean;
  sendReminder: string;
  remindTo: RemindTo;
  crmContact: CrmContact;
  scheduleAfter: number;
  reoccurring: boolean;
  remindToName?: string;
}

export interface CrmContact {
  id: number;
  name: string;
}

export interface RemindTo {
  id: number;
  name: string;
}

export interface ReminderParams extends QuotationFilterParams {
  contactId: string;
  deleted: boolean;
}

export interface ChangeAccountRepParams {
  ids: Number[],
  accountRepId: number
}