import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { InventoryListItem } from '@pages/inventory/models';
import { Notes, NotesDetailsResponse } from '@pages/inventory/models/notes.model';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { Observable, takeUntil } from 'rxjs';
@Component({
  selector: 'app-inventory-notes',
  templateUrl: './inventory-notes.component.html',
  styleUrls: []
})
export class InventoryNotesComponent extends BaseComponent implements OnInit, OnChanges {

  inventoryNotesExternalFormGroup!: FormGroup;
  internalNotesById!: NotesDetailsResponse;
  externalNotesById!: NotesDetailsResponse;
  internalNotesDetails!: string | null;
  externalNotesDetails!: string | null;
  unitId!: number;
  isEditMode = false;
  externalNotesMsg = "External Notes are visible to the public and will print on specifications sheets by default.";
  internalNotesMsg = "Internal Notes are visible internally only."
  accordionTabs = {
    externalNotes: true,
    internalNotes: true
  };
  isEditInternalNotesExist = false;
  isEditExternalNotesExist = false;
  @Input() isViewMode!: boolean;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Output() onChangeActiveIndex: EventEmitter<boolean> = new EventEmitter<boolean>()
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;

  constructor(private readonly formBuilder: FormBuilder,
    private readonly inventoryService: InventoryService,
    private readonly toasterService: AppToasterService,
    private readonly commonSharedService: CommonSharedService) {
    super();
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getUnitId();
    if (this.isEditMode && (this.inventoryInfo || this.inventoryIncomingInfo)) {
      this.NotesByUnitId();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.inventoryInfo?.currentValue || changes.inventoryIncomingInfo?.currentValue) {
      this.isEditMode = true;
    }
  }

  private initializeFormGroup(): void {
    this.inventoryNotesExternalFormGroup = this.formBuilder.group({
      internalNotes: new FormControl(null),
      externalNotes: new FormControl(null)
    })
  }

  notesCreateParams(unitId?: number): any {
    const params = [];
    if (this.newExternalNotesFormGroup(unitId)) {
      params.push(this.newExternalNotesFormGroup(unitId))
    }
    if (this.newInternalNotesFormGroup(unitId)) {
      params.push(this.newInternalNotesFormGroup(unitId))
    }
    return params;
  }

  newInternalNotesFormGroup(id?: number): any {
    const unitId = this.inventoryIncomingInfo ? this.inventoryIncomingInfo?.unitId : this.inventoryInfo?.id;
    const internalNotesById = this.internalNotesById?.id ? this.internalNotesById?.id : null;
    const notesunitId = this.internalNotesById?.unitId ? this.internalNotesById?.unitId : unitId;
    return {
      id: this.isEditMode ? internalNotesById : null,
      note: this.internalNotesDetails ? this.internalNotesDetails : null,
      noteType: Notes.internal,
      unitId: this.isEditMode ? notesunitId : (unitId ?? id)
    }
  }

  newExternalNotesFormGroup(id?: number): any {
    const unitId = this.inventoryIncomingInfo ? this.inventoryIncomingInfo?.unitId : this.inventoryInfo?.id;
    const externalNotesById = this.externalNotesById?.id ? this.externalNotesById?.id : null;
    const externalNotesunitId = this.externalNotesById?.unitId ? this.externalNotesById?.unitId : unitId;
    return {
      id: this.isEditMode ? externalNotesById : null,
      note: this.externalNotesDetails ? this.externalNotesDetails : null,
      noteType: Notes.external,
      unitId: this.isEditMode ? externalNotesunitId : (unitId ?? id)
    }
  }


  private NotesByUnitId() {
    const unitId = this.inventoryIncomingInfo?.unitId || this.inventoryInfo?.id;
    if (!unitId) {
      return;
    }
    const endpoint = API_URL_UTIL.inventory.notesDetails.replace(':unitId', String(unitId));
    this.inventoryService.get<NotesDetailsResponse[]>(endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (notesDetails) => {
          if (!notesDetails || notesDetails.length === 0) {
            return;
          }
          this.isEditExternalNotesExist = false;
          this.isEditInternalNotesExist = false;
          for (const notesDetail of notesDetails) {
            if (notesDetail.noteType === Notes.external) {
              this.isEditExternalNotesExist = true;
              this.externalNotesById = notesDetail;
              this.externalNotesDetails = notesDetail.note;
            } else if (notesDetail.noteType === Notes.internal) {
              this.isEditInternalNotesExist = true;
              this.internalNotesById = notesDetail;
              this.internalNotesDetails = notesDetail.note;
            }
          }
        }
      });
  }

  private getUnitId() {
    if (!this.isEditMode) {
      this.inventoryService.getUnitId$().pipe(takeUntil(this.destroy$)).subscribe(res => {
        this.unitId = res;
      });
    }
  }

  private saveNotes(close = true, unitId?: number): void {
    const request$ = this.inventoryService.add(this.notesCreateParams(unitId), API_URL_UTIL.inventory.notes);
    this.handleNotesRequest(request$, close);
  }

  private updateNotes(close = true): void {
    const request$ = this.inventoryService.update(this.notesCreateParams(), API_URL_UTIL.inventory.notes);
    this.handleNotesRequest(request$, close);
  }

  private handleNotesRequest(request$: Observable<any>, close: boolean): void {
    request$.pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(this.isEditMode ? MESSAGES.notesUpdatedSuccess : MESSAGES.notesAddSuccess);
        if (close) {
          this.commonSharedService.setBlockUI$(false);
        }
      },
      error: () => {
        this.commonSharedService.setBlockUI$(false);
      }
    });
  }


  onSubmit(close = true, unitId?: number): void {
    this.commonSharedService.setBlockUI$(true);
    if (this.isEditInternalNotesExist && this.isEditExternalNotesExist) {
      this.updateNotes(close)
    }
    else {
      this.saveNotes(close, unitId);
    }
  }

  onSubmitAndNext(close = false): void {
    this.onSubmit(close);
  }

}

