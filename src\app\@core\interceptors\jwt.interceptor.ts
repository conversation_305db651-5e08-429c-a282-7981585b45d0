import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { APIS_TO_IGNORE_DUPLICATE_CHECK, API_URL_UTIL, StorageItem, getItem } from '@core/utils';
import { RxJsUtils } from '@core/utils/rxjs.utils';
import { environment } from '@env/environment';
import { AuthService } from '@pages/auth/services/auth.service';
import { Observable } from 'rxjs';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {

  constructor(private readonly authService: AuthService) { }

  intercept(request: HttpRequest<unknown>, next: HttpHand<PERSON>,): Observable<HttpEvent<unknown>> {
    const isLoggedIn = this.authService.isLoggedIn;

    if (isLoggedIn) {
      let token = getItem(StorageItem.AuthToken);
      if (request.url.includes(API_URL_UTIL.account.refreshAccessToken)) {
        token = getItem(StorageItem.RefreshToken);
      }
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`,
        },
      });
    }
    request = request.clone({
      url: `${environment.apiUrl}/${request.url}`,
    });
    const { urlWithParams } = request;

    if (APIS_TO_IGNORE_DUPLICATE_CHECK.some(url => request.url.includes(url))) {
      return next.handle(request);
    }

    return next.handle(request).pipe(RxJsUtils.shareDuplicate(urlWithParams));
  }
}
