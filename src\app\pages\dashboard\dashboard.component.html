<div class="content">
  <div class="row m-t-20">
    <div class="col-xl col-lg-6 col-md-12 " *ngIf="dailyMonthlySalesVisible">
      <div class="widget style1 daily-sale-section cursor-pointer" (click)="onClickDailySales()">
        <div class="row">
          <div class="col-6">
            <h6>Daily Sales</h6>
            <h2 class="font-bold text-left sale-count-color">${{ dailySales | number: '1.0-0' }}</h2>
          </div>
          <div class="col-6 text-end">
            <img [src]="constants.staticImages.icons.sale" alt="" />
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl col-lg-6 col-md-12" *ngIf="dailyMonthlySalesVisible">
      <div class="widget style1 monthly-sale-section cursor-pointer" (click)="onClickDailySales()">
        <div class="row">
          <div class="col-6">
            <h6>Monthly Sales</h6>
            <h2 class="font-bold text-left monthly-count-color">${{ monthlySales | number: '1.0-0' }}</h2>
          </div>
          <div class="col-6 text-end">
            <img [src]="constants.staticImages.icons.truckSale" alt="" />
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl col-lg-6 col-md-12">
      <div class="widget style1 total-inventory-section cursor-pointer" (click)="onClickInventoryCount()">
        <div class="row">
          <div class="col-6">
            <h6>Total Inventory Count</h6>
            <h2 class="font-bold text-left truck-count">{{ totalTruckInvenotryCount | number: '1.0-0' }}</h2>
          </div>
          <div class="col-6 text-end">
            <img [src]="constants.staticImages.icons.inventory" alt="" />
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl col-lg-6 col-md-12">
      <div class="widget style1 incoming-truck-section cursor-pointer" (click)="onClickTruckCount()">
        <div class="row">
          <div class="col-6">
            <h6>Incoming Truck Count</h6>
            <h2 class="font-bold text-left incoming-truck-count-color">{{ incomingTruckInventoryCount | number: '1.0-0' }}</h2>
          </div>
          <div class="col-6 text-end m-t-25">
            <img [src]="constants.staticImages.icons.truck" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row m-t-0">
    <div class="col-12 col-sm-6" *appHasPermission="[permissionActions.VIEW_CUSTOMER_LEAD_INFO]">
      <div class="col-12 p-0 child-right card tabs sold-truck-list">
        <div class="tab-content">
          <div class="title d-flex space-between">
            <div>
              <h6>Inventory Preference Section</h6>
            </div>
            <div class="justify-content-end">
              <fa-icon [icon]="faIcons.faExpandAlt" *ngIf="!modelPopups.isFullViewInventory" (click)="expandViewModel('inventory-Preference')"></fa-icon>
            </div>
          </div>
          <div>
            <app-inventory-preference-list></app-inventory-preference-list>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-sm-6" *appHasPermission="[permissionActions.VIEW_TASK_INFO]">
      <div class="col-12 p-0 card tabs sold-truck-list">
        <div class="tab-content">
          <div class="title d-flex space-between">
            <div>
              <h6>Task</h6>
            </div>
            <div class="justify-content-end">
              <fa-icon [icon]="faIcons.faExpandAlt" *ngIf="!modelPopups.isFullViewTask" (click)="expandViewModel('task')"></fa-icon>
            </div>
          </div>
          <app-pending-task-chart></app-pending-task-chart>
        </div>
      </div>
    </div>
  </div>

  <div class="row m-t-0">
    <div class="col-12 col-sm-6" *appHasPermission="[permissionActions.VIEW_SALES_INFO]">
      <div class="col-12 p-0 child-right card tabs sold-truck-list">
        <div class="tab-content">
          <div class="title d-flex space-between">
            <div>
              <h6>Monthly Sales Summary for all users</h6>
            </div>
            <div class="justify-content-end">
              <fa-icon [icon]="faIcons.faExpandAlt" *ngIf="!modelPopups.isFullViewSaleByMonth" (click)="expandViewModel('Sales by month')"></fa-icon>
            </div>
          </div>
          <div>
            <app-sales-by-month-chart></app-sales-by-month-chart>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-sm-6" *appHasPermission="[permissionActions.VIEW_INVENTORY_INFO]">
      <div class="col-12 p-0 card tabs sold-truck-list">
        <div class="tab-content">
          <div class="title d-flex space-between">
            <div>
              <h6>Inventory Aging</h6>
            </div>
            <div class="justify-content-end">
              <fa-icon [icon]="faIcons.faExpandAlt" *ngIf="!modelPopups.isFullViewInventoryAging" (click)="expandViewModel('Inventory aging')"></fa-icon>
            </div>
          </div>
          <app-inventory-aging-chart></app-inventory-aging-chart>
        </div>
      </div>
    </div>
    <div class="col-12" *appHasPermission="[permissionActions.VIEW_TASK_INFO]">
      <div class="col-12 p-0 card tabs sold-truck-list">
        <div class="tab-content">
          <div class="title d-flex space-between">
            <div>
              <h6>Task New Alerts</h6>
            </div>
            <div class="justify-content-end">
              <fa-icon [icon]="faIcons.faExpandAlt" *ngIf="!modelPopups.isFullViewTaskAlerts" (click)="expandViewModel('Task Alerts')"></fa-icon>
            </div>
          </div>
          <app-task-alerts-list></app-task-alerts-list>
        </div>
      </div>
    </div>
    <div class="col-12" *appHasPermission="[permissionActions.VIEW_INVENTORY_INFO]">
      <div class="col-12 p-0 card tabs sold-truck-list">
        <div class="tab-content">
          <div class="title d-flex space-between">
            <div>
              <h6>Recent Inventories</h6>
            </div>
            <div class="justify-content-end">
              <fa-icon [icon]="faIcons.faExpandAlt" *ngIf="!modelPopups.isFullViewTaskAlerts" (click)="expandViewModel('Recent Inventory')"></fa-icon>
            </div>
          </div>
          <app-recently-added-inventory></app-recently-added-inventory>
        </div>
      </div>
    </div>
  </div>
</div>
<p-sidebar
  class="pipeline-config"
  [(visible)]="modelPopups.isFullViewInventory"
  [fullScreen]="true"
  (onHide)="modelPopups.isFullViewInventory = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-inventory-preference-list (onClose)="onAddEditPopupClose('isFullViewInventory')" *ngIf="modelPopups.isFullViewInventory" [isFullView]="true">
  </app-inventory-preference-list>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [(visible)]="modelPopups.isFullViewTask"
  [fullScreen]="true"
  (onHide)="modelPopups.isFullViewTask = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-pending-task-chart (onClose)="onAddEditPopupClose('isFullViewTask')" *ngIf="modelPopups.isFullViewTask" [isFullViewPendingTask]="true"> </app-pending-task-chart>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [(visible)]="modelPopups.isFullViewInventoryAging"
  [fullScreen]="true"
  (onHide)="modelPopups.isFullViewInventoryAging = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-inventory-aging-chart (onClose)="onAddEditPopupClose('isFullViewInventoryAging')" *ngIf="modelPopups.isFullViewInventoryAging" [isFullViewInventoryAging]="true">
  </app-inventory-aging-chart>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [(visible)]="modelPopups.isFullViewSaleByMonth"
  [fullScreen]="true"
  (onHide)="modelPopups.isFullViewSaleByMonth = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-sales-by-month-chart (onClose)="onAddEditPopupClose('isFullViewSaleByMonth')" *ngIf="modelPopups.isFullViewSaleByMonth" [isFullViewSalesByMonth]="true">
  </app-sales-by-month-chart>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [(visible)]="modelPopups.isFullViewTaskAlerts"
  [fullScreen]="true"
  (onHide)="modelPopups.isFullViewTaskAlerts = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-task-alerts-list (onClose)="onAddEditPopupClose('isFullViewTaskAlerts')" *ngIf="modelPopups.isFullViewTaskAlerts" [isFullViewTaskAlerts]="true"> </app-task-alerts-list>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [(visible)]="modelPopups.isFullViewRecentInventory"
  [fullScreen]="true"
  (onHide)="modelPopups.isFullViewRecentInventory = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-recently-added-inventory (onClose)="onAddEditPopupClose('isFullViewRecentInventory')" *ngIf="modelPopups.isFullViewRecentInventory" [isFullViewRecentInventory]="true">
  </app-recently-added-inventory>
</p-sidebar>
