
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { FullCalendarModule } from '@fullcalendar/angular';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { ConfirmationService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { EventCalendarComponent } from './event-calendar/event-calendar.component';


@NgModule({
  declarations: [
    EventCalendarComponent
  ],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    FullCalendarModule,
    DialogModule,
    DropdownModule,
    FormsModule,
    CalendarModule,
    RouterModule.forChild([
      {
        path: '',
        component: EventCalendarComponent,
        data: {
          title: 'calendar',
          robots: 'noindex, nofollow',
        },
      },
    ]),
  ],
  providers: [ConfirmationService],

})
export class EventCalendarModule { }
