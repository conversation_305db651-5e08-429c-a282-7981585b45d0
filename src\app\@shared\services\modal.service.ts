import { Injectable, OnDestroy, TemplateRef } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';

@Injectable({
  providedIn: 'root'
})
export class ModalService extends BsModalService implements OnDestroy {
  modalRefs: BsModalRef[] = [];

  showModal(content: string | TemplateRef<any> | any, config?: ModalOptions) {
    const modal = this.show(content, config);
    this.modalRefs.push(modal);
    return modal;
  }

  ngOnDestroy() {
    this.modalRefs = [];
  }

  hideModal(id?: string | number | undefined) {
    this.hide(id);
  }
}
