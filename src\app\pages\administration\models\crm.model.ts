import { GenericFilterParams, IdNameModel } from "src/app/@shared/models";

export interface Contact {
  firstName: string,
  lastName: string,
  company: string,
  jobTitle: string,
  department: string,
  email: string,
  personalEmail: string,
  webSite: string,
  phoneWork: number,
  phoneMobile: number,
  fax: number,
  notes: string,
  streetAddress: string,
  city: string,
  state: string,
  zipcode: string,
  country: string,
  accountReporterId: number
}

export interface ContactDetails {
  id: number,
  company: string,
  contactName: string,
  email: string,
  phoneWork: number,
  streetAddress: string,
  city: string,
  state: string,
  zipcode: string,
  country: string,
  creator: IdNameModel,
  firstName: string,
  lastName: string,
  name: string,
  archived?: boolean,
  primaryPhone?: number,
  secondaryPhone?: number,
  primaryEmail?: string,
  secondaryEmail?: string
}

export class CrmContactListFilter extends GenericFilterParams {
  deleted = false
}

export class CrmContactListItem {
  id!: string;
  company!: string;
  firstName!: string;
  lastName!: string;
  contactName?: string;
  email!: string;
  primaryPhone!: number
  secondaryPhone!: number;
  city!: string;
  state!: string;
  creator!: IdNameModel;
  isSelected!: boolean;
  accountReporterId!: number;
  createdDate!: string;
  prospect!: boolean;
  customerLead!: number
  accountReporter!: AccountReporter
  latitude!: any
  longitude!: any;
  deleted?: boolean;
  primaryEmail?: string;
  archived?: boolean;
  constructor(json?: CrmContactListItem) {
    if (json) {
      Object.assign(this, json);
    }
  }
}

export interface AccountReporter {
  id: number;
  name: string;
}
