<div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
  <h4 class="header-title">{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form [formGroup]="bannerForm" (ngSubmit)="onSubmit()">
  <section class="content">
    <div class="card p-3">
      <div class="row">
        <div class="col-12 mb-2">
          <label class="required">Banner Title</label>
          <input class="form-control" type="text" placeholder="Enter banner title" formControlName="name" />
          <app-error-messages [control]="bannerForm.controls.name"></app-error-messages>
        </div>
        <div *ngIf="banner?.id === NEW_LISTING_BANNER_ID" class="col-12 mb-2">
          <label class="required">Clear after</label>
          <input class="form-control" type="number" placeholder="Clear after (Days)" formControlName="clearAfter" />
          <app-error-messages [control]="bannerForm.controls.clearAfter"></app-error-messages>
        </div>
        <div class="col-12 mb-2">
          <label class="required">Background Color</label>
          <input class="form-control" type="color" placeholder="Enter banner title" formControlName="bgColor" />
          <app-error-messages [control]="bannerForm.controls.bgColor"></app-error-messages>
        </div>
        <div class="col-12 mb-2">
          <label class="required">Text Color</label>
          <input class="form-control" type="color" placeholder="Enter banner title" formControlName="fontColor" />
          <app-error-messages [control]="bannerForm.controls.fontColor"></app-error-messages>
        </div>
      </div>
    </div>
  </section>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>
