<div
  class="add-wrapper"
  [ngClass]="{
    'financial-tab': (inventoryInfo?.id || inventoryIncomingInfo?.unitId) && this.isFinancialPermission(),
    'sale-tab': showSoldTabs || showSoldVisible || showHoldTabs || showHoldVisible,
  }"
>
  <p-tabView [(activeIndex)]="activeIndex" [scrollable]="true" appToggleMoreLinksButton>
    <p-tabPanel headerStyleClass="inventory-tab-headers" header="General">
      <app-inventory-general-info
        [inventoryInfo]="inventoryInfo"
        (onClose)="onCancel($event)"
        (showLoader)="setLoaderStatus($event)"
        [inventoryIncomingInfo]="inventoryIncomingInfo"
        [isViewMode]="isViewMode"
        (showSoldTab)="showTab($event)"
        (showHoldTab)="showOnHoldTab($event)"
        (onCategoryChange)="setSelectedCategoryId($event)"
        [categoriesToShow]="categoriesToShow"
      >
      </app-inventory-general-info>
    </p-tabPanel>
    <p-tabPanel headerStyleClass="inventory-tab-headers" header="Specification" [disabled]="!!!selectedCategoryId">
      <app-inventory-specification
        [inventoryInfo]="inventoryInfo"
        [isViewMode]="isViewMode"
        [inventoryIncomingInfo]="inventoryIncomingInfo"
        [selectedCategoryId]="selectedCategoryId"
      >
      </app-inventory-specification>
    </p-tabPanel>
    <p-tabPanel headerStyleClass="inventory-tab-headers" header="Photos">
      <app-inventory-photos [inventoryInfo]="inventoryInfo" [isViewMode]="isViewMode" [inventoryIncomingInfo]="inventoryIncomingInfo"> </app-inventory-photos>
    </p-tabPanel>
    <p-tabPanel headerStyleClass="inventory-tab-headers" header="Condition">
      <app-inventory-condition [inventoryInfo]="inventoryInfo" [isViewMode]="isViewMode" [inventoryIncomingInfo]="inventoryIncomingInfo"> </app-inventory-condition>
    </p-tabPanel>
    <p-tabPanel headerStyleClass="inventory-tab-headers" header="Notes">
      <app-inventory-notes [inventoryInfo]="inventoryInfo" [isViewMode]="isViewMode" [inventoryIncomingInfo]="inventoryIncomingInfo"> </app-inventory-notes>
    </p-tabPanel>
    <p-tabPanel header="Documents" headerStyleClass="document inventory-tab-headers">
      <app-inventory-documents [inventoryInfo]="inventoryInfo" [inventoryIncomingInfo]="inventoryIncomingInfo" [isViewMode]="isViewMode"></app-inventory-documents>
    </p-tabPanel>
    <p-tabPanel headerStyleClass="inventory-tab-headers" header="Associated Inventory" *ngIf="showAssociation">
      <app-inventory-associations
        [inventoryInfo]="inventoryInfo"
        [isViewMode]="isViewMode"
        [selectedCategoryId]="selectedCategoryId"
        (selectedUnitsList)="getUpdatedUnitsList($event)"
      >
      </app-inventory-associations>
    </p-tabPanel>
    <ng-container *ngIf="inventoryInfo?.id || inventoryIncomingInfo?.unitId">
      <p-tabPanel header="Communication" *appHasPermission="[permissionActions.VIEW_COMMUNICATION]" headerStyleClass="communication inventory-tab-headers">
        <app-communication [inventoryInfo]="inventoryInfo" [inventoryIncomingInfo]="inventoryIncomingInfo"></app-communication>
      </p-tabPanel>
    </ng-container>
    <p-tabPanel header="Sold" *ngIf="showSoldTabs || showSoldVisible" headerStyleClass="inventory-tab-headers">
      <app-add-new-sale [inventoryInfo]="inventoryInfo" [inventoryIncomingInfo]="inventoryIncomingInfo" [isViewMode]="isViewMode"></app-add-new-sale>
    </p-tabPanel>
    <p-tabPanel header="On Hold" *ngIf="showHoldTabs || showHoldVisible" headerStyleClass="inventory-tab-headers">
      <app-add-hold-details [inventoryInfo]="inventoryInfo" [inventoryIncomingInfo]="inventoryIncomingInfo" [isViewMode]="isViewMode"></app-add-hold-details>
    </p-tabPanel>
    <ng-container *appHasPermission="[permissionActions.VIEW_FINANCIAL]">
      <p-tabPanel headerStyleClass="inventory-tab-headers" header="Financial" *ngIf="inventoryInfo?.id || inventoryIncomingInfo?.unitId">
        <app-financial-wrapper
          [inventoryInfo]="inventoryInfo"
          [inventoryIncomingInfo]="inventoryIncomingInfo"
          [isViewMode]="isViewMode"
          [salePrice]="salePrice"
          (isAssociateUnitRetailPrice)="isAssociateUnitRetailPrice($event)"
        >
        </app-financial-wrapper>
      </p-tabPanel>
    </ng-container>
  </p-tabView>
  <p-dialog header="Warning" [(visible)]="displayBasic" contentStyleClass="dialog-width" [style]="{ width: '20vw' }" [baseZIndex]="10000">
    <p>{{ message }}</p>
    <ng-template pTemplate="footer" class="general-warning">
      <p-button icon="pi pi-check" (click)="displayBasic = false" label="Okay" styleClass="p-button-text center"> </p-button>
    </ng-template>
  </p-dialog>
</div>
