import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { BaseComponent } from '@core/utils';
import { takeUntil } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Component({
  templateUrl: './forgot-password-init.component.html',
  styleUrls: ['./forgot-password-init.component.scss'],
})
export class ForgotPasswordInitComponent extends BaseComponent implements OnInit {
  forgotPasswordFormGroup!: UntypedFormGroup;
  instructionsSent = false;

  constructor(private readonly authService: AuthService,) {
    super();
  }

  ngOnInit() {
    this.initializeForm();
  }

  initializeForm() {
    this.forgotPasswordFormGroup = new UntypedFormGroup({
      email: new UntypedFormControl('', [Validators.required, Validators.minLength(1), Validators.maxLength(50), Validators.email]),
    });
  }

  onForgotPasswordInit(): void {
    if (this.forgotPasswordFormGroup.invalid) {
      this.forgotPasswordFormGroup.markAllAsTouched();
      return;
    }
    this.authService.onForgotPasswordInit(this.forgotPasswordFormGroup.value).pipe(takeUntil(this.destroy$)).subscribe(res => {
      this.instructionsSent = true;
    }, () => {
      this.instructionsSent = false;
    });
  }

}
