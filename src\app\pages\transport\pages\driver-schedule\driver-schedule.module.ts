import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { ColumnDropdownModule } from '@pages/common-table-column/column-dropdown.module';
import { GoogleMapModule } from '@sharedComponents/google-map/google-map.component.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { MentionModule } from 'angular-mentions';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { NgxMaskModule } from 'ngx-mask';
import { ConfirmationService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { EditorModule } from 'primeng/editor';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { ProgressBarModule } from 'primeng/progressbar';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { DriverAddComponent } from './driver-add/driver-add.component';
import { DriverScheduleAddComponent } from './driver-schedule-add/driver-schedule-add.component';
import { DriverScheduleCommentComponent } from './driver-schedule-comment/driver-schedule-comment.component';
import { DriverScheduleListComponent } from './driver-schedule-list/driver-schedule-list.component';
import { DriverScheduleRoutingModule } from './driver-schedule-routing.module';
import { DriverScheduleComponent } from './driver-schedule.component';

@NgModule({
  declarations: [
    DriverScheduleComponent,
    DriverScheduleListComponent,
    DriverScheduleAddComponent,
    DriverAddComponent,
    DriverScheduleCommentComponent
  ],
  imports: [
    CommonModule,
    DriverScheduleRoutingModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    RadioButtonModule,
    CalendarModule,
    MultiSelectModule,
    CheckboxModule,
    ConfirmDialogModule,
    GoogleMapModule,
    NgxGpAutocompleteModule,
    ColumnDropdownModule,
    EditorModule,
    MentionModule,
    TabsModule.forRoot(),
    MenuModule,
    ProgressBarModule,
    TabViewModule,
    NgxMaskModule.forRoot(),
  ],
  providers: [ConfirmationService, {
    provide: Loader,
    useValue: new Loader({
      apiKey: environment.googleMapApiKey,
      libraries: ['places']
    })
  }],

})
export class DriverScheduleModule { }
