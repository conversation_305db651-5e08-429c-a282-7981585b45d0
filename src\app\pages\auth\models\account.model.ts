import { DealerGroup, Shop } from "@pages/administration/models";
import { IdNameModel } from "src/app/@shared/models";

export interface Account {
  id: number;
  role: Role;
  email: string;
  firstName: string;
  lastName: string;
  userName: string;
  remainingDealers: DealerGroup[];
  currentlyActiveDealer: IdNameModel;
  shops: Shop[];
  userShops: Shop[];
  employeeId: string;
}

export interface Role {
  id: number;
  name: string;
  organizationId: number;
  privilegeActionResponseDTOs: Array<PrivilegeActionResponseDTOs>;
  systemDefault: boolean;
}

export interface UserCount {
  id: number,
  name: string,
  userCount: number
}
export interface PrivilegeActionResponseDTOs {
  id: number;
  principalId: number;
  actionDto: ActionDto;
  module: Module;
}

export interface ActionDto {
  id: number;
  name: string;
  assetId: number;
  moduleId: number;
}

export interface Module {
  id: number;
  name: string;
}

export enum Authority {
  ROLE_ADMIN = 'ROLE_ADMIN',
  ROLE_USER = 'ROLE_USER',
}
