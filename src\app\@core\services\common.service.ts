import { Injectable } from "@angular/core";
import { Observable, Subject } from "rxjs";

@Injectable({ providedIn: 'root' })
export class CommonService {
  isApiCallInProgress$: Subject<boolean> = new Subject();
  private readonly blockUI$Subject: Subject<boolean> = new Subject();
  private readonly message$Subject: Subject<string> = new Subject();

  get blockUI$(): Observable<boolean> {
    return this.blockUI$Subject.asObservable();
  }

  get message$(): Observable<string> {
    return this.message$Subject.asObservable();
  }

  setBlockUI$(value: boolean): void {
    this.blockUI$Subject.next(value);
  }

  setMessage$(value: string): void {
    this.message$Subject.next(value);
  }
}
