<div class="auth-wrapper">
  <div class="layer"></div>
  <div class="company-logo">
    <img [src]="constants.applicationLogoUrl" class="card-img-top img-fluid" alt="Company Icon">
  </div>
  <div class="card col-md-8 col-lg-6 col-xl-4 col-12 p-md-4 p-2">
    <div class="card-body mt-2">
      <ng-container
        [ngTemplateOutlet]="instructionsSent ? forgotPasswordInstructionSentTemplate: forgotPasswordInitTemplate">
      </ng-container>
    </div>
  </div>
</div>

<ng-template #forgotPasswordInitTemplate>
  <h5 class="card-title">Forgot password?</h5>
  <p class="card-text">Please enter your email and we will send you instructions to reset your password.</p>
  <form class="form" [formGroup]="forgotPasswordFormGroup" (ngSubmit)="onForgotPasswordInit()">
    <div class="mt-4">
      <label>Email</label>
      <input formControlName="email" type="email" class="form-control email" placeholder="Enter your email" />
      <app-error-messages [control]="forgotPasswordFormGroup.controls?.email"></app-error-messages>
    </div>
    <button class="btn btn-primary mt-5 mb-2 w-100" type="submit" [disabled]="forgotPasswordFormGroup?.invalid"
      appShowLoaderOnApiCall>
      <span>Send password reset email</span>
    </button>
    <ng-container [ngTemplateOutlet]="backToLoginLink"></ng-container>
  </form>
</ng-template>

<ng-template #forgotPasswordInstructionSentTemplate>
  <h5 class="card-title email-icon justify-content-center d-flex">
    <fa-icon [icon]="faIcons.faEnvelope" [size]="'3x'"></fa-icon>
  </h5>
  <p class="card-text mt-4">An email has been send to your email id. Please check for an email and click on the included
    link to reset your password</p>
  <ng-container [ngTemplateOutlet]="backToLoginBtn"></ng-container>
</ng-template>

<ng-template #backToLoginLink>
  <div>
    <a class="forgot" href="javascript:;" [routerLink]="[path.base.root, path.auth.root]">Back to Login</a>
  </div>
</ng-template>

<ng-template #backToLoginBtn>
  <div>
    <button class="btn btn-primary mt-3 mb-2 w-100" [routerLink]="[path.base.root, path.auth.root]" type="submit">
      <span>Back to login</span>
    </button>
  </div>
</ng-template>
