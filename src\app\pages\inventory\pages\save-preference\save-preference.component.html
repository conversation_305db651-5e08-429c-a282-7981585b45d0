<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form #filterNameForm="ngForm" (ngSubmit)="onSubmit()">
  <div class="card p-4 m-4">
    <label class="required">Preference Name</label>
    <input class="form-control" required type="text" placeholder="Enter preference name" name="preferenceName" #preferenceName="ngModel" [(ngModel)]="filterParam.filterName" />
    <small class="text-danger" *ngIf="!preferenceName.valid && preferenceName.touched"> This field is required </small>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button appShowLoaderOnApiCall class="btn btn-primary" type="submit" [disabled]="!filterNameForm.form.valid">Save</button>
  </div>
</form>
