@import 'src/assets/scss/theme/mixins';
@import 'src/assets/scss/variables';

ol,
ul {
  margin-bottom: 0 !important;
  padding-left: 0 !important;
}

form.submitted input.ng-invalid,
input.ng-dirty.ng-invalid {
  border: 1px solid red;
}

.login-input-group .password-eye {
  top: 30px !important;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-default {
  cursor: default !important;
}

.auth-wrapper {
  height: calc(100vh - 45px);
  display: flex;
  flex-direction: column;
  padding-top: 70px;
  align-items: center;
  background-image: url('../images/truck_image.png');
  background-size: cover;

  .btn {
    height: 50px;
  }

  .forgot {
    margin-top: 20px;
    margin-bottom: 0;
    text-align: right;
    float: right;
    font-size: 15px;
    font-weight: 400;
    letter-spacing: -0.12px;
    line-height: 19px;
  }

  .company-logo {
    z-index: 1;
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 50px;

    img {
      width: 180px;
    }
  }

  .layer {
    background-color: rgba(35, 22, 75, 0.85);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .card {
    border-radius: 25px;
    box-shadow: 0 0 7px 0px lighten($shadow-color, 10%);
    border: none;
    width: 514px;

    .footer {
      padding: 0 20px;
      font-size: 14px;
      margin: 20px 0 0;
    }

    .card-title {
      @include auth-header-label;
    }

    .card-text {
      @include auth-header-small;
    }
  }
}

label {
  color: var(--text-color);
  font-size: 14px;
  letter-spacing: -0.28px;
  line-height: 21px;
}

.action-wrapper {
  position: absolute;
  bottom: 20px;
}

app-error-messages {
  // TODO Will be remove once complete
  // position: absolute;
}

a.active {
  color: #fff !important;
  text-decoration: none;
  background-color: var(--active-color) !important;
}

fa-icon .fa-sort {
  font-size: 13px;
  color: var(--text-color);
}

fa-icon .fa-sort-up,
fa-icon .fa-sort-down {
  font-size: 13px;
  color: var(--active-color);
}

fa-icon .fa-sort-down {
  margin-bottom: 3px;
}

fa-icon .fa-sort-up {
  margin-bottom: -3px;
}

.left {
  position: relative;

  img {
    position: absolute;
    left: 10%;
    top: 24%;
  }

  span {
    margin-left: 10px;
  }
}

@media only screen and (max-width: 500px) {
  .left {
    position: relative;

    img {
      position: absolute;
      left: 27% !important;
      top: 19%;
    }

    span {
      margin-left: 0px !important;
    }
  }
}

.content-between {
  display: flex;
  justify-content: space-between;
}

.actions-content {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;

  img,
  fa-icon {
    margin-left: 10px;
  }
}

.content-center {
  display: flex;
  justify-content: center;
}

.p-sidebar .p-sidebar-header {
  display: none;
}

@media only screen and (max-width: 500px) {
  .edit-header {
    height: 6rem;
  }
}

.modal-title {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  background-color: var(--card-bg-color);
  .header-title {
    color: var(--content-head-color);
  }

  * {
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 27px;
    color: var(--active-color);
    margin-bottom: 0;
  }

  fa-icon {
    background-color: white;
    padding: 0px 8px;
    height: 1.8rem;
  }
}

.modal-footer {
  position: fixed;
  bottom: 0;
  padding: 0.3rem;
  background-color: var(--card-bg-color);
  width: -webkit-fill-available;
  flex-wrap: nowrap !important;
  z-index: 2000;

  * {
    margin: 0.25rem !important;
  }
}

.p-dialog .p-dialog-content {
  padding: 0 1.5rem 1rem 1.5rem !important;
}

.p-dialog .p-dialog-footer button {
  margin: 0 0.5rem 0 0 !important;
  width: auto;
  border-radius: 6px !important;
}

.p-sidebar-content {
  padding: 0 !important;

  .content {
    padding: 20px;
    position: relative;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    height: 100%;

    section {
      margin-bottom: 25px;
    }

    .title {
      border-bottom: 1px solid var(--table-border-color);
      margin-bottom: 10px;

      * {
        color: var(--content-head-color);
        font-size: 16px !important;
        font-weight: 600 !important;
        letter-spacing: 0;
        line-height: 25px !important;
        text-transform: uppercase;
      }
    }

    label {
      color: var(--text-color);
      font-size: 14px !important;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 31px;
    }

    textarea {
      width: 100%;
    }
  }
}

.dummy-data-btn {
  position: fixed;
  right: 70px;
  top: 8px;
}

.required {
  &:after {
    content: ' *';
    color: var(--danger-color);
  }
}

fa-icon:hover {
  cursor: pointer;
}

.p-component-overlay {
  background-color: rgba(0, 0, 0, 0.5) !important;
  // top: 130px;
  height: 100%;
}

.flex-wrapper {
  background-color: var(--app-background-color);
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  justify-content: space-between;
}

.new-line {
  white-space: pre-line;
}

td {
  word-break: break-word;
}

.p-disabled,
.p-component:disabled {
  opacity: 0.4;
}

.p-toast .p-toast-message .p-toast-message-content .p-toast-detail {
  margin: 0;
}

.p-multiselect-panel .p-multiselect-header .p-multiselect-filter-container .p-inputtext {
  padding: 5px 10px;
}

p-table .p-multiselect .p-multiselect-label {
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  border-radius: 9px !important;
}

.p-multiselect .p-multiselect-label.p-placeholder {
  color: #6c757d !important;
}

.p-menu .p-menuitem-link {
  text-decoration: none !important;
}

:root {
  ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #e7e7e7;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 7px;
    background-color: #e7e7e7;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: rgb(158, 158, 158);
  }
}

bs-dropdown-container {
  z-index: 10000 !important;
}

.p-component-overlay.p-sidebar-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4) !important;
  transition-duration: 0.2s;
  z-index: 0;
}

.p-dropdown-empty-message {
  text-align: center;
}

form.loading {
  display: flex;
  align-items: center;
  justify-content: center;

  fa-icon {
    font-size: 26px;
  }
}

.no-column-selection {
  .p-component-overlay {
    // top: 75px;
  }
}

@media (max-width: 600px) {
  .auth-wrapper .card {
    width: 95%;
  }

  footer {
    span {
      font-size: 13px !important;
    }

    .version-info {
      font-size: 8px !important;
    }
  }
}

.red {
  color: $red-color !important;

  .p-progressbar .p-progressbar-value {
    background-color: $red-color !important;
  }
}

.orange {
  color: $orange-color !important;
}

.orange-bg {
  background-color: $orange-color !important;
}

.yellow {
  color: $yellow-color !important;
}

.green {
  color: $green-color !important;

  .p-progressbar .p-progressbar-value {
    background-color: $green-color !important;
  }
}

.blue {
  color: $blue-color !important;

  .p-progressbar .p-progressbar-value {
    background-color: $blue-color !important;
  }
}

.placeholder {
  color: $placeholder-color !important;

  .p-progressbar .p-progressbar-value {
    background-color: $placeholder-color !important;
  }
}

.loader-icon {
  display: flex;
  justify-content: center;
}

.status {
  background-color: var(--active-color);
  padding: 2px;
  border-radius: 2px;
  color: white;
  font-size: 13px;
  letter-spacing: 0;
  line-height: 20px;
  width: 160px;
  display: block;
  text-align: center;
}

.no-data {
  width: 100%;
  display: flex;
  justify-content: center;

  * {
    font-size: 18px;
    font-weight: 400;
  }

  .pi-spin {
    font-size: 30px;
  }
}

td.no-data {
  display: table-cell;
}

.file-wrapper {
  display: flex;
  flex-wrap: wrap;

  .files {
    width: 150px;
    height: 150px;
    margin-left: 20px;

    .file-box-wrapper {
      box-shadow: var(--dropdown-box-shadow);
      border-radius: 10px;
      width: 100%;
      height: 100%;
    }

    .file-box {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-size: cover;

      &.in-progress {
        opacity: 0.2;
      }
    }

    &.drop-zone {
      height: 150px;
      width: 150px;
      padding: 10px;
      margin-left: 0;

      .title {
        font-size: 14px;
      }

      .subtitle {
        font-size: 12px;
      }
    }
  }

  .file-footer {
    margin-top: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .file-name {
      max-width: 100px;
      font-size: 14px;
    }
  }
}

.download-icon {
  color: var(--active-color);
}

.background-blue {
  background-color: $blue-color !important;
  border: 2px solid $blue-color !important;
}

.background-white {
  background-color: $white-color !important;
  border: 1px solid $blue-color !important;
  color: var(--text-color) !important;
}

.background-grey {
  background-color: $placeholder-color !important;
  border: 1px solid $placeholder-color !important;
}

.hidden {
  display: none;
}

div.coming-soon {
  text-align: center;
  position: absolute;
  font-size: 1.25rem;
  opacity: 0.85;
  top: 0px;
  height: 100%;
  width: 100%;
  font-weight: 700;
  color: var(--text-color);
  z-index: 2;
}

.sfl-coming-soon-element {
  width: 100%;
  position: relative;
  display: block;
  padding: 0;
}

.coming-soon p {
  margin: auto;
}

.sfl-coming-soon-element section {
  opacity: 0;
}

.p-blockui-document {
  z-index: 100000 !important;
}

.spinner-message {
  position: absolute;
  top: 248px;
  font-size: 20px;
  font-weight: 500;
}
.pac-container {
  z-index: 9999999 !important;
}

.reset-btn {
  width: 110px;
  height: 35px;
  padding: 0 10px;
}

.min-width-100 {
  min-width: 100px !important;
}

.min-width-175 {
  min-width: 175px !important;
}

.min-width-220 {
  min-width: 220px !important;
}

.min-width-300 {
  min-width: 300px !important;
}

.dynamic-tabs {
  .p-tabview-nav-content {
    margin-top: 5px;
  }
}

@media only screen and (max-width: 500px) {
  .hide-populate-data {
    display: none;
  }

  .p-toast-bottom-right {
    right: 2px !important;
  }

  .p-toast {
    width: 24rem !important;
  }

  .map-icon {
    margin-top: 31px !important;
  }
}

.confirm-dialog {
  font-size: 14px;
  .p-dialog-header {
    span {
      font-size: 16px;
    }
  }
  .p-dialog-footer {
    button {
      padding: 0.5rem 1rem;
      transition: 0.2s ease-in-out;
      color: var(--white-color);
      &:first-child {
        color: var(--primary-color);
        background: var(--light-grey-color);
        &:hover {
          background: var(--light-grey-color) !important;
          color: var(--active-color);
          border-color: var(--active-color) !important;
        }
      }
    }
  }
}

.p-sidebar {
  &.p-sidebar-active {
    background-color: var(--app-background-color);
  }
}

.p-tabview .p-tabview-nav-btn.p-link {
  color: var(--text-color);
  background-color: var(--card-bg-color);
}

.p-inline-message .p-inline-message-text {
  font-size: 0.8rem;
}

.p-inline-message.p-inline-message-info {
  background: var(--app-background-color);
  border: solid #e6f0fc;
  border-width: 0px;
  color: var(--content-head-color);
  width: 100%;
  justify-content: start;
  .p-inline-message-icon {
    color: var(--content-head-color);
  }
}

.text-color {
  color: var(--text-color);
}

body.DARK {
  .switch {
    border: none;
  }

  .border-less {
    .p-dropdown {
      border: none !important;
    }
  }
  .modal-footer {
    border: none;
  }
}

.mr--12 {
  margin-right: -12px !important;
}

.p-datepicker {
  max-width: 450px !important;
  width: 450px !important;

  .p-datepicker-header {
    padding: 0.1rem;
  }

  table td {
    padding: 0.1rem;
  }

  table th > span {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    height: 1.5rem;
  }

  .p-datepicker-buttonbar {
    padding: 0;

    .p-button{
      background-color: transparent;
      color: $calendar-btn-color;
      border-color: transparent;
      border-radius: 8px !important;
      margin-top: 4px !important;
    }
    .p-button:hover{
      border-color: transparent !important;
      background-color: $calendar-btn-color !important;
      color: white !important;
    }
  }
}

.public-page-action-btn {
  background-color: $public-page-active-color;
  border-color: $public-page-active-color;
  color: $white-color;

  &:hover {
    background-color: $public-page-active-color-lighter;
    border-color: $public-page-active-color-lighter;
    color: $white-color;
  }
}

.confirm-dialog .p-dialog-footer button:first-child {
  color: var(--primary-color);
  background: var(--light-grey-color);
}

.p-confirm-popup-footer {
  padding: 0 0.5rem 0.75rem;
  button:first-child {
    color: var(--primary-color);
    background: var(--light-grey-color);
  }

  button {
    margin: 0 0.5rem 0 0 !important;
    padding: 0.5rem 1rem;
    width: auto;
    border-radius: 6px !important;
    transition: 0.2s ease-in-out;
  }
}
.disabled-dropdown-item {
  color: $disable-color !important;
  cursor: not-allowed;
  pointer-events: none;
}

.p-dropdown {
  border: 1px solid var(--border-color-dark) !important;
}

.p-multiselect {
  border: 1px solid var(--border-color-dark) !important;
}

.p-dropdown:hover {
  border: 1px solid var(--border-color-dark) !important;
}