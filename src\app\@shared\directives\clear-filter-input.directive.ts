import { AfterViewInit, Directive, Host, Optional } from '@angular/core';
import { Dropdown } from 'primeng/dropdown'; // Import Dropdown component
import { MultiSelect } from 'primeng/multiselect';

@Directive({
  selector: '[appPreventClearFilter]'  // Apply this directive to p-multiSelect and p-dropdown
})
export class PreventClearFilterDirective implements AfterViewInit {

  constructor(@Host() @Optional() private multiSelect: MultiSelect, @Host() @Optional() private dropdown: Dropdown) {}

  ngAfterViewInit() {
    // Handle p-multiSelect
    if (this.multiSelect) {
      this.multiSelect.onPanelHide.subscribe(() => {
        this.multiSelect.filterValue = ''; // Reset the filter value on panel hide
      });
    }

    // Handle p-dropdown
    if (this.dropdown) {
      this.dropdown.onHide.subscribe(() => {
        this.dropdown.filterValue = ''; // Reset the filter value on panel hide
      });
    }
  }
}