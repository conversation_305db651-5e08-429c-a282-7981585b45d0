
export interface Role {
  id: number;
  name: string;
  displayName?: string | null;
  parent: Role;
  privileges: string[];
}

export class RoleModule {
  id?: number;
  name?: string = '';
  actions?: RoleModuleAssets[] = [];
  checked?: boolean = false;
}

export class RoleModuleAssets {
  id?: number;
  name?: string;
  assetId?: number;
}

export class RoleModuleGroup {
  id?: number;
  moduleName?: string;
  actions?: RoleModuleAssets[];
  checked?: boolean = false;
}

export interface Assets {
  id: number;
  name: string;
  parentId: string;
  assetType: string;
  action: string;
}
