import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { ColumnItem, FilterList } from '@pages/common-table-column/models/common-table.column.model';
import { InventorySpecification, InventorySpecificationFields, InventorySpecificationResponse } from '@pages/inventory/models';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { ConfirmationService } from 'primeng/api';
import { EMPTY, Observable, catchError, switchMap, takeUntil } from 'rxjs';
import { ColumnMasterService } from 'src/app/@shared/services/column-master.service';
import { DataTypeOptions, DeleteUnitSpecificationDetailsParams, SpecificationMasterColumn, UnitTypeCategory, UpdateMode, UserFilterUpdateFunctionParam } from '../../models/specification.model';
import { CategoryService } from '../category/category.service';
import { SpecificationService } from './specification.service';

@Component({
  selector: 'app-specification',
  templateUrl: './specification.component.html',
  styleUrls: ['./specification.component.scss']
})
export class SpecificationComponent extends BaseComponent implements OnInit {
  specificationList!: InventorySpecificationResponse;
  categories: UnitTypeCategory[] = [];
  allUsersFilterData: FilterList[] = [];
  allUsersFilterPreferenceData: FilterList[] = [];
  selectedCategory!: number;
  categoryName!: string;
  activeCategoryName!: string;
  selectedSpecificationGroup!: InventorySpecification;
  selectedSpecificationFields!: InventorySpecificationFields;
  showCreateGroupModal = false;
  showCreateGroupFieldModal = false;
  tableColumn!: FilterList | undefined;
  cols: any[] = [];
  isEditMode = false;
  groupAndFieldNames: string[] = []
  oldColumnMasterObject!: ColumnItem | null;
  newColumnMasterObject!: ColumnItem | null;
  oldFieldName!: string | null;
  oldGroupName!: string | null;
  newGroupName!: string | null;
  _selectedColumns: any[] = [];
  currentUserId!: number;
  specificationMasterColumn!: SpecificationMasterColumn | null;
  specificationDropDownColumnList: ColumnItem[] = [];
  constructor(
    private readonly specificationService: SpecificationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly accountService: AuthService,
    private readonly columnDropDownService: ColumnDropdownService,
    private readonly inventoryService: InventoryService,
    private readonly categoryService: CategoryService,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService,
    private readonly commonSharedService: CommonSharedService,
    private readonly columnMasterService: ColumnMasterService,
  ) {
    super();
    this.pageTitle = 'SPECIFICATION';
  }

  ngOnInit(): void {
    this.getUserId();
    this.getCategories();
  }

  getSpecificationList(categoryId: number, categoryName: string): void {
    this.isLoading = true;
    const endpoint = `${API_URL_UTIL.admin.specifications.specificationByCategoryId}`.replace(':id', categoryId.toString());
    this.specificationService.get<InventorySpecificationResponse>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: InventorySpecificationResponse) => {
        this.specificationList = res;
        this.getColumnMasterDetail(categoryName);
        this.groupAndFieldNames = [];
        if (res.masterData.specification?.length) {
          res.masterData.specification.forEach(section => {
            this.groupAndFieldNames.push(section.sectionName.toLowerCase());
            if (section.fields?.length) {
              section.fields.forEach(field => {
                this.groupAndFieldNames.push(field.label.toLowerCase());
              });
            }
          });
        }
        this.cdf.detectChanges();
        this.isLoading = false;
      }
    });
  }

  getUserId(): void {
    this.accountService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe((res) => {
      if (res?.id) {
        this.currentUserId = res.id;
      }
    });
  }

  getCategories() {
    this.categoryService.getList<UnitTypeCategory>().pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.selectedCategory = res[0]?.id;
        this.categoryName = res[0].name.split(' ').join('_').toUpperCase();
        this.activeCategoryName = res[0].name;
        this.categories = res;
        this.cdf.detectChanges();
        this.getSpecificationList(this.selectedCategory, this.categoryName);
      }
    });
  }

  onAddEditGroup(specification?: InventorySpecification): void {
    if (specification?.order) {
      this.selectedSpecificationGroup = specification;
      this.oldGroupName = specification.sectionName;
      this.groupAndFieldNames = this.groupAndFieldNames.filter(name => name !== specification.sectionName.toLowerCase());
    }
    this.showCreateGroupModal = true;
  }

  onDeleteField(childIndex: number, fieldId: number, specification: InventorySpecification, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'specification field data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.commonSharedService.setBlockUI$(true);
        const fieldName = specification.fields[childIndex]?.label?.toLowerCase();
        this.groupAndFieldNames.splice(this.groupAndFieldNames.indexOf(fieldName), 1);
        const selectedSpecificationGroupIndex = this.specificationList.masterData.specification.findIndex(
          (specificationItem: InventorySpecification) => specification.id === specificationItem.id
        );
        this.deleteUnitSpecificationDetails(new DeleteUnitSpecificationDetailsParams(selectedSpecificationGroupIndex, childIndex, this.selectedCategory, false))
          .pipe(
            switchMap(() => {
              this.specificationList.masterData.specification[selectedSpecificationGroupIndex].fields =
                this.specificationList.masterData.specification[selectedSpecificationGroupIndex].fields.filter((field: InventorySpecificationFields) =>
                  field.id !== fieldId
                );
              this.remakeColumnMaster();
              this.updateColumnMaster(new UserFilterUpdateFunctionParam(UpdateMode.FIELD_DELETE, selectedSpecificationGroupIndex, childIndex));
              this.onDeleteModel(this.specificationList, "FIELD");
              return EMPTY;
            })
          )
          .subscribe({
            complete: () => {
              this.commonSharedService.setBlockUI$(false);
            },
          });
      },
    });
  }

  onDeleteGroup(specification: InventorySpecification, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'specification group data'),
      icon: icons.triangle,
      accept: () => {
        this.commonSharedService.setBlockUI$(true);

        const fieldAndGroupNames = [specification.sectionName.toLowerCase()];
        if (specification.fields?.length) {
          specification.fields.forEach(field => fieldAndGroupNames.push(field.label.toLowerCase()));
        }
        this.groupAndFieldNames = this.groupAndFieldNames.filter(name => !fieldAndGroupNames.includes(name));
        const specificationIndex = this.specificationList.masterData.specification.findIndex((section: InventorySpecification) => {
          return section.sectionName === specification.sectionName;
        });

        this.deleteUnitSpecificationDetails(new DeleteUnitSpecificationDetailsParams(specificationIndex, 0, this.selectedCategory, true))
          .pipe(
            switchMap(() => {
              this.specificationList.masterData.specification = this.specificationList.masterData.specification.filter(
                specificationItem => specificationItem.order !== specification.order
              );
              this.remakeColumnMaster();
              this.updateColumnMaster(new UserFilterUpdateFunctionParam(UpdateMode.GROUP_DELETE, 0, 0));
              this.onDeleteModel(this.specificationList, "SPECIFICATION");
              return EMPTY;
            }),
            catchError((error) => {
              this.commonSharedService.setBlockUI$(false);
              return EMPTY;
            })
          )
          .subscribe({
            complete: () => {
              this.commonSharedService.setBlockUI$(false);
            }
          });
      },
    });
  }

  private deleteUnitSpecificationDetails(obj: DeleteUnitSpecificationDetailsParams): Observable<void> {
    const endpoint = `${API_URL_UTIL.inventory.unitSpecifications}/${API_URL_UTIL.inventory.remove}`;
    return this.inventoryService.update(obj, endpoint).pipe(takeUntil(this.destroy$));
  }


  private remakeColumnMaster(): void {
    this.specificationDropDownColumnList = [];
    const specification = this.specificationList?.masterData?.specification ?? [];
    specification.forEach((section, parentIndex) => {
      section?.fields.forEach((field, childIndex) => {
        const key = `specificationDetails.${section?.sectionName}.${field.label}`;
        let type = '';
        switch (field.dataType) {
          case DataTypeOptions[0]:
            type = "MULTI_DROP_DOWN";
            break;
          case DataTypeOptions[1]:
            type = "INTEGER";
            break;
          default:
            type = "STRING"
            break;
        }
        const obj: ColumnItem = {
          id: null,
          name: field.label,
          key,
          disable: false,
          default: false,
          shorting: false,
          type,
          value: null,
          shortingKey: null,
          parentIndex,
          childIndex,
          isSpecificationField: true,
        };
        this.specificationDropDownColumnList.push(obj);
      })
    })
  }

  onDeleteModel(specification: InventorySpecificationResponse, type: string): void {
    this.manageFieldCount();
    this.specificationService.update<InventorySpecificationResponse>(specification).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        const msg = (type === 'FIELD' ? 'Specification field' : 'Specification group')
        this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('{item}', msg));
      }
    });
  }

  closeModal(obj: { dataHasBeenModified: boolean, newName: string }): void {
    if (obj.dataHasBeenModified) {
      this.groupAndFieldNames.push(obj.newName)
    } else if (!obj.dataHasBeenModified && this.oldGroupName) {
      this.groupAndFieldNames.push(this.oldGroupName)
      this.oldGroupName = this.newGroupName = null;
    }
    this.showCreateGroupModal = false;
    this.selectedSpecificationGroup = {} as InventorySpecification;
    this.selectedSpecificationFields = {} as InventorySpecificationFields;
  }

  addSpecificationGroup(specificationDetails: InventorySpecificationResponse): void {
    this.specificationList.masterData.specification = specificationDetails.masterData.specification;
  }

  onAddEditField(specification?: InventorySpecification, field?: InventorySpecificationFields): void {
    if (field && specification) {
      this.oldFieldName = field.label;
      this.groupAndFieldNames = this.groupAndFieldNames.filter(name => name !== field.label.toLowerCase());
      const columnObj = this.specificationDropDownColumnList.find(obj => obj.name === field.label);
      if (columnObj) {
        this.oldColumnMasterObject = columnObj
      }
      this.selectedSpecificationFields = field;
      this.selectedSpecificationGroup = specification;
      this.isEditMode = true;
    } else {
      this.isEditMode = false;
    }
    this.showCreateGroupFieldModal = true;
  }

  closeGroupFieldModal(obj: { dataHasBeenModified: boolean, newName: string }): void {
    if (obj.dataHasBeenModified) {
      this.groupAndFieldNames.push(obj.newName)
    } else if (!obj.dataHasBeenModified && this.oldFieldName) {
      this.groupAndFieldNames.push(this.oldFieldName)
      this.oldColumnMasterObject = this.newColumnMasterObject = this.oldFieldName = null;
    }
    this.showCreateGroupFieldModal = false;
    this.isEditMode = false;
    this.selectedSpecificationFields = {} as InventorySpecificationFields
  }

  onChange(event: any) {
    if (event.value) {
      this.categoryName = this.categories.find(category => category.id === event.value)?.name?.split(' ')?.join('_')?.toUpperCase() ?? '';
      this.activeCategoryName = this.categories.find(category => category.id === event.value)?.name ?? '';
      this.getSpecificationList(event.value, this.categoryName);
      this.specificationMasterColumn = null;
      this.specificationDropDownColumnList = [];
    }
  }

  updateSpecificationPreference() {
    this.specificationService.update<InventorySpecificationResponse>(this.specificationList).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: InventorySpecificationResponse) => {
        this.toasterService.success(MESSAGES.includingPreferenceAllowed);
      }
    });
  }

  updateMultipleSpecification(specification: InventorySpecification) {
    const updateFunction = () => {
      this.specificationService.update<InventorySpecificationResponse>(this.specificationList).pipe(
        takeUntil(this.destroy$)
      ).subscribe({
        next: (res: InventorySpecificationResponse) => {
          this.toasterService.success(MESSAGES.allowMultipleSpecifications);
          this.remakeColumnMaster();
          this.updateColumnMaster(new UserFilterUpdateFunctionParam(UpdateMode.GROUP_DELETE, 0, 0));
        }
      });
    };

    const rejectFunction = () => {
      specification.multiple = false;
    };

    if (specification.multiple) {
      this.confirmationService.confirm({
        target: event?.target as EventTarget,
        message: MESSAGES.addMultipleGroup,
        icon: icons.triangle,
        accept: updateFunction,
        reject: rejectFunction,
        header: "Confirmation",
      });
    } else {
      updateFunction();
    }
  }

  manageFieldCount() {
    let count = 0
    this.specificationList.masterData.specification.forEach(s => {
      count = count + s.fields.length
    });
    this.specificationList.fieldCount = count;
  }

  async handleColumnUpdate(event: { isEdit: boolean, data: ColumnItem }): Promise<void> {
    this.newColumnMasterObject = event.data;
    if (event.isEdit) {
      await this.updateUnitSpecificationDetails({ newName: this.newColumnMasterObject?.name ?? '', oldName: this.oldFieldName ?? '' });
    }
    this.commonSharedService.setBlockUI$(true);
    if (event.isEdit && this.oldColumnMasterObject) {
      event.data.id = this.oldColumnMasterObject?.id;
      const index = this.specificationDropDownColumnList.indexOf(this.oldColumnMasterObject);
      if (index !== -1) {
        this.specificationDropDownColumnList[index] = event.data;
      }
    } else {
      this.specificationDropDownColumnList.push(event.data);
    }
    this.updateColumnMaster(new UserFilterUpdateFunctionParam(UpdateMode.EDIT, 0, 0));
  }

  async handleGroupNameChange(event: { newName: string }): Promise<void> {
    this.newGroupName = event.newName;
    if (this.oldGroupName) {
      await this.updateUnitSpecificationDetails({ newName: event.newName, oldName: this.oldGroupName });
    }
    this.commonSharedService.setBlockUI$(true);
    this.remakeColumnMaster();
    this.updateColumnMaster(new UserFilterUpdateFunctionParam(UpdateMode.GROUP_EDIT, 0, 0));
  }

  private updateUnitSpecificationDetails(obj: { newName: string, oldName: string }): Promise<void> {
    this.commonSharedService.setBlockUI$(true);
    const endpoint = `${API_URL_UTIL.inventory.unitSpecifications}/${API_URL_UTIL.inventory.update}/${this.selectedCategory}?oldValue=${obj.oldName}&newValue=${obj.newName}`;

    return new Promise<void>((resolve, reject) => {
      this.inventoryService.update(null, endpoint)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.commonSharedService.setBlockUI$(false);
            resolve();
          },
          error: (err) => {
            this.commonSharedService.setBlockUI$(false);
            reject(err);
          }
        });
    });
  }

  private updateColumnMaster(UserFilterUpdateParam: UserFilterUpdateFunctionParam): void {
    this.commonSharedService.setBlockUI$(true);
    this.columnMasterService.update<SpecificationMasterColumn>(this.filterInfoParams).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.specificationMasterColumn = res;
        this.specificationDropDownColumnList = res?.masterData?.columns ?? [];
        this.getAndUpdateUsersFilterData(UserFilterUpdateParam);
        this.getAndUpdateUsersPreferenceSpecificationData(UserFilterUpdateParam);
      }
    });
  }

  private getAndUpdateUsersFilterData(UserFilterUpdateParam: UserFilterUpdateFunctionParam): void {
    const endpoint = `${API_URL_UTIL.columnMasters.moduleWithoutSlash}?${API_URL_UTIL.columnMasters.moduleWithoutSlash}=${this.categoryName}`
    this.columnDropDownService.getList<FilterList>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.allUsersFilterData = res;
        if (res.length) {
          this.handleFilterChanges(UserFilterUpdateParam, false);
          this.updateFilterChanges(this.allUsersFilterData);
        } else {
          this.commonSharedService.setBlockUI$(false);
        }
      }
    });
  }
  private getAndUpdateUsersPreferenceSpecificationData(UserFilterUpdateParam: UserFilterUpdateFunctionParam): void {
    const endpoint = `${API_URL_UTIL.columnMasters.preference}?${API_URL_UTIL.columnMasters.activeTabName}=${this.activeCategoryName}`
    this.columnDropDownService.getList<FilterList>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.allUsersFilterPreferenceData = res;
        if (res.length) {
          this.handleFilterChanges(UserFilterUpdateParam, true);
          this.updateFilterChanges(this.allUsersFilterPreferenceData);
        } else {
          this.commonSharedService.setBlockUI$(false);
        }
      }
    });
  }

  private handleFilterChanges(UserFilterUpdateParam: UserFilterUpdateFunctionParam, isPreference: boolean): void {
    switch (UserFilterUpdateParam.mode) {
      case UpdateMode.EDIT:
        this.handleEditModeForFilterChange(isPreference ? this.allUsersFilterPreferenceData : this.allUsersFilterData)
        break;
      case UpdateMode.FIELD_DELETE:
      case UpdateMode.GROUP_DELETE:
        this.replaceUserColumnsWithNewOnes(isPreference ? this.allUsersFilterPreferenceData : this.allUsersFilterData);
        break;
      case UpdateMode.GROUP_EDIT:
        this.handleGroupEditModeForFilterChange(isPreference ? this.allUsersFilterPreferenceData : this.allUsersFilterData);
        break;
      default:
        break;
    }
  }

  private handleEditModeForFilterChange(allUserData: FilterList[]): void {
    allUserData.forEach(userFilter => {
      if (userFilter?.data && this.newColumnMasterObject?.key) {
        const data: ColumnItem[] = JSON.parse(userFilter.data);
        const targetColumn = data.find(column => column.childIndex === this.newColumnMasterObject?.childIndex && column.parentIndex === this.newColumnMasterObject?.parentIndex);
        if (targetColumn?.key) {
          const index = data.indexOf(targetColumn);
          const obj = { ...this.newColumnMasterObject, default: true, value: targetColumn.value };
          if (index !== -1) {
            data.splice(index, 1, obj);
          }
        }
        userFilter.data = JSON.stringify(data);
      }
    });
  }

  private replaceUserColumnsWithNewOnes(allUserData: FilterList[]): void {
    allUserData.forEach(userFilter => {
      if (userFilter?.data) {
        const userOldData: ColumnItem[] = JSON.parse(userFilter.data);
        const updatedUserData: ColumnItem[] = [];
        userOldData.forEach(filterColumn => {
          const updatedColumn = this.specificationDropDownColumnList.find(masterColumn => masterColumn.key === filterColumn.key);
          if (updatedColumn?.isSpecificationField) {
            updatedColumn.default = true;
            updatedColumn.value = filterColumn.value;
            updatedUserData.push(updatedColumn);
          }
        });
        userFilter.data = JSON.stringify(updatedUserData);
      }
    })
  }

  private handleGroupEditModeForFilterChange(allUserData: FilterList[]): void {
    allUserData.forEach(userFilter => {
      if (userFilter?.data) {
        const data: ColumnItem[] = JSON.parse(userFilter.data);
        data.forEach(filterColumn => {
          if (filterColumn.key?.split('.')[1] === this.oldGroupName) {
            const keyArray = filterColumn.key?.split('.');
            keyArray[1] = this.newGroupName ?? "";
            filterColumn.key = keyArray.join('.');
          }
        });
        userFilter.data = JSON.stringify(data);
      }
    });
  }

  private updateFilterChanges(allUserData: FilterList[]): void {
    this.columnDropDownService.update<FilterList[]>(allUserData, API_URL_UTIL.filter.all).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.commonSharedService.setBlockUI$(false);
        this.newColumnMasterObject = this.oldColumnMasterObject = null;
        this.oldGroupName = this.newGroupName = '';
      }
    });
  }

  private getColumnMasterDetail(categoryName: string): void {
    this.specificationDropDownColumnList = [];
    const endpoint = `${API_URL_UTIL.columnMasters.moduleWithoutSlash}`.concat(`?module=${categoryName}`);
    this.columnMasterService.get<SpecificationMasterColumn[]>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: SpecificationMasterColumn[]) => {
        if (response?.length) {
          this.specificationMasterColumn = response[0];
          this.specificationDropDownColumnList = response[0]?.masterData?.columns ?? [];
        }
        this.cdf.detectChanges();
      });
  }

  get filterInfoParams(): SpecificationMasterColumn {
    return {
      id: this.specificationMasterColumn?.id,
      module: this.categoryName,
      masterData: {
        columns: this.specificationDropDownColumnList,
      }
    }
  }
}
