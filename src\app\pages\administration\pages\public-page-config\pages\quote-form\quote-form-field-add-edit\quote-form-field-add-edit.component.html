<div class="modal-title close">
  <h4 class="header-title">{{ pageTitle }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose(false)"></fa-icon>
</div>

<ng-container *ngIf="isUpdateEmail">
  <div class="quote-field-form content">
    <label class="required mt-3 mb-2">Notify Persons</label>
    <p-multiSelect
      appPreventClearFilter
      styleClass="w-100"
      appendTo="body"
      [options]="users"
      [(ngModel)]="selectedUsers"
      optionLabel="name"
      optionValue="id"
      required
      [filter]="true"
      filterBy="name"
      placeholder="Select notify persons"
      [maxSelectedLabels]="1"
      [showToggleAll]="false"
      optionDisabled="disabled"
      (onChange)="updateDisabledStatusForUsers()"
      selectedItemsLabel="{0} persons selected"
    >
      <ng-template pTemplate="empty">
        <ng-container [ngTemplateOutlet]="emptyMessage"></ng-container>
      </ng-template>
      <ng-template let-user pTemplate="item">
        <span>{{ user.name }}</span>
      </ng-template>
    </p-multiSelect>
    <small class="text-danger error-msg">
      <span *ngIf="selectedUsers.length < 1">Minimum 1 person must be selected</span>
    </small>
  </div>
</ng-container>

<ng-container *ngIf="!isUpdateEmail">
  <form [formGroup]="quoteFieldFrom">
    <div class="quote-field-form content">
      <div class="form-group">
        <label for="label" class="required">Label</label>
        <input formControlName="label" type="text" class="form-control" placeholder="Enter field label" (input)="onLabelChange()" />
        <app-error-messages [control]="quoteFieldFrom?.controls?.label"></app-error-messages>
      </div>
      <div class="form-group">
        <label for="label" class="required">Data Type</label>
        <p-dropdown [options]="dataTypeOptions" [disabled]="isEditMode" placeholder="Select Data Type" formControlName="dataType" (onChange)="onDataTypeChange($event)">
        </p-dropdown>
        <app-error-messages [control]="quoteFieldFrom?.controls?.dataType"></app-error-messages>
      </div>
      <div *ngIf="quoteFieldFrom?.controls?.dataType?.value === 'DropDown'">
        <label for="label" class="required">Options</label>
        <textarea formControlName="options" class="form-control" placeholder="Enter dropdown option" rows="5"></textarea>
        <small class="text-danger error-msg d-block" *ngIf="quoteFieldFrom.controls.options?.touched && quoteFieldFrom.controls.options?.hasError('required')">
          This field is required
        </small>
        <small class="bold mb-1 text-secondary">Note: Please enter comma separated values</small>
      </div>
      <div class="form-group">
        <label for="label">Watermark Message</label>
        <input formControlName="placeholder" type="text" class="form-control" placeholder="Enter field placeholder" />
        <app-error-messages [control]="quoteFieldFrom?.controls?.placeholder"></app-error-messages>
      </div>
      <div class="form-group">
        <div class="d-flex align-items-center">
          <label for="isRequired" class="me-2 mb-1">Is Required: </label>
          <ui-switch formControlName="isRequired" name="isRequired"> </ui-switch>
        </div>
      </div>
    </div>
  </form>
</ng-container>

<div class="modal-footer">
  <button class="btn btn-secondary" type="button" (click)="onModalClose(false)">Cancel</button>
  <button class="btn btn-primary d-flex align-items-center" type="submit" (click)="onSubmit()" [disabled]="isLoading">
    Save
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isLoading"></fa-icon>
  </button>
</div>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
