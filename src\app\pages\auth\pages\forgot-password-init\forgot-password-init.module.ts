import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { SharedComponentsModule } from "@sharedComponents/shared-components.module";
import { DirectivesModule } from "src/app/@shared/directives/directives.module";
import { SharedLibsModule } from "src/app/@shared/shared-libs.module";
import { ForgotPasswordInitComponent } from './forgot-password-init.component';

const ROUTES: Routes = [
  {
    path: '',
    component: ForgotPasswordInitComponent
  }
]

@NgModule({
  declarations: [ForgotPasswordInitComponent],
  imports: [
    DirectivesModule,
    SharedComponentsModule,
    SharedLibsModule,
    FontAwesomeModule,
    RouterModule.forChild(ROUTES),
  ]
})
export class ForgotPasswordInitComponentModule { }
