import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionForChildGuard } from '@core/guards';
import { ROUTER_UTILS } from '@core/utils';
import { PermissionActions } from 'src/app/@shared/models';
import { InventoryReportsComponent } from './inventory-reports.component';

const routes: Routes = [{
  path: '',
  component: InventoryReportsComponent,
  title: 'Skeye - Inventory Reports',
  children: [
    {
      path: ROUTER_UTILS.config.reporting.inventoryReport.profitability.root,
      loadChildren: async () => (await import('./pages/profitability-report/profitability-report.module')).ProfitabilityReportModule,
      canActivate: [PermissionForChildGuard],
      data: {
        permission: [PermissionActions.VIEW_PROFITABILITY_REPORT]
      }
    },
    {
      path: ROUTER_UTILS.config.reporting.inventoryReport.inventoryAging.root,
      loadChildren: async () => (await import('./pages/inventory-aging/inventory-aging.module')).InventoryAgingModule,
      canActivate: [PermissionForChildGuard],
      data: {
        permission: [PermissionActions.VIEW_INVENTORY_AGING]
      }
    }
  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class InventoryReportsRoutingModule { }
