import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { GoogleMapModule } from '@sharedComponents/google-map/google-map.component.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { NgxMaskModule } from 'ngx-mask';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ConfirmationService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from 'src/app/@shared/pipes/pipes.module';
import { VendorAddComponent } from './vendor-add/vendor-add.component';
import { VendorExpensesListComponent } from './vendor-expenses-list/vendor-expenses-list.component';
import { VendorListComponent } from './vendor-list/vendor-list.component';
import { VendorsRoutingModule } from './vendors-routing.module';
import { VendorsComponent } from './vendors.component';

@NgModule({
  declarations: [
    VendorsComponent,
    VendorListComponent,
    VendorAddComponent,
    VendorExpensesListComponent,

  ],
  imports: [
    CommonModule,
    VendorsRoutingModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    TabsModule.forRoot(),
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    CalendarModule,
    GoogleMapModule,
    NgxGpAutocompleteModule,
    NgxMaskModule.forRoot(),
    PipesModule
  ],
  providers: [ConfirmationService, {
    provide: Loader,
    useValue: new Loader({
      apiKey: environment.googleMapApiKey,
      libraries: ['places']
    })
  }],
  exports: [VendorAddComponent, NgxMaskModule]
})
export class VendorsModule { }
