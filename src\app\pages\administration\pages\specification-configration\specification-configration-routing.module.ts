import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionForChildGuard } from '@core/guards';
import { ROUTER_UTILS } from '@core/utils';
import { PermissionActions } from 'src/app/@shared/models';
import { SpecificationConfigrationComponent } from './specification-configration.component';

const routes: Routes = [
  {
    path: '',
    component: SpecificationConfigrationComponent,
    title: 'Skeye - Category Specification',
    children: [
      {
        path: ROUTER_UTILS.config.administration.specificationConfig.category.root,
        loadChildren: async () => (await import('./pages/category/category.module')).CategoryModule,
        canActivate: [PermissionForChildGuard],
        data: {
          permission: [PermissionActions.VIEW_CATEGORY]
        }
      },
      {
        path: ROUTER_UTILS.config.administration.specificationConfig.unitType.root,
        loadChildren: async () => (await import('./pages/unit-type/unit-type.module')).UnitTypeModule,
        canActivate: [PermissionForChildGuard],
        data: {
          permission: [PermissionActions.VIEW_UNIT_TYPE]
        }
      },
      {
        path: ROUTER_UTILS.config.administration.specificationConfig.makeModel.root,
        loadChildren: async () => (await import('./pages/make-model/make-model.module')).MakeModelModule,
        canActivate: [PermissionForChildGuard],
        data: {
          permission: [PermissionActions.VIEW_MAKE_MODEL]
        }
      },
      {
        path: ROUTER_UTILS.config.administration.specificationConfig.specification.root,
        loadChildren: async () => (await import('./pages/specification/specification.module')).SpecificationModule,
        canActivate: [PermissionForChildGuard],
        data: {
          permission: [PermissionActions.VIEW_SPECIFICATION_MASTER]
        }
      }
    ]
  }
];


@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})


export class SpecificationConfigrationRoutingModule { }
