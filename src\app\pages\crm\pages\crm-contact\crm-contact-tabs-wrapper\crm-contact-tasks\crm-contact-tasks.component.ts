import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CrmContactListItem } from '@pages/administration/models/crm.model';
import { CrmTaskListFilter } from '@pages/crm/models/crm-task.model';
import { TaskListItem } from '@pages/shops/models';
import { TaskService } from '@pages/shops/services/tasks.service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { DataType, IdNameModel, OperatorType, TreeOperatorType, ViewMode } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';

@Component({
  selector: 'app-crm-contact-tasks',
  templateUrl: './crm-contact-tasks.component.html',
  styleUrls: ['./crm-contact-tasks.component.scss'],
})
export class CrmContactTasksComponent extends BaseComponent implements OnInit {

  selectedTask!: TaskListItem | null;
  isStatusList!: number | null;
  tasks: TaskListItem[] = [];
  taskStatuses: IdNameModel[] = [];
  status!: string;
  taskId!: string;
  showCreateModal = false;
  showConfirmationDialog = false;
  keyByTaskId = "taskType.id";
  keyByCrmContactId = 'crmContact.id'
  @Input() filterParams: any = new CrmTaskListFilter();
  @Input() crmContactInfo!: CrmContactListItem | null;
  @Input() isViewMode: boolean | null = false;
  isActiveTab = true;
  isArchiveInProgress = false;
  constructor(private readonly taskService: TaskService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService) { super(); }

  ngOnInit(): void {
    this.getTaskStatuses();
    this.listenToRouteParams();
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  private listenToRouteParams(): void {
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      if (params.id) {
        this.isViewMode = ViewMode[(params.mode as ViewMode)] !== ViewMode[ViewMode.EDIT];
        this.showCreateModal = true;
        this.taskId = params.id;
      }
    });
  }

  get taskInfoParams(): CrmTaskListFilter {
    return {
      archived: false,
      treeOperator: TreeOperatorType.AND,
      values: [
        {
          dataType: DataType.LONG,
          key: this.keyByTaskId,
          operator: OperatorType.EQUAL,
          value: 4
        },
        {
          dataType: DataType.LONG,
          key: this.keyByCrmContactId,
          operator: OperatorType.EQUAL,
          value: this.crmContactInfo?.id ? this.crmContactInfo?.id : ''
        }
      ]
    };
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams = this.taskInfoParams;
    this.taskService.add(this.filterParams, API_URL_UTIL.tasks.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.tasks = res.content;
          this.setActiveFlagForAll()
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  private setActiveFlagForAll() {
    this.tasks.forEach(task => task.isActive = this.isActiveTab);
  }
  onAdd(): void {
    this.showCreateModal = true;
    this.isViewMode = false;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedTask = null;
    if (refreshList) {
      this.getAll();
    }
    this.router.navigate([], { queryParams: {} });
  }

  onEdit(crmContact: TaskListItem): void {
    this.showCreateModal = true;
    this.selectedTask = crmContact;
    this.cdf.detectChanges();
  }

  onDelete(pipelineConfig: CrmContactListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'crm task data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(pipelineConfig);
      }
    });
  }

  onDeleteConfirmation(task: CrmContactListItem): void {
    this.taskService.delete(task.id, API_URL_UTIL.tasks.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.shopTaskDeleteSuccess);
          this.getAll();
        }
      });
  }

  onViewEdit(task: TaskListItem, isEdit: boolean): void {
    this.isViewMode = isEdit ? false : true;
    this.router.navigate([], { queryParams: { id: task.id, mode: isEdit ? ViewMode.EDIT : ViewMode.READ } });
    this.selectedTask = task;
  }

  private getTaskStatuses(): void {
    this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.statuses).pipe(takeUntil(this.destroy$)).subscribe({
      next: (taskStatuses) => {
        this.taskStatuses = taskStatuses;
      },
    });
  }
  findStatusIndex(index: number): void {
    this.isStatusList = index;
  }

  changeStatus(task: TaskListItem, status: number, id: number, isActive: any): void {
    const statusParam = {
      statusId: status
    }
    const endpoint = API_URL_UTIL.tasks.status.replace(':taskId', id.toString());
    this.taskService.update(statusParam, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.statusChangeSuccess);
      this.isStatusList = null;
      this.getAll();
    });
    if (status === 4) {
      const message = task.isActive
        ? MESSAGES.archiveWarning.replace('{record}', 'task')
        : MESSAGES.unArchiveWarning.replace('{record}', 'vendor');
      this.handleTaskConfirmation(task, isActive, message, true);
    }
  }

  onArchive(task: TaskListItem, isActive: boolean): void {
    this.selectedTask = task;
    const message = task.isActive
      ? MESSAGES.archiveWarning.replace('{record}', 'task')
      : MESSAGES.unArchiveWarning.replace('{record}', 'task');
    this.handleTaskConfirmation(task, isActive, message);
  }

  private handleTaskConfirmation(task: TaskListItem, isActive: boolean, message: string, isDialog = false): void {
    this.showConfirmationDialog = isDialog;
    this.cdf.detectChanges();
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: message,
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.isArchiveInProgress = true;
        this.showConfirmationDialog = false;
        this.onArchiveConfirmation(task);
        task.isActive = isActive;
      },
      reject: () => {
        task.isActive = !isActive;
        this.showConfirmationDialog = false;
        this.cdf.detectChanges();
      }
    });
  }

  private onArchiveConfirmation(task: TaskListItem): void {
    this.taskService.archivedTask((task.id).toString())
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.isActiveTab ? MESSAGES.taskArchiveSuccess : MESSAGES.taskUnArchiveSuccess);
          this.isArchiveInProgress = false;
          this.selectedTask = null;
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedTask = null;
          this.getAll();
        }
      });
  }

  onBackdropClick(element: HTMLElement): void {
    Utils.handlePopoverBackdropClick(element, this.selectedTask, 'status', this.isActiveTab, this.cdf);
  }
}
