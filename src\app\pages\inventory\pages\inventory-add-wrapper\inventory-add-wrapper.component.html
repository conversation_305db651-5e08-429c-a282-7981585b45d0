<div class="modal-title close">
  <h4 class="header-title">{{ title }}</h4>
  <span *ngIf="isEditMode && inventoryIncomingInfo" class="created-by">
    <span class="bold">#{{ inventoryIncomingInfo.unit.generalInformation.stockNumber }}</span>
    Created By
    <span class="bold">{{ inventoryIncomingInfo.unit.createdBy.name }}</span> on
    {{ inventoryIncomingInfo.unit.createdDate | date: constants.fullDateFormat }}
  </span>
  <span *ngIf="isEditMode && !inventoryIncomingInfo" class="created-by">
    <span class="bold">#{{ inventoryInfo?.generalInformation?.stockNumber }}</span>
    Created By <span class="bold">{{ inventoryInfo?.createdBy?.name }}</span> on
    {{ inventoryInfo?.createdDate | date: constants.fullDateFormat }}
  </span>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()" class="z-1"></fa-icon>
</div>

<form (ngSubmit)="onSubmit()">
  <div class="content">
    <ng-container [ngSwitch]="currentStep">
      <div *ngSwitchCase="addUnitSteps.SEARCH_UNIT">
        <app-inventory-search (onVehicleSelect)="onVehicleSelect($event)"></app-inventory-search>
      </div>
      <div *ngSwitchCase="addUnitSteps.ADD_UNIT">
        <app-inventory-add
          (onClose)="onCancel($event)"
          (isPrevious)="isPreviousTab($event)"
          [isEditMode]="isEditMode"
          [inventoryInfo]="inventoryInfo"
          [inventoryIncomingInfo]="inventoryIncomingInfo"
          (addedUnitId)="onAddedUnitId($event)"
          (showLoader)="setLoaderStatus($event)"
          [showAssociation]="showAssociation"
          (onTabChange)="onTabChange($event)"
          (isEditTitle)="onEditTitle($event)"
          [isViewMode]="isViewMode"
          [activeIndexes]="activeIndexes"
          [showSoldTabs]="showSoldTabs"
          [showHoldTabs]="showHoldTabs"
          [categoriesToShow]="categoriesToShow"
        >
        </app-inventory-add>
      </div>
    </ng-container>
  </div>

  <div class="modal-footer">
    <ng-container [ngSwitch]="currentStep">
      <ng-container *ngSwitchCase="addUnitSteps.SEARCH_UNIT">
        <ng-container [ngTemplateOutlet]="searchUnitActionsTemplate"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="addUnitSteps.ADD_UNIT">
        <ng-container [ngTemplateOutlet]="addUnitActionsTemplate"></ng-container>
      </ng-container>
    </ng-container>
  </div>
</form>

<ng-template #searchUnitActionsTemplate>
  <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
  <button class="btn btn-primary" type="submit" (click)="onStepChange(addUnitSteps.ADD_UNIT)">Add information manually</button>
  <button class="btn btn-primary" type="submit" (click)="onStepChange(addUnitSteps.ADD_UNIT)" *ngIf="selectedVehicle">Continue with selected vehicle</button>
</ng-template>

<ng-template #addUnitActionsTemplate>
  <div class="step-actions" [ngClass]="{ 'justify-content-end': isEditMode || !isPrevious }">
    <div>
      <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
      <button class="btn btn-primary mx-2" type="submit" *ngIf="!isViewMode || isFlagEditButton" appShowLoaderOnApiCall>
        Save
      </button>
      <ng-container *appHasPermission="[permissionActions.UPDATE_INVENTORY]">
        <button class="btn btn-primary mx-2" type="submit" *ngIf="isViewMode && !isFlagEditButton">Edit</button>
      </ng-container>
    </div>
  </div>
</ng-template>
