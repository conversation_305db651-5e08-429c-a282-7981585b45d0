import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ConfirmationService } from 'primeng/api';
import { forkJoin, takeUntil } from 'rxjs';
import { MakeModel, MakeModelCountResponse, UnitTypeCategory } from '../../models/specification.model';
import { CategoryService } from '../category/category.service';
import { MakeModelService } from './make-model.service';

@Component({
  selector: 'app-make-model',
  templateUrl: './make-model.component.html',
  styleUrls: ['./make-model.component.scss']
})
export class MakeModelComponent extends BaseComponent implements OnInit {

  modelList: MakeModel[] = [];
  countModelList: MakeModelCountResponse[] = [];
  categories!: Array<UnitTypeCategory>;
  selectedModelList!: MakeModelCountResponse[];
  selectedCategory!: number;
  selectedMake!: MakeModel | null;
  showCreateModal = false;

  constructor(
    private readonly makeModelService: MakeModelService,
    private readonly cdf: ChangeDetectorRef,
    private readonly categoryService: CategoryService,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService
  ) {
    super();
    this.pageTitle = 'MAKE/MODEL';
  }

  ngOnInit(): void {
    this.getCategories();
  }

  getCategories(): void {
    this.isLoading = true;
    this.categoryService.getList<UnitTypeCategory>().pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Array<UnitTypeCategory>) => {
        this.categories = res;
        this.selectedCategory = res[0]?.id;
        this.unitTypeCategoryCounts(res[0]?.id);
        this.cdf.detectChanges();
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  unitTypeCategoryCounts(categoryId: number): void {
    this.makeModelApiCall(categoryId);
  }

  private makeModelApiCall(categoryId: any): Promise<void> {
    return new Promise((resolve) => {
      this.isLoading = true;
      const request1 = this.makeModelService.getList<MakeModel>(`${API_URL_UTIL.admin.makeSpecification.category}/${categoryId}`)
      const request2 = this.makeModelService.getList<MakeModelCountResponse>(`${API_URL_UTIL.admin.makeSpecification.category}/${API_URL_UTIL.admin.makeSpecification.count}?isInventory=false&categoryId=${categoryId}`)

      forkJoin([request1, request2]).subscribe(response => {
        this.modelList = response[0];
        this.countModelList = response[1];
        this.modelList.forEach(m => {
          const tempModelList = this.countModelList.find(make => make.id === m.id)
          if (tempModelList) {
            m['inventoryCount'] = tempModelList?.inventoryCount
          }
        })
        this.cdf.detectChanges();
        this.isLoading = false;
        resolve();
      });
    });
  }

  onCategoryChange(categoryId: number): void {
    if (categoryId) {
      this.unitTypeCategoryCounts(categoryId);
    }
  }

  onAddEditMake(makeDetails?: MakeModel): void {
    if (makeDetails?.id) {
      this.selectedMake = makeDetails;
    }
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  closeModal(): void {
    this.showCreateModal = false;
    this.selectedMake = null;
  }

  addNewMake(makeDetails: MakeModel): void {
    if (makeDetails.categoryId === this.selectedCategory) {
      makeDetails.models = makeDetails?.models?.map(model => {
        return {
          ...model,
          makeId: makeDetails.id
        }
      });
      this.modelList.push({ ...makeDetails, inventoryCount: 0 });
    }
  }

  updateMake(makeDetails: MakeModel): void {
    const selectedMakeIndex = this.modelList.findIndex((catgory: MakeModel) => catgory.id === makeDetails.id);
    if (this.modelList[selectedMakeIndex].categoryId === makeDetails.categoryId) {
      this.modelList[selectedMakeIndex].name = makeDetails.name;
      this.modelList[selectedMakeIndex].models = makeDetails.models;
    } else {
      this.modelList = this.modelList.filter((model: MakeModel) => model.id !== makeDetails.id);
    }
    this.toasterService.success(MESSAGES.updateSuccessMessage.replace('{item}', 'Make/Model'));
  }

  showDeleteModal(makeDetails?: MakeModel, event?: Event, deleteAll = false): void {
    if (makeDetails && makeDetails?.inventoryCount) {
      this.toasterService.warning(MESSAGES.makeModelDeleteWarning);
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'Make/Model data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        if (deleteAll) {
          this.selectedModelList = this.selectedModelList.filter(model => !model.inventoryCount)
          const unitTypeShouldBeDeleted = this.selectedModelList.map(({ id }) => id)
          this.onDeleteModel(unitTypeShouldBeDeleted, true);
        } else if (!deleteAll && makeDetails?.id) {
          this.onDeleteModel([makeDetails.id]);
        }
      }
    });
  }

  getSelectedMakeModelCount(): number {
    return this.selectedModelList?.filter(make => !make?.inventoryCount)?.length ?? 0;
  }

  getMakeModelCount(): number {
    return this.modelList?.filter(make => !make?.inventoryCount)?.length ?? 0;
  }

  onDeleteModel(makeDetails: Array<number>, deleteAll = false): void {
    this.makeModelService.bulkDelete(makeDetails, API_URL_UTIL.admin.unitSpecification.delete).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        if (deleteAll) {
          this.modelList = this.modelList.filter(i => !this.selectedModelList.some(j => j.id === i.id))
        } else {
          this.modelList = this.modelList.filter((model: MakeModel) => model.id !== makeDetails[0]);
        }
        this.selectedModelList = [];
        this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('{item}', `${deleteAll ? 'Make/Model without active inventories' : 'Make/Model'}`));
      }
    });
  }
}
