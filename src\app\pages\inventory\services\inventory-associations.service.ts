import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { Observable } from 'rxjs';
import { AssociationHistory } from '../models';

@Injectable({
  providedIn: 'root'
})
export class InventoryAssociationsService extends BaseCrudService {

  constructor(protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  getBaseAPIPath(): string {
    return API_URL_UTIL.inventory.unitsAssociation;
  }

  getAssociationHistory(id: number): Observable<Array<AssociationHistory>> {
    return this.httpClient.get<Array<AssociationHistory>>(`${API_URL_UTIL.inventory.unitEventAuditTrails}/${API_URL_UTIL.inventory.unit}/${id}`);
  }
}
