<div class="content">
  <div class="add-wrapper">
    <p-tabView [(activeIndex)]="activeIndex" [scrollable]="true">
      <p-tabPanel header="Inventory Matched {{inventoryMatchedCount ? '(' + inventoryMatchedCount + ')' : '' }}">
        <ng-template pTemplate="content">
          <app-crm-customer-inventory-matched [inventorySpecificationForm]="inventorySpecificationForm" [crmId]="crmId"
            [crmCustomerInfo]="crmCustomerInfo" [quotationCustomerList]="quotationCustomerList"
            (onQuotationAccepted)="onQuotationAccepted($event)" [customerDetails]="customerDetails"
            (onInventoryMatchedCount)="onInventoryMatched($event)">
          </app-crm-customer-inventory-matched>
        </ng-template>
      </p-tabPanel>
      <p-tabPanel header="Quote">
        <ng-template pTemplate="content">
          <app-crm-customer-quotation [crmId]="crmId" [quotationCustomerList]="quotationCustomerList"
            [categoryId]="crmCustomerInfo.category.id" (onQuotationAccepted)=" onQuotationAccepted($event)">
          </app-crm-customer-quotation>
        </ng-template>
      </p-tabPanel>
      <p-tabPanel header="Task" *appHasPermission="[permissionActions.VIEW_SALES_TASK]">
        <ng-template pTemplate="content">
          <app-crm-customer-task [crmId]="crmId" [crmCustomerInfoId]="crmCustomerInfoId"></app-crm-customer-task>
        </ng-template>
      </p-tabPanel>
    </p-tabView>
  </div>
</div>