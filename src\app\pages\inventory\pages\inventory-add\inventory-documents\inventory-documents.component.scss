@import 'src/assets/scss/variables';

.inventory-col-width-sm {
  width: 20%;
}

.inventory-col-width-md {
  width: 45%;
}

.upload-button {
  padding: 0 10px 0 40px;

  img {
    height: 20px;
    margin: 10px 16px;
    position: absolute;
    left: -2%;
    top: -5%;
  }
}

.drop-zone__input {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 197px;
}

.view-document-enabled {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
}

.upload-doc {
  margin-left: 0px;
  float: right;
  margin-bottom: 10px;
  position: relative;
  top: -57px;
}

.ass-doc h4 {
  margin-top: 10px;
}

.pdf-icon {
  padding: 0 5px 0 0 !important;
  pointer-events: none;

  img {
    height: 20px;
  }
}

.files {
  width: 600px;
  padding: 12px 40px;
  border: 1px solid #add2ff;
  background-color: #f6f9fd;
}

.file-progress {
  width: 95%;
  margin-left: 40px;
  margin-top: 24px;
}

.file-box {
  margin-left: -29px;
  position: absolute;
  margin-top: -5px;
}

.file-box {
  img {
    height: 55px;
  }
}

.file-box-wrapper {
  position: relative;
}

.parent-associate-document {
  padding: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right {
  position: relative;
}

.add-margin {
  margin-top: 4rem;
}

.view-icon {
  width: 15px;
  cursor: pointer;
  margin-right: 15px;
}

::ng-deep .upload-doc {
  p-menu .p-menu {
    top: 40px !important;
    left: 0px !important;
  }

  .hide-upload-input {
    display: none;
  }
}

@media only screen and (max-width: 500px) {
  ::ng-deep .content {
    overflow-y: unset !important;
  }

  .upload-button {
    padding: 0 10px 0 40px;

    img {
      left: -12%;
      top: -22%;
    }
  }
}
