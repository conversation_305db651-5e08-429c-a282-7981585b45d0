import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RedirectToAuthorizedPageComponent } from './redirect-to-authorized-page.component';

const routes: Routes = [{
  path: '',
  component: RedirectToAuthorizedPageComponent
}]

@NgModule({
  declarations: [
    RedirectToAuthorizedPageComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ]
})
export class RedirectToAuthorizedPageModule { }
