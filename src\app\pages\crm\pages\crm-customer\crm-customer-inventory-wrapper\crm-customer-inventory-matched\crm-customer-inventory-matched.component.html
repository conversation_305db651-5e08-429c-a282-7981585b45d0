<div class="quotation-wrapper">
  <div class="row customer-inventory-matched">
    <ng-container *ngIf="isInventoryLoading; else loaderTemplate">
      <p-card *ngFor="let quotationDetail of quotationDetails; let quotationIndex = index" class="col-lg-6 col-12">
        <div class="row customer-details">
          <div class="col-12 col-lg-3 text-center">
            <img
              [src]="quotationDetail?.unitImages?.fullUrl ? quotationDetail.unitImages.fullUrl : constants.staticImages.noImages"
              alt=""
              class="unit-image"
            />
          </div>
          <div [ngClass]="'col-12 col-lg-8'">
            <div>
              <h6>
                {{ quotationDetail.generalInformation.make.name }}
                {{ quotationDetail.generalInformation.unitModel.name }}
                {{ quotationDetail.generalInformation.year }}
              </h6>
            </div>
            <div class="d-flex justify-content-between content-wrapper">
              <div class="mb-2">
                <div>
                  <span class="gray">#Stock:</span>
                  <span class="black"> {{ quotationDetail.generalInformation.stockNumber }}</span>
                </div>
                <div>
                  <span class="gray">Retail:</span>
                  <span class="black">
                    {{ quotationDetail.retailAskingPrice ? (quotationDetail.retailAskingPrice | currency: 'USD':'symbol':'1.0-0') : 'NA' }}
                  </span>
                </div>
                <div>
                  <span class="gray">Associated Stocks:</span>
                  <ng-container *ngIf="quotationDetail?.unitQuotationAssociationDTOS?.length; else noUnitAssociated">
                    <span class="black" *ngFor="let unit of quotationDetail?.unitQuotationAssociationDTOS; let index = index">
                      {{ unit?.stockNumber }}
                      <span *ngIf="index !== quotationDetail.unitQuotationAssociationDTOS.length - 1">,</span>
                    </span>
                  </ng-container>
                  <ng-template #noUnitAssociated>
                    <span class="black">NA</span>
                  </ng-template>
                </div>
              </div>
              <div>
                <div>
                  <span class="gray">Vin:</span>
                  <span class="black">
                    {{ quotationDetail.generalInformation.vin ? quotationDetail.generalInformation.vin : 'NA' }}
                  </span>
                </div>
                <div>
                  <span class="gray">Investment Cost:</span>
                  <span class="black">
                    {{
                      quotationDetail?.totalProjectedInvestment
                        ? (quotationDetail?.totalProjectedInvestment | currency: 'USD':'symbol':'1.0-0')
                        : 'NA'
                    }}
                  </span>
                </div>
              </div>
            </div>
            <div class="send-quote">
              <button
                [ngClass]="quotationDetail?.isExisting ? 'btn btn-outline-primary' : 'btn btn-primary'"
                type="button"
                (click)="showInventoryDetails(quotationDetail)"
              >
                <span class="m-l-20">
                  <span *ngIf="quotationDetail?.isExisting"> Resend Email </span>
                  <span *ngIf="!quotationDetail?.isExisting"> Send Email </span>
                  <span class="show-label">to Customer</span>
                </span>
              </button>
            </div>
          </div>
        </div>
      </p-card>
      <div *ngIf="!quotationDetails?.length" class="center">No data to display</div>
    </ng-container>
  </div>
</div>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-customer-inventory-details
    *ngIf="showCreateModal"
    [quotationDetails]="selectedQuotation"
    [crmId]="crmId"
    (saveQuotation)="onSaveQuotation()"
    (closeModal)="onCloseModal()"
  >
  </app-crm-customer-inventory-details>
</p-sidebar>

<ng-template #loaderTemplate>
  <div class="notification-loader">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
  </div>
</ng-template>
