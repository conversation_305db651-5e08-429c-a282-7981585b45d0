import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SupplierListComponent } from './supplier-list/supplier-list.component';
import { SuppliersComponent } from './suppliers.component';

const routes: Routes = [
  {
    path: '',
    component: SuppliersComponent,
    title: 'Skeye - Suppliers',
    children: [
      {
        path: '',
        component: SupplierListComponent,
        pathMatch: 'full',
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SuppliersRoutingModule { }
