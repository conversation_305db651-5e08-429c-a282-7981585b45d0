<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<div class="card p-3 m-3 preference-list">
  <p-table [value]="preferenceList" dataKey="id" editMode="row" responsiveLayout="scroll" styleClass="p-datatable-gridlines" [lazy]="true" class="inventory-page-table" [loading]="isLoading">
    <ng-template pTemplate="header">
      <tr>
        <th>Preference Name</th>
        <th class="text-center">Action</th>
      </tr>
      <tr class="preference-search-tr">
        <th pResizableColumn class="small-col" scope="col">
          <span class="search-input"><input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event)" [(ngModel)]="searchedTerm" /> </span>
        </th>
        <th></th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-preference let-editing="editing" let-ri="rowIndex">
      <tr [pEditableRow]="preference">
        <td>
          <p-cellEditor>
            <ng-template pTemplate="input">
              <input class="form-control" pInputText type="text" [(ngModel)]="preference.filterName" />
            </ng-template>
            <ng-template pTemplate="output">
              {{ preference.filterName }}
            </ng-template>
          </p-cellEditor>
        </td>
        <td style="text-align: center">
          <button
            pTooltip="Apply"
            tooltipPosition="top"
            *ngIf="!editing"
            pButton
            pRipple
            type="button"
            icon="pi pi-send"
            (click)="onApplyFilters(preference)"
            class="p-button-rounded p-button-text action-btn"
          ></button>
          <button
            pTooltip="Edit name"
            tooltipPosition="top"
            *ngIf="!editing"
            pButton
            pRipple
            type="button"
            pInitEditableRow
            icon="pi pi-pencil"
            (click)="onRowEditInit(preference)"
            class="p-button-rounded p-button-text action-btn"
          ></button>
          <button
            pTooltip="Delete"
            tooltipPosition="top"
            *ngIf="!editing"
            pButton
            pRipple
            type="button"
            icon="pi pi-trash"
            (click)="onDelete(preference, ri)"
            class="p-button-rounded p-button-text text-danger action-btn"
          ></button>
          <button
            pTooltip="Save"
            tooltipPosition="top"
            *ngIf="editing"
            pButton
            pRipple
            type="button"
            pSaveEditableRow
            icon="pi pi-check"
            (click)="onRowEditSave(preference)"
            class="p-button-rounded p-button-text text-success action-btn mr-2"
          ></button>
          <button
            pTooltip="Cancel"
            tooltipPosition="top"
            *ngIf="editing"
            pButton
            pRipple
            type="button"
            pCancelEditableRow
            icon="pi pi-times"
            (click)="onRowEditCancel(preference, ri)"
            class="p-button-rounded p-button-text text-danger action-btn"
          ></button>
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="2" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
</div>
