import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { AddNewUnitTypeService } from './add-new-unit-type.service';

@Component({
  selector: 'app-add-new-unit-type',
  templateUrl: './add-new-unit-type.component.html',
  styleUrls: []
})
export class AddNewUnitTypeComponent extends BaseComponent implements OnInit {
  title = 'Add Unit Type';
  hasDataBeenModified = false;
  unitTypeFormGroup!: FormGroup;

  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() unitTypeCategoryId!: number | null;
  constructor(private readonly formBuilder: FormBuilder,
    private readonly addNewUnitService: AddNewUnitTypeService,
    private readonly toasterService: AppToasterService,
  ) { super(); }

  ngOnInit(): void {
    this.initializeFormGroup();
  }

  private initializeFormGroup(): void {
    this.unitTypeFormGroup = this.formBuilder.group({
      name: new FormControl(null, Validators.required)
    });
  }

  get unitTypeParams(): IdNameModel {
    return {
      ...this.unitTypeFormGroup.value,
      unitTypeCategoryId: this.unitTypeCategoryId
    };
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  onSubmit(close = true): void {
    if (this.unitTypeFormGroup.invalid) {
      this.unitTypeFormGroup.markAllAsTouched();
      return;
    }
    this.save(close);
  }

  save(close = true): void {
    this.addNewUnitService.add(this.unitTypeParams, API_URL_UTIL.units.type).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.unitTypeAddSuccess);
      if (close) {
        this.onClose.emit(true);
      } else {
        this.unitTypeFormGroup.reset();
      }
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

}
