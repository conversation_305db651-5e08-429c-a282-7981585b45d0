import { ApplicationConfig, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { distinctUntilChanged, takeUntil } from 'rxjs';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';
import { InventoryAging, InventoryAgingLabelColors, InventoryLabels } from '../dashboard.model';
import { DashboardService } from '../dashboard.service';

@Component({
  selector: 'app-inventory-aging-chart',
  templateUrl: './inventory-aging-chart.component.html',
  styleUrls: ['./inventory-aging-chart.component.scss']
})
export class InventoryAgingChartComponent extends BaseComponent implements OnInit {
  inventoryAging!: InventoryAging;
  isDarkMode = false;
  data: any;
  chartOptions: any;
  dataCountArray: number[] = [];
  config!: ApplicationConfig;
  @Input() isFullViewInventoryAging!: boolean;
  inventoryLabels = InventoryLabels;
  inventoryAgingLabelColors = InventoryAgingLabelColors;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(private readonly dashboardService: DashboardService, private readonly cdf: ChangeDetectorRef, private readonly themeService: ThemeService) { super() }

  ngOnInit(): void {
    this.getAllInventoryAgingData();
    this.subscribeToTheme();
  }

  getAllInventoryAgingData(): void {
    this.dashboardService.get<InventoryAging>(API_URL_UTIL.dashboard.inventoryAging)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.inventoryAging = data;
          Object.keys(this.inventoryAging).forEach(key => {
            this.dataCountArray.push(this.inventoryAging[key as keyof InventoryAging]);
          });
          this.cdf.detectChanges();
          this.showInvenotryAgingChart();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  showInvenotryAgingChart(): void {
    this.data = {
      datasets: [
        {
          data: this.dataCountArray,
          backgroundColor: [
            InventoryAgingLabelColors.NULL_COLR,
            InventoryAgingLabelColors.UNDER_30_COLOR,
            InventoryAgingLabelColors.DAY_30_TO_60_COLOR,
            InventoryAgingLabelColors.DAY_60_TO_90_COLOR,
            InventoryAgingLabelColors.DAY_90_TO_120_COLOR,
            InventoryAgingLabelColors.DAY_120_TO_180_COLOR,
            InventoryAgingLabelColors.OVER_180_COLOR
          ],
        }
      ],
      labels: [this.inventoryLabels.NULL, this.inventoryLabels.UNDER_30, this.inventoryLabels.DAY_30_TO_60, this.inventoryLabels.DAY_60_TO_90, this.inventoryLabels.DAY_90_TO_120, this.inventoryLabels.DAY_120_TO_180, this.inventoryLabels.OVER_180],
    };
    this.chartOptions = {
      plugins: {
        legend: {
          labels: {
            color: this.isDarkMode ? 'white' : 'black',
          }
        }
      }
    };
  }


  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        if (theme === THEMES.DARK) {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
        this.showInvenotryAgingChart();
        this.cdf.detectChanges();
      });
  }

  onCancel(): void {
    this.onClose.emit(true);
  }
}
