import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { CategoryComponent } from "./category.component";

const routes: Routes = [
    {
      path: '',
      component: CategoryComponent,
      title: 'Skeye - Specification Category',
      children: [
        {
          path: '',
          component: CategoryComponent,
          pathMatch: 'full'
        }
      ]
    }
  ];

  @NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
  })

  export class CategoryRoutingModule { }
