.step-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.financial-title {
  color: white;
  font-weight: 300;
}

.m-r-500 {
  margin-right: 500px;
}

.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
  position: absolute;
  top: 30px;
}

.header-title {
  top: -8px;
  position: relative;
}

.bold {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

@media only screen and (max-width: 500px) {
  .created-by {
    display: none;
  }
}

@media only screen and (max-width: 500px) {
  .z-1 {
    z-index: 1;
  }
}
