import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { ShopsModule } from '@pages/shops/shops.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { MessageModule } from 'primeng/message';
import { ProgressBarModule } from 'primeng/progressbar';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmContactCustomerAddModule } from '../../crm-customer/crm-contact-customer-add/crm-contact-customer-add.module';
import { CrmTaskAddExtendedComponent } from './crm-task-add-extended';

@NgModule({
  declarations: [
    CrmTaskAddExtendedComponent
  ],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    SidebarModule,
    ReactiveFormsModule,
    TabViewModule,
    CardModule,
    TableModule,
    DropdownModule,
    CheckboxModule,
    ConfirmPopupModule,
    ProgressBarModule,
    MessageModule,
    CalendarModule,
    AccordionModule,
    ShopsModule,
    CrmContactCustomerAddModule,
    DialogModule
  ],
  exports: [CrmTaskAddExtendedComponent],
  providers: [ConfirmationService, MessageService],
})

export class CrmTaskAddExtendedModule { }
