<div class="modal-title">
  <h4>Email Config</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="closePopup()"></fa-icon>
</div>

<div class="card p-3 m-3" *ngIf="agingConfigList.length">
  <ng-container *ngFor="let config of agingConfigList">
    <div class="days-item p-3">
      <span> {{ config.days }} Days </span>
      <span> <img pTooltip="Edit" [src]="constants.staticImages.icons.edit" alt="edit" class="cursor-pointer" (click)="onEdit(config)" /></span>
    </div>
  </ng-container>
</div>
<p-sidebar
  [(visible)]="showEditModal"
  position="right"
  (onHide)="showEditModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  [fullScreen]="true"
  styleClass="p-sidebar-md"
>
  <app-config-update *ngIf="showEditModal" [ageConfig]="selectedData" (onClose)="closeEditModal($event)"></app-config-update>
</p-sidebar>
