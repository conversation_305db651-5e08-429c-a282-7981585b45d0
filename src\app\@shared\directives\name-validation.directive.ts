import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[appNameValidation]'
})
export class NameValidationDirective {
  constructor(private el: ElementRef) {}

  // Listen for keydown events to prevent leading space on the first key press
  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    const inputElement = this.el.nativeElement as HTMLInputElement;
    // If the first character typed is a space, prevent it
    if (event.key === ' ' && inputElement.selectionStart === 0) {
      event.preventDefault();  // Prevent space at the beginning
      inputElement.setCustomValidity('No leading spaces allowed');
      inputElement.reportValidity();
    } else {
      inputElement.setCustomValidity('');
    }
  }

  // Listen for blur events to sanitize input after typing
  @HostListener('blur', ['$event'])
  onBlur(event: Event): void {
    const inputElement = this.el.nativeElement as HTMLInputElement;

    // Remove leading spaces, multiple spaces between words, and trailing spaces
    let value = inputElement.value
      .replace(/^\s+/, '')  // Remove leading spaces
      .replace(/\s+/g, ' ') // Replace multiple spaces between words with a single space
      .replace(/\s+$/, ''); // Remove trailing spaces

    // If the value only contains spaces, set it to empty string (invalid input)
    if (value.trim() === '') {
      inputElement.value = ''; // Remove value if only spaces
      inputElement.setCustomValidity('This field cannot be empty or just spaces');
      inputElement.reportValidity(); // Immediately show the validation message
    } else {
      inputElement.value = value; // Else keep the cleaned value
      inputElement.setCustomValidity(''); // Clear the custom validity message
    }
  }

  // Listen for input events to prevent unnecessary modifications while typing
  @HostListener('input', ['$event'])
  onInputChange(event: Event): void {
    const inputElement = this.el.nativeElement as HTMLInputElement;
    // No changes to input value here during typing
  }
}
