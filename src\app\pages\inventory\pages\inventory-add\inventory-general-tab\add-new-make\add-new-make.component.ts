import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, UntypedFormArray, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { MakeModel, UnitTypeCategory } from '@pages/administration/pages/specification-configration/models/specification.model';
import { MakeModelService } from '@pages/administration/pages/specification-configration/pages/make-model/make-model.service';
import { UnitModel } from '@pages/inventory/models';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-add-new-make',
  templateUrl: './add-new-make.component.html',
  styleUrls: ['./add-new-make.component.scss']
})
export class AddNewMakeComponent extends BaseComponent implements OnInit, OnChanges {

  @Input() isAddEdit!: boolean;
  @Input() categories!: Array<UnitTypeCategory>;
  @Input() selectedMake!: MakeModel | null;

  title!: string;
  hasDataBeenModified = false;
  makeFormGroup!: FormGroup;

  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() addNewMake = new EventEmitter<MakeModel>();
  @Output() editMake = new EventEmitter<MakeModel>();

  @Input() categoryId!: number | null;

  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly toasterService: AppToasterService,
    private readonly makeModelService: MakeModelService
  ) {
    super();
    this.initializeFormGroup();
  }

  ngOnInit(): void {
    this.setPageConfigurations();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.categoryId?.currentValue) {
      this.addMakeConfiguration();
    }

    if (changes?.selectedMake?.currentValue) {
      this.setPageConfigurations();
    }
  }

  setPageConfigurations(): void {
    if (this.selectedMake?.id) {
      this.title = `Edit Make${this.isAddEdit ? '/Model' : ''}`;
      this.setMakeFormValues();
      return;
    }
    this.addMakeConfiguration();
  }

  setMakeFormValues(): void {
    this.makeFormGroup.patchValue({
      name: this.selectedMake?.name,
      categoryId: this.categoryId
    });
    this.modelsFormArray.clear();
    this.setModelsFormArray();
  }

  setModelsFormArray(): void {
    if (!this.selectedMake?.models?.length) {
      this.modelsFormArray.push(this.newModelFormGroup);
      return;
    }
    for (const model of this.selectedMake?.models) {
      this.modelsFormArray.push(this.formBuilder.group({
        name: new FormControl(model.name, Validators.required),
        id: new FormControl(model.id),
        makeId: new FormControl(model.makeId)
      }));
    }
  }

  addMakeConfiguration(): void {
    if (this.categoryId) {
      this.makeFormGroup.get('categoryId')?.setValue(this.categoryId);
    }
    this.title = `Add Make${this.isAddEdit ? '/Model' : ''}`;
  }

  private initializeFormGroup(): void {
    this.makeFormGroup = this.formBuilder.group({
      name: new FormControl(null, [Validators.required, Validators.maxLength(50)]),
      categoryId: new FormControl(this.categoryId, Validators.required),
      models: this.newModelsFormArray
    });
  }

  get newModelsFormArray(): UntypedFormArray {
    return this.formBuilder.array([this.newModelFormGroup]);
  }

  get modelsFormArray(): UntypedFormArray {
    return this.makeFormGroup.get('models') as UntypedFormArray;
  }

  get newModelFormGroup(): FormGroup {
    return this.formBuilder.group({
      name: new FormControl('', [Validators.required, Validators.maxLength(50)]),
      id: new FormControl(null),
      makeId: new FormControl(this.selectedMake?.id ?? null)
    });
  }

  isAddNewModelVisible(index: number): boolean {
    return index === this.modelsFormArray.length - 1;
  }

  isDeleteModelVisible(index: number): boolean {
    return !this.isAddNewModelVisible(index);
  }

  onAddNewModel(): void {
    this.modelsFormArray.push(this.newModelFormGroup);
    document.getElementById('addModelBtn')?.scrollIntoView({
      behavior: 'smooth',
    });
  }

  onDeleteModel(index: number): void {
    this.modelsFormArray.removeAt(index);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.categoryId = null;
    this.clearMakeForm();
  }

  onSubmit(close = true): void {
    if (this.makeFormGroup.invalid) {
      this.makeFormGroup.markAllAsTouched();
      return;
    }
    if (this.checkIfNamesAreUnique) {
      this.selectedMake?.id ? this.updateMake() : this.save(close);
    } else {
      this.toasterService.error(MESSAGES.namesMustBeUnique);
    }
    return;
  }

  private get checkIfNamesAreUnique(): boolean {
    const modelArray = this.makeFormGroup.get('models')?.value ?? [];
    const modelNames = modelArray.map((model: UnitModel) => model.name);
    const uniqueModels = new Set(modelNames);
    return modelNames?.length === uniqueModels?.size;
  }

  save(close = true): void {
    this.makeModelService.add(this.makeFormGroup.value).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.toasterService.success(this.isAddEdit ? MESSAGES.makeModelAddedSuccess : MESSAGES.makeAddSuccess);
        if (close) {
          this.onClose.emit(true);
          if (this.isAddEdit) {
            this.addNewMake.emit(res)
          }
        }
        this.clearMakeForm();
      }
    });
  }

  updateMake(): void {
    this.makeModelService.update<MakeModel>({ ...this.makeFormGroup.value, id: this.selectedMake?.id }).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: MakeModel) => {
        this.onClose.emit(true);
        this.editMake.emit(res);
        this.clearMakeForm();
      }
    });
  }

  clearMakeForm(): void {
    this.makeFormGroup.reset();
    this.modelsFormArray.clear();
    this.modelsFormArray.push(this.newModelFormGroup);
  }

}
