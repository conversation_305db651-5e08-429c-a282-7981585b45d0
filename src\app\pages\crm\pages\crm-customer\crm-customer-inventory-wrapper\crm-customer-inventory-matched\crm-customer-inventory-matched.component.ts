import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { InventoryMatchedParams, InventoryPreferenceDetails, QuotationKey, QuotationResponse, UnitStaus } from '@pages/crm/models/customer-inventory.model';
import { QuotationCustomerListItem } from '@pages/crm/models/customer-lead-quotation.model';
import { CustomerLeadListFilter, CustomerLeadListItem, Make, UnitModel } from '@pages/crm/models/customer-lead.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { AssociationsUnits, InventorySpecification } from '@pages/inventory/models';
import { takeUntil } from 'rxjs';
import { DataType, OperatorType } from 'src/app/@shared/models';
import { CommonService } from 'src/app/@shared/services/common.service';

@Component({
  selector: 'app-crm-customer-inventory-matched',
  templateUrl: './crm-customer-inventory-matched.component.html',
  styleUrls: ['./crm-customer-inventory-matched.component.scss']
})
export class CrmCustomerInventoryMatchedComponent extends BaseComponent implements OnInit, OnChanges {
  inventoryMatchedFormGroup!: FormGroup;
  quotationDetails!: QuotationResponse[];
  @Input() crmId!: string;
  @Input() quotationCustomerList!: QuotationCustomerListItem[] | null;
  @Output() onQuotationAccepted: EventEmitter<boolean> = new EventEmitter<boolean>()
  @Output() onInventoryMatchedCount: EventEmitter<number> = new EventEmitter<number>();
  @Input() crmCustomerInfo!: CustomerLeadListItem;
  @Input() inventorySpecificationForm!: Array<InventorySpecification>;
  @Input() customerDetails!: any;
  filterParams: CustomerLeadListFilter = new CustomerLeadListFilter();
  crmCustomerLeadList: CustomerLeadListItem[] = [];
  showCreateModal = false;
  selectedQuotation!: QuotationResponse;
  isInventoryLoading = false;

  constructor(private readonly crmService: CrmService,
    private readonly fb: FormBuilder,
    private readonly toasterService: AppToasterService,
    private readonly commonService: CommonService,
    private readonly cdf: ChangeDetectorRef) { super(); }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.quotationCustomerList?.currentValue || !changes.quotationCustomerList?.firstChange) {
      setTimeout(() => {
        this.getInventoryMatchedList();
      }, 0);
    }
    if (changes.inventorySpecificationForm?.currentValue || changes.customerDetails?.currentValue) {
      this.inventorySpecificationForm = changes.inventorySpecificationForm?.currentValue;
      this.getInventoryMatchedList();
    }
  }

  async ngOnInit(): Promise<void> {
    this.initializeMatchedFormGroup();
    await this.getInventoryPreferenceDetail();
    this.getInventoryMatchedList();
  }

  private initializeMatchedFormGroup(): void {
    this.inventoryMatchedFormGroup = this.fb.group({
      treeOperator: new FormControl(null),
      values: this.quotationValues
    });
  }

  get quotationValues(): FormArray {
    return this.fb.array([])
  }

  get quotationFormArray(): FormArray {
    return this.inventoryMatchedFormGroup?.get('values') as FormArray;
  }

  private getInventoryPreferenceDetail(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.crmId) {
        const endpoint = API_URL_UTIL.admin.crm.customerLeadsById.replace(':id', this.crmId)
        this.crmService.get<InventoryPreferenceDetails>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
          next: (preferenceDetails) => {
            this.getInventoryMatchedParams(preferenceDetails);
            resolve();
          },
          error: () => {
            reject();
          }
        });
      }
    })
  }

  private getInventoryMatchedList() {
    if (this.inventorySpecificationForm) {
      this.commonService.addWithoutBaseUrl(this.getInventoryMatchedParam(), API_URL_UTIL.admin.crm.customerInventoryMatched).pipe(takeUntil(this.destroy$)).subscribe({
        next: (quotation) => {
          this.quotationDetails = quotation;
          if (this.crmCustomerInfo.matchingCount !== quotation.totalElements) {
            this.updateInventoryMatchedCount(this.crmCustomerInfo.id, quotation.length)
          }
          this.onInventoryMatchedCount.emit(quotation.totalElements);
          this.setAlreadyQuotatedList();
        }
      });
    }
  }

  getInventoryMatchedParam(): InventoryMatchedParams {
    const specificationPrefrence: Array<string> = []
    if (this.inventorySpecificationForm) {
      for (const specification of this.inventorySpecificationForm) {
        for (const field of specification.fields) {
          if (field.value) {
            specificationPrefrence.push(`"${field.label}": ${typeof (field.value) === 'number' ? field.value : "field.value"}`);
          }
        }
      }
    }
    return {
      dealerId: this.customerDetails?.location?.id,
      unitTypeId: this.customerDetails?.unitType?.id,
      makeIds: this.customerDetails?.makes.map((make: Make) => make?.makeId),
      unitModelIds: this.customerDetails?.unitModels.map((model: UnitModel) => model?.unitModelId),
      designationId: this.customerDetails?.designation?.id,
      categoryId: this.customerDetails?.category?.id,
      specifications: specificationPrefrence
    }
  }

  updateInventoryMatchedCount(id: string, count: number) {
    const endpoint = API_URL_UTIL.admin.crm.customerMatchedCount.replace(':customerId', id.toString());
    const fullEndpoint = `${API_URL_UTIL.admin.crm.customer}${endpoint}matchingCount=${count}`;
    this.crmService.patch(Number(null), fullEndpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.inventoryMatchingSuccess);
      this.getAll()
    })
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.crmService.getListWithFiltersWithPagination<CustomerLeadListFilter, CustomerLeadListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.crm.customerFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.crmCustomerLeadList = res.content;
          this.setPaginationParamsFromPageResponse<CustomerLeadListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  private setAlreadyQuotatedList() {
    let quotationUnitResponseDTOList: Array<AssociationsUnits> = [];
    if (this.quotationCustomerList) {
      for (const quotationUnit of this.quotationCustomerList) {
        quotationUnitResponseDTOList = [...quotationUnitResponseDTOList, ...quotationUnit.quotationUnitResponseDTOList];
      }
    }
    for (const quotation of this.quotationDetails) {
      quotation.isExisting = quotationUnitResponseDTOList?.some(
        (existingQuotationUnits: AssociationsUnits) => {
          return existingQuotationUnits.unitId === quotation.id;
        }
      );
    }
    this.isInventoryLoading = true
  }

  private getInventoryMatchedParams(preferenceDetails: InventoryPreferenceDetails) {
    if (preferenceDetails.unitType) {
      this.quotationFormArray.push(this.fb.group({
        dataType: DataType.STRING,
        key: QuotationKey.UNIT_TYPE,
        operator: OperatorType.EQUAL,
        value: preferenceDetails.unitType,
        id: preferenceDetails.id
      }));
    }
    if (preferenceDetails.unitModel) {
      this.quotationFormArray.push(this.fb.group({
        dataType: DataType.LONG,
        key: QuotationKey.UNIT_MODEL,
        operator: OperatorType.EQUAL,
        value: preferenceDetails.unitModel.id
      }));
    }
    if (preferenceDetails.maxYear) {
      this.quotationFormArray.push(this.fb.group({
        dataType: DataType.LONG,
        key: QuotationKey.YEAR,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: preferenceDetails.maxYear
      }));
    }
    if (preferenceDetails.minYear) {
      this.quotationFormArray.push(this.fb.group({
        dataType: DataType.LONG,
        key: QuotationKey.YEAR,
        operator: OperatorType.GREATER_THAN_OR_EQUAL,
        value: preferenceDetails.minYear
      }));
    }
    if (preferenceDetails.make) {
      this.quotationFormArray.push(this.fb.group({
        dataType: DataType.LONG,
        key: QuotationKey.MAKE,
        operator: OperatorType.EQUAL,
        value: preferenceDetails.make.id
      }));
    }
    if (preferenceDetails.designation) {
      this.quotationFormArray.push(this.fb.group({
        dataType: DataType.LONG,
        key: QuotationKey.DESIGNATION,
        operator: OperatorType.EQUAL,
        value: preferenceDetails.designation.id
      }));
    }
    this.quotationFormArray.push(this.fb.group({
      dataType: DataType.STRING,
      key: QuotationKey.UNIT_STATUS_NAME,
      operator: OperatorType.EQUAL,
      value: UnitStaus.AVAILABLE
    }))

    if (this.quotationFormArray.length > 1) {
      this.inventoryMatchedFormGroup.get('treeOperator')?.patchValue('AND');
    }
    else {
      this.inventoryMatchedFormGroup.get('treeOperator')?.patchValue('NOOP');
    }
  }

  showInventoryDetails(quotationDetail: QuotationResponse): void {
    this.showCreateModal = true;
    this.selectedQuotation = quotationDetail
  }

  onCloseModal(): void {
    this.showCreateModal = false;
    this.selectedQuotation = {} as QuotationResponse;
  }

  onSaveQuotation() {
    this.onQuotationAccepted.emit(true);
  }
}
