<div class="modal-title">
  <h4>Edit configurations for {{ ageConfig?.days }} days email</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="closePopup()"></fa-icon>
</div>
<form [formGroup]="emailConfigForm" (ngSubmit)="onSubmit()">
  <section class="content">
    <div class="card p-3">
      <div class="row">
        <div class="col-12">
          <label class="required">Subject</label>
          <input class="form-control" type="text" placeholder="Enter subject" formControlName="subject" />
          <app-error-messages [control]="emailConfigForm.controls.subject"></app-error-messages>
        </div>
        <div class="col-12">
          <label class="required">Body</label>
          <textarea placeholder="Enter body" rows="3" formControlName="body"></textarea>
          <app-error-messages [control]="emailConfigForm.controls.body"></app-error-messages>
        </div>
        <div class="col-12 mt-0 d-flex align-items-center">
          <p-message severity="info" text="Use {Stock #} to replace it with Item's actual stock number when email is send." class="w-100"></p-message>
          <fa-icon class="p-3" [icon]="faIcons.faCopy" pTooltip="Copy" tooltipPosition="top" (click)="copyToClipboard('{Stock #}')"></fa-icon>
        </div>
        <div class="col-12 d-flex flex-column">
          <label class="required">Send email to</label>
          <p-dropdown appendTo="body" [options]="users" formControlName="toEmpId" optionLabel="name" [showClear]="true" optionValue="id" [filter]="true" filterBy="name" placeholder="Select user">
          </p-dropdown>
          <app-error-messages [control]="emailConfigForm.controls.toEmpId"></app-error-messages>
        </div>
      </div>
    </div>
  </section>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="closePopup()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>
