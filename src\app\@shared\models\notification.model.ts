import { QuotationFilterParams } from "@pages/crm/models/customer-lead-quotation.model";

export interface FirebaseRegistrationParams {
  recipientId: string;
  token: string;
}

export interface Notifications {
  body: string;
  id: string;
  jsonReferenceData: JsonReferenceDataNotification;
  readStatus: string;
  recipientId: string;
  sentDate: string;
  subject: string;
}

export interface UnreadNotificationCount {
  unreadCount: number;
}

export enum NotificationsStatus {
  READ = 'READ',
  UNREAD = 'UNREAD'
}

export interface JsonReferenceDataNotification {
  action: string;
  actionPerformById: number;
  actionPerformByName: string;
  childModuleId: string;
  childModuleName: string;
  parentModuleId: string;
  parentModuleName: string;
}

export interface NotificationParams extends QuotationFilterParams {
  recipientId: number;
  isDeleted: boolean;
  orderBy: Array<OrderBy>;
}

export interface OrderBy {
  ascending: boolean;
  field: string;
}
