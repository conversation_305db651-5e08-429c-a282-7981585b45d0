import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { Role, UserListFilter, UserListItem } from '@pages/administration/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService, PrimeNGConfig } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { TableColumn } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { RoleService } from '../roles.service';
import { UserService } from '../users.service';

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserListComponent extends BaseComponent implements OnInit {
  roles: Role[] = [];
  cols: TableColumn[] = [];
  _selectedColumns: any[] = [];
  users: UserListItem[] = [];
  filterParams: UserListFilter = new UserListFilter();
  showCreateModal = false;

  showHistoryModal = false;
  historyModuleName = HistoryModuleName.USERS;
  showChangePasswordModal = false;
  selectedUser!: UserListItem | null;
  isActiveTab = true;
  isArchiveInProgress = false;

  constructor(
    private readonly roleService: RoleService,
    private readonly userService: UserService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly primengConfig: PrimeNGConfig,
    public readonly authService: AuthService,
    private readonly activeRoute: ActivatedRoute,
  ) {
    super();
    this.pageTitle = 'Users';
    this.primengConfig.ripple = true;
  }

  ngOnInit(): void {
    this.paginationConfig.predicate = 'email';
    this.setTableColumns();
    this.getAllRoles();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getUserById(Number(params.id));
        }
      });
    this.getAll()
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  getUserById(id: number) {
    this.userService.get<UserListItem>(id).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.onEdit(res);
      }
    });
  }

  onTabChanged(tabChangeEvent: any): void {
    if (tabChangeEvent.index === 1) {
      this.isActiveTab = false;
      this.filterParams.archived = true;
    } else {
      this.isActiveTab = true;
      this.filterParams.archived = false;
    }
    this.getAll();
  }

  setTableColumns() {
    this.cols = [
      { field: 'name', sortKey: 'firstName', header: 'Name', sortable: true, reorderable: true },
      { field: 'phoneNumber', header: 'Phone', sortable: true, sortKey: 'phoneNumber', reorderable: true, isATemplate: true },
      { field: 'roleDto.name', sortable: true, sortKey: 'role.name', header: 'Role', reorderable: true, isATemplate: true },
    ];
    this._selectedColumns = this.cols;
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.userService.getListWithFiltersWithPagination<UserListFilter, UserListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.users.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.users = res.content;
          this.setPaginationParamsFromPageResponse<UserListItem>(res);
          this.isLoading = false;
          this.setActiveFlagForAll();
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  private setActiveFlagForAll() {
    this.users.forEach(user => user.status = this.isActiveTab);
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedUser = null;
    if (refreshList) {
      this.getAll();
    }
  }

  onChangePasswordPopupClose(): void {
    this.showChangePasswordModal = false;
    this.selectedUser = null;
  }

  onAdd(): void {
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  onEdit(user: UserListItem): void {
    if (!this.checkRole(user) && user.roleDto && user.roleDto.id) {
      user.roleDto.id = undefined;
    }
    this.showCreateModal = true;
    this.selectedUser = user;
    this.cdf.detectChanges();
  }

  onChangePassword(user: UserListItem): void {
    this.showChangePasswordModal = true;
    this.selectedUser = user;
    this.cdf.detectChanges();
  }

  onArchive(user: UserListItem, isActive: boolean): void {
    this.selectedUser = user;
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: user.status ? MESSAGES.archiveWarning.replace('{record}', 'user') : MESSAGES.unArchiveWarning.replace('{record}', 'user'),
      icon: icons.triangle,
      accept: () => {
        if (!this.checkRole(user)) {
          this.toasterService.error(MESSAGES.userRoleDeleted);
          user.status = !isActive;
          return;
        }
        this.isArchiveInProgress = true;
        this.selectedUser = user;
        this.onArchiveConfirmation(user);
        user.status = isActive;
      },
      reject: () => {
        user.status = !isActive;
        this.cdf.detectChanges();
      }
    });
  }

  checkRole(user: UserListItem): Role | undefined {
    return this.roles.find(role => role.id === user.roleDto?.id);
  }

  private onArchiveConfirmation(dealer: UserListItem): void {
    this.userAnnotationService.add(null, `${API_URL_UTIL.userAnnotation.archive}/${!dealer.archived}/${dealer.id}`).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.isActiveTab ? MESSAGES.userArchiveSuccess : MESSAGES.userUnArchiveSuccess);
          this.isArchiveInProgress = false;
          this.selectedUser = null;
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedUser = null;
          this.getAll();
        }
      });
  }

  exportUsersToExcel(): void {
    this.isExporting = true;
    this.userService.getListWithFiltersWithPagination<UserListFilter, UserListItem>(this.filterParams, 1, this.paginationConfig.totalElements, API_URL_UTIL.admin.users.list).pipe(takeUntil(this.destroy$)).subscribe(users => {
      import("xlsx").then(xlsx => {
        const worksheet = xlsx.utils.json_to_sheet(this.userService.getUsersForExport(users.content));
        const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
        const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
        Utils.saveAsExcelFile(excelBuffer, "users");
        this.isExporting = false;
        this.cdf.detectChanges();
      });
    });
  }

  onBackdropClick(element: HTMLElement): void {
    Utils.handlePopoverBackdropClick(element, this.selectedUser, 'status', this.isActiveTab, this.cdf);
  }

  getAllRoles(): void {
    this.roleService.getList<Role>().pipe(takeUntil(this.destroy$)).subscribe(res => {
      this.roles = res;
      this.cdf.detectChanges();
    });
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
