import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, UntypedFormArray, UntypedFormGroup, Validators } from '@angular/forms';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ContactDetails } from '@pages/administration/models/crm.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { InventoryListItem, ModelType, UnitType } from '@pages/inventory/models';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { ConfirmationService } from 'primeng/api';
import { forkJoin, takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { CommonService } from 'src/app/@shared/services/common.service';

@Component({
  selector: 'app-duplicate-inventory',
  templateUrl: './duplicate-inventory.component.html',
  styleUrls: ['./duplicate-inventory.component.scss']
})
export class DuplicateInventoryComponent extends BaseComponent implements OnInit, OnChanges {

  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() inventoryStatuses!: Array<IdNameModel>;

  vendors: Array<IdNameModel> = [];
  contactList!: Array<ContactDetails>;
  duplicateInventoryFormGroup!: FormGroup;

  loaders = {
    vendors: false,
    previousOwnerName: false
  }

  @Output() closeModal = new EventEmitter<void>();
  @Output() updateInventories = new EventEmitter<void>();

  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly inventoryService: InventoryService,
    private readonly commonService: CommonService,
    private readonly cdf: ChangeDetectorRef,
    private readonly crmService: CrmService,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeDuplicateInventoryFormGroup();
    this.getVendorList();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.inventoryInfo?.currentValue) {
      this.initializeDuplicateInventoryFormGroup();
    }
  }

  initializeDuplicateInventoryFormGroup(): void {
    this.duplicateInventoryFormGroup = this.formBuilder.group({
      specification: new FormControl(false),
      photos: new FormControl(false),
      condition: new FormControl(false),
      notes: new FormControl(false),
      documents: new FormControl(false),
      inventories: this.newInventoryFormArray,
      inventoryStatusId: new FormControl(this.inventoryInfo?.generalInformation?.unitStatus?.id)
    });
  }

  get newInventoryFormArray(): UntypedFormArray {
    return this.formBuilder.array([this.newInventoryFormGroup]);
  }

  get clonedInventoryFormArray(): UntypedFormArray {
    return this.duplicateInventoryFormGroup.get('inventories') as UntypedFormArray;
  }

  get newInventoryFormGroup(): FormGroup {
    return this.formBuilder.group({
      stock: new FormControl('', [Validators.required, Validators.maxLength(50)]),
      vin: new FormControl(null),
      odometer: new FormControl(null),
      hours: new FormControl(null),
      retail: new FormControl(null),
      wholesale: new FormControl(null),
      type: new FormControl(null),
      previousOwnerContactId: new FormControl(null),
      previousOwnerVendorId: new FormControl(null),
      previousOwnerSupplierId: new FormControl(null),
    });
  }

  addNewInventory(): void {
    this.clonedInventoryFormArray.push(this.newInventoryFormGroup);
  }

  getPrimaryInventoryImage(): string {
    return (this.inventoryInfo as InventoryListItem)?.unitImages?.fullUrl ?
      (this.inventoryInfo as InventoryListItem).unitImages?.fullUrl ?? '' :
      this.constants.staticImages.noImages;
  }

  getCurrentClonedInventoryFormGroup(index: number): UntypedFormGroup {
    return this.clonedInventoryFormArray.at(index) as UntypedFormGroup;
  }

  onModalClose() {
    this.closeModal.emit();
    this.clearDuplicationInventoryForm();
  }

  clearDuplicationInventoryForm(): void {
    this.duplicateInventoryFormGroup.reset();
    this.clonedInventoryFormArray.clear();
    this.clonedInventoryFormArray.push(this.newInventoryFormGroup);
  }

  getVendorList(): void {
    this.loaders.vendors = true;
    this.commonService.getList(API_URL_UTIL.vendorsContactsSuppliers).pipe(takeUntil(this.destroy$)).subscribe({
      next: (vendors: any) => {
        if (typeof (vendors[0].id) === 'number') {
          vendors.map((d: any) => {
            d.name = `${d.name} (${d.type})`;
            d.id = `${d.id}-${d.type}`;
            return d
          })
        }
        this.vendors = vendors;
        this.loaders.vendors = false;
      },
      error: () => {
        this.loaders.vendors = false;
        this.cdf.detectChanges();
      }
    });
  }

  onSubmit() {
    if (this.duplicateInventoryFormGroup.invalid) {
      this.duplicateInventoryFormGroup.markAllAsTouched();
      return;
    }
    this.duplicateInventoryFormGroup.markAsUntouched();
    const inventoryToBeCloned = [];
    for (const inventory of this.clonedInventoryFormArray.value) {
      inventoryToBeCloned.push(this.inventoryService.add(
        this.getClonedInventoryParams(inventory, this.duplicateInventoryFormGroup.value),
        API_URL_UTIL.inventory.clone
      ));
    }
    forkJoin([...inventoryToBeCloned]).subscribe({
      next: () => {
        this.onModalClose();
        this.updateInventories.emit();
      }
    });
  }

  getClonedInventoryParams(inventory: any, duplicateInventoryFormValue: any) {
    return {
      specification: duplicateInventoryFormValue.specification,
      photos: duplicateInventoryFormValue.photos,
      condition: duplicateInventoryFormValue.condition,
      notes: duplicateInventoryFormValue.notes,
      documents: duplicateInventoryFormValue.documents,
      retailPrice: inventory.retail,
      wholesalePrice: inventory.wholesale,
      unitId: this.inventoryInfo?.id,
      unitDTO: {
        archived: this.inventoryInfo?.archived,
        deleted: false,
        generalInformation: {
          id: null,
          unitTypeId: (this.inventoryInfo?.generalInformation.unitType as UnitType)?.id,
          year: this.inventoryInfo?.generalInformation.year,
          vin: inventory.vin,
          stockNumber: inventory.stock,
          designationId: this.inventoryInfo?.generalInformation?.designation.id,
          unitStatusId: duplicateInventoryFormValue.inventoryStatusId,
          categoryId: this.inventoryInfo?.generalInformation?.unitTypeCategory.id,
          makeId: this.inventoryInfo?.generalInformation?.make?.id,
          unitModelId: this.inventoryInfo?.generalInformation?.unitModel.id,
          ownerId: this.inventoryInfo?.generalInformation?.owner.id,
          unitTypeCategoryId: this.inventoryInfo?.generalInformation?.unitTypeCategory?.id
        },
        internetOption: {
        },
        previousOwner: {
          previousOwnerName: null,
          type: inventory.type,
          previousOwnerContactId: inventory.previousOwnerContactId,
          previousOwnerVendorId: inventory.previousOwnerVendorId,
          previousOwnerSupplierId: inventory.previousOwnerSupplierId,
        },
        internetGroups: null,
        unitLotLocation: {
          receivingLocationId: this.inventoryInfo?.generalInformation?.owner.id,
          currentLocationId: this.inventoryInfo?.generalInformation?.owner.id,
        },
        odometer: {
          odometerReading: inventory.odometer,
          hours: inventory.hours,
          unitOfDistance: {
            id: 1
          },
        },
        unitAssociation: {
          name: `${inventory.stock}-Association`
        }
      }
    }
  }

  displayContactDetails(event: any, rowIndex: number): void {
    const selectedText = event.originalEvent.srcElement.innerText;
    const id = event.value && event.value.split('-')[0];
    const type = id && selectedText.split('(')[1].split(')')[0];
    const resetPreviousOwner = () => {
      this.clonedInventoryFormArray.value[rowIndex].type = null;
      this.clonedInventoryFormArray.value[rowIndex].previousOwnerContactId = null;
      this.clonedInventoryFormArray.value[rowIndex].previousOwnerVendorId = null;
      this.clonedInventoryFormArray.value[rowIndex].previousOwnerSupplierId = null;
    }
    resetPreviousOwner();
    this.clonedInventoryFormArray.value[rowIndex].type = type;
    switch (type) {
      case ModelType.CONTACT.toUpperCase():
        this.clonedInventoryFormArray.value[rowIndex].previousOwnerContactId = id;
        break;
      case ModelType.VENDOR.toUpperCase():
        this.clonedInventoryFormArray.value[rowIndex].previousOwnerVendorId = id;
        break;
      case ModelType.SUPPLIER.toUpperCase():
        this.clonedInventoryFormArray.value[rowIndex].previousOwnerSupplierId = id;
        break;
      default:
        resetPreviousOwner();
        break;
    }
    this.cdf.detectChanges();
  }

  getContactInfo(): void {
    this.loaders.previousOwnerName = true;
    this.crmService.getList<ContactDetails>(API_URL_UTIL.admin.crm.contact).pipe(takeUntil(this.destroy$)).subscribe({
      next: (contactList) => {
        this.contactList = contactList;
        this.loaders.previousOwnerName = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.previousOwnerName = false;
        this.cdf.detectChanges();
      }
    });
  }

  onDelete(rowIndex: number, event: Event): void {
    if (rowIndex || this.clonedInventoryFormArray.value.length !== 1) {
      this.confirmationService.confirm({
        target: event?.target as EventTarget,
        message: MESSAGES.deleteWarning.replace('{record}', 'unit data'),
        icon: icons.triangle,
        header: "Confirmation",
        accept: () => {
          this.clonedInventoryFormArray.removeAt(rowIndex);
        }
      });
      return;
    }
    this.toasterService.error('Atleast one unit is required');
  }

}
