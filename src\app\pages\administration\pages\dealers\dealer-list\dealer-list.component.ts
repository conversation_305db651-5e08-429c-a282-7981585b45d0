import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { DealerListFilter, DealerListItem } from '@pages/administration/models';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { TabDirective } from 'ngx-bootstrap/tabs';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { DataType, TableColumn } from 'src/app/@shared/models';
import { PatchParam } from 'src/app/@shared/models/patch-param.model';
import { Utils } from 'src/app/@shared/services';
import { DealerService } from '../dealers.service';

@Component({
  selector: 'app-dealer-list',
  templateUrl: './dealer-list.component.html',
  styleUrls: ['./dealer-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DealerListComponent extends BaseComponent implements OnInit {

  cols: TableColumn[] = [];
  _selectedColumns: any[] = [];
  dealers: DealerListItem[] = [];
  filterParams: DealerListFilter = new DealerListFilter();
  showCreateModal = false;
  showHistoryModal = false;
  showEmailFieldModal = false;
  historyModuleName = HistoryModuleName.DEALERS;
  selectedDealer!: DealerListItem | null;
  isActiveTab = true;
  isArchiveInProgress = false;

  constructor(private readonly dealerService: DealerService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly activeRoute: ActivatedRoute) {
    super();
    this.pageTitle = 'Dealers';
    this.paginationConfig.predicate = 'name';
  }

  ngOnInit(): void {
    this.setTableColumns();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getDealerById(Number(params.id));
        }
      });
  }

  getDealerById(id: number) {
    this.dealerService.get<DealerListItem>(id).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.onEdit(res);
      }
    });
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  onTabChanged(tabChangeEvent: TabDirective): void {
    if (tabChangeEvent.heading === 'Archived') {
      this.isActiveTab = false;
      this.filterParams.archived = true;
    } else {
      this.isActiveTab = true;
      this.filterParams.archived = false;
    }
    this.getAll();
  }

  private setTableColumns() {
    this.cols = [
      { field: 'name', sortKey: 'name', header: 'Name', sortable: true, reorderable: true },
      { field: 'email', header: 'Email', sortable: true, reorderable: true },
      { field: 'contactPersonName', header: 'Contact Person', sortable: true, sortKey: 'contactPerson.firstName', reorderable: true },
      { field: 'shopNames', header: 'Shops', reorderable: true },
    ];
    this._selectedColumns = this.cols;
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.dealerService.getListWithFiltersWithPagination<DealerListFilter, DealerListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.dealers.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.dealers = res.content;
          this.setActiveFlagForAllDealers();
          this.setPaginationParamsFromPageResponse<DealerListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  private setActiveFlagForAllDealers() {
    this.dealers.forEach(dealer => dealer.isActive = this.isActiveTab);
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedDealer = null;
    if (refreshList) {
      this.getAll();
    }
  }

  onAdd(): void {
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  onEdit(dealer: DealerListItem): void {
    this.showCreateModal = true;
    this.selectedDealer = dealer;
    this.cdf.detectChanges();
  }

  onArchive(dealer: DealerListItem, isActive: boolean): void {
    this.selectedDealer = dealer;
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      closeOnEscape: true,
      blockScroll: true,
      message: dealer.isActive ? MESSAGES.archiveWarning.replace('{record}', 'dealer') : MESSAGES.unArchiveWarning.replace('{record}', 'dealer'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.isArchiveInProgress = true;
        this.onArchiveConfirmation(dealer);
        dealer.isActive = isActive;
      },
      reject: () => {
        dealer.isActive = !isActive;
        this.cdf.detectChanges();
      }
    });
  }

  private getPatchRequestParams(dealer: DealerListItem): PatchParam {
    return {
      id: dealer.id,
      values: [{
        key: 'archived',
        value: `${this.isActiveTab}`,
        dataType: DataType.BOOLEAN
      }]
    }
  }

  private onArchiveConfirmation(dealer: DealerListItem): void {
    this.dealerService.patch(this.getPatchRequestParams(dealer))
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.isActiveTab ? MESSAGES.dealerArchiveSuccess : MESSAGES.dealerUnArchiveSuccess);
          this.isArchiveInProgress = false;
          this.selectedDealer = null;
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedDealer = null;
          this.getAll();
        }
      });
  }

  onBackdropClick(element: HTMLElement): void {
    Utils.handlePopoverBackdropClick(element, this.selectedDealer, 'isActive', this.isActiveTab, this.cdf);
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }

  toggleEmailFieldSidebar() {
    this.showEmailFieldModal = !this.showEmailFieldModal;
  }
}
