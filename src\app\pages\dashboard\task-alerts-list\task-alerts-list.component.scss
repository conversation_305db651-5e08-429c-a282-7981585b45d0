@import 'src/assets/scss/variables';

.space-between {
  justify-content: space-between;
}

.timeline {
  text-align: center;

  .footer {
    background-color: var(--listing-timeline-bg-color);
    width: fit-content;
    padding: 2px 20px;
    border-radius: 12px;

    .pi {
      margin-right: 5px;
    }

    span {
      font-size: 13px;
      letter-spacing: 0;
      line-height: 21px;
      color: var(--active-color);
      display: flex;
      align-items: center;
    }
  }
}

.content {
  max-height: unset;
}
