<div class="modal-title close">
  <h4 class="header-title">{{ pageTitle }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
</div>

<form [formGroup]="unitTypeEditFormGroup" (ngSubmit)="onUpdateUnitType()" *ngIf="selectedUnitType?.id">
  <div class="category-form">
    <div class="form-group">
      <label for="unitTypeCategoryId" class="required">Category</label>
      <p-dropdown [options]="categories" placeholder="Select Category" formControlName="unitTypeCategoryId" optionLabel="name" optionValue="id"> </p-dropdown>
    </div>
    <div class="form-group">
      <label for="name" class="required">Name</label>
      <input formControlName="name" type="text" class="form-control" placeholder="Enter unit type" />
      <app-error-messages [control]="unitTypeEditFormGroup?.controls?.name"></app-error-messages>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>

<form [formGroup]="unitTypeAddFormGroup" (ngSubmit)="onAddUnitType()" *ngIf="!selectedUnitType?.id">
  <div class="category-form">
    <div class="form-group">
      <label for="unitTypeCategoryId" class="required">Category</label>
      <p-dropdown [options]="categories" placeholder="Select Category" formControlName="unitTypeCategoryId" optionLabel="name" optionValue="id"> </p-dropdown>
    </div>
    <div class="form-group">
      <ng-container formArrayName="unitTypes">
        <label class="mb-2 required">Name</label>
        <ng-container *ngFor="let model of unitTypeFormArray.controls; let i = index">
          <div class="row align-items-center model-group" [formGroupName]="i">
            <div class="col-8">
              <input class="form-control" type="text" placeholder="Enter Name" formControlName="name" />
            </div>

            <div class="col-4 px-0">
              <div class="add-model">
                <img
                  *ngIf="i || unitTypeFormArray.value.length !== 1"
                  [src]="constants.staticImages.icons.deleteIcon"
                  alt=""
                  class="delete-shop mt-0"
                  (click)="onDeleteUnitType(i)"
                />
                <button
                  class="btn btn-primary"
                  id="addShopBtn"
                  type="button"
                  [appImageIconSrc]="constants.staticImages.icons.addNew"
                  (click)="onAddNewUnitType()"
                  *ngIf="isAddNewCategoryVisible(i)"
                >
                  <span class="hide-label">Add Unit Type</span>
                </button>
              </div>
            </div>
            <small class="text-danger error-msg mt-1" *ngIf="unitTypeFormArray.controls[i].get('name')?.touched || unitTypeFormArray.controls[i].get('name')?.dirty">
              <span *ngIf="unitTypeFormArray.controls[i].get('name')?.hasError('required')"> This field is required </span>
              <span *ngIf="unitTypeFormArray.controls[i].get('name')?.errors?.maxlength"> Maximum 50 characters are allowed </span>
            </small>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>
