<div class="card">
  <div class="tab-content column-list">
    <p-tabView [scrollable]="true" styleClass="dynamic-tabs">
      <p-tabPanel header="Columns">
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="columnDropDown"></ng-container>
        </ng-template>
      </p-tabPanel>
    </p-tabView>
  </div>
</div>

<ng-template #columnDropDown>
  <div class="model-body">
    <form (ngSubmit)="onSubmit()">
      <div class="content example" [ngClass]="{'pt-0': isInventoryModule}">
        <p-messages *ngIf="isInventoryModule" [(value)]="messages" [enableService]="false" [closable]="false" />
        <input type="text" pInputText name="search" [(ngModel)]="searchInput" placeholder="Search" class="form-control mb-3" />
        <div cdkDropList class="example-list" (cdkDropListDropped)="drop($event)">
          <ng-container *ngIf="columnsList | searchFilter: searchInput as result">
            <ng-container *ngFor="let column of result; trackBy: trackByColumnKey">
              <div class="example-box" cdkDrag [cdkDragDisabled]="column.disabled" [ngStyle]="disableColumnStyle(column)">
                <p-checkbox [name]="column" [(ngModel)]="selectedColumns" [value]="column" [label]="column.name"> </p-checkbox>
              </div>
            </ng-container>
            <p class="no-data" *ngIf="!result.length">No results found</p>
          </ng-container>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="onCancel()">Cancel</button>
        <button type="submit" class="btn btn-primary save-btn" appShowLoaderOnApiCall>Submit</button>
      </div>
    </form>
  </div>
</ng-template>
