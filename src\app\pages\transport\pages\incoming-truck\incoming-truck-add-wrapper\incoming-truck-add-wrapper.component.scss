.title {
  color: var(--light-black-color);
  font-weight: 500 !important;
  font-size: 16px !important;
}

.m-l-10 {
  margin-left: 10px;
}
.header-title {
  top: -8px;
  position: relative;
}
.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
  position: absolute;
  top: 30px;
}
.bold-text {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

.content {
  overflow-x: hidden;
}
