.header-content {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

@media only screen and (max-width: 500px) {
  .header-content {
    display: block;
  }
}

.filter-wrapper {
  display: flex;
  align-items: center;

  span {
    width: 100px;
  }
}

.action-btn {
  margin-right: 5px;
}

.view-reminder {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
  z-index: 1;
}
