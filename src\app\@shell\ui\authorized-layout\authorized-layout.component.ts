import { ChangeDetectionStrategy, Component, HostListener, OnInit } from '@angular/core';

@Component({
  selector: 'app-authorized-layout',
  templateUrl: './authorized-layout.component.html',
  styleUrls: ['./authorized-layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuthorizedLayoutComponent implements OnInit {
  mobile = false;

  getIsMobile(): boolean {
    const w = document.documentElement.clientWidth;
    const breakpoint = 1381;
    return w > breakpoint;
  }

  ngOnInit(): void {
    this.mobile = this.getIsMobile();
  }

  @HostListener('window:resize', ['$event']) onResize() {
    this.mobile = this.getIsMobile();
  }
}
