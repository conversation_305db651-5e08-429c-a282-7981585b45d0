import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { EventType, TaskEvent, TaskHistoryListItem, TaskListItem } from '@pages/shops/models';
import { TaskHistoryService } from '@pages/shops/services/task-history.service';
import { takeUntil } from 'rxjs';
@Component({
  selector: 'app-task-audit',
  templateUrl: './task-audit.component.html',
  styleUrls: ['./task-audit.component.scss']
})
export class TaskAuditComponent extends BaseComponent implements OnChanges {

  @Input() taskInfo!: TaskListItem | null;
  isEditMode = false;
  taskHistory: TaskHistoryListItem[] = [];
  EventType = EventType;
  taskEvent = TaskEvent;
  constructor(private readonly taskHistoryService: TaskHistoryService) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.taskInfo.currentValue) {
      this.getTaskHistory();
    }
  }

  getTaskHistory(): void {
    if (this.taskInfo?.id) {
      this.isLoading = true;
      const url = `${API_URL_UTIL.taskHistory.list.replace(':taskId', this.taskInfo?.id.toString() || '')}?`;
      this.taskHistoryService.getListWithPagination<TaskHistoryListItem>(this.paginationConfig.page, this.paginationConfig.itemsPerPage, url).pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          this.isLoading = false;
          for (const response of res.content) {
            this.taskHistory.push(this.taskHistoryService.fromServerModel(response));
          }
          this.taskHistory.sort(this.sortByDate);
        },
        error: (err) => {
          this.isLoading = false;
        }
      });
    }
  }

  sortByDate(object1: TaskHistoryListItem, object2: TaskHistoryListItem): any {
    return new Date(object2.eventDate).getTime() - new Date(object1.eventDate).getTime();
  }

  getCommentAdded(content: TaskHistoryListItem): string {
    return JSON.parse(content.newData)?.Comment
  }
}
