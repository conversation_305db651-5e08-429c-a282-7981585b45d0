
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { TypeaheadModule } from 'ngx-bootstrap/typeahead';
import { SharedComponentsModule } from 'src/app/@shared/components/shared-components.module';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { SharedLibsModule } from 'src/app/@shared/shared-libs.module';
import { AuthRoutingModule } from './auth-routing.module';
import { EmailPasswordLoginComponent } from './pages/login/email-password-login/email-password-login.component';
import { OtpLoginComponent } from './pages/login/otp-login/otp-login.component';

@NgModule({
  declarations: [
    OtpLoginComponent,
    EmailPasswordLoginComponent
  ],
imports: [
    CommonModule,
    AuthRoutingModule,
    SharedLibsModule,
    TypeaheadModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    SharedComponentsModule
  ],
})
export class AuthModule { }
