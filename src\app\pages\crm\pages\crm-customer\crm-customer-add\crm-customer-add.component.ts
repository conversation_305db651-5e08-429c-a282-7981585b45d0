import { TitleCasePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ContactDetails } from '@pages/administration/models/crm.model';
import { MakeModelService } from '@pages/administration/pages/specification-configration/pages/make-model/make-model.service';
import { UnitTypeService } from '@pages/administration/pages/specification-configration/pages/unit-type/unit-type.service';
import { CustomerCreateParams, CustomerLeadListItem, CustomerLeadMakes, CustomerLeadUnitModels, CustomerStatusList } from '@pages/crm/models/customer-lead.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { InventorySpecification, InventorySpecificationFields, InventorySpecificationResponse, ModelType } from '@pages/inventory/models';
import { InventorySpecificationService } from '@pages/inventory/services';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { FilterMakes, FilterModels } from '@pages/public-inventories/models';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { CommonService } from 'src/app/@shared/services/common.service';

@Component({
  selector: 'app-crm-customer-add',
  templateUrl: './crm-customer-add.component.html',
  styleUrls: ['./crm-customer-add.component.scss']
})
export class CrmCustomerAddComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Customer Lead';
  @Input() crmCustomerInfo!: CustomerLeadListItem | null;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() isViewMode!: boolean | null;
  @Input() crmId!: string;
  accordionTabs = {
    contactInfo: true,
    inventoryPreferenceInfo: true,
    salePersonInfo: true,
    specificationPreferenceInfo: true
  };

  loaders = {
    makes: false,
    models: false,
    categories: false,
    years: false,
    status: false,
    unitTypes: false,
    designations: false,
    vendors: false,
    previousOwnerName: false,
    lotLocation: false,
    currentLocation: false,
    internetGroup: false,
    ownedBy: false,
    categoryType: false
  };
  customerLeadFormGroup!: FormGroup;
  contactList: ContactDetails[] = [];
  displayContact!: ContactDetails | undefined;
  makes: IdNameModel[] = [];
  models: IdNameModel[] = [];
  years: IdNameModel[] = [];
  unitTypes: IdNameModel[] = [];
  designations: IdNameModel[] = [];
  categoryTypes: IdNameModel[] = [];
  modelPopups = {
    showCreateVendor: false,
    showCreateContact: false,
    showCreateModel: false,
    showCreateMake: false,
    showCreateUnitType: false,
    showPipelineDetails: false,
  };
  customerStatusList = CustomerStatusList;
  isEditMode = false;
  hasDataBeenModified = false;
  customerDetails!: any;
  showGoogleMapSideBar = false;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  fullAddress!: string;
  matchedCount!: number;
  categoryId!: number;
  inventorySpecificationForm!: Array<InventorySpecification>;
  isSpecificationLoading = false;
  redirectUrl!: string;
  selectedCustomerId!: number | undefined;
  makeIds!: Array<number>;
  modelIds!: Array<number>;

  constructor(
    private readonly fb: FormBuilder,
    private readonly commonSharedService: CommonSharedService,
    private readonly crmService: CrmService,
    private readonly toasterService: AppToasterService,
    private readonly commonService: CommonService,
    private readonly inventoryService: InventoryService,
    private readonly unitTypeService: UnitTypeService,
    private readonly cdf: ChangeDetectorRef,
    private readonly inventorySpecificationService: InventorySpecificationService,
    private readonly titleCasePipe: TitleCasePipe,
    private readonly activeRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly makeModelService: MakeModelService,
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    this.commonSharedService.setBlockUI$(true);
    this.initializeFormGroup();
    await this.getIdByCustomer();

    this.getAll();
    this.commonSharedService.setBlockUI$(false);
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.returnUrl) {
          this.redirectUrl = params?.returnUrl;
        }
      });
  }

  private getAll() {
    this.getContactInfo();
    this.getDesignationList();
    this.getCategoryTypes();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.crmCustomerInfo.currentValue) {
      if (this.isViewMode) {
        this.title = 'View Customer Lead';
      } else {
        this.title = 'Edit Customer Lead';
        this.isEditMode = true;
      }
      this.accordionTabs.contactInfo = this.accordionTabs.inventoryPreferenceInfo = this.accordionTabs.salePersonInfo = this.accordionTabs.specificationPreferenceInfo = false
    }
    else {
      this.isViewMode = false;
    }
  }

  getIdByCustomer(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.crmCustomerInfo) {
        this.isLoading = true;
        const endpoint = API_URL_UTIL.admin.crm.customerLead;
        this.crmService.get(this.crmCustomerInfo.id, endpoint).pipe(takeUntil(this.destroy$)).subscribe({
          next: (crmContactDetails: any) => {
            this.customerDetails = crmContactDetails;
            this.matchedCount = crmContactDetails?.matchingCount;
            this.setCrmContactInfoInFormGroup();
            this.getInventorySpecificationsForm(crmContactDetails.category.id);
            this.isLoading = false;
            this.cdf.detectChanges();
            resolve();

          },
          error: () => {
            this.isLoading = false;
            this.commonSharedService.setBlockUI$(false);
            this.cdf.detectChanges();
            reject();
          }
        });
      }
      else {
        resolve();
      }
    })
  }

  getInventorySpecificationsForm(categoryId: number): void {
    if (categoryId) {
      this.isSpecificationLoading = true;
      this.inventorySpecificationService.get<InventorySpecificationResponse>(
        categoryId,
        `${API_URL_UTIL.specificationMasters.latest}`).pipe(takeUntil(this.destroy$)).subscribe(
          {
            next: (res: InventorySpecificationResponse) => {
              for (const specificationGroup of res?.masterData?.specification) {
                specificationGroup.fields = specificationGroup.fields.filter(field => field.includePreference)
              }
              this.inventorySpecificationForm = res?.masterData?.specification;

              if (!this.customerDetails) {
                this.initializeInventorySpecificationForm();
              } else {
                this.setInventorySpecificationsFormData();
              }

            }
          });
    } else {
      this.inventorySpecificationForm = [];
    }
  }

  private initializeInventorySpecificationForm(): void {
    for (const inventorySpecification of this.inventorySpecificationForm) {
      for (const field of inventorySpecification.fields) {
        field.value = field.value || null;
      }
    }
    this.isSpecificationLoading = false;
  }


  setInventorySpecificationsFormData(): void {
    const specificationPreviousDetails = this.customerDetails.specificationData.specification;
    if (!specificationPreviousDetails) {
      return;
    }
    for (const inventorySpecification of this.inventorySpecificationForm) {
      const specificationGroupPreviousValue = this.findPreviousValue(inventorySpecification, specificationPreviousDetails);
      if (!specificationGroupPreviousValue) {
        continue;
      }
      this.updateFields(inventorySpecification, specificationGroupPreviousValue);
      if (specificationGroupPreviousValue.duplicateGeneratedSectionsCount) {
        inventorySpecification.duplicateGeneratedSectionsCount = specificationGroupPreviousValue.duplicateGeneratedSectionsCount;
      }
    }
    this.isSpecificationLoading = false;
  }

  private findPreviousValue(inventorySpecification: InventorySpecification, specificationPreviousDetails: any[]) {
    return specificationPreviousDetails.find((specification: InventorySpecification) =>
      specification.sectionName === inventorySpecification.sectionName
    );
  }

  private updateFields(inventorySpecification: InventorySpecification, specificationGroupPreviousValue: any): void {
    for (const inventorySpecificationField of inventorySpecification.fields) {
      const specificationFieldsOfSelectedGroup = specificationGroupPreviousValue.fields?.find((field: any) => {
        return field?.label === inventorySpecificationField?.label && field.dataType === inventorySpecificationField.dataType
      }
      );
      if (specificationFieldsOfSelectedGroup) {
        this.updateFieldValue(inventorySpecificationField, specificationFieldsOfSelectedGroup);
      }
    }
  }

  private updateFieldValue(inventorySpecificationField: InventorySpecificationFields, specificationFieldsOfSelectedGroup: any): void {
    if (inventorySpecificationField.dataType === 'DropDown') {
      const selectedNewOption = specificationFieldsOfSelectedGroup.options.find((option: any) =>
        option.id === Number(inventorySpecificationField.value)
      );
      const selectedPreviousOption = inventorySpecificationField.options.find((option) =>
        option.id === Number(inventorySpecificationField.value)
      );
      if (selectedNewOption?.name !== selectedPreviousOption?.name) {
        inventorySpecificationField.value = null;
      } else {
        inventorySpecificationField.value = specificationFieldsOfSelectedGroup.value;
      }
    } else {
      inventorySpecificationField.value = specificationFieldsOfSelectedGroup.value;
    }
  }


  initializeFormGroup(): void {
    this.customerLeadFormGroup = this.fb.group({
      crmContactId: new FormControl(null, [Validators.required]),
      unitTypeId: new FormControl(null, [Validators.required]),
      maxYear: new FormControl(null),
      minYear: new FormControl(null),
      makeId: new FormControl(null),
      unitModelId: new FormControl(null),
      designationId: new FormControl(null),
      status: new FormControl('OPEN', [Validators.required]),
      notes: new FormControl(null),
      leadDescription: new FormControl(null),
      categoryId: new FormControl(null, Validators.required)
    });
  }

  private getContactInfo(): void {
    this.loaders.previousOwnerName = true;
    this.crmService.getList<ContactDetails>(API_URL_UTIL.admin.crm.contact).pipe(takeUntil(this.destroy$)).subscribe({
      next: (contactList) => {
        this.contactList = contactList;
        if (this.selectedCustomerId) {
          this.customerLeadFormGroup.get('crmContactId')?.setValue(this.selectedCustomerId)
          this.displayContactDetails(this.selectedCustomerId);
          this.selectedCustomerId = undefined;
        }
        if (this.customerDetails?.crmContact?.id) {
          this.displayContactDetails(this.customerDetails.crmContact.id);
        }
        this.loaders.previousOwnerName = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.previousOwnerName = false;
        this.commonSharedService.setBlockUI$(false);
        this.cdf.detectChanges();
      }
    });
  }

  isOptionDisabled(option: { archived: boolean; id: number }): boolean {
    const selectedValue = this.customerLeadFormGroup.get('crmContactId')?.value as number;
    return option.archived && option.id !== selectedValue;
  }

  displayContactDetails(id: number): void {
    this.displayContact = this.contactList.find(contact => contact.id === id);
    this.cdf.detectChanges(); 
  }

  private getUnitTypes(categoryId: number | string) {
    if (categoryId) {
      this.loaders.unitTypes = true;
      const endpoint = `${API_URL_UTIL.inventory.unitTypesList}?categoryId=${categoryId}`;
      this.inventoryService.getList<IdNameModel>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
        next: (unitTypes) => {
          this.unitTypes = unitTypes;
          this.loaders.unitTypes = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.loaders.unitTypes = false;
          this.cdf.detectChanges();
        }
      });
    } else {
      this.unitTypes = [];
    }
  }

  getMake(categoryId: number | string): void {
    this.loaders.makes = true;
    this.makeModelService.get<Array<FilterMakes>>(`${API_URL_UTIL.admin.makeSpecification.category}/${categoryId ?? ''}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<FilterMakes>) => {
          this.makes = res;
          this.loaders.makes = false;
          this.cdf.detectChanges();
        }
      });
  }

  getModels(makeIds: Array<number>): void {
    this.models = [];
    if (makeIds?.length) {
      this.loaders.models = true;
      this.unitTypeService.add<Array<FilterModels>>(makeIds as any, `${API_URL_UTIL.inventory.models}/${API_URL_UTIL.admin.users.make}`)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res: Array<FilterModels>) => {
            this.models = res;
            this.unselectRemovedMakeModels();
            this.loaders.models = false;
            this.cdf.detectChanges();
          }
        });
    } else {
      this.modelIds = [];
    }
  }

  unselectRemovedMakeModels(): void {
    this.modelIds = this.modelIds.filter(id => this.models.some(model => model.id === id));
  }

  private getDesignationList(): void {
    this.loaders.designations = true;
    this.commonService.getList(API_URL_UTIL.designations.root).pipe(takeUntil(this.destroy$)).subscribe({
      next: (designations: IdNameModel[]) => {
        this.designations = designations;
        this.loaders.designations = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.designations = false;
        this.commonSharedService.setBlockUI$(false);
        this.cdf.detectChanges();
      }
    });
  }
  openModel(modelType: string): void {
    switch (modelType) {
      case ModelType.MAKE:
        const isCategoryIdPresentForMake = !!this.customerLeadFormGroup.controls.categoryId?.value;
        if (isCategoryIdPresentForMake) {
          this.modelPopups.showCreateMake = true;
        } else {
          this.toasterService.warning(MESSAGES.addCategoryBeforeAddingMake);
        }
        break;
      case ModelType.UNIT_TYPE:
        const isCategoryIdPresentForUnitType = !!this.customerLeadFormGroup.controls.categoryId?.value;
        if (isCategoryIdPresentForUnitType) {
          this.modelPopups.showCreateUnitType = true;
        } else {
          this.toasterService.warning(MESSAGES.addCategoryBeforeAddingUnitType);
        }
        break;
      case ModelType.MODEL:
        if (this.categoryId) {
          this.modelPopups.showCreateModel = true;
        } else {
          this.toasterService.warning(MESSAGES.addCategoryBeforeAddingModel);
        }
        break;
      case ModelType.VENDOR:
        this.modelPopups.showCreateVendor = true;
        break;
      case ModelType.CONTACT:
        this.modelPopups.showCreateContact = true;
        break;
      default:
        break;
    }
  }

  onAddEditPopupClose(modelType: string): void {
    this.modelPopups.showCreateMake = false;
    this.modelPopups.showCreateModel = false;
    this.modelPopups.showCreateVendor = false;
    this.modelPopups.showCreateContact = false;
    this.modelPopups.showPipelineDetails = false;
    this.modelPopups.showCreateUnitType = false;
    switch (modelType) {
      case ModelType.CONTACT:
        this.getContactInfo();
        break;
      case ModelType.UNIT_TYPE:
        this.getUnitTypes(this.categoryId);
        break;
      case ModelType.MAKE:
        this.getMake(this.categoryId);
        break;
      case ModelType.MODEL:
        this.getModels(this.makeIds);
        break;
      default:
        break;
    }
  }

  selectContactPerson(customerId: number) {
    this.selectedCustomerId = customerId;
  }

  onSubmit(close = true): void {
    if (this.isViewMode) {
      this.customerLeadFormGroup.enable();
      this.isViewMode = false;
      this.isEditMode = true;
      this.title = 'Edit Customer Lead';
      return;
    }
    this.customerLeadFormGroup.updateValueAndValidity();
    if (this.customerLeadFormGroup.invalid) {
      this.customerLeadFormGroup.markAllAsTouched();
      return;
    }

    if (this.isEditMode) {
      this.editCustomerLeadData();
    }
    else {
      this.saveCustomerLeadData(close);
    }
  }

  setMakesParam(): CustomerLeadMakes[] {
    let makes: CustomerLeadMakes[] = this.crmCustomerInfo?.makes ?? [];
    if (makes.length) {
      makes = makes.filter((make: CustomerLeadMakes) => {
        const existingMake = this.makeIds.includes(make.makeId);
        if (existingMake) {
          this.makeIds = this.makeIds.filter(id => id !== make.makeId)
        }
        return existingMake;
      })
    }
    const newMakes: CustomerLeadMakes[] = this.makeIds.map((id: number) => {
      return {
        makeId: id,
        customerLeadId: this.crmCustomerInfo?.id ?? null
      };
    });
    return [...makes, ...newMakes];
  }

  setModelsParam(): CustomerLeadUnitModels[] {
    let models: CustomerLeadUnitModels[] = this.crmCustomerInfo?.unitModels ?? [];
    models = models.filter((model: CustomerLeadUnitModels) => {
      const existingModel = this.modelIds.includes(model.unitModelId);
      if (existingModel) {
        this.modelIds = this.modelIds.filter(id => id !== model.unitModelId)
      }
      return existingModel;
    })
    const newModels: CustomerLeadUnitModels[] = this.modelIds.map((id: number) => {
      return {
        unitModelId: id,
        customerLeadId: this.crmCustomerInfo?.id ?? null
      };
    });
    return [...models, ...newModels];
  }

  get customerInfoCreateParams(): CustomerCreateParams {
    delete this.customerLeadFormGroup.value.makeId;
    delete this.customerLeadFormGroup.value.unitModelId;
    for (const [index, specification] of this.inventorySpecificationForm.entries()) {
      specification.id = index + 1;
      specification.order = index + 1;
      for (const field of specification.fields) {
        if (field.dataType === 'TextField' || field.dataType === 'TextBox') {
          field.value = this.titleCasePipe.transform(field.value)
        }
      }
    }
    const makes: CustomerLeadMakes[] = this.setMakesParam();
    const unitModels: CustomerLeadUnitModels[] = this.setModelsParam();
    return {
      ...this.customerLeadFormGroup.value,
      unitModels,
      makes,
      id: this.crmCustomerInfo?.id,
      matchingCount: this.matchedCount,
      maxYear: this.customerLeadFormGroup.get('maxYear')?.value ? (new Date(this.customerLeadFormGroup.get('maxYear')?.value))?.getFullYear() : null,
      minYear: this.customerLeadFormGroup.get('minYear')?.value ? (new Date(this.customerLeadFormGroup.get('minYear')?.value))?.getFullYear() : null,
      specificationData: { specification: this.inventorySpecificationForm }
    };
  }

  saveCustomerLeadData(close = true): void {
    this.crmService.add(this.customerInfoCreateParams, API_URL_UTIL.admin.crm.customerLead).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.customerLeadAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
        this.goBack();
      } else {
        this.customerLeadFormGroup.reset({
          status: 'OPEN'
        });        
        this.displayContact = undefined;
      }
    });
  }

  editCustomerLeadData(): void {
    this.crmService.update(this.customerInfoCreateParams, API_URL_UTIL.admin.crm.customerLead).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.customerLeadEditSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
      this.goBack();
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);

  }
  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.goBack();
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  setIds(type: string, key: string): Array<number> {
    return this.customerDetails[type].map((type: any) => type[key]);
  }

  private setCrmContactInfoInFormGroup(): void {
    let maxYear = null;
    let minYear = null;
    if (this.customerDetails?.maxYear) {
      maxYear = new Date();
      maxYear.setFullYear(this.customerDetails?.maxYear ?? new Date().getFullYear())
    }
    if (this.customerDetails?.minYear) {
      minYear = new Date();
      minYear.setFullYear(this.customerDetails?.minYear ?? new Date().getFullYear())
    }
    if (this.customerDetails) {
      this.customerLeadFormGroup.patchValue({
        crmContactId: this.customerDetails?.crmContact?.id,
        unitTypeId: this.customerDetails?.unitType?.id,
        maxYear: maxYear,
        minYear: minYear,
        designationId: this.customerDetails?.designation?.id,
        status: this.customerDetails?.status,
        notes: this.customerDetails?.notes,
        leadDescription: this.customerDetails?.leadDescription,
        categoryId: this.customerDetails?.category?.id
      });
      this.makeIds = this.setIds('makes', 'makeId');
      this.modelIds = this.setIds('unitModels', 'unitModelId');
      if (this.customerDetails?.category?.id) {
        this.categoryId = this.customerDetails.category.id
        this.getUnitTypes(this.customerDetails.category.id)
        this.getMake(this.customerDetails.category.id)
      }
      if (this.customerDetails?.makes.length) {
        this.getModels(this.makeIds);
      }
      if (this.isViewMode) {
        this.customerLeadFormGroup.disable();
      }
    }

  }

  toggleGoogleMapPopUp(contact: ContactDetails) {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getFullAddress(contact)
  }

  getFullAddress(contact: ContactDetails) {
    const rowAddress = []
    rowAddress.push(contact?.streetAddress ? contact.streetAddress : '')
    rowAddress.push(contact?.city ? contact.city : '')
    rowAddress.push(contact?.state ? contact.state : '')
    rowAddress.push(contact?.zipcode ? contact.zipcode : '')
    const cleanAddress = rowAddress.filter(str => {
      return str !== ""
    })
    this.fullAddress = cleanAddress.join(", ")
  }

  inventoryMatchedCount(event: number) {
    if (event) {
      this.matchedCount = event
    }
  }

  private getCategoryTypes() {
    this.loaders.categoryType = true;
    this.inventoryService.getCategoryType().pipe(takeUntil(this.destroy$)).subscribe({
      next: (categoryTypes: IdNameModel[]) => {
        this.categoryTypes = categoryTypes;
        this.loaders.categoryType = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.categoryType = false;
        this.commonSharedService.setBlockUI$(false);
        this.cdf.detectChanges();
      }
    });
  }

  changeCategory(id: number) {
    this.clearMakes();
    this.categoryId = id;
    this.getUnitTypes(id);
    this.getMake(id);
    this.getInventorySpecificationsForm(id);
  }

  clearMakes(): void {
    this.makeIds = this.models = this.modelIds = [];
  }

  clearModels(): void {
    this.modelIds = [];
  }

  getInventoryPerference(): string {
    const customerInfo = this.customerLeadFormGroup.getRawValue()
    const inventoryPreferenceInfo = [
      `catergory: ${this.categoryTypes.find(c => c.id === customerInfo.categoryId)?.name}`,
      `unitType: ${this.unitTypes.find(c => c.id === customerInfo.unitTypeId)?.name}`,
      `minYear: ${(new Date(customerInfo.minYear)).getFullYear()}`,
      `maxYear: ${(new Date(customerInfo.maxYear)).getFullYear()}`,
      `make: ${this.makes.find(c => c.id === customerInfo.makes)?.name}`,
      `model: ${this.models.find(c => c.id === customerInfo.unitModelId)?.name}`,
      `designations: ${this.designations.find(c => c.id === customerInfo.designationId)?.name}`,
      `status: ${this.customerStatusList.find(c => c.value === customerInfo.status)?.name}`,
      ` notes: ${customerInfo.notes}`,
      `leadDescription: ${customerInfo.leadDescription}`,
    ];

    return inventoryPreferenceInfo.join(', ');
  }

  getSpecificationPrefrecence(): string {
    const specification = []
    if (this.inventorySpecificationForm) {
      for (const section of this.inventorySpecificationForm) {
        for (const field of section.fields) {
          if (field.value) {
            if (field.dataType === 'DropDown') {
              specification.push(`${field.label}: ${field.options.find(option => option.id === Number(field.value))?.name}`)
            } else {
              specification.push(`${field.label}: ${field.value}`)
            }
          }
        }
      }
    }
    return specification.join(', ');
  }
}
