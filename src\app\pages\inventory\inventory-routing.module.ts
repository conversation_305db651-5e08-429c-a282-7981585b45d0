import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { InventoryComponent } from './inventory.component';
import { InventoryListComponent } from './pages/inventory-list/inventory-list.component';

const routes: Routes = [{
  path: '',
  pathMatch: 'full',
  component: InventoryComponent,
  title: 'Skeye - Inventory',
  children: [
    {
      path: '',
      pathMatch: 'full',
      component: InventoryListComponent
    }
  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class InventoryRoutingModule { }
