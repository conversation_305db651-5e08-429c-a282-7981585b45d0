import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '@pages/auth/services/auth.service';
import { ROUTER_UTILS } from '../utils/router.utils';

@Injectable({
  providedIn: 'root',
})
export class NoAuthGuard  {
  constructor(private readonly router: Router, private readonly authService: AuthService) {}

  canLoad(): boolean {
    const isLoggedIn = this.authService.isLoggedIn;

    if (isLoggedIn) {
      this.router.navigate([ROUTER_UTILS.config.base.dashboard]);
      return false;
    }

    return true;
  }
}
