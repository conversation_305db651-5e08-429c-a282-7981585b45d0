@import 'src/assets/scss/variables';

form {
  height: calc(100vh - 57px);
}

.modal-footer {
  position: absolute;
}

.mt-10 {
  margin-top: 10px;
}

.mt-43 {
  margin-top: 43px;
}

.displayContactDetails {
  margin: 0px;
  color: $contact-company-gray-color;
}

.displayContactDetails.company {
  font-weight: 500;
}

.displayContactIcons {
  border: none;
  background-color: transparent;
  float: right;
  margin-right: 5px;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

.bold {
  font-weight: 600;
  font-size: 20px;
}

.add-btn {
  height: 25px;
  padding: 0px 5px;
  float: right;
  margin: 3px;
}

.load-more {
  color: var(--text-color);
  text-decoration: underline;
  text-align: center;
  cursor: pointer;
}

.displayContactDetails.company {
  font-weight: 500;
}

.displayContactIcons {
  border: none;
  background-color: transparent;
  float: right;
  margin-right: 5px;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

.bold {
  font-weight: 600;
  font-size: 20px;
}

.add-btn {
  height: 25px;
  padding: 0px 5px;
  float: right;
  margin: 3px;
}

::ng-deep .width-80 {
  width: 80rem !important;
}

::ng-deep .sale-parent-div {

  .p-disabled,
  .p-calendar-disabled {
    opacity: 1;
  }

  .p-disabled .p-dropdown-label,
  .task-add .p-component:disabled .p-dropdown-label,
  .p-calendar-disabled {
    color: #797979;
  }
}

::ng-deep .display-info {
  .p-card-content {
    padding: 0;
  }
}

.m-r-9 {
  margin-right: 6rem;
}