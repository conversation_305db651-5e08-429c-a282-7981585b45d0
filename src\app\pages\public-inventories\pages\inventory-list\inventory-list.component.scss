@import '/src/assets/scss/theme/mixins';

header {
  display: flex;
  align-items: center;
  background: var(--public-page-nav-color);
  box-shadow: var(--box-shadow);
  padding-top: 10px;
  padding-bottom: 10px;

  .logo {
    width: 100%;

    .search-wrapper {
      display: flex;
      flex-wrap: nowrap;
      justify-content: center;

      .header-search {
        border-radius: 0px;
        padding-bottom: 0;
        height: 40px;
        font-size: 14px;
        width: 500px;
        margin-right: 10px;
      }

      .btn {
        position: relative;
        left: -115px;
        width: 100px;
        height: 28px;
        top: 7px;
        padding: 0 22px;
      }

      .form-control:focus {
        box-shadow: 0 0 0 0.05rem $public-page-active-color;
      }
    }

    img {
      padding: 0px 25px;
      width: 140px;
      height: 30px;
    }

    &:hover {
      cursor: pointer;
    }

    ::ng-deep .dealer-name .p-dropdown {
      height: 40px;
      font-size: 14px;
      align-items: center;
    }
  }
}

.page-wrapper-login {
  margin-bottom: -15px;
  margin-left: -28px;
  height: unset !important;
  min-height: 100vh !important;

  .inventory-wrapper {
    min-height: 100vh !important;
    height: unset !important;
    overflow: unset !important;
  }
}

.page-wrapper {
  overflow: hidden;
  height: 100vh;

  .inventory-wrapper {
    height: calc(100vh - 100px);
    overflow: auto;
    width: 100%;
    --bs-gutter-x: 0;
    --bs-gutter-y: 0;

    .filters-wrapper {
      max-height: calc(100vh - 100px);
      overflow: auto;
      background: var(--public-page-nav-color);
      border: 1px solid #ffffff;
      padding-right: 0px;

      .wrapper-section {
        padding: 14px 16px;
        border-bottom: 1px solid #e3e3e3;

        .search-box {
          border-top: 0px;
          border-radius: 0px;
          border-right: 0;
          border-left: 0;
          padding-bottom: 0;
          height: 30px;
          font-size: 14px;
          margin-bottom: 15px;
        }

        .more-records {
          font-weight: 500;
          font-size: 12px;
          line-height: 18px;
          color: var(--card-text-color);
          cursor: pointer;
        }

        .title {
          color: #999999;
          margin-bottom: 15px;
        }

        .date-picker-wrapper,
        .price-wrapper {
          display: flex;
          align-items: center;
          color: var(--filter-text-color);

          .dash {
            margin: 20px 5px 0 5px;
          }

          .form-control {
            height: 40px;
            border-radius: 0;
          }
          label {
            color: var(--filter-text-color);
          }
        }
      }

      .title-wrapper {
        @include flex-space-between;

        span {
          color: var(--filter-text-color);
          font-weight: 500;
          font-size: 16px;
        }

        .clear-all {
          color: var(--card-text-color);
          cursor: pointer;
        }
      }

      .p-field-radiobutton {
        margin-bottom: 9px;

        p-radiobutton {
          margin-right: 10px;
        }
        label {
          color: var(--filter-text-color);
        }
      }

      .checkbox {
        margin-bottom: 9px;
        color: #000000;
        font-size: 14px;
        letter-spacing: -0.28px;
      }
      ::ng-deep.p-checkbox-label {
        color: var(--filter-text-color);
      }
    }

    .inventory-item-wrapper {
      max-height: calc(100vh - 100px);
      overflow: auto;
      background-color: var(--public-page-bg-color);
      padding: 0 20px 10px 20px;

      .invetory-content {
        text-align: center;

        img {
          height: 200px;
          width: 266px;
        }

        .card {
          width: 266px;
          margin: -10px auto 0 auto;
          text-align: left;
          border-radius: 7px 7px 0 0;

          .card-body {
            .basic-details {
              .model-name,
              .model-description {
                color: var(--card-text-color);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .model-name {
                font-weight: 500;
                margin-bottom: 3px;
              }

              .model-description {
                color: #717171;
                margin-bottom: 10px;
              }

              .model-conditional-details {
                font-size: 12px;
                display: flex;
                justify-content: space-between;
                padding-bottom: 12px;
                border-bottom: 1px solid #d0d0d0;
                color: var(--card-text-color);

                .miles {
                  display: flex;
                  align-items: center;

                  img {
                    width: 14px;
                    height: 10px;
                    margin-right: 7px;
                  }
                }

                .matching-precentage {
                  display: flex;
                  align-items: center;

                  img {
                    width: 12px;
                    height: 12px;
                    margin-right: 7px;
                  }

                  span {
                    color: #2ca257;
                  }
                }
              }
            }

            .model-details {
              font-weight: 400;
              font-size: 12px;
              display: flex;
              justify-content: space-between;
              flex-wrap: wrap;
              margin-top: 15px;

              div {
                width: 50%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin-bottom: 9px;

                .model-detail-label {
                  color: #939393;
                  margin-right: 2px;
                }
              }
              .model-detail-info {
                color: var(--card-text-color);
              }

              div:last-child {
                width: 100%;

                .model-detail-info {
                  font-weight: 500;
                  font-size: 16px;
                }
              }
            }
          }
        }
      }
    }

    .sort-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;

      .total-inventories {
        font-size: 16px;
        color: black;
      }

      .sorting-dropdown {
        width: 237px;
      }
    }

    .selected-filter-wrapper {
      padding: 0 20px 15px 20px;
      display: flex;
      flex-wrap: wrap;

      .item {
        border: 1px solid #92969c;
        border-radius: 17px;
        margin-bottom: 5px;
        padding: 0px 10px;
        margin-right: 5px;

        .name {
          font-size: 12px;
          margin-right: 7px;
        }

        .close {
          font-size: 12px;
        }
      }
    }
  }
}

::ng-deep .p-calendar {
  height: 40px;

  input {
    border-radius: 0;
  }
}

@media only screen and (max-width: 550px) {
  .search-wrapper {
    margin-left: 3rem;
  }

  .sort-wrapper {
    display: block !important;
  }
}

.details-parent {
  .advertising-banner {
    position: absolute;
    bottom: 100%;
    left: 0;
    width: 150px;
    min-height: 30px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.DARK {
  .miles {
    img {
      filter: invert(1) !important;
    }
  }
}

::ng-deep .public-page {
  .card {
    background-color: #ffffff;
  }

  .p-dropdown {
    background-color: #ffffff !important;
    border: 1px solid #edf1fb !important;
  }

  .form-control,
  textarea {
    background-color: #ffffff !important;
    border-color: #edf1fb !important;
    color: var(--card-text-color);
  }

  .p-calendar input {
    background-color: #ffffff !important;
    border-color: #edf1fb !important;
    color: var(--card-text-color);
  }

  .p-radiobutton .p-radiobutton-box {
    border: 2px solid #ced4da;
    background: #ffffff;
  }

  .p-checkbox .p-checkbox-box {
    border: 2px solid #ced4da;
    background: #ffffff;
  }

  .p-checkbox .p-checkbox-box .p-checkbox-icon {
    color: #ffffff;
  }

  .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
    background-color: #ffffff;
  }

  .server-pagination .items-per-page label,
  .server-pagination .items-per-page .pagination-info,
  .p-dropdown-label,
  .p-dropdown .p-dropdown-trigger,
  .server-pagination .page-link {
    color: var(--card-text-color);
  }
  .server-pagination .page-item.active .page-link {
    color: #ffffff !important;
    background-color: $public-page-active-color !important;
  }

  .server-pagination .pagination-prev a,
  .server-pagination .pagination-first a,
  .server-pagination .pagination-next a,
  .server-pagination .pagination-last a {
    color: var(--card-text-color) !important;
    background-color: var(--public-page-nav-color) !important;
  }

  footer {
    .version-info {
      color: var(--card-text-color) !important;
    }
    background-color: var(--public-page-nav-color);
    color: var(--card-text-color);
  }
}
.listing-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: $public-page-active-color;
}
.slider-container {
  width: 300px;
  margin: 20px auto;
  width: 80%;
  .left-number {
    float: left;
  }
  .right-number {
    float: right;
  }
}
