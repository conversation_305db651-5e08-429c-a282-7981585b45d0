import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { UnitTypeComponent } from "./unit-type.component";

const routes: Routes = [
    {
      path: '',
      component: UnitTypeComponent,
      title: 'Skeye - Specification Model',
      children: [
        {
          path: '',
          component: UnitTypeComponent,
          pathMatch: 'full'
        }
      ]
    }
  ];

  @NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
  })

  export class UnitTypeRoutingModule { }
