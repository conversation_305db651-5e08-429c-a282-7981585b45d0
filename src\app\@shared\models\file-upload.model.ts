import { BehaviorSubject, Observable } from "rxjs";

export class FileUploadProgress {
  index!: number;
  progress$: BehaviorSubject<number> = new BehaviorSubject(0);
  progress: Observable<number> = this.progress$.asObservable();
  file!: File;
  uploadedFileUrl!: string;
  fileProperty?: FileProperties = new FileProperties();
  isResolved = false;
  isSelected? = false;
}

export class FileProperties {
  constructor(public name?: string, public fileUrl?: string) {
  }
}
