<div class="card shops-list p-3 m-t-80">
  <app-page-header [pageTitle]="pageTitle">
    <div headerActionBtn>
      <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
        <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
        <span class="btn-label">Activities</span>
      </button>
      <button class="btn btn-primary left me-3 show-label" type="button" [appImageIconSrc]="constants.staticImages.icons.exportFile" (click)="exportUsersToExcel()" [disabled]="!tasks?.length">
        <span class="show-label">Export</span>
        <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
      </button>
      <button class="btn btn-primary left me-3 expand-btn" (click)="onExpandCollapse(isButtonExpanded)">
        <fa-icon [icon]="isButtonExpanded ? faIcons.faChevronCircleDown : faIcons.faChevronCircleUp"></fa-icon>
        <span class="show-label">
          {{ expandCollapseButtonLabel }}
        </span>
      </button>
      <button class="btn btn-primary left" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAddTask()" *appHasPermission="[permissionActions.CREATE_TASK]">
        <span class="show-label">Add New Task</span>
      </button>
    </div>
  </app-page-header>

  <ng-container *ngIf="!isLoading; else pageLoaderTemplate">
    <p-accordion [multiple]="true" class="parent-accordion" *ngIf="taskUserGroups?.length; else noDataTemplate">
      <p-accordionTab *ngFor="let userGroup of taskUserGroups" [(selected)]="userGroup.isTabOpen">
        <ng-template pTemplate="header">
          <div class="accordion-header" [ngClass]="{ active: userGroup.isTabOpen }">
            <span>{{ userGroup.shopName }}</span>
            <em class="pi" [ngClass]="userGroup.isTabOpen ? 'pi-angle-up' : 'pi-angle-down'"></em>
          </div>
        </ng-template>
        <ng-template pTemplate="content">
          <p-accordion class="nested-accordion" [multiple]="true" *ngIf="userGroup.assignees?.length; else noAssigneeDataTemplate">
            <p-accordionTab *ngFor="let assignee of userGroup.assignees" [(selected)]="assignee.isTabOpen">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: assignee }">
                  <span>{{ assignee.name }}</span>
                  <em class="pi" [ngClass]="assignee.isTabOpen ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <app-task-list [filterParams]="getTaskListFilter(assignee, userGroup.id)" (updateTasks)="getAllShopsData()"></app-task-list>
              </ng-template>
            </p-accordionTab>
          </p-accordion>
        </ng-template>
      </p-accordionTab>
    </p-accordion>
  </ng-container>
</div>

<p-sidebar [(visible)]="showCreateModal" [fullScreen]="true" (onHide)="showCreateModal = false; taskId = ''" [blockScroll]="true" [transitionOptions]="modalTransition" [showCloseIcon]="false">
  <app-task-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal" [isViewMode]="isViewMode" [taskId]="taskId"></app-task-add>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>

<ng-template #noDataTemplate>
  <div class="no-data mt-5">
    <h5>No shop tasks available.</h5>
  </div>
</ng-template>

<ng-template #noAssigneeDataTemplate>
  <div class="no-data mt-0">
    <h5>No tasks available.</h5>
  </div>
</ng-template>

<ng-template #pageLoaderTemplate>
  <div class="no-data mt-5">
    <em class="pi pi-spin pi-spinner"></em>
  </div>
</ng-template>
