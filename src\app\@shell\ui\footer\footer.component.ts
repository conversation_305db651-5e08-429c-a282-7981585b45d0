import { ChangeDetectionStrategy, Component } from '@angular/core';
import { environment } from '@env/environment';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FooterComponent {
  today: number = Date.now();
  currentApplicationVersion = `${environment.versions.version}-${environment.versions.revision}-${environment.versions.branch}`;
  buildDateTime = environment.versions.buildDateTime;
}
