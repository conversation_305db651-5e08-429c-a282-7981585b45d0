import { CommonModule, TitleCasePipe } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DragDropModule } from 'primeng/dragdrop';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { SharedComponentsModule } from "../../../../../../@shared/components/shared-components.module";
import { QuoteFormFieldAddEditComponent } from './quote-form-field-add-edit/quote-form-field-add-edit.component';
import { QuoteFormRoutingModule } from './quote-form-routing.module';
import { QuoteFormComponent } from './quote-form.component';


@NgModule({
    declarations: [
        QuoteFormComponent,
        QuoteFormFieldAddEditComponent
    ],
    imports: [
        CommonModule,
        QuoteFormRoutingModule,
        SharedComponentsModule,
        DirectivesModule,
        SidebarModule,
        TableModule,
        FontAwesomeModule,
        ReactiveFormsModule,
        ConfirmPopupModule,
        FormsModule,
        DropdownModule,
        CheckboxModule,
        DragDropModule,
        MultiSelectModule,
        UiSwitchModule.forRoot(UiSwitchConfig),
    ],
    providers: [
        ConfirmationService,
        MessageService,
        TitleCasePipe
    ]
})
export class QuoteFormModule { }
