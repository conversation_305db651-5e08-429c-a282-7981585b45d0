.m-l-20 {
  margin-left: 20px;
}

.view-task {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
}

.w-130 {
  width: 130px !important;
}

::ng-deep .customer-status {
  .p-dropdown .p-dropdown-label {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
    padding: 3px 0px 3px 10px !important;
    font-size: 14px;
  }

  .p-dropdown .p-dropdown-trigger {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
  }

  .p-dropdown {
    background-color: var(--active-color) !important;
    height: inherit;
  }

  .pi-chevron-down:before {
    font-size: 14px;
  }

  .priority-icon {
    padding: 17px !important;
  }
}

.top-header {
  .column-btn {
    margin-left: 10px;
    color: #0b0b69;
    background-color: #fff;
    border-color: #0b0b69;
    font-weight: 600;
    border: 3px solid #0b0b69;
    padding: 0px 17px !important;
    fa-icon {
      margin-left: 5px;
      font-size: 16px;
      margin-right: 9px;
    }
  }
}

::ng-deep .inventory-search-tr th {
  .search-input {
    input {
      width: 100%;
      height: 36px !important;
    }

    p-calendar.p-inputwrapper {
      span.p-calendar-w-btn {
        height: 36px !important;
      }
    }
  }

  .float-end img {
    cursor: pointer;
  }

  .reset-icon {
    fa-icon {
      color: var(--active-color) !important;
      font-size: 23px;
    }
  }
}

::ng-deep .stock-truck-list {
  .p-multiselect-label-container {
    width: 150px;
  }
}

.cross-icon {
  position: relative;
  left: 150px;
  top: -28px;
  height: 0;
  display: flex;
  color: #6c757d;
  font-size: 15px;
}

.stock-truck-list {
  max-width: inherit;
  overflow: auto;
}

@media only screen and (max-width: 500px) {
  .reset-btn {
    padding: 0px 10px !important;
  }
}

.left img {
  left: 7%;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
