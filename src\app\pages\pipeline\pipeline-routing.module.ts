import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionForChildGuard } from '@core/guards';
import { ROUTER_UTILS } from '@core/utils';
import { PermissionActions } from 'src/app/@shared/models';

const routes: Routes = [
  {
    path: ROUTER_UTILS.config.pipeline.soldTruckBoard.root,
    loadChildren: async () => (await import('./pages/sold-truck-board/sold-truck-board.module')).SoldTruckBoardModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_SOLD_PIPELINE]
    }
  },
  {
    path: ROUTER_UTILS.config.pipeline.stockTruckBoard.root,
    loadChildren: async () => (await import('./pages/stock-truck-board/stock-truck-board.module')).StockTruckBoardModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_STOCK_PIPELINE]
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PipeLineRoutingModule { }
