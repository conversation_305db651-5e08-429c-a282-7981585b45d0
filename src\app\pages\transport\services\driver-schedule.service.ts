import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { Observable } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { DriverScheduleCommentListItem } from '../models/driver-schedule-comment.model';
import { Address, DriverList, DriverScheduleBoardListItem } from '../models/driver-schedule.model';

@Injectable({
  providedIn: 'root'
})
export class DriverScheduleService extends BaseCrudService {

  constructor(protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  getBaseAPIPath(): string {
    return API_URL_UTIL.driverSchedule.root;
  }

  fromServerModel(json: DriverScheduleBoardListItem): DriverScheduleBoardListItem {
    if (!json) {
      return new DriverScheduleBoardListItem();
    }
    return new DriverScheduleBoardListItem(json);
  }

  getStockNumberByUnitId(unitId: number): Observable<Address> {
    return this.httpClient.get<Address>(`${API_URL_UTIL.driverSchedule.address}/${unitId}/address`);
  }

  getRoleByDriverNameList(): Observable<DriverList[]> {
    const endpoint = API_URL_UTIL.admin.users.role.replace(':roleName', 'driver');
    return this.httpClient.get<DriverList[]>(`${endpoint}`);
  }

  getRoleByDispatcherList(): Observable<IdNameModel[]> {
    const endpoint = API_URL_UTIL.admin.users.role.replace(':roleName', 'dispatcher');
    return this.httpClient.get<IdNameModel[]>(`${endpoint}`);
  }

  getDriverStatus(): Observable<{}> {
    return this.httpClient.get<{}>(`${this.getFullAPIUrl()}/${API_URL_UTIL.driverSchedule.status}`);
  }

  downloadPdf(endpoint: string) {
    return this.httpClient.get(`${this.getFullAPIUrl()}/${endpoint}`,
      {
        responseType: "arraybuffer"
      });
  }

  getDriverScheduleComments(unitId: string): Observable<DriverScheduleCommentListItem[]> {
    return this.httpClient.get<DriverScheduleCommentListItem[]>(`${API_URL_UTIL.driverSchedule.driverScheduleComment}/${unitId}/comments`);
  }

  updateDriverScheduleStatus(id: number, status: string): Observable<void> {
    return this.httpClient.patch<void>(`${this.getFullAPIUrl('update-status')}?id=${id}&statusEnum=${status}`, {});
  }
}
