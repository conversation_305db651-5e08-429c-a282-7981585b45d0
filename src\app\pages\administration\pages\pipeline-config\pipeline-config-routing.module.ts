import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PipelineConfigComponent } from './pipeline-config.component';
import { PipelineListComponent } from './pipeline-list/pipeline-list.component';

const routes: Routes = [
  {
    path: '',
    component: PipelineConfigComponent,
    title: 'Skeye - Pipeline Config',
    children: [
      {
        path: '',
        component: PipelineListComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PipelineConfigRoutingModule { }
