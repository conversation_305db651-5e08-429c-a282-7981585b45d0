import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionForChildGuard } from '@core/guards';
import { ROUTER_UTILS } from '@core/utils';
import { PermissionActions } from 'src/app/@shared/models';
import { CrmReportsComponent } from './crm-reports.component';

const routes: Routes = [{
  path: '',
  component: CrmReportsComponent,
  title: 'Skeye - Customer Relationship Management',
  children: [
    {
      path: ROUTER_UTILS.config.reporting.crm.dailySalesReport.root,
      loadChildren: async () => (await import('./pages/daily-sales-report/daily-sales-report.module')).DailySalesReportModule,
      canActivate: [PermissionForChildGuard],
      data: {
        permission: [PermissionActions.VIEW_DAILY_SALES_REPORT]
      }
    },
    {
      path: ROUTER_UTILS.config.reporting.crm.activity.root,
      loadChildren: async () => (await import('./pages/activity-report/activity-report.module')).ActivityReportModule,
      canActivate: [PermissionForChildGuard],
      data: {
        permission: [PermissionActions.VIEW_ACTIVITY_REPORT]
      }
    },
    {
      path: ROUTER_UTILS.config.reporting.crm.inventorySales.root,
      loadChildren: async () => (await import('./pages/inventory-sales-report/inventory-sales-report.module')).InventorySalesReportModule,
      canActivate: [PermissionForChildGuard],
      data: {
        permission: [PermissionActions.VIEW_INVENTORY_SALES_REPORT]
      }
    }
  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CrmReportsRoutingModule { }
