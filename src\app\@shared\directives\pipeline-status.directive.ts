import { Directive, ElementRef, OnInit, Renderer2 } from "@angular/core";
import { Status } from "../constants/app.constants";

@Directive({
  selector: '[appPipelineStatus]'
})
export class PipelineStatusDirective implements OnInit {
  constructor(private readonly elem: ElementRef, private readonly renderer: Renderer2) { }

  ngOnInit(): string | null {
    const inputElem = this.elem.nativeElement;
    const childElem = inputElem.nextElementSibling;
    if (inputElem.className === Status.completed || inputElem.className === Status.done) {
      this.renderer.addClass(inputElem, 'background-blue');
      if (childElem) {
        this.renderer.addClass(childElem, 'background-blue');
      }
    }
    else if (inputElem.className === Status.inProgress) {
      this.renderer.addClass(inputElem, 'background-white');
      if (childElem) {
        this.renderer?.addClass(childElem, 'background-grey');
      }
    }
    return null;
  }
}
