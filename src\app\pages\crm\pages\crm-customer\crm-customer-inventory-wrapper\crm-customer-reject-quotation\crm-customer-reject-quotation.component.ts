import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CrmService } from '@pages/crm/services/crm.service';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-crm-customer-reject-quotation',
  templateUrl: './crm-customer-reject-quotation.component.html',
  styleUrls: ['./crm-customer-reject-quotation.component.scss']
})
export class CrmCustomerRejectQuotationComponent extends BaseComponent implements OnInit {
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  title = 'Reject Quote';
  quotationFormGroup!: FormGroup;
  hasDataBeenModified = false
  @Input() selectedQuotationId!: string;
  @Output() onQuotationAccepted: EventEmitter<boolean> = new EventEmitter<boolean>()
  constructor(private readonly formBuilder: FormBuilder, private readonly crmService: CrmService, private readonly toasterService: AppToasterService) { super() }

  ngOnInit(): void {
    this.initializeFormGroup();
  }

  private initializeFormGroup(): void {
    this.quotationFormGroup = this.formBuilder.group({
      reasonForRejection: new FormControl(null, Validators.required)
    });
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  onSubmit(close = true): void {
    if (this.quotationFormGroup.invalid) {
      this.quotationFormGroup.markAllAsTouched();
      return;
    }
    this.save(close);
  }

  get rejectParams() {
    return {
      ...this.quotationFormGroup.value,
      quotationStatus: "REJECTED",
    };
  }

  save(close = true): void {
    const endpoint = API_URL_UTIL.admin.crm.acceptQuotation.replace(':quotationId', this.selectedQuotationId);
    this.crmService.patch(this.rejectParams, endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.quotationRejected);
        if (close) {
          this.onClose.emit(true);
        }
      }
    });
  }

}
