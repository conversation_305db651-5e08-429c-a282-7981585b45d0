@import "src/assets/scss/variables";

.example-list {
  width: 100%;
  max-width: 100%;
  border: solid 1px #ccc;
  min-height: 60px;
  display: block;
  background: var(--card-bg-color) !important;
  border-radius: 4px;
  overflow: hidden;
}

.p-input-icon-right {
  width: 100%;

  input {
    width: 100% !important ;
    border-radius: none;
  }
}

::ng-deep .content.example {
  .p-message .p-message-wrapper {
      padding: 1rem !important;
    }
  }

.example-box {
  padding: 15px 15px;
  border-bottom: solid 1px #ccc;
  color: var(--text-color);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  cursor: move;
  background: var(--card-bg-color) !important;
  font-size: 14px;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drag.example-box.ng-star-inserted.cdk-drag-preview {
  z-index: 10000 !important;
  background-color: $gray-highlight-color;
}

.example-box:last-child {
  border: none;
}

.example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.content.example input[type="text"] {
  padding: 10px;
  font-size: 17px;
  border: 1px solid $contact-company-gray-color;
  float: left;
  width: 100%;
  background: #f1f1f1;
}

.content.example button {
  padding: 10px;
  background: none;
  font-size: 17px;
  border: none;
  border-left: none;
  cursor: pointer;
  color: gray;
}

.content.example::after {
  content: "";
  clear: both;
  display: table;
}

.save-btn {
  margin-left: 16px;
}

.sidebar-loader {
  display: flex;
  justify-content: center;
  align-content: center;
  align-items: center;
  height: calc(100vh - 110px);
}

.example-box.cdk-drag-disabled {
  background: var(--form-placeholder-color);
  cursor: default;
  pointer-events: none;
  opacity: 0.5;
}

.example-box.cdk-drag-disabled {
  background: #ccc;
  cursor: default;
}

.no-data {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}
::ng-deep .column-list {
  .p-tabview-panels {
    padding: 0 !important;
  }
}
