import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { Shops } from '@pages/administration/models';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { ShopService } from '../../pipeline-config/shop.service';

@Component({
  selector: 'app-shop-list',
  templateUrl: './shop-list.component.html',
  styleUrls: ['./shop-list.component.scss']
})
export class ShopListComponent extends BaseComponent implements OnInit {

  shops!: Array<Shops>;
  selectedShop!: Shops | null;
  showCreateModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.SHOP;

  constructor(
    private readonly shopService: ShopService,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef,
    private readonly activeRoute: ActivatedRoute
  ) {
    super();
  }

  ngOnInit(): void {
    this.getShops();
    this.pageTitle = 'Shops';
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getShopById(Number(params.id));
        }
      });
  }

  getShopById(id: number) {
    this.shopService.get<Shops>(id, 'id').pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.onAddEditShop(res);
      }
    });
  }

  getShops(): void {
    this.isLoading = true;
    this.shopService.getList<Shops>(`${API_URL_UTIL.admin.makeSpecification.count}`)
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: (res: Array<Shops>) => {
          this.shops = res;
          this.isLoading = false;
        }, error: () => {
          this.isLoading = false;
        }
      })
  }

  updateShop(shopDetails: Shops): void {
    const selectedShopIndex = this.shops.findIndex((shop: Shops) => shop.id === shopDetails.id);
    this.shops[selectedShopIndex] = { ...this.shops[selectedShopIndex], ...shopDetails };
    this.toasterService.success(MESSAGES.updateSuccessMessage.replace('{item}', 'Shop'));
  }

  addNewShop(shops: Array<Shops>) {
    for (const shop of shops) {
      this.shops.push({
        ...shop,
        taskCount: 0,
        pipelineCount: 0,
      });
    }
    this.toasterService.success(MESSAGES.addSuccessMessage.replace('{item}', 'Shop'));
  }

  onAddEditShop(shop?: Shops): void {
    if (shop?.id) {
      this.selectedShop = shop;
    }
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  showDeleteModal(shopDetails: Shops, event: Event): void {
    if (shopDetails.taskCount) {
      this.toasterService.warning(MESSAGES.shopActiveTaskDeleteWarning);
      return;
    }
    if (shopDetails.pipelineCount) {
      this.toasterService.warning(MESSAGES.shopActivePipelineDeleteWarning);
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'shop data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteCategory(shopDetails);
      }
    });
  }

  onDeleteCategory(shopDetails: Shops): void {
    this.shopService.delete(shopDetails?.id, `${API_URL_UTIL.admin.makeSpecification.id}`).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.shops = this.shops.filter((shop: Shops) => shop.id !== shopDetails.id);
        this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('{item}', 'Shop'));
      }
    });
  }

  closeModal(): void {
    this.showCreateModal = false;
    this.selectedShop = {} as Shops;
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
