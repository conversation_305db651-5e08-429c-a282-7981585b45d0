import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService } from 'primeng/api';
import { CardModule } from 'primeng/card';
import { DropdownModule } from 'primeng/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { AddNewMakeComponent } from './add-new-make.component';
@NgModule({
  declarations: [AddNewMakeComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeIconsModule,
    AccordionModule,
    DropdownModule,
    CardModule,
    SidebarModule,
  ],
  providers: [ConfirmationService],
  exports: [AddNewMakeComponent]

})
export class AddNewMakeModule { }
