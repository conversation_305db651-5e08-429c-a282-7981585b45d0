<div class="content">
  <div class="tab-content">
    <div *ngIf="isFullViewRecentInventory" class="title d-flex justify-content-between">
      <div>
        <h6>Recently Added Inventories</h6>
      </div>
      <div class="justify-content-end">
        <fa-icon [icon]="faIcons.faTimes" *ngIf="isFullViewRecentInventory" (click)="onCancel()"></fa-icon>
      </div>
    </div>
    <p-table
      [resizableColumns]="true"
      class="no-column-selection"
      styleClass="p-datatable-gridlines"
      [value]="inventories"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAllRecentInventory.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      scrollDirection="horizontal"
      columnResizeMode="expand"
      [loading]="isLoading"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th pResizableColumn id="Stock">#Stock</th>
          <th pResizableColumn id="Category">Category</th>
          <th pResizableColumn id="Category">Year</th>
          <th pResizableColumn id="Make">Make</th>
          <th pResizableColumn id="Model">Model</th>
          <th pResizableColumn id="Status">Status</th>
          <th pResizableColumn id="Status">Owner</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData>
        <tr>
          <td>{{ rowData?.generalInformation?.stockNumber }}</td>
          <td>{{ rowData?.generalInformation?.unitTypeCategory?.name }}</td>
          <td>{{ rowData?.generalInformation?.year }}</td>
          <td>{{ rowData?.generalInformation?.make?.name }}</td>
          <td>{{ rowData?.generalInformation?.unitModel?.name }}</td>
          <td>{{ rowData?.generalInformation?.unitStatus?.name }}</td>
          <td>{{ rowData?.generalInformation?.owner?.name }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="9" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
  </div>
</div>
