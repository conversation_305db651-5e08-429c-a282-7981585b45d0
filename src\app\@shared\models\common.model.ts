export interface ConfigChildren {
  name: string;
  routerLink: string[];
  moduleKey?: number
  children?: ConfigChildren[];
}

export interface MenuConfigModel {
  name: string;
  iconPath: string;
  hoverIconPath: string;
  routerLink: string[];
  moduleKey?: number;
  children?: ConfigChildren[];
}

export interface IdNameModel {
  id?: number | string;
  name?: string;
  abbreviation?: string;
  role?: Role;
  isDisabled?: boolean;
  archived?: boolean;
}

export interface IdUrlModel {
  id?: number;
  url: string;
}

export interface IdValueModel {
  id: number;
  value: boolean;
}

export enum DropdownRouteName {
  NO_PARENT_ROUTE = 1,
  ADMIN = 2,
  PIPELINE = 3,
  TRANSPORT = 4,
  CRM = 5,
  SPECIFICATION_CONFIG = 6,
  SPECIFICATION = 7,
  CATEGORY = 8,
  MAKE_MODEL = 9,
  UNITY_TYPE = 10,
  REPORTING = 11,
  CUSTOMER_RELATIONSHIP_MANAGEMENT = 12,
  DAILY_SALES_REPORT = 13,
  ACTIVITY_REPORT = 14,
  INVENTORY_SALES_REPORT = 15,
  INVENTORY_REPORTING = 16,
  INVENTORY_PROFITABILITY = 17,
  INVENTORY_AGING = 18,
  VENDORS = 19,
  SUPPLIERS = 29,
  PUBLIC_PAGE_CONFIG = 21,
  ADVERTISING = 22,
  QUOTE_FORM = 23,
}

export interface Role {
  id: number;
  name: RoleNames;
}

export enum RoleNames {
  ROLE_ADMIN = 'admin',
  ROLE_USER = 'user',
  ROLE_SALESPERSON = 'salesperson',
  ROLE_DRIVER = 'driver',
  ROLE_DISPATCHER = 'dispatcher',
  ROLE_MANAGER = 'manager',
  ROLE_SUPER_ADMIN = "super admin",
}

export enum ViewMode {
  READ = 1,
  EDIT
}

export class MentionParam {
  id!: number;
  label!: string;
}
