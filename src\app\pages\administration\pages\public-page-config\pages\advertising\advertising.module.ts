import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { AdvertisingAddEditComponent } from './advertising-add-edit/advertising-add-edit.component';
import { AdvertisingRoutingModule } from './advertising-routing.module';
import { AdvertisingComponent } from './advertising.component';


@NgModule({
  declarations: [
    AdvertisingComponent,
    AdvertisingAddEditComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    AdvertisingRoutingModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    TooltipModule,
    SidebarModule,
    ConfirmPopupModule,
  ],
  exports: [AdvertisingAddEditComponent]
})
export class AdvertisingModule { }
