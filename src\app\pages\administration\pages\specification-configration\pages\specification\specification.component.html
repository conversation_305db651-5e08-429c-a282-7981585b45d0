<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button
      class="btn btn-primary left btn-add-new-group"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      (click)="onAddEditGroup()"
      *appHasPermission="[permissionActions.CREATE_SPECIFICATION_MASTER]"
    >
      <span class="show-label">Add New Group</span>
    </button>
    <button class="btn btn-primary left" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAddEditField()" *appHasPermission="[permissionActions.CREATE_SPECIFICATION_MASTER]">
      <span class="show-label">Add New Group Field</span>
    </button>
  </div>
</app-page-header>

<div class="content">
  <section>
    <div class="row">
      <div>
        <p-dropdown [options]="categories" [(ngModel)]="selectedCategory" optionLabel="name" optionValue="id" (onChange)="onChange($event)"></p-dropdown>
      </div>
    </div>
  </section>
</div>

<ng-container *ngIf="!isLoading && specificationList?.masterData?.specification; else pageLoaderTemplate">
  <p-table
    class="no-column-selection"
    [value]="specificationList.masterData.specification"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="true"
    [rowHover]="true"
    [loading]="isLoading"
    styleClass="p-datatable-gridlines"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <th pResizableColumn scope="col">Group</th>
        <th pResizableColumn scope="col" class="text-center s-col" *appHasPermission="[permissionActions.UPDATE_SPECIFICATION_MASTER]">Allow Multiple</th>
        <th pResizableColumn scope="col">Field</th>
        <th pResizableColumn scope="col">Data Type</th>
        <th pResizableColumn scope="col" class="col-width">Option</th>
        <th pResizableColumn scope="col" class="text-center s-col" *appHasPermission="[permissionActions.UPDATE_SPECIFICATION_MASTER]">Include Preference</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-specification>
      <ng-container *ngIf="specification.fields.length; else noFields">
        <tr *ngFor="let field of specification.fields; let i = index">
          <td [attr.rowspan]="specification.fields.length" *ngIf="i === 0">
            {{ specification.sectionName }}
            <img [src]="constants.staticImages.icons.edit" class="group-btn-m" (click)="onAddEditGroup(specification)" alt="" *appHasPermission="[permissionActions.UPDATE_SPECIFICATION_MASTER]" />
            <img
              [src]="constants.staticImages.icons.deleteIcon"
              class="group-btn-m"
              (click)="onDeleteGroup(specification, $event)"
              alt=""
              *appHasPermission="[permissionActions.DELETE_SPECIFICATION_MASTER]"
            />
          </td>
          <td *appHasPermission="[permissionActions.UPDATE_SPECIFICATION_MASTER]" class="text-center s-col">
            <div [attr.rowspan]="specification.fields.length" *ngIf="i === 0">
              <p-checkbox [(ngModel)]="specification.multiple" name="multiple" [binary]="true" (onChange)="updateMultipleSpecification(specification)"></p-checkbox>
            </div>
           
          </td>
          <td>
            {{ field.label }}
            <img
              [src]="constants.staticImages.icons.edit"
              class="group-btn-m"
              (click)="onAddEditField(specification, field)"
              alt=""
              *appHasPermission="[permissionActions.UPDATE_SPECIFICATION_MASTER]"
            />
            <img
              [src]="constants.staticImages.icons.deleteIcon"
              class="group-btn-m"
              (click)="onDeleteField(i, field.id, specification, $event)"
              alt=""
              *appHasPermission="[permissionActions.DELETE_SPECIFICATION_MASTER]"
            />
          </td>
          <td>
            {{ field.dataType }}
          </td>
          <td>
            <span *ngFor="let item of field.options; let oi = index"
              >{{ item.name }}
              <ng-container *ngIf="field.options.length - 1 > oi">,</ng-container>
            </span>
          </td>
          <td class="text-center s-col" *appHasPermission="[permissionActions.UPDATE_SPECIFICATION_MASTER]">
            <p-checkbox [(ngModel)]="field.includePreference" name="includePreference" [binary]="true" (onChange)="updateSpecificationPreference()" ></p-checkbox>
          </td>
        </tr>
      </ng-container>
      <ng-template #noFields>
        <tr>
          <td>
            {{ specification.sectionName }}
            <img [src]="constants.staticImages.icons.edit" class="group-btn-m" (click)="onAddEditGroup(specification)" alt="" />
            <img [src]="constants.staticImages.icons.deleteIcon" class="group-btn-m" (click)="onDeleteGroup(specification, $event)" alt="" />
          </td>
          <td>NA</td>
          <td>NA</td>
          <td>NA</td>
          <td>NA</td>
          <td>NA</td>
        </tr>
      </ng-template>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="6" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
</ng-container>

<ng-template #pageLoaderTemplate>
  <div class="no-data mt-5">
    <em class="pi pi-spin pi-spinner"></em>
  </div>
</ng-template>

<p-sidebar
  class="dealer"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showCreateGroupModal"
  (onHide)="showCreateGroupModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  *ngIf="categories"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-specification-group-add-update
    [categories]="categories"
    [specificationList]="specificationList"
    [groupAndFieldNames]="groupAndFieldNames"
    [selectedCategory]="selectedCategory"
    (closeModal)="closeModal($event)"
    [selectedSpecificationGroup]="selectedSpecificationGroup"
    (addSpecificationGroup)="addSpecificationGroup($event)"
    (nameToBeUpdated)="handleGroupNameChange($event)"
  >
  </app-specification-group-add-update>
</p-sidebar>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  class="dealer"
  [(visible)]="showCreateGroupFieldModal"
  (onHide)="showCreateGroupFieldModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  *ngIf="categories"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-specification-group-field-add-update
    [categories]="categories"
    [specificationList]="specificationList"
    [groupAndFieldNames]="groupAndFieldNames"
    [selectedCategory]="selectedCategory"
    [selectedSpecificationFields]="selectedSpecificationFields"
    [selectedSpecificationGroup]="selectedSpecificationGroup"
    (closeModal)="closeGroupFieldModal($event)"
    (fieldToBeAddedInColumnMaster)="handleColumnUpdate($event)"
    (addSpecificationGroup)="addSpecificationGroup($event)"
    [isEditMode]="isEditMode"
  >
  </app-specification-group-field-add-update>
</p-sidebar>

<p-confirmPopup></p-confirmPopup>
