import { ColumnItem } from '@pages/common-table-column/models/common-table.column.model';

export interface Category {
  id: number;
  name: string;
  unitType: number;
  make: number;
  model: number;
  specification: number;
  inventoryCount: number;
}

export interface UnitTypeCategory {
  id: number;
  name: string;
  parentUnitTypeCategoryId: number
}

export interface UnitType {
  id: number;
  name: string;
  unitTypeCategoryId: number;
  unitTypeCategoryName: string
}

export interface MakeModel {
  id: number;
  name: string;
  makeId: number;
  categoryName: string;
  makeName: string;
  models: Array<Model>;
  categoryId: number;
  inventoryCount?: number;
}

export interface MakeModelCountResponse {
  id: number;
  inventoryCount: number;
}

export interface Model {
  id: number;
  makeId: number;
  name: string;
}

export interface Specification {
  id: number;
  name: string;
  makeId: number;
  categoryName: string;
  makeName: string
}

export interface SpecificationMasterColumn {
  id?: number;
  masterData: MasterData;
  module: string
}
export interface MasterData {
  columns: ColumnItem[];
}
export const UpdateMode = {
  EDIT: 'EDIT',
  FIELD_DELETE: 'FIELD_DELETE',
  GROUP_DELETE: 'GROUP_DELETE',
  GROUP_EDIT: 'GROUP_EDIT'
}

export const DataTypeOptions = [
  'DropDown',
  'Number',
  'TextField',
  'TextBox'
]

export class UserFilterUpdateFunctionParam {
  constructor(public mode: string, public parentIndex: number, public childIndex: number) { }
}


export class DeleteUnitSpecificationDetailsParams {
  constructor(
    public specificationIndex: number,
    public fieldIndex: number,
    public categoryId: number,
    public isGroup: boolean
  ) { }
}