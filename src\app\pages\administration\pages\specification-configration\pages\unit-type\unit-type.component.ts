import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { UnitType } from '@pages/inventory/models';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { UnitTypeCategory } from '../../models/specification.model';
import { CategoryService } from '../category/category.service';
import { UnitTypeService } from './unit-type.service';

@Component({
  selector: 'app-unit-type',
  templateUrl: './unit-type.component.html',
  styleUrls: ['./unit-type.component.scss']
})
export class UnitTypeComponent extends BaseComponent implements OnInit {
  categories!: Array<UnitTypeCategory>;
  unitTypes: Array<UnitType> = [];
  selectedCategory!: number;
  selectedUnitType!: UnitType;
  showCreateModal = false;
  selectedUnitTypes: Array<UnitType> = [];
  constructor(
    private readonly unitTypeService: UnitTypeService,
    private readonly categoryService: CategoryService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
  ) {
    super();
    this.pageTitle = 'Unit Type';
  }

  ngOnInit(): void {
    this.getCategories();
  }

  getCategories(): void {
    this.isLoading = true;
    this.categoryService.getList<UnitTypeCategory>().pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Array<UnitTypeCategory>) => {
        this.categories = res;
        this.selectedCategory = res[0]?.id;
        this.getUnitTypes(res[0]?.id);
        this.cdf.detectChanges();
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  getUnitTypes(unitId: number): void {
    this.unitTypeService.get<Array<UnitType>>(`${API_URL_UTIL.inventory.unitTypesList}?isInventory=false&categoryId=${unitId ?? ''}`).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Array<UnitType>) => {
        this.unitTypes = res;
        this.cdf.detectChanges();
        this.isLoading = false;
      }
    });
  }

  onAddEditUnitType(unitType?: UnitType): void {
    if (unitType?.id) {
      this.selectedUnitType = unitType;
    }
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  addNewUnitType(unitTypeDetails: Array<UnitType>): void {
    for (const unitType of unitTypeDetails) {
      if (unitType.unitTypeCategoryId === this.selectedCategory) {
        this.unitTypes.push({
          ...unitType,
          inventoryCount: 0
        });
      }
    }
    this.toasterService.success(MESSAGES.addSuccessMessage.replace('{item}', 'Unit Types'));
  }

  updateUnitType(unitTypeDetails: UnitType): void {
    if (unitTypeDetails.unitTypeCategoryId === this.selectedCategory) {
      const selectedUnitTypeIndex = this.unitTypes.findIndex(
        (category: UnitType) => category.id === unitTypeDetails.id
      );
      this.unitTypes[selectedUnitTypeIndex].name = unitTypeDetails.name;
    } else {
      this.unitTypes = this.unitTypes.filter((unitType: UnitType) => {
        return unitType.id !== unitTypeDetails.id;
      });
    }
    this.toasterService.success(MESSAGES.updateSuccessMessage.replace('{item}', 'Unit Type'));
  }

  onCategoryChange(categoryId: number): void {
    if (categoryId) {
      this.getUnitTypes(categoryId);
    }
  }

  showDeleteModal(unitTypeDetails?: UnitType, event?: Event, deleteAll = false): void {
    if (unitTypeDetails?.inventoryCount) {
      this.toasterService.warning(MESSAGES.unitTypeDeleteWarning);
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'unit type data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        if (deleteAll) {
          this.selectedUnitTypes = this.selectedUnitTypes.filter(unitType => !unitType.inventoryCount)
          const unitTypeShouldBeDeleted = this.selectedUnitTypes.map(({ id }) => id);
          this.onDeleteCategory(unitTypeShouldBeDeleted, true);
        } else if (!deleteAll && unitTypeDetails?.id) {
          this.onDeleteCategory([unitTypeDetails.id]);
        }
      }
    });
  }

  onDeleteCategory(unitTypeDetails: Array<number>, deleteAll = false): void {
    this.unitTypeService.bulkDelete(unitTypeDetails, `${API_URL_UTIL.admin.unitSpecification.unitTypes}/${API_URL_UTIL.admin.unitSpecification.delete}`)
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          if (deleteAll) {
            this.unitTypes = this.unitTypes.filter(i => !this.selectedUnitTypes.some(j => j.id === i.id))
          } else {
            this.unitTypes = this.unitTypes.filter((unitType: UnitType) => unitType.id !== unitTypeDetails[0]);
          }
          this.selectedUnitTypes = [];
          this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('{item}', `${deleteAll ? 'Unit type without active inventories' : 'Unit type'}`));
        }
      });
  }

  getSelectedUnitTypeCount(): number {
    return this.selectedUnitTypes?.filter(unitType => !unitType?.inventoryCount)?.length ?? 0;
  }

  getUnitTypeCount(): number {
    return this.unitTypes?.filter(unitType => !unitType?.inventoryCount)?.length ?? 0;
  }

  closeModal(): void {
    this.showCreateModal = false;
    this.selectedUnitType = {} as UnitType;
  }
}
