import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { NavigationService, SplashScreenService } from '@core/services';
import { BaseComponent, ROUTER_UTILS } from '@core/utils';
import { MenuConfig } from '@core/utils/menu-config';
import { AuthService } from '@pages/auth/services/auth.service';
import { AccountService } from '@pages/settings/services/account.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { PrimeNGConfig } from 'primeng/api';
import { Observable, Subscription, distinctUntilChanged, takeUntil } from 'rxjs';
import { DropdownRouteName, FirebaseRegistrationParams, MenuConfigModel, UnreadNotificationCount } from 'src/app/@shared/models';
import { MessagingService } from 'src/app/@shared/services';
import { MenuConfigService } from 'src/app/@shared/services/menu-config.service';
import { ModalService } from 'src/app/@shared/services/modal.service';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';


@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HeaderComponent extends BaseComponent implements OnInit {
  @ViewChild('splashScreen', { static: true }) splashScreen!: ElementRef;
  themeChanged = false;
  selectedTheme!: THEMES;
  dropdownOpts = DropdownRouteName;
  currentActiveParentRoute!: DropdownRouteName;
  menuConfig!: MenuConfigModel[];
  // NOTE: The following modules are currently disabled per client's request.
  // rightMenuConfig = RightHeaderMenu;
  notificationModalRef!: BsModalRef;
  showModal = false;
  unreadNotificationCount!: number;
  isLoggedIn$!: Observable<boolean>;
  currentUserId!: number;
  subscription!: Subscription;
  showCalenderModel = false;
  isDarkMode = false;

  constructor(private readonly router: Router,
    private readonly splashScreenService: SplashScreenService,
    private readonly authService: AuthService,
    private readonly cdf: ChangeDetectorRef,
    private readonly accountService: AccountService,
    public readonly navigationService: NavigationService,
    public readonly modalService: ModalService,
    public readonly primengConfig: PrimeNGConfig,
    public readonly messagingService: MessagingService,
    private readonly menuConfigService: MenuConfigService,
    private readonly themeService: ThemeService
  ) {
    super();
  }

  ngOnInit(): void {
    this.isLoggedIn$ = this.authService.isLoggedIn$;
    this.subscribeToTheme();
    this.getCurrentUser();
    this.setActiveRoute();
    this.getAccountDetails();
    this.refreshUnreadNotificationCount();
    this.menuConfigService.modifyMenuConfig(MenuConfig);
    this.menuConfigAccordingToPermission();
  }

  onClickLogOut(): void {
    if (this.currentUser?.id) {
      const tokenIdModel: FirebaseRegistrationParams = {
        recipientId: this.currentUser.id.toString(),
        token: localStorage.getItem(this.constants.firebase.registrationToken) as string
      }
      this.messagingService.unregisterToken(tokenIdModel).pipe(takeUntil(this.destroy$)).subscribe();
      this.authService.logOut();
      const { root, login } = ROUTER_UTILS.config.auth;
      this.router.navigate(['/', root, login]);
    }
  }

  onCloseClick() {
    this.showModal = false;
  }

  getAccountDetails(): void {
    this.subscription = this.isLoggedIn$.subscribe((isLoggedIn: boolean) => {
      if (isLoggedIn) {
        this.accountService.getCurrentUser(true).pipe(takeUntil(this.destroy$)).subscribe((user: any) => {
          this.currentUserId = user.id;
          this.cdf.detectChanges();
          this.getNotificationCount();
        });
      }
    });
  }


  async getCurrentUser(): Promise<void> {
    if (!this.authService.currentUser) {
      this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(res => {

        this.currentUser = res;
        this.cdf.detectChanges();
      });
    }
    this.authService.getCurrentUser$().pipe(takeUntil(this.destroy$)).subscribe(res => {
      this.currentUser = res;
      this.cdf.detectChanges();
    });
  }

  openNotifications(template: TemplateRef<unknown>): void {
    this.showModal = true;
    this.notificationModalRef = this.modalService.showModal(template, { ...this.constants.globalModalConfig, ...this.constants.sidebarModalConfig });
  }

  refreshUnreadNotificationCount(): void {
    this.messagingService.unreadNotificationCount.asObservable().subscribe(() => {
      this.getNotificationCount();
    });
  }

  getNotificationCount(): void {
    this.messagingService.getNotificationCount(this.currentUserId).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: UnreadNotificationCount) => {
        this.unreadNotificationCount = res.unreadCount;
        this.cdf.detectChanges();
      }
    });
  }

  decrementUnreadCount(): void {
    if (this.unreadNotificationCount) {
      this.unreadNotificationCount -= 1;
    }
  }

  setNotificationUnreadCountToZero(): void {
    if (this.unreadNotificationCount) {
      this.unreadNotificationCount = 0;
    }
  }


  setActiveRoute(): void {
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.currentActiveParentRoute = DropdownRouteName.NO_PARENT_ROUTE;
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.administration.root}${ROUTER_UTILS.config.base.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.ADMIN;
        }
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.pipeline.root}${ROUTER_UTILS.config.base.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.PIPELINE;
        }
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.transport.root}${ROUTER_UTILS.config.base.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.TRANSPORT;
        }
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.crm.root}${ROUTER_UTILS.config.base.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.CRM;
        }
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.administration.root}/${ROUTER_UTILS.config.administration.publicPageConfig.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.PUBLIC_PAGE_CONFIG;
        }
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.administration.root}/${ROUTER_UTILS.config.administration.specificationConfig.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.SPECIFICATION_CONFIG;
        }
        this.cdf.detectChanges();
        // add other conditions here when more parent routes come.
      }
    });
  }

  toggleCalenderModel() {
    this.showCalenderModel = !this.showCalenderModel;
  }

  openCalender() {
    this.showCalenderModel = true;
  }

  menuConfigAccordingToPermission() {
    this.menuConfig = this.menuConfigService.getMenuConfig();
  }

  switchMode(bool: boolean) {
    this.themeChanged = true;
    this.themeService.changeTheme(bool);
    setTimeout(() => {
      this.themeChanged = false;
      this.cdf.detectChanges();
    }, 500);
  }

  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        this.selectedTheme = theme;
        if (this.selectedTheme === THEMES.DARK) {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
        this.cdf.detectChanges();
      });
  }
}
