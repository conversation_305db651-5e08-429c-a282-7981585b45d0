import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { DriverScheduleListComponent } from "./driver-schedule-list/driver-schedule-list.component";
import { DriverScheduleComponent } from "./driver-schedule.component";

const routes: Routes = [
  {
    path: '',
    component: DriverScheduleComponent,
    title: 'Skeye - Driver schedule board',
    children: [
      {
        path: '',
        component: DriverScheduleListComponent,
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DriverScheduleRoutingModule { }
