<div class="auth-wrapper">
  <div class="layer"></div>
  <div class="company-logo">
    <img [src]="constants.applicationLogoUrl" class="card-img-top img-fluid" alt="Company Icon">
  </div>
  <div class="card col-md-8 col-lg-6 col-xl-4 col-12 p-md-4 p-2">
    <div class="card-body mt-2">
      <ng-container
        [ngTemplateOutlet]="passwordChanged ? forgotPasswordInstructionSentTemplate: forgotPasswordInitTemplate">
      </ng-container>
    </div>
  </div>
</div>

<ng-template #forgotPasswordInitTemplate>
  <h5 class="card-title">Reset Password</h5>
  <p class="card-text">Please enter your new password.</p>
  <form class="form" [formGroup]="forgotPasswordFormGroup" (ngSubmit)="onForgotPasswordFinish()">
    <div class="mt-4">
      <div class="login-input-group">
        <label>New Password</label>
        <input formControlName="newPassword" type="password" appPasswordEye class="form-control password"
          placeholder="Enter your new password" />
        <app-error-messages [control]="forgotPasswordFormGroup.controls?.newPassword"></app-error-messages>
      </div>
      <div class="mt-4 login-input-group">
        <label>Confirm Password</label>
        <input formControlName="confirmPassword" type="password" appPasswordEye class="form-control password"
          placeholder="Enter your new password again" />
        <app-error-messages [control]="forgotPasswordFormGroup.controls?.confirmPassword"></app-error-messages>
      </div>
    </div>
    <button class="btn btn-primary mt-5 mb-2 w-100" type="submit" [disabled]="forgotPasswordFormGroup?.invalid"
      appShowLoaderOnApiCall>
      <span>Change password</span>
    </button>
    <ng-container [ngTemplateOutlet]="backToLoginLink"></ng-container>
  </form>
</ng-template>

<ng-template #forgotPasswordInstructionSentTemplate>
  <h5 class="card-title email-icon d-flex justify-content-center">
    <fa-icon [icon]="faIcons.faCheckCircle" [size]="'3x'"></fa-icon>
  </h5>
  <p class="card-text mt-4">Your password has been changed successfully. You can now login with your new password.</p>
  <ng-container [ngTemplateOutlet]="backToLoginBtn"></ng-container>
</ng-template>

<ng-template #backToLoginLink>
  <div>
    <a class="forgot" href="javascript:;" [routerLink]="[path.base.root, path.auth.root]">Back to Login</a>
  </div>
</ng-template>

<ng-template #backToLoginBtn>
  <div>
    <button class="btn btn-primary mt-3 mb-2 w-100" [routerLink]="[path.base.root, path.auth.root]" type="submit">
      <span>Login now</span>
    </button>
  </div>
</ng-template>
