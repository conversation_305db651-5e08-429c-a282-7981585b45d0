import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { AssociationsUnits, InventoryListFilter, InventoryListItem, Tabs } from '@pages/inventory/models';
import { FinancialService } from '@pages/inventory/services/financial.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { AddHoldDetailsComponent } from './add-hold-details/add-hold-details.component';
import { AddNewSaleComponent } from './add-new-sale/add-new-sale.component';
import { FinancialWrapperComponent } from './financial-wrapper/financial-wrapper.component';
import { FinancialComponent } from './financial/financial.component';
import { InventoryAssociationsComponent } from './inventory-associations/inventory-associations.component';
import { InventoryConditionComponent } from './inventory-condition/inventory-condition.component';
import { InventoryDocumentsComponent } from './inventory-documents/inventory-documents.component';
import { InventoryGeneralInfoComponent } from './inventory-general-tab/inventory-general-info/inventory-general-info.component';
import { InventoryNotesComponent } from './inventory-notes/inventory-notes.component';
import { InventoryPhotosComponent } from './inventory-photos/inventory-photos.component';
import { InventorySpecificationComponent } from './inventory-specification/inventory-specification.component';

@Component({
  selector: 'app-inventory-add',
  templateUrl: './inventory-add.component.html',
  styleUrls: ['./inventory-add.component.scss']
})
export class InventoryAddComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Unit';
  inventoryFormGroup!: FormGroup;
  hasDataBeenModified = false;
  filterParams: InventoryListFilter = new InventoryListFilter();
  activeIndex = 0;
  selectedCategoryId!: number | null;
  updatedUnitsList: Array<AssociationsUnits> = [];
  redirectUrl!: string;
  salePrice = 0;
  @Input() activeIndexes!: number;
  @Input() showSoldTabs!: boolean;
  @Input() showHoldTabs!: boolean;

  @Input() categoriesToShow!: Array<IdNameModel>;
  displayBasic = false;
  message = "";
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Input() isEditMode!: boolean;
  @Input() isViewMode!: boolean;
  @Input() showAssociation!: boolean;
  showSoldVisible = false;
  showHoldVisible = false;
  isRetailAskingPrice = false;
  @Output() showSoldTab: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() showHoldTab: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() showLoader = new EventEmitter<boolean>();
  @Output() isPrevious: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onTabChange: EventEmitter<number> = new EventEmitter<number>();
  @ViewChild(InventoryGeneralInfoComponent) inventoryGeneralInfoComponent!: InventoryGeneralInfoComponent;
  @ViewChild(InventorySpecificationComponent) inventorySpecificationComponent!: InventorySpecificationComponent;
  @ViewChild(InventoryPhotosComponent) inventoryPhotosComponent!: InventoryPhotosComponent;
  @ViewChild(InventoryConditionComponent) inventoryConditionComponent!: InventoryConditionComponent;
  @ViewChild(InventoryNotesComponent) inventoryNotesComponent!: InventoryNotesComponent;
  @ViewChild(InventoryDocumentsComponent) inventoryDocumentsComponent!: InventoryDocumentsComponent;
  @ViewChild(FinancialComponent) financialComponent!: FinancialComponent;
  @ViewChild(FinancialWrapperComponent) financialWrapperComponent!: FinancialWrapperComponent;
  @ViewChild(AddNewSaleComponent) addNewSaleComponent!: AddNewSaleComponent;
  @ViewChild(AddHoldDetailsComponent) addHoldDetailsComponent!: AddHoldDetailsComponent;
  @ViewChild(InventoryAssociationsComponent) inventoryAssociationsComponent!: InventoryAssociationsComponent;
  @Output() isEditTitle: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() addedUnitId = new EventEmitter<number>();

  constructor(
    private readonly inventoryService: InventoryService,
    private readonly financialService: FinancialService,
    private readonly activeRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly authService: AuthService,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService,
  ) {
    super();
  }

  ngOnInit(): void {
    if (this.showSoldTabs && this.activeIndexes) {
      this.activeIndex = Tabs.SALEINFORMATION;
    }
    if (this.showHoldTabs && this.activeIndexes) {
      this.activeIndex = Tabs.HOLD;
    }
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.commentId) {
          this.activeIndex = Tabs.COMMUNICATION;
        }
        if (params?.returnUrl) {
          this.redirectUrl = params?.returnUrl;
        }
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.inventoryInfo?.currentValue || changes.inventoryIncomingInfo?.currentValue) {
      this.title = 'Edit Unit';
      this.isEditMode = true;
    }
  }

  onCancel(hasDataBeenModified: any): void {
    if (this.showSoldTabs && this.activeIndexes) {
      this.addNewSaleComponent.onSubmit();
    }
    if (this.showHoldTabs && this.activeIndexes) {
      this.addHoldDetailsComponent.onSubmit();
    }
    this.inventoryService.getAssociateId$().subscribe(res => {
      this.inventoryAssociationsComponent?.onSubmit(res);
    });
    if (this.isEditMode || hasDataBeenModified) {
      let unitId;
      if (hasDataBeenModified) {
        this.inventoryService.getUnitId$().pipe(takeUntil(this.destroy$)).subscribe(res => {
          unitId = res;
        });
      }
      this.inventorySpecificationComponent.onSubmit(!this.isEditMode ? unitId : undefined).then(() => {
        if (this.isEditMode) {
          if (this.isFinancialPermission()) {
            this.financialWrapperComponent.onFincancialSubmit();
          }
        }
        if (hasDataBeenModified) {
          this.financialService.geUnitIdByFinancialInfo([Number(unitId)]).pipe(takeUntil(this.destroy$)).subscribe();
          this.inventoryPhotosComponent.onSubmit(true, unitId);
          const length = this.inventoryConditionComponent.conditionFormGroup.value.inventoryCondition.filter((e: any) => {
            return e.condition;
          })
          if (length) {
            this.inventoryConditionComponent.onSubmit();
          }

          if (this.inventoryNotesComponent.internalNotesDetails || this.inventoryNotesComponent.externalNotesDetails) {
            this.inventoryNotesComponent.onSubmit(true, unitId);
          }
          if (this.inventoryDocumentsComponent.inventoryDocumentList.length) {
            this.inventoryDocumentsComponent.onSubmit();
          }
          if (this.redirectUrl) {
            this.router.navigateByUrl(this.redirectUrl);
          }
          this.onClose.emit(true);
          this.addedUnitId.emit(unitId)
        } else {
          this.onClose.emit(this.hasDataBeenModified);
          if (this.redirectUrl) {
            this.router.navigateByUrl(this.redirectUrl);
          }
        }
      }).catch(error => {
        this.toasterService.error(MESSAGES.specificationFailed);
      });;
    }
  }

  tabChange(event: any) {
    this.onTabChange.next(this.activeIndex);
    if (this.activeIndex === 6) {
      this.isPrevious.next(false);
    }
  }

  onSubmit() {
    if (this.isEditMode) {
      this.isViewMode = false;
      this.isEditTitle.emit(true);
    }
    if (this.inventorySpecificationComponent?.inventoryForm?.invalid) {
      this.inventorySpecificationComponent.inventoryForm.form.markAllAsTouched();
      this.activeIndex = 1;
      return;
    }
    this.checkGeneralTabFrom();
  }

  private checkGeneralTabFrom() {

    if (this.inventoryGeneralInfoComponent.generalInfoFormGroup.invalid || this.inventoryGeneralInfoComponent.lotLocationFormGroup.invalid) {
      this.displayBasic = true;
      this.inventoryGeneralInfoComponent.generalInfoFormGroup.markAllAsTouched();
      this.message = "Please add required details in General tab before saving.";
      return;
    }
    if (this.showSoldVisible || this.showSoldTabs) {
      if (this.addNewSaleComponent?.saleFormGroup?.invalid) {
        this.displayBasic = true;
        this.addNewSaleComponent.saleFormGroup.markAllAsTouched();
        this.message = "Please add required details in Sold tab before saving.";
        return;
      }
    }
    if (this.showHoldVisible || this.showHoldTabs) {
      if (this.addHoldDetailsComponent?.holdDetailsFormGroup?.invalid) {
        this.displayBasic = true;
        this.addHoldDetailsComponent.holdDetailsFormGroup.markAllAsTouched();
        this.message = "Please add required details in Hold tab before saving.";
        return;
      }
    }
    if (this.financialWrapperComponent?.financialComponent?.financialFormGroup?.invalid && this.isFinancialPermission()) {
      this.displayBasic = true;
      this.financialWrapperComponent?.financialComponent?.financialFormGroup?.markAllAsTouched();
      this.message = "Please add required details in financial tab before saving.";
      return;
    } else if (this.financialWrapperComponent?.financialComponent?.financialFormGroup.valid && (this.inventoryInfo?.id || this.inventoryIncomingInfo?.unitId) && this.isFinancialPermission()) {
      const hasNoRetailAskingPrice = this.updatedUnitsList.some((unit) => !unit.retailAskingPrice);
      if (this.updatedUnitsList.length && (!Number(this.financialWrapperComponent?.financialComponent?.financialFormGroup?.get('retailAskingPrice')?.value) || this.isRetailAskingPrice || hasNoRetailAskingPrice)) {
        this.checkRetailPriceStatus();
      } else {
        this.inventoryGeneralInfoComponent.onSubmit();
        this.salePrice = Number(this.addNewSaleComponent?.saleFormGroup?.value?.sellPrice);
        this.addNewSaleComponent?.onSubmit();
        this.addHoldDetailsComponent?.onSubmit();
      }
    }
    else {
      this.inventoryGeneralInfoComponent.onSubmit();
      this.addHoldDetailsComponent.onSubmit();
      this.salePrice = Number(this.addNewSaleComponent?.saleFormGroup?.value?.sellPrice);
    }
  }

  getUpdatedUnitsList($event: any) {
    this.updatedUnitsList = $event;
  }

  setLoaderStatus(bool: boolean) {
    this.showLoader.emit(bool);
  }

  showTab(data: boolean): void {
    if (data) {
      this.showSoldTab.emit(data);
      this.showSoldVisible = data;
      setTimeout(() => {
        this.activeIndex = Tabs.SALEINFORMATION;
      }, 100);
    }
    else {
      this.showSoldTabs = false;
      this.showSoldVisible = data;
    }
  }

  showOnHoldTab(data: boolean): void {
    if (data) {
      this.showHoldTab.emit(data);
      this.showHoldVisible = data;
      setTimeout(() => {
        this.activeIndex = Tabs.HOLD;
      }, 100);
    }
    else {
      this.showHoldTabs = false;
      this.showHoldVisible = data;
    }
  }

  setSelectedCategoryId(categoryId: number | null) {
    this.selectedCategoryId = categoryId;
  }

  isFinancialPermission() {
    return this.utils.hasSubModulePermission(
      this.authService.getRoleInfo().privilegeActionResponseDTOs,
      [this.permissionActions.VIEW_FINANCIAL]
    )
  }

  checkRetailPriceStatus(): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      header: 'Confirmation',
      message: MESSAGES.retailAskingPriceMessage,
      acceptLabel: "Save & Close",
      rejectLabel: 'Yes',
      icon: icons.triangle,
      accept: () => {
        this.inventoryGeneralInfoComponent.onSubmit();
        this.salePrice = Number(this.addNewSaleComponent?.saleFormGroup?.value?.sellPrice);
        this.addNewSaleComponent?.onSubmit();
      },
      reject: () => {
        return;
      }
    });
  }

  isAssociateUnitRetailPrice(event: boolean): void {
    this.isRetailAskingPrice = event;
  }
}
