import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { NgxMaskModule } from 'ngx-mask';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from 'src/app/@shared/pipes/pipes.module';
import { RoleAddUpdateComponent } from './role-add-update/role-add-update.component';
import { RoleListComponent } from './role-list/role-list.component';
import { RolesComponent } from './roles.component';

const routes: Routes = [
  {
    path: '',
    component: RolesComponent,
    title: 'Skeye - Roles',
    children: [
      {
        path: '',
        component: RoleListComponent,
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  declarations: [
    RolesComponent,
    RoleListComponent,
    RoleAddUpdateComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedComponentsModule,
    TabsModule.forRoot(),
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    NgxMaskModule.forRoot(),
    PipesModule,
    CheckboxModule
  ]
})
export class RolesModule { }
