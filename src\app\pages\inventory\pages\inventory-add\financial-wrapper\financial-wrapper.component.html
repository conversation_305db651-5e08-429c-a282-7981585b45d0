<div class="">
  <div class="row">
    <p-tabView [(activeIndex)]="activeIndex" [scrollable]="true" appToggleMoreLinksButton *ngIf="associatedUnits?.length">
      <p-tabPanel *ngFor="let tab of associatedUnits; let i = index" [header]="tab.stockNumber">
        <app-financial
          *ngIf="financialInformationResponse[i]"
          [inventoryInfo]="inventoryInfo"
          [inventoryIncomingInfo]="inventoryIncomingInfo"
          [isViewMode]="isViewMode"
          [salePrice]="salePrice"
          (onFincancialSubmit)="onFincancialSubmit()"
          [activeAssociatedUnit]="associatedUnits[i]"
          [isPrimaryInventory]="inventoryInfo?.generalInformation?.id === tab.id || inventoryIncomingInfo?.unit?.generalInformation?.id === tab.id"
          [financialInformation]="financialInformationResponse[i]"
          (expensessPopupClosed)="handleExpensesPopupClosed()"
        >
        </app-financial>
      </p-tabPanel>
    </p-tabView>
    <div class="row" *ngIf="!associatedUnits?.length && (inventoryIncomingInfo?.unit?.generalInformation || inventoryInfo?.generalInformation)">
      <p-tabView [(activeIndex)]="activeIndex" [scrollable]="true" appToggleMoreLinksButton>
        <p-tabPanel
          [header]="
            inventoryIncomingInfo?.unit?.generalInformation
              ? inventoryIncomingInfo?.unit?.generalInformation?.stockNumber + ''
              : inventoryInfo?.generalInformation?.stockNumber + ''
          "
        >
          <app-financial
            *ngIf="financialInformationResponse[0]"
            [inventoryInfo]="inventoryInfo"
            [inventoryIncomingInfo]="inventoryIncomingInfo"
            [isViewMode]="isViewMode"
            [salePrice]="salePrice"
            (onFincancialSubmit)="onFincancialSubmit()"
            [isPrimaryInventory]="!!(inventoryInfo?.generalInformation?.id || inventoryIncomingInfo?.unit?.generalInformation?.id)"
            [financialInformation]="financialInformationResponse[0]"
            (expensessPopupClosed)="handleExpensesPopupClosed()"
          >
          </app-financial>
        </p-tabPanel>
      </p-tabView>
    </div>
  </div>
</div>
