import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { MessagesModule } from 'primeng/messages';
import { MultiSelectModule } from 'primeng/multiselect';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from 'src/app/@shared/pipes/pipes.module';
import { ColumnDropdownComponent } from './column-dropdown/column-dropdown.component';


@NgModule({
  declarations: [
    ColumnDropdownComponent,
  ],
  imports: [
    CommonModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    DragDropModule,
    CheckboxModule,
    FormsModule,
    InputTextModule,
    ReactiveFormsModule,
    TabViewModule,
    PipesModule,
    DropdownModule,
    MultiSelectModule,
    MessagesModule
  ],
  providers: [],
  exports: [
    ColumnDropdownComponent
  ],

})
export class ColumnDropdownModule {

}
