import { GenericFilterParams } from "src/app/@shared/models";

export class DocumentListFilter extends GenericFilterParams {
  deleted = false
}

export enum SharingType {
  PRIVATE = 'PRIVATE',
  PUBLIC = 'PUBLIC'
}

export interface DocumentListItem {
  createdDate?: Date | string | null,
  description: string | null,
  documentType?: string,
  documentUrl?: string,
  fileName: string,
  fileSize: string | null,
  folderName?: string | null,
  id?: number | null,
  sharing: SharingType,
  title: string,
  unitId: number | null,
  url: string | null,
  isEditable?: boolean,
  uploadDate?: Date | string | null,
  file?: File
}

export interface CRMContactDocumentListItem {
  id?: number | null,
  createdDate?: Date | string | null,
  fileName: string,
  fileSize: string | null,
  title: string,
  url: string | null,
  crmContactId: number | null,
  uploadDate?: Date | string | null,
  file?: File,
  documentUrl?: string,
}
