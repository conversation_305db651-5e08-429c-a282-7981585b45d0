<app-page-header class="crm-customer-contact-lead">
  <div headerActionBtn>
    <button
      class="btn btn-primary left"
      (click)="onAdd()"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      type="button"
      *appHasPermission="[permissionActions.CREATE_CUSTOMER_LEAD]"
    >
      <span class="m-l-20 show-label">Add New Customer Lead</span>
    </button>
  </div>
</app-page-header>

<div class="card tabs stock-truck-list">
  <div class="tab-content">
    <p-table
      class="no-column-selection"
      [value]="crmCustomerLeadList"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      [scrollable]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th scope="col" pFrozenColumn>#Lead Id</th>
          <th scope="col">Unit Type</th>
          <th scope="col">Make</th>
          <th scope="col">Year</th>
          <th scope="col">Model</th>
          <th scope="col">Sales Person</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td class="view-task" pFrozenColumn>
            {{ rowData?.id }}
          </td>
          <td>
            {{ rowData?.unitType?.name }}
          </td>
          <td>
            {{ rowData?.makes[0]?.makeName }}
          </td>
          <td>
            <span *ngIf="rowData?.minYear">{{ rowData?.minYear }}</span> <span *ngIf="rowData?.minYear && rowData?.maxYear" class="mx-1">-</span>
            <span *ngIf="rowData?.maxYear">{{ rowData?.maxYear }}</span>
          </td>
          <td>
            {{ rowData?.unitModels[0]?.unitModelName }}
          </td>
          <td>
            {{ rowData?.reporter?.name }}
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="6" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
  </div>
</div>

<p-sidebar
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-customer-add-extended (onClose)="onClose($event)" *ngIf="showCreateModal" [crmId]="crmContactInfo?.id"> </app-crm-customer-add-extended>
</p-sidebar>
