<div class="inventory-spacification">
  <form #inventoryForm="ngForm" name="inventoryForm">
    <section>
      <p-accordion class="nested-accordion" [multiple]="true">
        <div class="row">
          <ng-container *ngIf="inventorySpecificationForm?.length"></ng-container>
          <div class="col-12" *ngFor="let specification of inventorySpecificationForm">
            <ng-container *ngIf="specification.fields.length">
              <p-accordionTab [(selected)]="specification.isShow">
                <ng-template pTemplate="header">
                  <div class="accordion-header" [ngClass]="{ active: specification.isShow }">
                    <span>
                      {{ specification.sectionName }}
                      <button
                        class="btn btn-primary add-btn"
                        type="button"
                        *ngIf="!isViewMode && specification?.multiple"
                        (click)="createDuplicationSection($event, specification)"
                        [appImageIconSrc]="constants.staticImages.icons.add"
                      ></button>
                      <button
                        class="btn btn-primary add-btn"
                        type="button"
                        *ngIf="!isViewMode && !specification?.multiple && specification.parentName"
                        (click)="removeDuplicationSection($event, specification)"
                        [appImageIconSrc]="constants.staticImages.icons.minus"
                      ></button>
                    </span>
                    <em class="pi" [ngClass]="specification.isShow ? 'pi-angle-up' : 'pi-angle-down'"></em>
                  </div>
                </ng-template>
                <ng-template pTemplate="content">
                  <div class="row">
                    <div [ngClass]="{ 'col-12': true, ' col-sm-6 col-md-6 col-lg-3': field.dataType !== 'TextBox' }" *ngFor="let field of specification.fields">
                      <label [ngClass]="{ required: field.isRequired }">{{ field.label }}</label>
                      <ng-container *ngIf="field.dataType === 'DropDown'">
                        <button
                          class="btn btn-primary add-option-btn"
                          type="button"
                          [appImageIconSrc]="constants.staticImages.icons.add"
                          *ngIf="!isViewMode && !specification?.parentName"
                          (click)="openAddOptionsToSpecificationFieldsDialog(specification, field)"
                        ></button>
                        <p-dropdown
                          appPreventClearFilter
                          appendTo="body"
                          [name]="field.for"
                          [options]="field.options"
                          [(ngModel)]="field.value"
                          optionLabel="name"
                          [showClear]="true"
                          optionValue="id"
                          [filter]="true"
                          filterBy="name"
                          [disabled]="isViewMode"
                          [placeholder]="field.placeholder ? field.placeholder : ''"
                          [required]="field.isRequired"
                          #dropDownField="ngModel"
                        >
                          <ng-template pTemplate="empty">
                            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: false, data: field.for }"></ng-container>
                          </ng-template>
                          <ng-template let-item pTemplate="item">
                            <span>{{ item.name }}</span>
                          </ng-template>
                        </p-dropdown>
                        <small class="text-danger" *ngIf="!dropDownField.valid && dropDownField.touched"> This field is required </small>
                      </ng-container>
                      <ng-container *ngIf="field.dataType === 'TextField'">
                        <input
                          class="form-control"
                          type="text"
                          [name]="field.for"
                          [placeholder]="field.placeholder ? field.placeholder : ''"
                          [required]="field.isRequired"
                          [(ngModel)]="field.value"
                          #textFeild="ngModel"
                          [disabled]="isViewMode"
                        />
                        <small class="text-danger" *ngIf="!textFeild.valid && textFeild.touched"> This field is required </small>
                      </ng-container>
                      <ng-container *ngIf="field.dataType === 'Number'">
                        <p-inputNumber
                          styleClass="w-100"
                          inputStyleClass="form-control"
                          [name]="field.for"
                          [placeholder]="field.placeholder ? field.placeholder : ''"
                          [(ngModel)]="field.value"
                          [required]="field.isRequired"
                          #numberField="ngModel"
                          [disabled]="isViewMode"
                          mode="decimal"
                          [minFractionDigits]="0"
                          [maxFractionDigits]="10"
                        ></p-inputNumber>
                        <small class="text-danger" *ngIf="!numberField.valid && numberField.touched"> This field is required </small>
                      </ng-container>
                      <ng-container *ngIf="field.dataType === 'TextBox'">
                        <textarea
                          [placeholder]="field.placeholder ? field.placeholder : ''"
                          [(ngModel)]="field.value"
                          rows="3"
                          [name]="field.for"
                          [required]="field.isRequired"
                          [disabled]="isViewMode"
                          #textAreaField="ngModel"
                        ></textarea>
                        <small class="text-danger" *ngIf="!textAreaField.valid && textAreaField.touched"> This field is required </small>
                      </ng-container>
                    </div>
                  </div>
                </ng-template>
              </p-accordionTab>
            </ng-container>
          </div>
        </div>
      </p-accordion>
    </section>
  </form>
</div>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="displayAddOptionsToSpecificationFieldDialog"
  position="right"
  (onHide)="displayAddOptionsToSpecificationFieldDialog = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-specification-field-option
    (onClose)="onAddEditPopupClose()"
    [categoryId]="selectedCategoryId"
    [selectedSpecification]="selectedSpecification"
    [selectedSpecificationField]="selectedSpecificationField"
    *ngIf="displayAddOptionsToSpecificationFieldDialog"
    [originalMasterSpecification]="originalMasterSpecification"
    (updateSpecificationFieldOptions)="updateSpecificationFieldOptions($event)"
  >
  </app-add-specification-field-option>
</p-sidebar>
