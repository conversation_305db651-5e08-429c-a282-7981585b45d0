<app-page-header class="crm-customer-contact-lead">
  <div headerActionBtn class="header-content d-flex">
    <div class="filter-wrapper">
      <span class="show-label"> Filter By: </span>
      <p-dropdown appendTo="body" [options]="filterReminderOptions" placeholder="Filter Reminder by" [(ngModel)]="filterBy" (onChange)="onFilterChange($event?.value)">
      </p-dropdown>
    </div>
    <button class="btn btn-primary left" (click)="showCreateModal = true" [disabled]="isViewMode" [appImageIconSrc]="constants.staticImages.icons.addNew" type="button">
      <span class="m-l-20 show-label">Add New Reminder</span>
    </button>
  </div>
</app-page-header>
<div class="card tabs crm-contact-previously-owned" *ngIf="!isLoading; else pageLoaderTemplate">
  <div class="tab-content">
    <ng-container>
      <p-table
        class="no-column-selection"
        [value]="reminders"
        responsiveLayout="stack"
        sortMode="single"
        [customSort]="true"
        [lazy]="true"
        [reorderableColumns]="true"
        [rowHover]="true"
        [loading]="isLoading"
        [scrollable]="true"
      >
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th scope="col" pFrozenColumn>Reminder For</th>
            <th scope="col">Scheduled On</th>
            <th scope="col">Scheduled After</th>
            <th scope="col">Status</th>
            <th scope="col">Notes</th>
            <th scope="col" *ngIf="filterBy === ActiveReminders">Recurrence Reminder</th>
            <th scope="col" class="small-col text-center" *ngIf="filterBy === ActiveReminders">Actions</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
          <tr>
            <td class="view-reminder" pFrozenColumn>
              <span (click)="onEdit(rowData, true)">
                {{ rowData?.remindTo?.name }}
              </span>
            </td>
            <td>
              {{ rowData?.reminderTime | date : constants.dateFormat }}
            </td>
            <td>
              {{ getReminderScheduleAfter(rowData?.scheduleAfter) }}
            </td>
            <td>
              {{ rowData?.reminderStatus }}
            </td>
            <td>
              {{ rowData?.notes }}
            </td>
            <td *ngIf="filterBy === ActiveReminders">
              <ui-switch
                [(ngModel)]="rowData.reoccurring"
                *ngIf="rowData?.scheduleAfter !== '0'"
                [loading]="selectedReminder?.id === rowData.id && isArchiveInProgress"
                (change)="toggleReminder(rowData, $event)"
              >
                <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedReminder?.id === rowData.id"></fa-icon>
              </ui-switch>
            </td>
            <td *ngIf="filterBy === ActiveReminders" class="actions">
              <div class="actions-content">
                <img [src]="constants.staticImages.icons.edit" (click)="onEdit(rowData)" alt="" class="action-btn" />
                <img [src]="constants.staticImages.icons.deleteIcon" alt="" class="action-btn ms-2" (click)="onDelete(rowData, $event)" />
              </div>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <td [colSpan]="6" class="no-data">No data to display</td>
        </ng-template>
      </p-table>
    </ng-container>
  </div>
</div>
<p-sidebar
  [(visible)]="showCreateModal"
  [fullScreen]="false"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  position="right"
  styleClass="p-sidebar-md"
  [showCloseIcon]="false"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-contact-reminder-add
    [accountRepList]="accountRepList"
    [selectedReminder]="selectedReminder"
    [isViewMode]="isViewMode"
    [customerId]="customerId"
    [reminderFor]="reminderFor"
    (onClose)="onClose()"
    *ngIf="showCreateModal"
    (addNewReminder)="addNewReminder($event)"
    (editNewReminder)="editNewReminder($event)"
  >
  </app-crm-contact-reminder-add>
</p-sidebar>

<ng-template #pageLoaderTemplate>
  <div class="no-data mb-3">
    <em class="pi pi-spin pi-spinner"></em>
  </div>
</ng-template>
