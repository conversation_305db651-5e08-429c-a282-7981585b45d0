<header class="fixed-header">
  <div class="logo">
    <img [src]="constants.applicationLogoUrl" class="img-fluid" [routerLink]="[path.base.dashboard]" alt="Company Logo" />
  </div>
  <ul class="nav nav-pills">
    <ng-container *ngFor="let menu of menuConfig">
      <li class="nav-item" *ngIf="!menu?.children?.length">
        <a class="nav-link" [routerLink]="menu.routerLink" routerLinkActive="active" [appImageIconSrc]="menu.iconPath" [hoverImgSrc]="menu.hoverIconPath">
          {{ menu?.name }}
        </a>
      </li>

      <!-- For children -->
      <li class="nav-item dropdown" dropdown *ngIf="menu.children?.length">
        <a
          class="nav-link dropdown-toggle"
          id="profile"
          dropdownToggle
          type="button"
          aria-controls="dropdown-animated"
          role="button"
          aria-expanded="false"
          [appImageIconSrc]="menu.iconPath"
          [hoverImgSrc]="menu.hoverIconPath"
          [ngClass]="{ active: currentActiveParentRoute === menu.moduleKey }"
        >
          {{ menu?.name }}
        </a>
        <ul class="dropdown-menu" *dropdownMenu class="dropdown-menu" role="menu" aria-labelledby="button-animated" id="dropdown-animated">
          <li *ngFor="let childMenu of menu.children">
            <ng-container *ngIf="!childMenu?.children?.length">
              <a class="dropdown-item" href="javascript:;" [routerLink]="childMenu.routerLink" routerLinkActive="active">{{ childMenu.name }} </a>
            </ng-container>
            <ng-container *ngIf="childMenu?.children?.length">
              <a class="dropdown-item sub-parent" href="javascript:;" [ngClass]="{ active: currentActiveParentRoute === childMenu.moduleKey }" aria-expanded="false">
                {{ childMenu?.name }} <fa-icon [icon]="faIcons.faCaretRight"></fa-icon>
              </a>
              <ul class="dropdown-menu dropdown-submenu" role="menu" aria-labelledby="button-animated" id="dropdown-animated">
                <li *ngFor="let subChild of childMenu?.children">
                  <a
                    class="dropdown-item"
                    href="javascript:;"
                    [routerLink]="subChild.routerLink"
                    [ngClass]="{ active: currentActiveParentRoute === subChild.moduleKey }"
                    routerLinkActive="active"
                    >{{ subChild.name }}
                  </a>
                </li>
              </ul>
            </ng-container>
          </li>
        </ul>
      </li>
    </ng-container>
  </ul>
  <ul class="nav nav-pills userprofile">
    <li class="nav-item dropdown profile d-flex" dropdown>
      <!-- NOTE: The following modules are currently disabled per client's request. -->
      <!-- <a class="nav-link" [routerLink]="rightMenuConfig.routerLink" routerLinkActive="active" *appHasPermission="[permissionActions.VIEW_CALENDAR]">
        {{ rightMenuConfig?.name }}
      </a> -->
      <a class="nav-link" [appImageIconSrc]="constants.staticImages.icons.bellNotification" (click)="getNotificationCount(); showModal = true"></a>
      <span class="badge" *ngIf="unreadNotificationCount">{{ unreadNotificationCount }}</span>
      <div class="position-relative">
        <a class="nav-link dropdown-toggle" id="profile" dropdownToggle type="button" aria-controls="dropdown-animated" role="button" aria-expanded="false">
          <span class="user-name" [pTooltip]="currentUser?.firstName ? (currentUser!.firstName | titlecase) : ''" tooltipPosition="bottom">
            {{ currentUser?.firstName | titlecase }}</span
          >
        </a>
        <span class="initials">{{ utils.getInitials(currentUser?.firstName, currentUser?.lastName) | uppercase }}</span>
      </div>
      <ul class="dropdown-menu" *dropdownMenu class="dropdown-menu dropdown-menu-right" role="menu" aria-labelledby="profile" id="profile-animated">
        <li>
          <a class="dropdown-item username">
            {{ utils.getFullName(currentUser?.firstName, currentUser?.lastName) | titlecase }}
          </a>
        </li>
        <li>
          <hr class="dropdown-divider" />
        </li>
        <li>
          <a class="dropdown-item" href="javascript:;" (click)="navigationService.toProfile()" [appImageIconSrc]="constants.staticImages.icons.loginUser">My Profile</a>
        </li>
        <li>
          <a class="dropdown-item" href="javascript:;" (click)="navigationService.toChangePassword()" [appImageIconSrc]="constants.staticImages.icons.lockPassword">
            Change Password</a
          >
        </li>
        <li>
          <div class="d-flex align-items-center w-100 justify-content-around my-2">
            <fa-icon class="theme-icons fs-5 pe-none" [icon]="faIcons.faSun"></fa-icon>
            <ui-switch class="theme-switch" (change)="switchMode($event)" [(ngModel)]="isDarkMode"> </ui-switch>
            <fa-icon class="theme-icons fs-5 pe-none" [icon]="faIcons.faMoon"></fa-icon>
          </div>
        </li>
        <li>
          <hr class="dropdown-divider" />
        </li>
        <li>
          <a class="dropdown-item" (click)="onClickLogOut()"> Logout <fa-icon [icon]="faIcons.faSignOutAlt"></fa-icon> </a>
        </li>
      </ul>
    </li>
  </ul>
</header>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showModal"
  position="right"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-notifications
    *ngIf="showModal"
    (decrementUnreadCount)="decrementUnreadCount()"
    [unreadNotificationCount]="unreadNotificationCount"
    (setNotificationUnreadCountToZero)="setNotificationUnreadCountToZero()"
    (onClose)="onCloseClick()"
  >
  </app-notifications>
</p-sidebar>
<div #splashScreen class="splash-screen" *ngIf="themeChanged">
  <img [src]="constants.applicationLogoUrl" alt="Logo" />
</div>
