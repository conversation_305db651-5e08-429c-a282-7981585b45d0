<div class="modal-title close">
  <h4 class="header-title">{{ pageTitle }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
</div>

<form [formGroup]="specificationGroupFieldFrom" class="specification-form" (ngSubmit)="onSubmit()">
  <div class="category-form content">
    <div class="form-group">
      <label for="categoryName">Category</label>
      <input formControlName="categoryName" type="text" class="form-control" placeholder="Enter category" />
    </div>
    <div class="form-group select-input">
      <label for="unitTypeCategoryId" class="required">Group</label>
      <ng-container *ngIf="specificationList?.masterData?.specification">
        <p-dropdown [options]="specificationList.masterData.specification" placeholder="Select Group" formControlName="parentId" optionLabel="sectionName" optionValue="id">
        </p-dropdown>
        <app-error-messages [control]="specificationGroupFieldFrom?.controls?.parentId"></app-error-messages>
      </ng-container>
    </div>
    <div class="form-group">
      <label for="label" class="required">Label</label>
      <input formControlName="label" type="text" class="form-control" placeholder="Enter field label" (input)="onLabelChange()" />
      <app-error-messages [control]="specificationGroupFieldFrom?.controls?.label"></app-error-messages>
    </div>
    <div class="form-group">
      <label for="label" class="required">Data Type</label>
      <p-dropdown [options]="dataTypeOptions" [disabled]="isEditMode" placeholder="Select Data Type" formControlName="dataType" (onChange)="onDataTypeChange($event)"> </p-dropdown>
      <app-error-messages [control]="specificationGroupFieldFrom?.controls?.dataType"></app-error-messages>
    </div>
    <div *ngIf="specificationGroupFieldFrom?.controls?.dataType?.value === 'DropDown'">
      <label for="label" class="required">Options</label>
      <textarea formControlName="options" class="form-control" placeholder="Enter dropdown option" rows="5"></textarea>
      <small
        class="text-danger error-msg d-block"
        *ngIf="specificationGroupFieldFrom.controls.options?.touched && specificationGroupFieldFrom.controls.options?.hasError('required')"
      >
        This field is required
      </small>
      <small class="bold mb-1 text-secondary">Note: Please enter comma separated values</small>
    </div>
    <div class="form-group">
      <label for="label">Watermark Message</label>
      <input formControlName="placeholder" type="text" class="form-control" placeholder="Enter field placeholder" />
      <app-error-messages [control]="specificationGroupFieldFrom?.controls?.placeholder"></app-error-messages>
    </div>
    <div class="form-group d-flex justify-content-between">
      <div class="d-flex align-items-center">
        <label for="isRequired" class="mr-5 mb-1">Is Required: </label>
        <ui-switch formControlName="isRequired" name="isRequired"> </ui-switch>
      </div>
      <div class="d-flex align-items-center">
        <label for="includePreference" class="mr-5 mb-1">Include Preference: </label>
        <ui-switch formControlName="includePreference" name="includePreference"> </ui-switch>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
    <button class="btn btn-primary d-flex align-items-center" type="submit" [disabled]="isLoading">
      Save
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isLoading"></fa-icon>
    </button>
  </div>
</form>
