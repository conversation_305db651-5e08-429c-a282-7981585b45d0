export enum PermissionActions {
  CREATE_ADVERTISE = 'Create Advertising',
  UPDATE_ADVERTISE = 'Update Advertising',
  DELETE_ADVERTISE = 'Delete Advertising',
  VIEW_ADVERTISE = 'View Advertising',
  VIEW_QUOTE_FORM = 'View Quote Form Config',
  UPDATE_QUOTE_FORM = 'Update Quote Form Config',
  CREATE_USERS = 'Create Users',
  CREATE_DEALERS = 'Create Dealers',
  CREATE_VENDORS = 'Create Vendors',
  CREATE_SUPPLIERS = 'Create Suppliers',
  CREATE_PIPELINE_CONFIG = 'Create Pipeline Config',
  CREATE_CATEGORY = 'Create Category',
  CREATE_UNIT_TYPE = 'Create Unit Type',
  CREATE_MAKE_MODEL = 'Create Make /Model',
  CREATE_SPECIFICATION_MASTER = 'Create Specification Master',
  CREATE_SHOPS = 'Create Shops',
  UPDATE_USERS = 'Update Users',
  UPDATE_DEALERS = 'Update Dealers',
  UPDATE_VENDORS = 'Update Vendors',
  UPDATE_SUPPLIERS = 'Update Suppliers',
  UPDATE_PIPELINE_CONFIG = 'Update Pipeline Config',
  UPDATE_CATEGORY = 'Update Category',
  UPDATE_UNIT_TYPE = 'Update Unit Type',
  UPDATE_MAKE_MODEL = 'Update Make /Model',
  UPDATE_SPECIFICATION_MASTER = 'Update Specification Master',
  UPDATE_SHOPS = 'Update Shops',
  DELETE_PIPELINE_CONFIG = 'Delete Pipeline Config',
  DELETE_CATEGORY = 'Delete Category',
  DELETE_UNIT_TYPE = 'Delete Unit Type',
  DELETE_MAKE_MODEL = 'Delete Make /Model',
  DELETE_SPECIFICATION_MASTER = 'Delete Specification Master',
  DELETE_SHOPS = 'Delete Shops',
  VIEW_USERS = 'View Users',
  VIEW_DEALERS = 'View Dealers',
  VIEW_VENDORS = 'View Vendors',
  VIEW_PIPELINE_CONFIG = 'View Pipeline Config',
  VIEW_CATEGORY = 'View Category',
  VIEW_UNIT_TYPE = 'View Unit Type',
  VIEW_MAKE_MODEL = 'View Make /Model',
  VIEW_SPECIFICATION_MASTER = 'View Specification Master',
  VIEW_SHOPS = 'View Shops',
  VIEW_CALENDAR = 'View calendar',
  CREATE_TASK = 'Create Task',
  UPDATE_TASK = 'Update Task',
  DELETE_TASK = 'Delete Task',
  VIEW_TASK = 'View Task',
  CREATE_SOLD_PIPELINE = 'Create Sold Pipeline',
  CREATE_STOCK_PIPELINE = 'Create Stock Pipeline',
  UPDATE_SOLD_PIPELINE = 'Update Sold Pipeline',
  UPDATE_STOCK_PIPELINE = 'Update Stock Pipeline',
  DELETE_SOLD_PIPELINE = 'Delete Sold Pipeline',
  DELETE_STOCK_PIPELINE = 'Delete Stock Pipeline',
  VIEW_SOLD_PIPELINE = 'View Sold Pipeline',
  VIEW_STOCK_PIPELINE = 'View Stock Pipeline',
  CREATE_INCOMING_TRUCK = 'Create Incoming Truck',
  CREATE_DRIVER_SCHEDULE = 'Create Driver Schedule',
  UPDATE_INCOMING_TRUCK = 'Update Incoming Truck',
  UPDATE_DRIVER_SCHEDULE = 'Update Driver schedule',
  DELETE_INCOMING_TRUCK = 'Delete Incoming Truck',
  DELETE_DRIVER_SCHEDULE = 'Delete Driver Schedule',
  VIEW_INCOMING_TRUCK = 'View Incoming Truck',
  VIEW_DRIVER_SCHEDULE = 'View Driver Schedule',
  VIEW_SALES_INFO = 'View Sales Info',
  VIEW_CUSTOMER_LEAD_INFO = 'View CustomerLead Info',
  VIEW_TASK_INFO = 'View Task Info',
  VIEW_INVENTORY_INFO = 'View Inventory Info',
  CREATE_INVENTORY = 'Create Inventory',
  CREATE_FINANCIAL = 'Create Financial/Expenses',
  CREATE_COMMUNICATION = 'Create Communication',
  UPDATE_INVENTORY = 'Update Inventory',
  UPDATE_FINANCIAL = 'Update Financial/Expenses',
  UPDATE_COMMUNICATION = 'Update Communication',
  DELETE_INVENTORY = 'Delete Inventory',
  DELETE_FINANCIAL = 'Delete Financial/Expenses',
  DELETE_COMMUNICATION = 'Delete Communication',
  VIEW_INVENTORY = 'View Inventory',
  VIEW_FINANCIAL = 'View Financial/Expenses',
  VIEW_COMMUNICATION = 'View Communication',
  CREATE_CUSTOMER_LEAD = 'Create Customer Lead',
  CREATE_CONTACTS = 'Create Contacts',
  CREATE_SALES_TASK = 'Create sales task',
  UPDATE_CUSTOMER_LEAD = 'Update Customer Lead',
  UPDATE_CONTACTS = 'Update Contacts',
  UPDATE_SALES_TASK = 'Update sales task',
  DELETE_CUSTOMER_LEAD = 'Delete Customer Lead',
  DELETE_CONTACTS = 'Delete Contacts',
  DELETE_SALES_TASK = 'Delete sales task',
  VIEW_CUSTOMER_LEAD = 'View Customer Lead',
  VIEW_CONTACTS = 'View Contacts',
  VIEW_SALES_TASK = 'View sales task',
  DELETE_ROLE = 'Delete Role',
  VIEW_ROLE = 'View Role',
  UPDATE_ROLE = 'Update Role',
  CREATE_ROLE = 'Create Role',
  VIEW_ACTIVITY = 'View Activities',
  VIEW_DAILY_SALES_REPORT = 'View Daily Sales Report',
  VIEW_ACTIVITY_REPORT = 'View Activity Report',
  VIEW_INVENTORY_SALES_REPORT = 'View Inventory Sales Report',
  VIEW_PROFITABILITY_REPORT = 'View Profitability Report',
  VIEW_INVENTORY_AGING = 'View Inventory Aging',
  VIEW_VENDORS_REPORT = 'View Vendors Report',
  VIEW_SUPPLIERS_REPORT = 'View Suppliers Report',
}
