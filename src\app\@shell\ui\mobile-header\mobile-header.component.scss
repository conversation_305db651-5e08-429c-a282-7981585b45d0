@import "/src/assets/scss/variables";

.nav-mobile {
  background-color: #0b0b69 !important;
  color: white;
}

.navbar-light .navbar-nav .nav-link {
  color: white !important;
}

.navbar-light .navbar-nav .nav-link {
  color: white !important;
}

.nav {
  display: flex;
  background-color: var(--primary-color);
  box-shadow: var(--box-shadow);

  .logo {
    display: flex;
    align-items: center;

    img {
      padding: 0px 25px;
      width: 200px;
    }

    &:hover {
      cursor: pointer;
    }
  }

  .nav-link {
    align-items: center;
    flex-direction: row-reverse;
    position: relative;
    display: flex;
    justify-content: flex-end;

    img {
      margin-right: 10px;
    }
  }

  .dropdown-toggle::after {
    position: absolute;
    color: var(--light-grey-color);
    margin-left: -4px !important;
    right: 5%;
  }
}

.initials {
  position: absolute;
  left: -30px;
  width: 25px;
  height: 25px;
  background-color: var(--light-grey-color);
  color: var(--text-color);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 65%;
  margin-left: -3px;
  font-size: 13px;
}

.skyLogo {
  padding: 10px !important;
  margin-top: 3px;
}

.navbar-brand {
  height: 50px;
}

.w-100 {
  width: 100% !important;
}

.mt-10 {
  margin-top: 10%;
}

.w-50 {
  width: 50% !important;
}

.nav-link.bell-icon {
  padding: 20px 0;
}

.navbar-nav .nav-link {
  padding: 10px 20px !important;
  font-size: 15px;
  font-weight: 300;
}

.text {
  display: flex;
  justify-content: flex-end;
  flex-direction: row-reverse;
}

.m-l-sm {
  margin-left: 10px;
}

.position-absolute {
  position: absolute !important;
  top: 58px !important;
  z-index: 9999;
}

.mr-25 {
  margin-right: 2.5rem;
}

.color-white {
  color: var(--light-grey-color);
}

.icon {
  color: var(--light-grey-color);
  height: 40px !important;
}

.navbar-toggle {
  position: relative;
  float: right;
  padding: 9px 10px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}

.navbar-collapse {
  display: block;
  height: auto;
  overflow: visible;
  position: absolute;
  z-index: 9998;
  width: 100%;
}

.navbar-nav .dropdown-menu {
  position: absolute !important;
}

.dropdown-menu .dropdown-submenu {
  display: none;
  position: absolute;
  left: 100%;
  bottom: 0;
}

.dropdown-menu .dropdown-submenu-left {
  right: 100%;
  left: auto;
}

.dropdown-menu > li:active > .dropdown-submenu {
  display: block;
}

.change-css {
  display: block;
}

.initial-css {
  display: none;
}

.cal-icon {
  margin-right: 0.5rem;
  padding: 20px 0;
}

.submenu {
  position: absolute;
  left: 100%;
  bottom: 0;
}

@media only screen and (max-width: 1180px) {
  .pt-24 {
    padding-top: 24px;
  }
}

@media only screen and (max-height: 880px) {
  .submenu {
    top: -12rem !important;
  }
}
