<div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
  <h4 class="header-title">{{ title }}</h4>
  <span *ngIf="isEditMode" class="created-by">
    <span class="bold-text">#{{ pipelineConfig?.title }}</span>
    Created By <span class="bold-text">{{ pipelineConfig?.createdBy?.name }}</span> on
    {{ pipelineConfig?.createdDate | date: constants.fullDateFormat }}
  </span>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>

<form [formGroup]="pipelineConfigFormGroup" (ngSubmit)="onSubmit()" [ngClass]="{ loading: isLoading }">
  <div class="content" *ngIf="!isLoading; else loaderTemplate">
    <section class="card p-3">
      <div class="row">
        <div class="col-12">
          <label class="required">Title</label>
          <input class="form-control" type="text" maxlength="150" placeholder="Enter pipeline title" formControlName="title" />
          <app-error-messages [control]="pipelineConfigFormGroup.controls.title"></app-error-messages>
        </div>
      </div>
    </section>
    <section class="card p-3">
      <div class="title">
        <h4>Selected Shops</h4>
      </div>
      <div class="d-flex align-items-center">
        <p-timeline [value]="templatePipelinePhasesFormArray.controls | keyvalue">
          <ng-template pTemplate="marker" let-event>
            <span>{{ getSequenceNumber(event.key) }}</span>
          </ng-template>
        </p-timeline>
        <p-table [value]="templatePipelinePhasesFormArray.controls" responsiveLayout="scroll" class="draggable">
          <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
            <ng-container formArrayName="templatePipelinePhases">
              <tr [pReorderableRow]="rowIndex" [formGroupName]="rowIndex">
                <td>
                  <span class="pi pi-bars" pReorderableRowHandle></span>
                </td>
                <td class="pipeline-col-width">
                  <p-dropdown
                    appendTo="body"
                    [options]="getAvailableShops(rowIndex)"
                    (onChange)="setDefaultUser(rowIndex, $event)"
                    formControlName="shopId"
                    [showClear]="true"
                    optionLabel="name"
                    optionValue="id"
                    [filter]="true"
                    filterBy="name"
                    placeholder="Select a shop"
                    [name]="'shop' + rowIndex"
                  >
                    <ng-template pTemplate="empty">
                      <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.shops, data: getAvailableShops(rowIndex) }"></ng-container>
                    </ng-template>
                    <ng-template let-shop pTemplate="item">
                      <span>{{ shop?.name }}</span>
                    </ng-template>
                  </p-dropdown>
                  <app-error-messages [control]="getCurrentTemplatePipelineFormGroup(rowIndex).controls.shopId"></app-error-messages>
                </td>
                <td class="pipeline-col-width">
                  <p-dropdown
                    appendTo="body"
                    [options]="users"
                    formControlName="assigneeId"
                    optionLabel="name"
                    [showClear]="true"
                    optionValue="id"
                    [filter]="true"
                    filterBy="name"
                    placeholder="Select a user"
                    [name]="'assignee' + rowIndex"
                  >
                    <ng-template pTemplate="empty">
                      <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.shopUsers, data: getShopUsersByShopId(rowIndex) }"></ng-container>
                    </ng-template>
                    <ng-template let-assignee pTemplate="item">
                      <span>{{ assignee.name }}</span>
                    </ng-template>
                  </p-dropdown>
                  <app-error-messages [control]="getCurrentTemplatePipelineFormGroup(rowIndex).controls.assigneeId"></app-error-messages>
                </td>
                <td *ngIf="templatePipelinePhasesFormArray.length > 1">
                  <img [src]="constants.staticImages.icons.deleteIcon" alt="" class="delete-shop" (click)="onDeleteShop(rowIndex)" />
                </td>
              </tr>
            </ng-container>
          </ng-template>
        </p-table>
      </div>
      <div class="add-shop mt-4">
        <button class="btn btn-primary" id="addShopBtn" type="button" *ngIf="isAddNewShopVisible" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAddNewShop()">Add Shop</button>
      </div>
    </section>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit">Save <fa-icon [icon]="faIcons.faSpinner" *ngIf="isSaving" [spin]="true"></fa-icon></button>
    <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!isEditMode" appShowLoaderOnApiCall>Save & Add New</button>
  </div>
</form>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>
