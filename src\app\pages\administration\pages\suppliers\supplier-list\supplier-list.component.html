<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left"
      (click)="showCreateModal = true"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      *appHasPermission="[permissionActions.CREATE_SUPPLIERS]"
    >
      <span class="show-label">Add New Supplier</span>
    </button>
  </div>
</app-page-header>

<div class="card">
  <div class="tabs">
    <tabset #profileTabs>
      <tab heading="Active" #activeTab="tab" (selectTab)="onTabChanged($event)">
        <ng-container [ngTemplateOutlet]="activeTabTemplate"></ng-container>
      </tab>

      <tab heading="Archived" #archivedTab="tab" (selectTab)="onTabChanged($event)">
        <ng-container [ngTemplateOutlet]="activeTabTemplate"></ng-container>
      </tab>
    </tabset>
  </div>
</div>

<ng-template #activeTabTemplate>
  <div class="card tabs pipeline-list">
    <div class="tab-content">
      <p-table
        class="no-column-selection"
        [columns]="selectedColumns"
        [value]="suppliers"
        responsiveLayout="scroll"
        sortMode="single"
        [customSort]="true"
        [lazy]="true"
        [reorderableColumns]="true"
        (onLazyLoad)="onSortChange($event, getAll.bind(this))"
        [sortField]="'name'"
        [rowHover]="true"
        [loading]="isLoading"
        styleClass="p-datatable-gridlines"
        [resizableColumns]="true"
        columnResizeMode="expand"
      >
        <ng-template pTemplate="header" let-columns>
          <tr>
            <ng-container *ngFor="let col of columns">
              <th pResizableColumn [pSortableColumn]="col.sortable ? col?.sortKey || col.field : ''" pReorderableColumn *ngIf="col.reorderable" [ngClass]="col.class">
                {{ col.header }} <p-sortIcon [field]="col.sortKey || col.field" *ngIf="col.sortable"></p-sortIcon>
              </th>
              <th pResizableColumn [pSortableColumn]="col.sortable ? col?.sortKey || col.field : ''" *ngIf="!col.reorderable" [ngClass]="col.class">
                {{ col.header }} <p-sortIcon [field]="col.sortKey || col.field" *ngIf="col.sortable"></p-sortIcon>
              </th>
            </ng-container>
            <th pResizableColumn class="small-col" *appHasPermission="[permissionActions.UPDATE_SUPPLIERS]">Active</th>
            <th pResizableColumn class="small-col">Actions</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-supplier let-columns="columns">
          <tr>
            <ng-container *ngFor="let col of columns">
              <td *ngIf="!col.isATemplate">
                {{ supplier[col.field] }}
              </td>
              <td *ngIf="col.field === 'phoneNumber'">{{ supplier?.phoneNumber | phone }}</td>
            </ng-container>
            <td *appHasPermission="[permissionActions.UPDATE_SUPPLIERS]" class="actions">
              <div class="actions-content">
                <ui-switch [(ngModel)]="supplier.isActive" [loading]="selectedSupplier?.id === supplier.id && isArchiveInProgress" (change)="onArchive(supplier, $event)">
                  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedSupplier?.id === supplier.id"></fa-icon>
                </ui-switch>
              </div>
            </td>
            <td class="actions">
              <div class="actions-content">
                <img [src]="isActiveTab ? constants.staticImages.icons.expenseContract : ''" (click)="onViewExpensesList(supplier)" alt="" />
                <img
                  [src]="isActiveTab ? constants.staticImages.icons.edit : constants.staticImages.icons.viewIcon"
                  (click)="onEdit(supplier, isActiveTab ? false : true)"
                  alt=""
                  *appHasPermission="[permissionActions.UPDATE_SUPPLIERS]"
                />
              </div>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <td [colSpan]="cols.length + 1" class="no-data">No data to display</td>
        </ng-template>
      </p-table>
      <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
    </div>
  </div>
</ng-template>

<p-sidebar [(visible)]="showCreateModal" [fullScreen]="true" (onHide)="showCreateModal = false" [blockScroll]="true" [transitionOptions]="modalTransition" [showCloseIcon]="false">
  <app-supplier-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal" [supplierInfo]="selectedSupplier" [isViewMode]="isViewMode"></app-supplier-add>
</p-sidebar>

<p-sidebar
  [(visible)]="showExpensesCreateModal"
  [fullScreen]="true"
  (onHide)="showExpensesCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-supplier-expense-list
    (onClose)="onAddEditExpensePopupClose($event)"
    *ngIf="showExpensesCreateModal"
    [supplierExpenseInfo]="selectedSupplierExpenses"
  ></app-supplier-expense-list>
</p-sidebar>

<p-confirmPopup appClickOutside (clickOutside)="onBackdropClick($event)"></p-confirmPopup>
<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>
