<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button class="btn btn-primary left" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAddEditShop()" *appHasPermission="[permissionActions.CREATE_SHOPS]">
      <span class="show-label">Add New Shop</span>
    </button>
  </div>
</app-page-header>

<ng-container *ngIf="!isLoading; else pageLoaderTemplate">
  <p-table
    class="no-column-selection"
    [scrollable]="true"
    [value]="shops"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="true"
    [rowHover]="true"
    [loading]="isLoading"
    styleClass="p-datatable-gridlines"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <th pResizableColumn scope="col">Name</th>
        <th pResizableColumn scope="col">Default User</th>
        <th pResizableColumn scope="col" *appHasPermission="[permissionActions.UPDATE_SHOPS, permissionActions.DELETE_SHOPS]">Actions</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-shop>
      <tr>
        <td>
          {{ shop?.name }}
        </td>
        <td>
          {{ shop?.defaultUserName }}
        </td>
        <td class="actions" *appHasPermission="[permissionActions.UPDATE_SHOPS, permissionActions.DELETE_SHOPS]">
          <img [src]="constants.staticImages.icons.edit" (click)="onAddEditShop(shop)" alt="" *appHasPermission="[permissionActions.UPDATE_SHOPS]" />
          <img [src]="constants.staticImages.icons.deleteIcon" (click)="showDeleteModal(shop, $event)" alt="" *appHasPermission="[permissionActions.DELETE_SHOPS]" />
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="6" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
</ng-container>

<ng-template #pageLoaderTemplate>
  <div class="no-data mt-5">
    <em class="pi pi-spin pi-spinner"></em>
  </div>
</ng-template>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  (onHide)="showCreateModal = false; selectedShop = null"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-shop-add-update *ngIf="showCreateModal" [selectedShop]="selectedShop" (closeModal)="closeModal()" (addNewShop)="addNewShop($event)" (updateShop)="updateShop($event)"> </app-shop-add-update>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>

<p-confirmPopup></p-confirmPopup>
