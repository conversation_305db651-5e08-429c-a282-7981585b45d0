import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CustomerInventory } from '@pages/crm/models/customer-inventory.model';
import { QuotationCustomerLeadKey, QuotationCustomerListItem, QuotationFilterParams } from '@pages/crm/models/customer-lead-quotation.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { InventorySpecification } from '@pages/inventory/models';
import { takeUntil } from 'rxjs';
import { DataType, OperatorType } from 'src/app/@shared/models';
import { CommonService } from 'src/app/@shared/services/common.service';
import { CrmCustomerQuotationComponent } from './crm-customer-quotation/crm-customer-quotation.component';

@Component({
  selector: 'app-crm-customer-inventory-wrapper',
  templateUrl: './crm-customer-inventory-wrapper.component.html',
  styleUrls: ['./crm-customer-inventory-wrapper.component.scss']
})
export class CrmCustomerInventoryWrapperComponent extends BaseComponent implements OnInit, OnChanges {
  activeIndex = 0;
  customerInventoryIndex = CustomerInventory;
  quotationCustomerList!: QuotationCustomerListItem[] | null
  @Input() crmId!: string;
  @Input() crmCustomerInfoId!: string | undefined;
  @ViewChild(CrmCustomerQuotationComponent) crmCustomerQuotationComponent!: CrmCustomerQuotationComponent;
  inventoryMatchedCount!: number | undefined;
  @Input() crmCustomerInfo!: any;
  @Output() inventoryMatchedCounts: EventEmitter<number> = new EventEmitter<number>();
  @Input() inventorySpecificationForm!: Array<InventorySpecification>;
  @Input() customerDetails!: any;

  constructor(
    private readonly crmService: CrmService,
    private readonly fb: FormBuilder,
    private readonly commonService: CommonService,
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.inventorySpecificationForm?.currentValue) {
      this.inventorySpecificationForm = changes.inventorySpecificationForm?.currentValue
    }
  }

  ngOnInit(): void {
    this.getQuotationDetails();
  }

  get getQuotationRequest(): QuotationFilterParams {
    return {
      values: [{
        dataType: DataType.STRING,
        key: QuotationCustomerLeadKey.CRM_CONTACT,
        operator: OperatorType.EQUAL,
        value: this.crmId ? this.crmId : ''
      }]
    };
  }

  private getQuotationDetails(): void {
    if (this.crmId) {
      this.crmService.add<QuotationFilterParams>(this.getQuotationRequest, API_URL_UTIL.admin.crm.quatationFilter).pipe(takeUntil(this.destroy$)).subscribe({
        next: (quotation) => {
          this.quotationCustomerList = quotation.content;
        }
      });
    }
  }

  onQuotationAccepted(isAccepted: boolean): void {
    if (isAccepted) {
      this.getQuotationDetails();
    }
  }

  onInventoryMatched(event: number) {
    this.inventoryMatchedCount = event;
    this.inventoryMatchedCounts.next(event)
  }
}
