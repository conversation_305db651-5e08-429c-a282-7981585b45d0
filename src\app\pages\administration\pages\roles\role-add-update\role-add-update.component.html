<div class="modal-title close">
  <h4 class="header-title">{{ pageTitle }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
</div>

<form>
  <div class="content category-form">
    <div class="form-group">
      <input type="text" name="title" class="form-control" id="title" placeholder="Enter Name" [(ngModel)]="role.name" #roleName="ngModel" [required]="true" [disabled]="isViewMode"/>
      <div *ngIf="roleName?.touched && roleName?.invalid" class="invalid-feedback">
        <div *ngIf="roleName?.errors?.required">*Role name is required.</div>
      </div>
    </div>

    <div class="wrapper" *ngFor="let item of roles">
      <div class="parent-permission title">
        <div class="" *ngIf="item.moduleName">
          <p-checkbox
            name="model"
            class="m-bt-7 title-checkbox"
            [binary]="true"
            [value]="item"
            [disabled]="isViewMode"
            [label]="item.moduleName === 'Skeye' ? 'Super Admin (All permission access)' : item.moduleName"
            [(ngModel)]="item.checked"
            [ngModelOptions]="{ standalone: true }"
            (onChange)="onToggleModule($event, item)"
          ></p-checkbox>
        </div>
      </div>

      <ng-container *ngIf="item.moduleName !== 'Skeye'">
        <div class="child-permission" *ngFor="let action of item?.actions">
          <ng-container *ngIf="action.name">
            <p-checkbox
              name="child-permission"
              class="p-content"
              [value]="action"
              [label]="action.name"
              [disabled]="isViewMode"
              [(ngModel)]="role.actions"
              (onChange)="onTogglePermission($event?.checked, action, item)"
            ></p-checkbox>
          </ng-container>
        </div>
      </ng-container>
    </div>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
    <button class="btn btn-primary" *ngIf="isViewMode && !selectedRole.systemDefault" type="button" (click)="onModalEdit()">Edit</button>
    <button class="btn btn-primary d-flex align-items-center" *ngIf="!isViewMode" [disabled]="!role?.name" type="submit" (click)="onSubmit()">
      Save
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isLoading"></fa-icon>
    </button>
  </div>
</form>
