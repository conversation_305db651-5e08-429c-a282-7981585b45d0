import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { CrmTaskAddModule } from '@pages/crm/pages/crm-task/crm-task-add/crm-task-add.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmCustomerTaskComponent } from './crm-customer-task.component';

@NgModule({
  declarations: [
    CrmCustomerTaskComponent
  ],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    SidebarModule,
    ReactiveFormsModule,
    FormsModule,
    TabViewModule,
    CardModule,
    TableModule,
    DropdownModule,
    CheckboxModule,
    ConfirmPopupModule,
    ConfirmDialogModule,
    CrmTaskAddModule,
    UiSwitchModule.forRoot(UiSwitchConfig)
  ],
  exports: [
    CrmCustomerTaskComponent
  ],
  providers: [ConfirmationService, MessageService],
})

export class CrmCustomerTaskModule { }
