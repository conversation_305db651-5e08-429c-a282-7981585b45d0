@import "../variables";

.btn {
  height: 40px;
  padding: 0 40px;
}

@media only screen and (max-width: 500px) {
  .show-label {
    display: none !important;
  }

  .btn {
    height: 30px !important;
    padding: 0 20px !important;
  }
}

a {
  color: var(--link-color);
}

.btn-primary {
  color: #fff;
  background-color: var(--active-color);
  border-color: var(--active-color);

  &:hover {
    color: #fff;
    background-color: var(--active-color-lighter);
    border-color: var(--active-color-lighter);
  }
}

.btn-secondary {
  background-color: var(--card-bg-color);
  color: var(--content-head-color);
}

.btn-primary:disabled,
.btn-primary.disabled {
  color: #fff;
  background-color: var(--primary-color-lighter);
  border-color: var(--primary-color-lighter);
}

.btn-check:checked + .btn-primary,
.btn-check:active + .btn-primary,
.btn-primary:active,
.btn-primary.active,
.show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.nav-pills .nav-link.active {
  background-color: var(--active-color);
}

.nav-pills .show > .nav-link {
  background-color: var(--primary-color);
}

.nav-link {
  padding: 20px 25px;
  border-radius: 0 !important;
  color: white;
}

.dropdown-menu.show {
  border: none !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  background-color: var(--dropdown-panel-color);
  box-shadow: var(--dropdown-box-shadow);
  padding: 10px 0;

  .dropdown-item {
    color: var(--text-color);
    padding: 0.5rem 1rem;

    &:hover,
    &:focus {
      color: white;
      background-color: var(--primary-color);
    }
  }
}

.nav-link:hover,
.nav-link:focus {
  color: var(--active-color);
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid $placeholder-color;
}

.btn-check:focus + .btn-primary,
.btn-primary:focus {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.05rem var(--active-color);
}

.p-button {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  color: var(--white-color);

  &:focus {
    box-shadow: none;
  }

  &:hover {
    background-color: var(--primary-color-lighter) !important;
    border-color: var(--primary-color-lighter) !important;
  }

  &.p-button-text:hover {
    background-color: white !important;
  }
}

.btn-with-loader {
  &.disabled {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: not-allowed;
  }
}

.btn-loader-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-left: 5px !important;
  margin-right: 0 !important;
  vertical-align: middle !important;
  line-height: 1 !important;
  position: relative !important;
  top: -1px !important;
}

em.disabled {
  position: relative;
  &::before {
    display: none;
  }
  fa-icon.btn-loader-icon {
    position: relative;
    margin-left: 0;
  }
}

@media only screen and (max-width: 800px) {
  .top-header {
    .column-btn {
      padding: 0px 5px !important;
    }

    .me-3 {
      margin-right: 0.5rem !important;
    }
  }
  .hide-history-label {
    display: none !important;
  }
}

.btn-icon {
  display: none !important;
}

.btn-label {
  display: block !important;
}

@media only screen and (max-width: 860px) {
  .hide-label {
    display: none !important;
  }
  .btn-icon {
    display: block !important;
  }
  .btn-label {
    display: none !important;
  }
  .big-label-btn {
    padding: 0px 20px;
    &.left {
      img {
        left: 22% !important;
      }
    }
  }
}

@media only screen and (max-width: 500px) {
  .reset-btn {
    padding: 0px 10px !important;
  }
}
