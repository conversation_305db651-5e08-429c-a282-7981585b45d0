<div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
  <h4 class="header-title">{{ title }}</h4>
  <span *ngIf="isEditMode || isViewMode" class="created-by">
    <span class="bold-text">#{{ taskInfo?.stockNumber }}</span>
    Created By <span class="bold-text">{{ taskInfo?.reporter?.name }}</span> on {{ taskInfo?.createdDate | date: constants.fullDateFormat }}
  </span>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form [formGroup]="taskFormGroup" (ngSubmit)="onSubmit()" class="crm-task-add">
  <div class="content">
    <p-accordion class="nested-accordion" [multiple]="true">
      <p-accordionTab [(selected)]="accordionTabs.details">
        <ng-template pTemplate="header">
          <div class="accordion-header" [ngClass]="{ active: accordionTabs.details }">
            <span>Details</span>
            <em class="pi" [ngClass]="accordionTabs.details ? 'pi-angle-up' : 'pi-angle-down'"></em>
          </div>
        </ng-template>
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="taskDetailsTemplate"></ng-container>
        </ng-template>
      </p-accordionTab>
      <p-accordionTab [(selected)]="accordionTabs.attachments" *ngIf="showAttachmentsTab">
        <ng-template pTemplate="header">
          <div class="accordion-header attachment-header" [ngClass]="{ active: accordionTabs.attachments }">
            <span>Attachments</span>
            <p-message severity="info" text="{{ maxFileSizeAllowed }}" styleClass="p-mr-2 file-size-message"> </p-message>
            <em class="pi" [ngClass]="accordionTabs.attachments ? 'pi-angle-up' : 'pi-angle-down'"></em>
          </div>
        </ng-template>
        <ng-template pTemplate="content">
          <div class="row" [ngClass]="{ 'mb-4': fileUploadProgresses.length || taskInfo?.taskAttachments?.length }">
            <div [ngClass]="!fileUploadProgresses.length && !taskInfo?.taskAttachments?.length ? 'col-12' : 'col-lg-2 col-md-4 col-12'">
              <ng-container [ngTemplateOutlet]="attachmentTemplate" *ngIf="!isViewMode"> </ng-container>
            </div>
            <ng-container [ngTemplateOutlet]="editImageTemplate"></ng-container>
          </div>
        </ng-template>
      </p-accordionTab>
      <p-accordionTab [(selected)]="accordionTabs.activity" class="activity" *ngIf="isEditMode">
        <ng-template pTemplate="header">
          <div class="accordion-header" [ngClass]="{ active: accordionTabs.activity }">
            <span>Activity</span>
            <em class="pi" [ngClass]="accordionTabs.activity ? 'pi-angle-up' : 'pi-angle-down'"></em>
          </div>
        </ng-template>
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="activityTemplate"></ng-container>
        </ng-template>
      </p-accordionTab>
    </p-accordion>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall *ngIf="!isViewMode">Save</button>
    <ng-container *appHasPermission="[permissionActions.UPDATE_SALES_TASK]">
      <button class="btn btn-primary" type="submit" (click)="editForm()" appShowLoaderOnApiCall *ngIf="isViewMode && showEditBtn">Edit</button>
      <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!isEditMode && !isViewMode" appShowLoaderOnApiCall>Save & Add New</button>
    </ng-container>
  </div>
</form>

<ng-template #taskDetailsTemplate [formGroup]="taskFormGroup">
  <div class="row">
    <div class="col-lg-7 col-md-6 col-12">
      <section>
        <div class="title">
          <h4>Task Information</h4>
        </div>
        <div class="row">
          <div class="col-12">
            <label class="required">Summary</label>
            <input class="form-control" type="text" placeholder="Enter task summary" formControlName="summary" />
            <app-error-messages [control]="taskFormGroup.controls.summary"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label class="required">Task Type</label>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [options]="taskTypes"
              formControlName="taskTypeId"
              optionLabel="name"
              [showClear]="true"
              optionValue="id"
              [filter]="true"
              filterBy="name"
              placeholder="Select type"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.taskTypes, data: taskTypes }"> </ng-container>
              </ng-template>
              <ng-template let-taskType pTemplate="item">
                <span>{{ taskType.name }}</span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="taskFormGroup.controls.taskTypeId"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label>Stock</label>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [options]="stockBasicInfo"
              formControlName="stockNumber"
              optionLabel="stockNumber"
              [showClear]="true"
              optionValue="stockNumber"
              [filter]="true"
              filterBy="stockNumber"
              placeholder="Select stock"
              [virtualScroll]="true"
              [itemSize]="30"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.stockNumbers, data: stockBasicInfo }"> </ng-container>
              </ng-template>
              <ng-template let-stock pTemplate="item">
                <span>{{ stock.stockNumber }}</span>
              </ng-template>
            </p-dropdown>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label> Contact</label>
            <ng-container *appHasPermission="[permissionActions.CREATE_CONTACTS]">
              <button class="btn btn-primary add-btn" id="addMakeBtn" type="button" *ngIf="!isViewMode" [appImageIconSrc]="constants.staticImages.icons.add" (click)="openContactModel()"></button>
            </ng-container>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [options]="contactList"
              formControlName="crmContactId"
              optionLabel="contactName"
              [showClear]="true"
              optionValue="id"
              [filter]="true"
              filterBy="contactName"
              placeholder="Select contact"
              [virtualScroll]="true"
              [itemSize]="30"
              [optionDisabled]="isOptionDisabled.bind(this)"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage"
                  [ngTemplateOutletContext]="{ loader: loaders.previousOwnerName, data: contactList }"> </ng-container>
              </ng-template>
              <ng-template let-item pTemplate="item">
                <span [ngClass]="{'disabled-dropdown-item': item.archived}">
                  {{ item.contactName }}
                  <span *ngIf="item.archived">{{ constants.archived }}</span>
                </span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="taskFormGroup.controls.crmContactId"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label>Lead ID</label>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [options]="customerLeadList"
              formControlName="customerLeadId"
              optionLabel="id"
              [showClear]="true"
              optionValue="id"
              [filter]="true"
              filterBy="contactName"
              placeholder="Select lead id"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.customerLead, data: customerLeadList }"> </ng-container>
              </ng-template>
              <ng-template let-item pTemplate="item">
                <span>{{ item.id }}</span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="taskFormGroup.controls.customerLeadId"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label class="required">Action/Activity</label>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [options]="activityList"
              formControlName="activity"
              optionLabel="name"
              [showClear]="true"
              optionValue="value"
              [filter]="true"
              filterBy="name"
              placeholder="Select activity"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.stockNumbers, data: activityList }"> </ng-container>
              </ng-template>
              <ng-template let-stock pTemplate="item">
                <span>{{ stock.name }}</span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="taskFormGroup.controls.activity"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label class="required">Assignee</label>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [options]="users"
              formControlName="assigneeId"
              optionLabel="name"
              [showClear]="true"
              optionValue="id"
              [filter]="true"
              filterBy="name"
              placeholder="Select assignee"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.shopUsers, data: users }"> </ng-container>
              </ng-template>
              <ng-template let-assignee pTemplate="item">
                <span>{{ assignee.name }}</span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="taskFormGroup.controls.assigneeId"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label class="required">Created By</label>
            <input class="form-control" type="text" placeholder="Reporter" formControlName="reporterName" />
          </div>
        </div>
      </section>
    </div>
    <div class="col-lg-5 col-md-6 col-12">
      <section>
        <div class="title">
          <h4>Timeline</h4>
        </div>
        <div class="row">
          <div class="col-md-6 col-12">
            <label class="required">Status</label>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [options]="taskStatuses"
              formControlName="taskStatusId"
              optionLabel="name"
              [showClear]="true"
              optionValue="id"
              [filter]="true"
              filterBy="name"
              placeholder="Select status"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.taskStatuses, data: taskStatuses }"> </ng-container>
              </ng-template>
              <ng-template let-taskStatus pTemplate="item">
                <span>{{ taskStatus.name }}</span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="taskFormGroup.controls.taskStatusId"></app-error-messages>
          </div>
          <div class="col-md-6 col-12">
            <label>Deadline</label>
            <div>
              <p-calendar appendTo="body" formControlName="endDate" [showIcon]="true" [showButtonBar]="true" [readonlyInput]="true" inputId="endDateIcon"> </p-calendar>
            </div>
          </div>
        </div>
      </section>
    </div>
    <div class="col-lg-12 col-md-8 col-12">
      <section>
        <label>Description</label>
        <div class="row">
          <div class="col-12 position-relative">
            <textarea placeholder="Enter description" rows="1" formControlName="description"></textarea>
            <app-error-messages [control]="taskFormGroup.controls.description"></app-error-messages>
          </div>
        </div>
      </section>
    </div>
  </div>
</ng-template>

<ng-template #attachmentTemplate class="task-attachment">
  <section [ngClass]="{ 'w-100': !fileUploadProgresses.length && !taskInfo?.taskAttachments?.length, 'section-attachment': true }">
    <div [ngClass]="{ files: fileUploadProgresses.length || taskInfo?.taskAttachments?.length }" class="drop-zone">
      <span class="drop-zone__prompt">
        <em class="pi pi-upload"></em>
        <p class="title">Drop image here</p>
        <p class="subtitle">or click to browse</p>
      </span>
      <input type="file" name="myFile" class="drop-zone__input" #inputElement [accept]="constants.allowedImgFormats" (change)="onFileSelect($event)" multiple [disabled]="isViewMode" />
    </div>
  </section>
</ng-template>

<ng-template #addImageTemplate>
  <div *ngFor="let fileProgress of fileUploadProgresses; let fileIndex = index" class="col-lg-2 col-md-4 col-12">
    <div class="files">
      <div class="file-box-wrapper">
        <div class="file-box" [ngClass]="{ 'in-progress': !fileProgress?.isResolved }" [style.background-image]="'url(' + fileProgress.fileProperty?.fileUrl + ')'"></div>
        <div class="file-progress" *ngIf="!fileProgress.isResolved">
          <p-progressBar [value]="fileProgress?.progress$ | async" [showValue]="true"></p-progressBar>
        </div>
      </div>
      <div class="file-footer">
        <span class="file-name text-truncate">{{ fileProgress?.fileProperty?.name }}</span>
        <div>
          <em class="pi pi-eye download-icon" (click)="onViewImage(fileProgress?.fileProperty?.fileUrl)"></em>
          <em class="pi pi-download download-icon ms-2" (click)="utils.downloadUploadedPdf(fileProgress?.file)"></em>
          <em class="pi pi-trash text-danger ms-2" (click)="removeFileFromUpload(fileIndex)"></em>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #editImageTemplate>
  <ng-container [ngTemplateOutlet]="addImageTemplate"></ng-container>
  <div class="col-lg-2 col-md-4 col-12" *ngFor="let file of taskInfo?.taskAttachments; let fileIndex = index">
    <div class="files">
      <div class="file-box-wrapper">
        <div class="file-box" [style.background-image]="'url(' + file?.fullTaskAttachmentUrl + ')'"></div>
      </div>
      <div class="file-footer">
        <span class="file-name text-truncate">{{ getFileName(file?.url) }}</span>
        <div>
          <em class="pi pi-eye download-icon" (click)="onViewImage(file.fullTaskAttachmentUrl)"></em>
          <em class="pi pi-download download-icon ms-2" (click)="downloadImage(file)"></em>
          <em class="pi pi-trash text-danger ms-2" appShowLoaderOnApiCall (click)="onDelete(file.id, $event)" *ngIf="!isViewMode"></em>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #activityTemplate>
  <p-tabView>
    <p-tabPanel header="Comments">
      <ng-template pTemplate="content">
        <app-task-comments [taskInfo]="taskInfo" *ngIf="taskInfo" [isViewMode]="isViewMode" [selectedCommentId]="selectedCommentId"></app-task-comments>
      </ng-template>
    </p-tabPanel>
    <p-tabPanel header="History">
      <ng-template pTemplate="content">
        <app-task-audit [taskInfo]="taskInfo"></app-task-audit>
      </ng-template>
    </p-tabPanel>
  </p-tabView>
</ng-template>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showCreateContact"
  position="right"
  (onHide)="showCreateContact = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  [fullScreen]="true"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-contact-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateContact"> </app-crm-contact-add>
</p-sidebar>

<p-dialog [(visible)]="showEnlargedImage" [modal]="true" [dismissableMask]="true" [showHeader]="false" [closable]="true" styleClass="transparent-background">
  <fa-icon [icon]="faIcons.faTimes" class="close-icon photos-close-icon" (click)="showEnlargedImage = false && (viewImageScr = '')"></fa-icon>
  <img class="view-zoomed-image" [src]="viewImageScr" alt="" />
</p-dialog>

<p-confirmPopup></p-confirmPopup>
