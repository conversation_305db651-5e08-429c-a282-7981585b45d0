import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ContactDetails, CrmContactListItem } from '@pages/administration/models/crm.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { inventoryHoldDetails, InventoryListItem, SalesPersonDetails, SaleUnitCreateParam } from '@pages/inventory/models';
import { FinancialService } from '@pages/inventory/services/financial.service';
import { HoldUnitService } from '@pages/inventory/services/hold-unit.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { DataType, FilterValue, GenericFilterParams, OperatorType, TreeOperatorType } from 'src/app/@shared/models';

@Component({
  selector: 'app-add-hold-details',
  templateUrl: './add-hold-details.component.html',
  styleUrls: ['./add-hold-details.component.scss']
})
export class AddHoldDetailsComponent extends BaseComponent implements OnInit, OnChanges {
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  hasDataBeenModified = false;
  holdDetailsFormGroup!: FormGroup;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() isViewMode!: boolean;
  purchasingAgent: SalesPersonDetails[] = [];
  displaySalesPerson!: SalesPersonDetails | undefined;
  holdInfo!: inventoryHoldDetails;
  unitId!: number | undefined;
  contactList: CrmContactListItem[] = [];
  displayContact!: ContactDetails | undefined;
  contactFilterParams: GenericFilterParams;
  isLastPage = false;
  loadMoreIcon = false;
  pageNumber = 1;
  globalSearch = new Subject<FilterValue[]>();
  showCreateContact = false;
  accordionTabs = {
    buyerInfo: true,
    salePersonInfo: true,
    holdDetailsInfo: true,
    notesInfo: true
  };
  loaders = {
    purchasingAgent: false,
    previousOwnerName: false
  };

  constructor(private readonly formBuilder: FormBuilder, private readonly toasterService: AppToasterService, private readonly userAnnotationService: UserAnnotationService, private readonly cdf: ChangeDetectorRef, private readonly financialService: FinancialService, private readonly crmService: CrmService, private readonly holdUnitService: HoldUnitService) { super() }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getPurchasingAgentList();
    this.getHoldUnitDetails();
    this.contactFilterParams = this.contactListParams;
    this.getContactList();
    this.displaySearchResult();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (!this.isViewMode) {
      this.holdDetailsFormGroup?.enable();
    }
  }

  private initializeFormGroup(): void {
    this.holdDetailsFormGroup = this.formBuilder.group({
      salesPersonId: new FormControl(null, [Validators.required]),
      buyerInformationId: new FormControl(null, [Validators.required]),
      depositDate: new FormControl(null),
      onHoldUntil: new FormControl(null),
      depositAmount: new FormControl(null),
      notes: new FormControl(null),
    });
    if (this.isViewMode) {
      this.holdDetailsFormGroup.disable();
    }
  }

  get contactListParams(): GenericFilterParams {
    return {
      "treeOperator": TreeOperatorType.OR,
      "values": [
        {
          "dataType": DataType.STRING,
          "key": "firstName",
          "operator": OperatorType.LIKE,
          "value": ""
        },
        {
          "dataType": DataType.STRING,
          "key": "lastName",
          "operator": OperatorType.LIKE,
          "value": ""
        },
        {
          "dataType": DataType.STRING,
          "key": "company",
          "operator": OperatorType.LIKE,
          "value": ""
        }
      ],
      "orderBy": [
        {
          "ascending": true,
          "field": "id"
        }
      ]
    };
  }

  private getPurchasingAgentList(): void {
    this.loaders.purchasingAgent = true;
    this.userAnnotationService.get(API_URL_UTIL.userAnnotation.userFilter).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: SalesPersonDetails[]) => {
        this.purchasingAgent = res;
        this.loaders.purchasingAgent = false;
      },
      error: () => {
        this.loaders.purchasingAgent = false;
      }
    });
  }

  getHoldUnitDetails(): void {
    this.isLoading = true;
    if (this.inventoryIncomingInfo) {
      this.unitId = this.inventoryIncomingInfo.unitId;
    }
    else {
      this.unitId = this.inventoryInfo?.id;
    }
    this.financialService.getUnitIdByHoldInfo(Number(this.unitId)).pipe(takeUntil(this.destroy$)).subscribe(holdInfo => {
      this.holdInfo = holdInfo;
      this.displayContact = this.holdInfo?.buyerInformation;
      if (this.displayContact?.firstName || this.displayContact?.lastName) {
        this.displayContact.name = `${this.displayContact?.firstName} ${this.displayContact?.lastName}`;
      }
      this.displaySalesPerson = this.holdInfo?.salesPerson;
      this.setHoldDetailsFormGroup();
    });
  }

  private async setHoldDetailsFormGroup(): Promise<void> {
    if (this.holdInfo) {
      this.holdDetailsFormGroup.patchValue({
        salesPersonId: this.holdInfo?.salesPerson?.id,
        buyerInformationId: this.holdInfo?.buyerInformation?.id,
        depositDate: this.holdInfo?.depositDate ? new Date(`${this.holdInfo?.depositDate}`) : '',
        onHoldUntil: this.holdInfo?.onHoldUntil ? new Date(`${this.holdInfo?.onHoldUntil}`) : '',
        depositAmount: this.holdInfo?.depositAmount,
        notes: this.holdInfo?.notes,
      });
      this.cdf.detectChanges();
    }
  }

  private getContactList(): void {
    this.loaders.previousOwnerName = true;
    this.crmService.getListWithFiltersWithPagination<GenericFilterParams, CrmContactListItem>(this.contactFilterParams, this.pageNumber, 25, API_URL_UTIL.admin.crm.filter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.contactList = [...this.contactList, ...res.content];
          this.contactList.forEach(contact => {
            if (contact.company) {
              contact.contactName = `${contact.firstName} ${contact.lastName} (${contact.company})`;
            } else {
              contact.contactName = `${contact.firstName} ${contact.lastName}`;
            }
          });
          this.isLastPage = res.last;
          this.isLoading = false;
          this.loadMoreIcon = false;
          this.loaders.previousOwnerName = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.loadMoreIcon = false;
          this.loaders.previousOwnerName = false;
          this.cdf.detectChanges();
        }
      });
  }

  get holdUnitCreateParams(): SaleUnitCreateParam {
    return {
      ...this.holdDetailsFormGroup.value,
      unitId: this.holdInfo?.unit?.id,
      id: this.holdInfo?.id,
    };
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateContact = false;
    if (refreshList) {
      this.getContactList();
    }
  }

  openContactModel(): void {
    this.showCreateContact = true;
  }

  displayContactDetails(id: number): void {
    const contactId = id.toString();
    this.displayContact = this.contactList.find(contact => contact.id.toString() === contactId) as unknown as ContactDetails;
    this.cdf.detectChanges();
  }

  onLoadMore(): void {
    if (!this.isLastPage) {
      this.loadMoreIcon = true;
      this.pageNumber++;
      this.getContactList();
    }
  }

  searchContact(event: any): void {
    this.contactFilterParams.values.forEach((item: FilterValue) => {
      item.value = event.filter;
    });
    this.globalSearch.next(this.contactFilterParams.values);
  }

  displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(500)).subscribe(() => {
      this.contactList = [];
      this.pageNumber = 1;
      this.isLastPage = false;
      this.getContactList();
      this.cdf.detectChanges();
    });
  }

  displaySalePersonDetails(id: number): void {
    this.displaySalesPerson = this.purchasingAgent.find(salePerson => salePerson.id === id);
    this.cdf.detectChanges();
  }

  isOptionDisabled(option: ContactDetails): boolean {
    const selectedValue = this.holdDetailsFormGroup?.get('buyerInformationId')?.value;
    return option.archived && option.id !== selectedValue;
  }

  onSubmit(close = true): void {
    if (this.holdDetailsFormGroup.invalid) {
      this.holdDetailsFormGroup.markAllAsTouched();
      return;
    }
    this.editHoldUnit();
  }

  editHoldUnit(): void {
    this.holdUnitService.update(this.holdUnitCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }
}
