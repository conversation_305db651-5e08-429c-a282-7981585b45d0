import { OrderByParam } from "./sort.model";

type FilterValueTypes = string | boolean | number | string[] | number[];
export class GenericFilterParams {
  treeOperator!: TreeOperatorType;
  values!: FilterValue[];
  orderBy!: OrderByParam[];
  isIncomingTruck?: boolean;
}

export class FilterValue {
  dataType: DataType = DataType.STRING;
  key!: string;
  operator: OperatorType = OperatorType.LIKE;
  value!: FilterValueTypes;
  enumName?: string;
}

export enum TreeOperatorType {
  OR = 'OR',
  AND = 'AND',
  NOT = 'NOT',
  NOOP = 'NOOP',
}

export enum DataType {
  STRING = 'STRING',
  LONG = 'LONG',
  DOUBLE = 'DOUBLE',
  DECIMAL = 'DECIMAL',
  INTEGER = 'INTEGER',
  FLOAT = 'FLOAT',
  DATE = 'DATE',
  BOOLEAN = 'BOOLEAN',
  ENUM = 'ENUM'
}

export enum OperatorType {
  LIKE = 'LIKE',
  EQUAL = 'EQUAL',
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
  NOT_EQUAL = 'NOT_EQUAL',
  GREATER_THAN_OR_EQUAL = 'GREATER_THAN_OR_EQUAL',
  LESS_THAN_OR_EQUAL = 'LESS_THAN_OR_EQUAL',
  IN = 'IN',
}

export class CalendarFilterParams {
  treeOperator!: TreeOperatorType;
  entity!: string;
  left!: LeftFilterParams;
  userId!: number | null;
}

export class LeftFilterParams {
  treeOperator!: TreeOperatorType;
  values!: FilterValue[];
}
