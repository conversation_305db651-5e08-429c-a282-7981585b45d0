@import 'src/assets/scss/variables';

.green-highlight {
  color: $green-color;
}

.btn {
  width: 100px;
  padding: 0 20px !important;
}

.action {
  text-align: center !important;
  justify-content: center;
}

.quotationAccepted {
  font-size: 13px;
  color: $green-color;
}

.quotationRejected {
  font-size: 13px;
  color: var(--danger-color);
}

::ng-deep {
  .crm-quotation {
    .p-datatable .p-datatable-thead > tr > th:last-child {
      text-align: center;
    }
  }
  .p-sidebar-content {
    height: 100vh;
  }
}
.m-l-10 {
  margin-left: 10px;
}

.m-r-10 {
  margin-right: 10px;
}

.resend-btn {
  height: 1.2rem;
}

.stock-btn {
  width: auto !important;
  padding: 0 !important;
}
