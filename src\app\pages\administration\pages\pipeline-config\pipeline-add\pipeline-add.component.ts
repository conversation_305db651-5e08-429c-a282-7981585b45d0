import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { DealerBasicInfoListItem, PipelineConfigCreateParam, PipelineConfigListItem, Shop, ShopUser, TemplatePipelinePhase } from '@pages/administration/models';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { User } from '@sentry/angular';
import { takeUntil } from 'rxjs';
import { DealerService } from '../../dealers/dealers.service';
import { PipelineConfigService } from '../pipeline-config.service';
import { ShopService } from '../shop.service';

@Component({
  selector: 'app-pipeline-add',
  templateUrl: './pipeline-add.component.html',
  styleUrls: ['./pipeline-add.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default
})
export class PipelineAddComponent extends BaseComponent implements OnInit, OnChanges {

  title = 'Add Template Pipeline';
  isSaving = false;
  pipelineConfigFormGroup!: UntypedFormGroup;
  redirectUrl!: string;
  hasDataBeenModified = false;
  isEditMode = false;
  dealers: DealerBasicInfoListItem[] = [];
  shops: Shop[] = [];
  users: User[] = [];
  shopUsersMap: Map<number, ShopUser[]> = new Map();
  loaders = {
    dealers: false,
    shops: false,
    shopUsers: false,
  }

  @Input() pipelineConfig!: PipelineConfigListItem | null;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly pipelineConfigService: PipelineConfigService,
    private readonly toasterService: AppToasterService,
    private readonly dealerService: DealerService,
    private readonly shopService: ShopService,
    private readonly cdf: ChangeDetectorRef,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly router: Router,
    private readonly activeRoute: ActivatedRoute,
    private readonly commonSharedService: CommonSharedService
  ) {
    super();
  }

  ngOnInit(): void {
    this.commonSharedService.setBlockUI$(true);
    this.initializeFormGroup();
    this.getShops();
    this.getUsers();
    this.activeRoute.queryParams.subscribe(params => {
      if (params?.returnUrl) {
        this.redirectUrl = params?.returnUrl;
      }
    });
    this.commonSharedService.setBlockUI$(false);
    this.cdf.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.pipelineConfig?.currentValue) {
      this.title = 'Edit template pipeline';
      this.isEditMode = true;
      this.isLoading = true;
    }
  }

  private initializeFormGroup(): void {
    this.pipelineConfigFormGroup = this.fb.group({
      id: new UntypedFormControl(null),
      title: new UntypedFormControl(null, [Validators.required]),
      templatePipelinePhases: this.fb.array([this.newTemplatePipelinePhaseFormGroup]),
    });
  }

  get templatePipelinePhasesFormArray(): UntypedFormArray {
    return this.pipelineConfigFormGroup.get('templatePipelinePhases') as UntypedFormArray;
  }

  get newTemplatePipelinePhaseFormGroup(): UntypedFormGroup {
    return this.fb.group({
      id: new UntypedFormControl(null),
      templatePipelineId: new UntypedFormControl(null),
      name: new UntypedFormControl(null),
      assigneeId: new UntypedFormControl(null, [Validators.required]),
      assigneeName: new UntypedFormControl(null),
      sequenceNumber: new UntypedFormControl(1),
      shopId: new UntypedFormControl(null, [Validators.required]),
    });
  }

  getCurrentTemplatePipelineFormGroup(index: number): UntypedFormGroup {
    return this.templatePipelinePhasesFormArray.at(index) as UntypedFormGroup;
  }

  get pipelineConfigCreateParams(): PipelineConfigCreateParam {
    return {
      ...this.pipelineConfigFormGroup.value,
      templatePipelinePhases: this.templatePipelinePhasesFormArray.value
    };
  }

  getAvailableShops(index: number): Shop[] {
    const phases: TemplatePipelinePhase[] = this.templatePipelinePhasesFormArray.value;
    const selectedShops = this.shops.filter(shop => phases.some(phase => phase.shopId === shop.id));
    const templatePipelinePhase: TemplatePipelinePhase = this.templatePipelinePhasesFormArray.at(index).value;
    const otherShops: Shop[] = this.shops.filter(shop => !selectedShops.some(selectedShop => selectedShop.id === shop.id));
    const currentShop = this.shops.find(shop => shop.id === templatePipelinePhase.shopId);
    if (currentShop) {
      otherShops.unshift(currentShop);
    }
    return otherShops;
  }

  private updateNameInTemplatePipelinePhases(): void {
    for (const [index, pipelinePhaseControl] of this.templatePipelinePhasesFormArray.controls.entries()) {
      const PipelinePhaseFormGroup = pipelinePhaseControl as UntypedFormGroup;
      const nameControl = PipelinePhaseFormGroup.get('name');
      const shopId = PipelinePhaseFormGroup.get('shopId')?.value;
      const shop = this.shops.find(shop => shop.id === shopId);
      if (shop && nameControl) {
        nameControl.patchValue(shop.name);
      }
      PipelinePhaseFormGroup.get('sequenceNumber')?.patchValue(index + 1);
    }
  }

  get isAddNewShopVisible(): boolean {
    const templatePipelinePhases = this.templatePipelinePhasesFormArray.value;
    return templatePipelinePhases.length < this.shops.length;
  }

  get isDeleteShopVisible(): boolean {
    return this.templatePipelinePhasesFormArray.length > 1;
  }

  getSequenceNumber(index: number): number {
    return Number(index) + 1;
  }

  onAddNewShop(): void {
    const formGroup = this.newTemplatePipelinePhaseFormGroup;
    this.templatePipelinePhasesFormArray.push(formGroup);
    document.getElementById('addShopBtn')?.scrollIntoView({
      behavior: 'smooth',
    });
  }

  onDeleteShop(index: number): void {
    if (this.templatePipelinePhasesFormArray.length > 1) {
      this.templatePipelinePhasesFormArray.removeAt(index);
    }
  }

  onSubmit(close = true): void {
    this.isSaving = true;
    this.updateNameInTemplatePipelinePhases();
    this.pipelineConfigFormGroup.updateValueAndValidity();

    if (this.pipelineConfigFormGroup.invalid) {
      this.pipelineConfigFormGroup.markAllAsTouched();
      this.isSaving = false;
      return;
    }
    if (this.isEditMode) {
      this.edit();
    } else {
      this.save(close);
    }
  }

  save(close = true): void {
    this.pipelineConfigService.add(this.pipelineConfigCreateParams).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(this.isEditMode ? MESSAGES.pipelineConfigUpdateSuccess : MESSAGES.pipelineConfigAddSuccess);
        this.hasDataBeenModified = true;
        this.isSaving = false;
        if (close) {
          this.onClose.emit(true);
        } else {
          this.pipelineConfigFormGroup.reset();
        }
      }
    });
  }

  edit(): void {
    this.pipelineConfigService.update(this.pipelineConfigCreateParams).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(this.isEditMode ? MESSAGES.pipelineConfigUpdateSuccess : MESSAGES.pipelineConfigAddSuccess);
        this.hasDataBeenModified = true;
        this.isSaving = false;
        this.onClose.emit(true);
      }
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.goBack();
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  private async setPipelineConfigInFormGroup(): Promise<void> {
    if (this.dealers?.length && this.pipelineConfig) {
      this.pipelineConfigFormGroup.patchValue({
        dealerId: this.pipelineConfig.dealerId,
        title: this.pipelineConfig.title,
        id: this.pipelineConfig.id
      });
      for (const [index] of this.pipelineConfig.templatePipelinePhases.entries()) {
        if (index !== 0) {
          this.templatePipelinePhasesFormArray.push(this.newTemplatePipelinePhaseFormGroup);
        }
      }
      this.templatePipelinePhasesFormArray.patchValue(this.pipelineConfig.templatePipelinePhases);
      this.updateNameInTemplatePipelinePhases();
      this.isLoading = false;
      this.cdf.detectChanges();
    }
  }

  private getAllDealers(): void {
    this.loaders.dealers = true;
    this.dealerService.getList<DealerBasicInfoListItem>(API_URL_UTIL.admin.dealers.basicInfos).pipe(takeUntil(this.destroy$)).subscribe(dealers => {
      this.dealers = dealers;
      this.loaders.dealers = false;
      if (this.isEditMode) {
        this.setPipelineConfigInFormGroup();
      }
      this.cdf.detectChanges();
    });
  }

  getShops(): void {
    this.loaders.shops = true;
    this.shopService.getList<Shop>().pipe(takeUntil(this.destroy$)).subscribe(shops => {
      this.shops = shops;
      this.loaders.shops = false;
      this.getAllDealers();
      if (this.pipelineConfigFormGroup.get('templatePipelinePhases')) {
        this.pipelineConfigFormGroup.controls.templatePipelinePhases = this.fb.array([this.newTemplatePipelinePhaseFormGroup]);
      }
      this.cdf.detectChanges();
    });
  }

  getUsers(): void {
    this.loaders.shopUsers = true;
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.users = res;
          if (this.pipelineConfig?.templatePipelinePhases) {
            this.templatePipelinePhasesFormArray.patchValue(this.pipelineConfig.templatePipelinePhases);
          }
          this.loaders.shopUsers = false;
        },
        error: () => {
          this.loaders.shopUsers = false;
        }
      });
  }

  getShopUsersByShopId(formArrayIndex: number): ShopUser[] {
    const templatePipelinePhase = this.templatePipelinePhasesFormArray.at(formArrayIndex).value;
    if (!templatePipelinePhase) {
      return [];
    }
    return this.shopUsersMap.get(templatePipelinePhase.shopId) || [];
  }

  setDefaultUser(index: number, event: any): void {
    const shop = this.shops.find(shop => shop.id === event?.value);
    const templatePipelineForm = this.templatePipelinePhasesFormArray.at(index);
    if (templatePipelineForm) {
      templatePipelineForm.get('assigneeId')?.patchValue(shop?.defaultUserId);
    }
    this.cdf.detectChanges();
  }
}
