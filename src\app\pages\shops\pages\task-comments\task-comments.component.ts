import { ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES, VALIDATION_CONSTANTS, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { AllUserListFilter, MentionUser } from '@pages/inventory/models/inventory-communication';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { TaskCommentCreateParam, TaskCommentListItem, TaskListItem } from '@pages/shops/models';
import { TaskService } from '@pages/shops/services/tasks.service';
import { MentionConfig } from 'angular-mentions';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { MENTION_CONFIG } from 'src/app/@shared/constants/common.constant';
import { MentionParam } from 'src/app/@shared/models';

@Component({
  selector: 'app-task-comments',
  templateUrl: './task-comments.component.html',
  styleUrls: ['./task-comments.component.scss']
})
export class TaskCommentsComponent extends BaseComponent implements OnInit {

  comments: TaskCommentListItem[] = [];
  commentFormGroup!: FormGroup;
  isAddingComment = false;

  @Input() taskInfo!: TaskListItem | null;
  @Input() isViewMode!: boolean | null;
  @Input() selectedCommentId!: number;
  mentionedUsers: MentionUser[] = [];
  availableMentionUsers: MentionParam[] = [];
  mentionConfig: MentionConfig = {
    ...MENTION_CONFIG,
    items: this.availableMentionUsers
  };
  @Input() filterParams: any = new AllUserListFilter();
  commentEditor: any;
  @ViewChild('commentEditor') set content(content: any) {
    if (content) {
      this.commentEditor = content;
    }
  }
  @ViewChild('boldText') boldText!: ElementRef;
  isFirstTime = true;
  currentUserId!: number;

  constructor(private readonly taskService: TaskService,
    private readonly formBuilder: FormBuilder,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly accountService: AuthService,
    private readonly cdf: ChangeDetectorRef) {
    super();
  }

  ngOnInit(): void {
    this.getTaskComments();
    this.initializeFormGroup();
    this.getUserId();
    this.getAll()
  }

  initializeFormGroup(): void {
    this.commentFormGroup = this.formBuilder.group({
      comment: new FormControl(null, [Validators.required, Validators.maxLength(VALIDATION_CONSTANTS.textAreaMaxLength)]),
      taskId: new FormControl(this.taskInfo?.id),
      id: new FormControl(null),
      mentionUserIds: new FormControl([])
    });
  }

  getUserId(): void {
    this.accountService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe((res) => {
      if (res?.id) {
        this.currentUserId = res.id;
      }
    });
  }

  get taskCommentCreateParams(): TaskCommentCreateParam {
    return this.commentFormGroup.value;
  }

  getTaskComments(): void {
    this.isLoading = true;
    const url = API_URL_UTIL.tasks.taskComments.replace(':taskId', this.taskInfo?.id.toString() || '');
    this.taskService.getList<TaskCommentListItem>(url).pipe(takeUntil(this.destroy$)).subscribe({
      next: comments => {
        this.isLoading = false;
        this.comments = comments;
        if (!this.comments?.length) {
          this.isAddingComment = true;
        }
      },
      error: () => {
        this.isLoading = false;
      }
    })
  }

  onAddNewCommentSubmit(): void {
    if (this.commentFormGroup.invalid) {
      this.commentFormGroup.markAllAsTouched();
      return;
    }
    this.commentFormGroup.markAsUntouched();
    if (this.taskCommentCreateParams?.id) {
      this.edit();
    } else {
      this.add();
    }
  }

  removeErasedUser(comment: string, ids: number[]): number[] {
    return ids.filter(id => {
      const label = this.availableMentionUsers.find(obj => obj.id === id)?.label;
      return label && comment.toLowerCase().includes(label.toLowerCase());
    });
  }

  add(): void {
    const params = this.taskCommentCreateParams
    params.mentionUserIds = this.mentionedUsers.map(i => i.id);
    if (this.commentFormGroup.controls['comment'].value.includes('&lt;strong')) {
      let val = this.commentFormGroup.controls['comment'].value.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['comment'].setValue(`${val}`);
      params.comment = this.commentFormGroup.controls['comment'].value;
    }
    params.mentionUserIds = this.removeErasedUser(params.comment, params.mentionUserIds);
    this.taskService.add<TaskCommentCreateParam>(params, API_URL_UTIL.tasks.comments).pipe(takeUntil(this.destroy$)).subscribe(savedComment => {
      this.toasterService.success(MESSAGES.commentAddSuccess);
      this.comments.push(savedComment);
      this.commentFormGroup.reset();
      this.isAddingComment = false;
      this.mentionedUsers = [];
    });
  }

  edit(): void {
    const params = this.taskCommentCreateParams
    params.mentionUserIds = [...new Set([...params.mentionUserIds, ...this.mentionedUsers.map(i => i.id)])];
    if (this.commentFormGroup.controls['comment'].value.includes('&lt;strong')) {
      let val = this.commentFormGroup.controls['comment'].value.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['comment'].setValue(`${val}`);
      params.comment = this.commentFormGroup.controls['comment'].value
    }
    params.mentionUserIds = this.removeErasedUser(params.comment, params.mentionUserIds);
    this.taskService.update<TaskCommentCreateParam>(params, API_URL_UTIL.tasks.comments).pipe(takeUntil(this.destroy$)).subscribe(savedComment => {
      this.toasterService.success(MESSAGES.commentUpdateSuccess);
      const index = this.comments.findIndex(comment => comment.id === (savedComment as TaskCommentCreateParam)?.id);
      if (index !== undefined && index > -1) {
        this.comments[index] = savedComment as TaskCommentListItem;
      }
      this.commentFormGroup.reset();
      this.isAddingComment = false;
      this.mentionedUsers = [];
    });
  }

  onAddComment(): void {
    this.commentFormGroup.patchValue({
      id: null,
      comment: null,
      taskId: this.taskInfo?.id,
      mentionUserIds: null
    });
    this.isAddingComment = true;
  }

  onCancel(): void {
    this.isAddingComment = false;
    this.mentionedUsers = [];
  }

  onEdit(taskComment: TaskCommentListItem): void {
    this.mentionedUsers = [];
    this.commentFormGroup.patchValue({
      id: taskComment.id,
      comment: taskComment.comment,
      taskId: taskComment.taskId,
      mentionUserIds: taskComment.mentionUserIds
    })
    this.isAddingComment = true;
  }

  onDelete(comment: TaskCommentListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'comment'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(comment);
      }
    });
  }

  onDeleteConfirmation(comment: TaskCommentListItem): void {
    this.taskService.delete(comment.id, API_URL_UTIL.tasks.comments)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.commentDeleteSuccess);
          const index = this.comments.findIndex(c => c.id === comment.id);
          if (index !== undefined && index > -1) {
            this.comments.splice(index, 1);
          }
        }
      });
  }
  mentionSelected(mention: any, childIndex = -1) {
    let count = 0;
    for (const i of this.mentionedUsers) {
      if (i.id === mention.id) {
        count++;
      }
    }
    if (count === 0) {
      this.mentionedUsers.push(mention);
    }
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.userAnnotationService.get(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          this.availableMentionUsers = res.map((user: any) => ({ id: user.id, label: user.name }));
          this.mentionConfig.items = this.availableMentionUsers;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: (err: any) => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  textChange(e: any) {
    if (e.htmlValue?.includes('&lt;strong')) {
      let val = e.htmlValue.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['comment'].setValue(`${val}`);
      setTimeout(() => {
        this.commentEditor.el.nativeElement.childNodes[0].lastChild.childNodes[0].focus();
        this.commentEditor.el.nativeElement.childNodes[0].lastChild.childNodes[0].click();
        this.boldText.nativeElement.click();
        this.commentEditor.getQuill().setSelection(val.length + 1);
      }, 0);
    }
  }

  selectionChange(e: any) {
    if (e && e.range && e.range.index > 0 && e.source === 'api') {
      this.boldText.nativeElement.click();
    }
  }

  decodeEntities(str: any) {
    // this prevents any overhead from creating the object each time
    const element = document.createElement('div');
    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
    element.innerHTML = str;

    return element.textContent;
  }

}
