import { HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Observable } from "rxjs";
import { ChangePasswordRequest } from "./models";

@Injectable({ providedIn: 'root' })
export class SettingsService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.account.root;
  }

  changePassword(changePasswordReq: ChangePasswordRequest): Observable<void> {
    return this.httpClient.post<void>(API_URL_UTIL.settings.changePassword, changePasswordReq);
  }

  changeDefultDealer(userId: number, dealerId: number): Observable<void> {
    const params: HttpParams = new HttpParams()
      .set('dealerId', dealerId);
    return this.httpClient.put<any>(`${API_URL_UTIL.admin.users.root}/${userId}/${API_URL_UTIL.admin.users.changeActiveDealer}`, undefined, { params });
  }
}
