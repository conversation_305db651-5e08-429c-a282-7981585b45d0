import { TitleCase<PERSON>ipe } from "@angular/common";
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { MESSAGES } from "@constants/*";
import { AppToasterService } from "@core/services";
import { BaseComponent } from "@core/utils";
import { SpecificationService } from "@pages/administration/pages/specification-configration/pages/specification/specification.service";
import { InventorySpecification, InventorySpecificationFields, InventorySpecificationResponse } from "@pages/inventory/models";
import { takeUntil } from "rxjs";


@Component({
  selector: 'app-add-specification-field-option',
  templateUrl: './add-specification-field-option.component.html',
  styleUrls: []
})
export class AddSpecificationFieldOptionComponent extends BaseComponent implements OnInit {
  title = 'Add Field Options';
  fieldOptionsFormGroup!: FormGroup;

  @Output() onClose: EventEmitter<void> = new EventEmitter<void>();
  @Input() categoryId!: number | null;
  @Input() selectedSpecification!: InventorySpecification;
  @Input() selectedSpecificationField!: InventorySpecificationFields;
  @Input() originalMasterSpecification!: InventorySpecificationResponse;
  @Output() updateSpecificationFieldOptions: EventEmitter<Array<string>> = new EventEmitter<Array<string>>();

  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly specificationService: SpecificationService,
    private readonly toasterService: AppToasterService,
    private readonly titlecasePipe: TitleCasePipe

  ) { super(); }

  ngOnInit(): void {
    this.initializeFormGroup();
  }

  private initializeFormGroup(): void {
    this.fieldOptionsFormGroup = this.formBuilder.group({
      options: new FormControl(null, Validators.required)
    });
  }

  onCancel(): void {
    this.onClose.emit();
  }

  onSubmit(): void {
    if (this.fieldOptionsFormGroup.invalid) {
      this.fieldOptionsFormGroup.markAllAsTouched();
      return;
    }
    this.save();
  }

  save(): void {
    const masterDataSpecification = this.originalMasterSpecification;
    const selectedSpecificationIndex = masterDataSpecification.masterData.specification.findIndex(specification => this.selectedSpecification.sectionName === `${specification.sectionName}${this.selectedSpecification?.duplicateGeneratedSectionsCount ? ' - 1 ' : ''}`)
    const selectedSpecificationFieldIndex = masterDataSpecification.masterData.specification[selectedSpecificationIndex].fields.findIndex(field => field.id === this.selectedSpecificationField.id);
    const optionsToBeAdded = this.fieldOptionsFormGroup.get('options')?.value?.split(',');
    const lastOptionIndex = masterDataSpecification.masterData.specification[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].options.length - 1;
    const lastOptionId = masterDataSpecification.masterData.specification[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].options[lastOptionIndex]?.id
    for (const [index, option] of optionsToBeAdded.entries()) {
      if (!masterDataSpecification.masterData.specification[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].options.some(o => o.name === this.titlecasePipe.transform(option))) {
        masterDataSpecification.masterData.specification[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].options.push({
          id: index + lastOptionId + 1,
          name: this.titlecasePipe.transform(option)
        })
      }
    }
    this.specificationService.update<InventorySpecificationResponse>(masterDataSpecification).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: InventorySpecificationResponse) => {
        this.onCancel();
        this.updateSpecificationFieldOptions.emit(this.fieldOptionsFormGroup.get('options')?.value?.split(','));
        this.toasterService.success(MESSAGES.specificationnFieldOptionsAddedSuccess);
      }
    });
  }

}
