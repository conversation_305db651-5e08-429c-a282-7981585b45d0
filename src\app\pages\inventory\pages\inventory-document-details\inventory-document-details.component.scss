@import "src/assets/scss/variables";

form {
  height: calc(100vh - 57px);
}

.modal-footer {
  position: absolute;
}

p-radiobutton {
  margin-right: 5px;
}

::ng-deep .document-label {
  color: $contact-company-gray-color;
  font-size: 15px;
}

.document-other-details {
  margin-top: 40px;
}

.document-label-detail {
  color: var(--text-color);
}

.disabled {
  pointer-events: none;
  background-color: #ededed !important;
}

.float-right {
  float: right;
}

.view-icon {
  width: 15px;
  cursor: pointer;
  margin-right: 15px;
}
