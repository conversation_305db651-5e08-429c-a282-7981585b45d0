@import 'src/assets/scss/variables';

.contact-info .row {
  margin-top: auto;
}

::ng-deep .crm-contact p-dropdown-label {
  color: $disable-color !important;
}

::ng-deep .crm-contact {
  .p-disabled,
  .p-component:disabled,
  textarea:disabled {
    color: $disable-color;
    opacity: 0.4;

    .p-dropdown-label {
      color: $disable-color !important;
    }
  }

  .map-icon {
    width: 200px;
    margin-top: 23px;

    button {
      display: flex;
      flex-direction: row-reverse;
      height: 100%;
      justify-content: center;
      align-items: center;
      padding: 10px 20px;
      scroll-margin-bottom: 100px;

      img {
        margin-right: 10px;
      }
    }
  }

  .m-t-20 {
    margin-top: 20px;
  }
  .f-s-12 {
    font-size: 12px;
  }

  .header-title {
    top: -8px;
    position: relative;
  }
  .created-by {
    font-size: 15px;
    color: var(--text-color);
    font-weight: 400;
    position: absolute;
    top: 30px;
  }
  .bold-text {
    font-size: 16px;
    color: var(--text-color);
    margin-right: 5px;
  }

  .p-tabview-nav-container {
    overflow-x: auto;
  }

  td.empty {
    display: flex;
    width: 100%;
    justify-content: center;
  }
}

::ng-deep .p-tooltip {
  max-width: 250px;
}
