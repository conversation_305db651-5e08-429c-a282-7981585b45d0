import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ContactDetails, CrmContactListItem } from '@pages/administration/models/crm.model';
import { DealerService } from '@pages/administration/pages/dealers/dealers.service';
import { QuatationListFilter, QuatationListItem } from '@pages/crm/models/quatation.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { InventoryListItem, SalesInformation, SalesPersonDetails, SaleTypeList, SaleUnitCreateParam } from '@pages/inventory/models';
import { FinancialService } from '@pages/inventory/services/financial.service';
import { SaleUnitService } from '@pages/inventory/services/sale-unit.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { DataType, FilterValue, GenericFilterParams, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';

@Component({
  selector: 'app-add-new-sale',
  templateUrl: './add-new-sale.component.html',
  styleUrls: ['./add-new-sale.component.scss']
})
export class AddNewSaleComponent extends BaseComponent implements OnInit, OnChanges {
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  title = 'Sale Information';
  hasDataBeenModified = false;
  saleFormGroup!: FormGroup;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() isViewMode!: boolean;
  purchasingAgent: SalesPersonDetails[] = [];
  dealerOptions: IdNameModel[] = [];
  accordionTabs = {
    saleInfo: true,
    buyerInfo: true,
    salePersonInfo: true,
    archiveSaleInfo: true
  };
  loaders = {
    location: false,
    purchasingAgent: false,
    previousOwnerName: false
  };
  saleTypeList = SaleTypeList;
  unitId!: number | undefined;
  saleInformation!: SalesInformation;
  contactList: CrmContactListItem[] = [];
  displayContact!: ContactDetails | undefined;
  displaySalesPerson!: SalesPersonDetails | undefined;
  showCreateContact = false;
  @Input() filterParams: any = new QuatationListFilter();
  contactFilterParams: GenericFilterParams;
  quatationList: QuatationListItem[] = [];
  key = "quotationUnits.unit.id";
  quotationData!: QuatationListItem[];
  isLastPage = false;
  loadMoreIcon = false;
  pageNumber = 1;
  globalSearch = new Subject<FilterValue[]>();
  constructor(private readonly formBuilder: FormBuilder, private readonly crmServices: CrmService, private readonly cdf: ChangeDetectorRef, private readonly toasterService: AppToasterService, private readonly crmService: CrmService, private readonly saleUnitService: SaleUnitService, private readonly financialService: FinancialService, private readonly dealerService: DealerService, private readonly userAnnotationService: UserAnnotationService) { super() }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getPurchasingAgentList();
    this.getDealersList();
    this.getUnitIdBySaleUnit();
    this.getAllQuatationList();
    this.contactFilterParams = this.contactListParams;
    this.getContactList();
    this.displaySearchResult();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (!this.isViewMode) {
      this.saleFormGroup?.enable();
    }
  }
  private initializeFormGroup(): void {
    this.saleFormGroup = this.formBuilder.group({
      sellType: new FormControl('RETAILER', [Validators.required]),
      sellPrice: new FormControl(null),
      salesPersonId: new FormControl(null, [Validators.required]),
      invoiceDate: new FormControl(null, [Validators.required]),
      buyerInformationId: new FormControl(null, [Validators.required]),
      titleProcessed: new FormControl(false),
      paymentCompleted: new FormControl(false),
      deliveredOrPickup: new FormControl(false),
      quotationId: new FormControl(null),
    });
    if (this.isViewMode) {
      this.saleFormGroup.disable();
    }
  }

  get contactListParams(): GenericFilterParams {
    return {
      "treeOperator": TreeOperatorType.OR,
      "values": [
        {
          "dataType": DataType.STRING,
          "key": "firstName",
          "operator": OperatorType.LIKE,
          "value": ""
        },
        {
          "dataType": DataType.STRING,
          "key": "lastName",
          "operator": OperatorType.LIKE,
          "value": ""
        },
        {
          "dataType": DataType.STRING,
          "key": "company",
          "operator": OperatorType.LIKE,
          "value": ""
        }
      ],
      "orderBy": [
        {
          "ascending": true,
          "field": "id"
        }
      ]
    };
  }

  getUnitIdBySaleUnit(): void {
    this.isLoading = true;
    if (this.inventoryIncomingInfo) {
      this.unitId = this.inventoryIncomingInfo.unitId;
    }
    else {
      this.unitId = this.inventoryInfo?.id;
    }
    this.financialService.geUnitIdBySaleInfo(Number(this.unitId)).pipe(takeUntil(this.destroy$)).subscribe(saleInformation => {
      this.saleInformation = saleInformation;
      this.displayContact = this.saleInformation?.buyerInformation;
      if (this.displayContact?.firstName || this.displayContact?.lastName) {
        this.displayContact.name = `${this.displayContact?.firstName} ${this.displayContact?.lastName}`;
      }
      this.displaySalesPerson = this.saleInformation?.salesPerson;
      this.setSaleInfoFormGroup();
    });
  }

  private async setSaleInfoFormGroup(): Promise<void> {
    if (this.saleInformation) {
      this.saleFormGroup.patchValue({
        sellType: this.saleInformation?.sellType || 'RETAILER',
        sellPrice: this.saleInformation?.sellPrice,
        quotationId: this.saleInformation?.quotationId,
        salesPersonId: this.saleInformation?.salesPerson?.id,
        buyerInformationId: this.saleInformation?.buyerInformation?.id,
        titleProcessed: this.saleInformation?.titleProcessed,
        paymentCompleted: this.saleInformation?.paymentCompleted,
        deliveredOrPickup: this.saleInformation?.deliveredOrPickup,
        invoiceDate: this.saleInformation?.invoiceDate ? new Date(`${this.saleInformation?.invoiceDate}`) : '',
      });
      this.cdf.detectChanges();
    }
  }

  private getPurchasingAgentList(): void {
    this.loaders.purchasingAgent = true;
    this.userAnnotationService.get(API_URL_UTIL.userAnnotation.userFilter).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: SalesPersonDetails[]) => {
        this.purchasingAgent = res;
        this.loaders.purchasingAgent = false;
      },
      error: () => {
        this.loaders.purchasingAgent = false;
      }
    });
  }

  private getDealersList(): void {
    this.loaders.location = true;
    this.dealerService.getList<IdNameModel>(API_URL_UTIL.admin.dealers.basicInfo).pipe(takeUntil(this.destroy$)).subscribe({
      next: (dealerOptions) => {
        this.dealerOptions = dealerOptions;
        this.loaders.location = false;
      },
      error: () => {
        this.loaders.location = false;
      }
    });
  }

  onSubmit(): void {
    if (this.saleFormGroup.invalid) {
      this.saleFormGroup.markAllAsTouched();
      return;
    }
    this.editSaleUnit();
  }

  get saleUnitCreateParams(): SaleUnitCreateParam {
    return {
      ...this.saleFormGroup.value,
      unitId: this.saleInformation?.unit?.id,
      id: this.saleInformation?.id,
    };
  }

  editSaleUnit(): void {
    this.saleUnitService.update(this.saleUnitCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.financialUpdateSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  openContactModel(): void {
    this.showCreateContact = true;
  }

  private getContactList(): void {
    this.loaders.previousOwnerName = true;
    this.crmService.getListWithFiltersWithPagination<GenericFilterParams, CrmContactListItem>(this.contactFilterParams, this.pageNumber, 25, API_URL_UTIL.admin.crm.filter)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res) => {
            this.contactList = [...this.contactList, ...res.content];
            this.contactList.forEach(contact => {
              if (contact.company) {
                contact.contactName = `${contact.firstName} ${contact.lastName} (${contact.company})`;
              } else {
                contact.contactName = `${contact.firstName} ${contact.lastName}`;
              }
            });
            this.isLastPage = res.last;
            this.isLoading = false;
            this.loadMoreIcon = false;
            this.loaders.previousOwnerName = false;
            this.cdf.detectChanges();
          },
          error: () => {
            this.isLoading = false;
            this.loadMoreIcon = false;
            this.loaders.previousOwnerName = false;
            this.cdf.detectChanges();
          }
        });
  }

  displayContactDetails(id: number): void {
    const contactId = id.toString();
    this.displayContact = this.contactList.find(contact => contact.id.toString() === contactId) as unknown as ContactDetails;
    this.cdf.detectChanges();
  }

  onLoadMore(): void {
    if (!this.isLastPage) {
      this.loadMoreIcon = true;
      this.pageNumber++;
      this.getContactList();
    }
  }

  searchContact(event: any): void {
    this.contactFilterParams.values.forEach((item: FilterValue) => {
      item.value = event.filter;
    });
    this.globalSearch.next(this.contactFilterParams.values);
  }

  displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(500)).subscribe(() => {
        this.contactList = [];
        this.pageNumber = 1;
        this.isLastPage = false;
        this.getContactList();
        this.cdf.detectChanges();
      });
    }

  displaySalePersonDetails(id: number): void {
    this.displaySalesPerson = this.purchasingAgent.find(salePerson => salePerson.id === id);
    this.cdf.detectChanges();
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateContact = false;
    if (refreshList) {
      this.getContactList();
    }
  }
  get taskInfoParams() {
    return {
      treeOperator: TreeOperatorType.NOOP,
      values: [
        {
          dataType: DataType.LONG,
          key: this.key,
          operator: OperatorType.EQUAL,
          value: this.unitId?.toString()
        }
      ]
    };
  }

  getAllQuatationList(): void {
    this.isLoading = true;
    this.filterParams = this.taskInfoParams;
    this.filterParams.orderBy = this.orderBy;
    this.crmService.getListWithFiltersWithPagination<QuatationListFilter, QuatationListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.crm.quatationFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.quatationList = res.content;
          this.setPaginationParamsFromPageResponse<QuatationListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  selectQuotation(qutotationId: string): void {
    this.quotationData = this.quatationList.filter(id => id.id === qutotationId);
    this.quotationData.forEach((data: QuatationListItem) => {
      this.saleFormGroup.patchValue({
        sellPrice: data?.quotePrice,
        invoiceDate: data.quotationDate ? new Date(`${data.quotationDate}`) : '',
        salesPersonId: data?.createdBy?.id,
        buyerInformationId: data?.crmContact.id
      });
      this.displaySalePersonDetails(data?.createdBy?.id);
      this.displayContactDetails(data?.crmContact.id);
    })
  }

  isOptionDisabled(option: ContactDetails): boolean {
    const selectedValue = this.saleFormGroup?.get('buyerInformationId')?.value;
    return option.archived && option.id !== selectedValue;
  }
}
