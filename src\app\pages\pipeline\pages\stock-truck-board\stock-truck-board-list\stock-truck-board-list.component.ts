import { DatePipe, TitleCasePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, PipelineTypeList, dateFormat, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { PrivilegeActionResponseDTOs } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { ColumnItem, FilterList, FilterModuleName } from '@pages/common-table-column/models/common-table.column.model';
import { PipelineGetDetail, SoldTruckBoardListFilter, SoldTruckBoardListItem, StatusList, StatusListCopy } from '@pages/pipeline/models';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';
import { SoldTruckService } from '../../sold-truck-board/sold-truck.service';

@Component({
  selector: 'app-stock-truck-board-list',
  templateUrl: './stock-truck-board-list.component.html',
  styleUrls: ['./stock-truck-board-list.component.scss'],
  providers: [TitleCasePipe]
})
export class StockTruckBoardListComponent extends BaseComponent implements OnInit {
  stockTruckBoard: SoldTruckBoardListItem[] = [];
  filterParams: any = new SoldTruckBoardListFilter();
  showCreateModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.PIPELINE_STOCK;
  showConfirmPopup = true;
  selectedStockTruckBoard!: SoldTruckBoardListItem | PipelineGetDetail | null;
  isStatusList!: number | null;
  status!: string;
  statusList = StatusList;
  statusesCopy = StatusListCopy;
  isMouseHover: any;
  pipelineType!: string;
  pipelineTypeKey = 'pipelineType';
  pipelineTypeName = 'STOCK';
  enumName = 'PipelineType';
  showColumnModal = false;
  dropDownColumnList: ColumnItem[] = [];
  tableColumn!: FilterList | undefined;
  defaultColumnsArray: ColumnItem[] = [];
  _selectedColumns: any[] = [];
  cols: any[] = [];
  defaultTabs!: FilterList[];
  hideFieldForSearch: string | null = '';
  globalSearch = new Subject<FilterValue[]>();
  initialFilterParams: any;
  userPermissions!: PrivilegeActionResponseDTOs[]
  excelDownloadMenuItems: MenuItem[] = [
    {
      label: "Selected Columns",
      command: () => this.exportUsersToExcel()
    },
    {
      label: "All Columnns",
      command: () => this.exportUsersToExcel(true)
    }
  ];

  constructor(private readonly cdf: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly authService: AuthService,
    private readonly columnDropDownService: ColumnDropdownService,
    private readonly commonService: CommonService,
    private readonly soldTruckService: SoldTruckService,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly activeRoute: ActivatedRoute,
    private readonly titleCasePipe: TitleCasePipe) {
    super();
    this.pageTitle = 'Stock Truck Board';
  }
  async ngOnInit() {
    this.filterParams = this.stockTruckInfoParams
    await this.getCurrentUser();
    this.getFilterDetail();
    this.getFilterSaveParams();
    this.getAll();
    this.displaySearchResult();
    this.userPermissions = this.authService.getRoleInfo().privilegeActionResponseDTOs;
    this.initialFilterParams = Object.assign([], this.filterParams.values);
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getPipelineDetailById(params.id);
        }
      })
  }
  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }
  private getCurrentUser(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe({
        next: (user) => {
          if (user) {
            this.currentUser = user;
          }
          resolve();
        },
        error: () => {
          reject();
        }
      });
    });
  }
  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }
  get stockTruckInfoParams() {
    return {
      treeOperator: TreeOperatorType.NOOP,
      values: [
        {
          dataType: DataType.ENUM,
          key: this.pipelineTypeKey,
          operator: OperatorType.EQUAL,
          value: this.pipelineTypeName,
          enumName: this.enumName
        }
      ]
    };
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.pipelineType = PipelineTypeList.stockPipelineType;
    const endpoint = `${API_URL_UTIL.pipeline.list}`;
    this.soldTruckService.getListWithFiltersWithPagination<SoldTruckBoardListFilter, SoldTruckBoardListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.stockTruckBoard = res.content;
          this.setPaginationParamsFromPageResponse<SoldTruckBoardListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }
  getFilterSaveParams(): FilterList {
    return {
      module: FilterModuleName.STOCK_TRUCK_BOARD.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      id: this.tableColumn?.id,
      hideField: ''
    }
  }

  callFilterApiAgain() {
    this.getFilterDetail();
  }
  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.showConfirmPopup = true;
    this.selectedStockTruckBoard = null;
    if (refreshList) {
      this.getAll();
    }
  }

  onAdd(): void {
    this.showCreateModal = true;
    this.showConfirmPopup = false;
    this.cdf.detectChanges();
  }

  onEdit(task: SoldTruckBoardListItem | PipelineGetDetail): void {
    this.showCreateModal = true;
    this.showConfirmPopup = false;
    this.selectedStockTruckBoard = task;
    this.cdf.detectChanges();
  }

  onDelete(pipelineConfig: SoldTruckBoardListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'stock truck board data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(pipelineConfig);
      }
    });
  }

  onDeleteConfirmation(task: SoldTruckBoardListItem): void {
    this.soldTruckService.delete(task.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.stockTruckDataDeleted);
          this.getAll();
        }
      });
  }

  getTaskProgress(task: SoldTruckBoardListItem): number {
    return task?.taskPercentage > 100 ? 100 : task?.taskPercentage;
  }

  getTaskProgressClass(task: SoldTruckBoardListItem) {
    const progress = task?.taskPercentage;
    return this.soldTruckService.getTaskProgressClass(progress);
  }

  getStatusName(status: string) {
    return this.soldTruckService.getStatusName(status);
  }

  findStatusIndex(index: number): void {
    this.isStatusList = index
  }

  changeStatus(status: any, id: number): void {
    const endpoint = API_URL_UTIL.pipeline.status.replace(':pipelineId', id.toString());
    const params = {
      status: status,
      targetCompletedDateFlag: false
    }
    this.soldTruckService.update(params, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.statusChangeSuccess);
      this.isStatusList = null;
      this.getAll();
    })
  }

  toggleFilterSidebar() {
    this.showColumnModal = true;
  }

  toggleColumnSidebar() {
    this.showColumnModal = !this.showColumnModal;
  }

  clearDate() {
    this.filterParams.values = this.filterParams.values.filter((param: any) => param.key !== "createdDate");
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  createDefaultColumns(): void {
    const defaultFields = this.dropDownColumnList.filter(a => a.default ? a.name : null)
    if (this.tableColumn === undefined && defaultFields?.length) {
      this._selectedColumns = defaultFields;
      this.columnDropDownService.add<FilterList>(this.filterInfoParams, '').pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          if (res.filterType === 'COLUMN') {
            this.tableColumn = res;
            this.defaultColumnsArray = JSON.parse(res.data);
            this.sortSelectedColumns()
            this.cdf.detectChanges();
          }
        }
      });
    }
  }
  get filterInfoParams() {
    return {
      id: this.tableColumn?.id,
      module: FilterModuleName.STOCK_TRUCK_BOARD.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      isDefault: false,
      deleted: false,
      isPrivate: true,
      filterName: 'Column filter',
      filterType: 'COLUMN',
      data: JSON.stringify(this.selectedColumns),
      hideField: null
    };
  }
  getDropDownColumnList(shouldCreateDefaultColumn = false) {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${'STOCK_TRUCK_BOARD'}`);
    this.commonService.getListFromObject<ColumnItem>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        if (shouldCreateDefaultColumn) {
          this.createDefaultColumns();
        } else {
          this.sortSelectedColumns()
        }
        this.cdf.detectChanges();
      });
  }

  getFilterDetail(): void {
    this.defaultColumnsArray = [];
    const userId = this.currentUser?.id ?? null
    const endpoint = `${API_URL_UTIL.filter.filterDataByUserIdAndModule}`.replace(':userId', String(userId)).concat(`?module=${FilterModuleName.STOCK_TRUCK_BOARD}`)
    this.columnDropDownService.getList<FilterList>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (filters) => {
        this.defaultTabs = filters.filter((item: any) => item.filterType === "TAB");
        this.tableColumn = filters.find((item: any) => item.filterType === "COLUMN")
        if (this.tableColumn && this.tableColumn?.data) {
          this.defaultColumnsArray = JSON.parse(this.tableColumn?.data)
          this.getDropDownColumnList();
          this.cdf.detectChanges();
        } else {
          this.getDropDownColumnList(true);
        }
      }
    });
  }

  getPipelineDetailById(id: number): void {
    this.soldTruckService.getPipelineDetailById(id).pipe(takeUntil(this.destroy$)).subscribe(pipelineDetail => {
      this.onEdit(pipelineDetail);
    });
  }

  sortColumnList(columns: ColumnItem[]) {
    const tempData: ColumnItem[] = columns.filter(x => x.name === 'Stock');
    const tempData1: ColumnItem[] = columns.filter(x => x.name !== 'Stock' && x.name !== 'Action');
    const tempData2: ColumnItem[] = columns.filter(x => x.name === 'Action');
    return tempData.concat(tempData1, tempData2);
  }

  sortSelectedColumns() {
    let temp: any[] = [];
    const ids = this.defaultColumnsArray.map(a => a.id);
    this.defaultColumnsArray = this.sortColumnList(this.defaultColumnsArray)
    this._selectedColumns = this.cols = this.defaultColumnsArray;
    if (this.tableColumn && this.tableColumn?.data) {
      temp = JSON.parse(this.tableColumn?.data);
    }
    this.dropDownColumnList.forEach((column: any) => {
      if (!ids.includes(column.id)) {
        temp.push(column);
      }
    });
    this.dropDownColumnList = temp;
    this.dropDownColumnList = this.sortColumnList(this.dropDownColumnList);
  }

  tableSearchByColumn(event: any, col: any) {
    this.isLoading = true;
    this.stockTruckBoard = [];
    const searchInput = this.getEventValue(event, col);
    this.getFilterInfo(searchInput, col);
    this.setSearchEndDate(event, col);
    this.globalSearch.next(this.filterParams.values);
    // Reset input code
    this.setValueForReset(searchInput, col);
  }

  setValueForReset(input: string, col: any) {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);
    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    } else if (col.key === 'status') {
      temp.value = this.titleCasePipe.transform(input.split('_').join(' '));
    } else {
      temp.value = input;
    }

    if (temp1) {
      temp1.value = temp.value;
    }

  }
  getEventValue(event: any, col: any) {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = event?.value?.split(" ")?.join("_")?.toUpperCase();
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format)
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  getFilterInfo(inputValue: any, col: any) {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.shortingKey)
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col.type) as DataType,
        key: col.shortingKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        if (inputValue?.length) {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
    if (col.type === 'DROP_DOWN' && (inputValue === 'ALL' || inputValue === '')) {
      const rm = this.filterParams.values.find((d: any) => d.key === 'status')
      if (rm) {
        this.filterParams.values.splice(this.filterParams.values.indexOf(rm), 1)
      }

    }
  }

  assignDataType(type: string): string {
    let stringDataType = '';
    switch (type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER
        break;
      case 'DROP_DOWN':
        stringDataType = DataType.ENUM
        break;
      case 'DATE':
        stringDataType = DataType.DATE
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE
        break;
      default:
        stringDataType = DataType.STRING
        break;
    }
    return stringDataType
  }

  assignOperator(type: string) {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DOUBLE':
        operatorType = OperatorType.EQUAL
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }

  displaySearchResult() {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
      this.getAll();
    });
  }

  setSearchEndDate(event: any, col: any) {
    const existingDate = this.filterParams.values.find((d: any) => d.key === col.key && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col.type) as DataType,
        key: col.shortingKey,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50)).toISOString();
  }

  setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString();
  }

  clearSearchInput() {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });

    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.filterParams.values = Object.assign([], this.initialFilterParams);
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  exportUsersToExcel(downloadAll = false): void {
    this.isExporting = true;
    this.soldTruckService.getListWithFiltersWithPagination<SoldTruckBoardListFilter, SoldTruckBoardListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, API_URL_UTIL.pipeline.list)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content, downloadAll)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "Stock-truck-board");
          this.isExporting = false;
        });
      });
  }

  getExcelData(incomingTruck: Array<SoldTruckBoardListItem>, downloadAll = false) {
    const excelData = incomingTruck.map(res => ({
      'Stock': res.stockNumber,
      'Created By': res.createdBy,
      'Creation Date': Utils.dateIntoUserReadableFormat(res?.createdDate ?? ''),
      'Status': res.status,
      'Timeline': res.taskPercentage,
      'Sales Person': res.pipelineOwner.name,
      'Pipeline': res.pipeline,
      'Pipeline Type': res.pipelineType,
    }));
    if (!downloadAll) {
      const selectedKeysToBeDisplayedInExcel = this.defaultColumnsArray.map(({ name }) => name);
      for (const [index, data] of excelData.entries()) {
        for (const key in data) {
          if (!selectedKeysToBeDisplayedInExcel.includes(key)) {
            delete (excelData as any)[index][key]
          }
        }
      }
    }

    return excelData;
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
