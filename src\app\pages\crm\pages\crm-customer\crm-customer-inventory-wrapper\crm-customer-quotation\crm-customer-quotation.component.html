<div class="card tabs crm-quotation">
  <div class="tab-content">
    <p-table
      class="no-column-selection"
      *ngIf="quotationCustomerList"
      [value]="quotationCustomerList"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      [rowHover]="true"
      [loading]="isLoading"
      [scrollable]="true"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th scope="col">#Quote ID</th>
          <th scope="col">#Stock</th>
          <th>Associated Stocks</th>
          <th scope="col">Lead Id</th>
          <th scope="col">Company</th>
          <th scope="col">Date Quoted</th>
          <th scope="col">Quoted By</th>
          <th scope="col">Unit Description</th>
          <th scope="col">Quoted Price</th>
          <th scope="col">Retail Price</th>
          <th scope="col" class="min-width-220 justify-content-center">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td class="view-reminder">
            <span (click)="onView(rowData)">
              {{ rowData?.id }}
            </span>
          </td>
          <td>
            {{ getPrimaryStocks(rowData)?.stockNumber }}
          </td>
          <td>
            <ng-container [ngTemplateOutlet]="associatedStocks" [ngTemplateOutletContext]="{ associatedStock: getAssociatedStocks(rowData) }"> </ng-container>
          </td>
          <td>
            {{ rowData?.customerLeadId ? rowData?.customerLeadId : '-' }}
          </td>
          <td>
            {{ rowData?.company }}
          </td>
          <td>
            {{ rowData?.quotationDate | date : constants.fullDateFormat }}
          </td>
          <td>
            {{ rowData?.createdBy?.name }}
          </td>
          <td>
            <ng-container [ngTemplateOutlet]="primaryUnitStocksDetails" [ngTemplateOutletContext]="{ primaryStock: getPrimaryStocks(rowData) }"> </ng-container>
          </td>
          <td class="green-highlight"><span *ngIf="rowData?.totalQuotePrice">$</span>{{ rowData?.totalQuotePrice }}</td>
          <td><span *ngIf="rowData?.totalRetailPrice">$</span>{{ rowData?.totalRetailPrice ? rowData?.totalRetailPrice : '-' }}</td>
          <td class="action min-width-220">
            <div class="actions-content">
              <div class="d-flex">
                <button id="button-container" type="button" class="btn btn-primary" (click)="onAccepted(rowData?.id)" *ngIf="rowData?.quotationStatus === null">Accept</button>
                <button id="button-container" type="button" class="btn btn-secondary m-l-10" (click)="onReject(rowData?.id)" *ngIf="rowData?.quotationStatus === null">
                  Reject
                </button>
              </div>
              <span *ngIf="rowData?.quotationStatus === quotationStatus.ACCEPTED" class="quotationAccepted">Accepted</span>
              <span *ngIf="rowData?.quotationStatus === quotationStatus.REJECTED" class="quotationRejected">Rejected</span>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="6" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
  </div>
</div>
<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showRejectModel"
  position="right"
  (onHide)="showRejectModel = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-customer-reject-quotation (onClose)="onAddEditPopupClose($event)" [selectedQuotationId]="selectedQuotationId" *ngIf="showRejectModel">
  </app-crm-customer-reject-quotation>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-customer-inventory-details *ngIf="showCreateModal" [quotationDetails]="selectedQuotation" [crmId]="crmId" [isViewMode]="true" (closeModal)="onCloseModal()">
  </app-crm-customer-inventory-details>
</p-sidebar>

<ng-template #associatedStocks let-associatedStock="associatedStock">
  <span *ngFor="let unit of associatedStock; let index = index">
    {{ unit?.stockNumber }}
    <span *ngIf="index !== associatedStock?.length - 1">,</span>
  </span>
</ng-template>

<ng-template #primaryUnitStocksDetails let-primaryStock="primaryStock">
  <img height="70" [src]="primaryStock?.imageUrl" alt="" />
  <span>
    {{ primaryStock?.yearMakeModel }}
  </span>
</ng-template>
