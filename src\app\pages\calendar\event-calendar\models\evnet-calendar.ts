export enum EventType {
  ALL = 'All',
  TASK = 'Task',
  DRIVER_SCHEDULE = 'DriverSchedule'
}

export class User {
  name!: string;
  phoneNumber!: string;
  id!: number;
  email!: string;
  roleId!: number;
  roleName!: string;
}

export class CalenderEventResponse {
  driverScheduleId!: number | null;
  driverScheduleStatus!: string | null;
  endDate!: Date;
  entityName!: string;
  startDate!: Date;
  taskType!: string | null;
  title!: string;
  userName!: string
}

export class CalenderEventMenipulateResponse {
  title!: string;
  start!: string | null;
  end!: string | null;
  color!: string | undefined;
  userName!: string;
  driverScheduleId!: number | null;
  entityName!: string;
  taskType!: string | null;
  initialTitle!: string;
  driverScheduleStatus!: string | null;
}

export const DropdownOptions = [
  { value: 'All', name: 'All' },
  { value: 'Task', name: 'Task' },
  { value: 'DriverSchedule', name: 'Driver Schedule' }
]

export const CalenderEventViewDetail = [
  { label: 'All', code: '', value: 'All', entity: 'All' },
  { label: 'Driver Schedule', code: '#2563eb', value: 'DriverSchedule', entity: 'DriverSchedule' },
  { label: 'Sales Task', code: '#219055', value: 'Sales', entity: 'Task' },
  { label: 'General Task', code: '#A72D63', value: 'General', entity: 'Task' },
  { label: 'Sold Pipeline Task', code: '#F4D03F', value: 'Sold Pipeline', entity: 'Task' },
  { label: 'Stock Pipeline Task', code: '#707B7C', value: 'Stock Pipeline', entity: 'Task' }
]
