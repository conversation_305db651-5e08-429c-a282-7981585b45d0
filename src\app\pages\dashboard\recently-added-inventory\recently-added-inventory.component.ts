import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { InventoryListGlobalFilter, InventoryListItem } from '@pages/inventory/models';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { takeUntil } from 'rxjs';
import { DataType, OperatorType, TreeOperatorType } from 'src/app/@shared/models';

@Component({
  selector: 'app-recently-added-inventory',
  templateUrl: './recently-added-inventory.component.html',
  styleUrls: []
})
export class RecentlyAddedInventoryComponent extends BaseComponent {
  @Input() isFullViewRecentInventory!: boolean;
  @Output() onClose = new EventEmitter<boolean>();
  inventories: InventoryListItem[] = [];

  constructor(private readonly inventoryService: InventoryService,
    private readonly cdf: ChangeDetectorRef,) {
    super();
  }

  setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString();
  }

  get recentDate(): string {
    const dte = new Date();
    dte.setDate(dte.getDate() - 1);
    return this.setStartDate(new Date(dte).toISOString())
  }

  getAllRecentInventory(): void {
    this.isLoading = true;
    const recentFilter: InventoryListGlobalFilter = {
      archived: false,
      treeOperator: TreeOperatorType.NOOP,
      values: [{
        dataType: DataType.DATE,
        key: "createdDate",
        operator: OperatorType.GREATER_THAN_OR_EQUAL,
        value: this.recentDate
      }],
      orderBy: [{ ascending: false, field: "generalInformation.stockNumber" }]
    }
    this.inventoryService.add(recentFilter, API_URL_UTIL.inventory.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.inventories = res.content;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onCancel(): void {
    this.onClose.emit(true);
  }
}
