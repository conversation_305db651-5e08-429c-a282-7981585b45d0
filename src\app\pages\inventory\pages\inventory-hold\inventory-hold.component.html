<div class="modal-title close">
  <div class="model-content">
    <h4 class="header-title">On Hold</h4>
  </div>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
</div>

<div class="inventory-matching-wrapper">
  <div [ngClass]="{ 'inventory-action-label': true, gray: true }">Please select inventory which are now on hold</div>

  <form [formGroup]="inventoryQuotationFormGroup">
    <div class="customer-inventory-matched" formGroupName="content">
      <p-card *ngFor="let quotationDetail of quotationDetailsFormArray?.controls; let quotationIndex = index">
        <div class="inventory-content-wrapper" [formGroupName]="quotationIndex">
          <div class="content">
            <p-checkbox class="customer-inventory-checkbox" [binary]="true" formControlName="isSelected" [disabled]="!quotationIndex"> </p-checkbox>
            <img [src]="quotationDetail?.value?.unitImages?.fullUrl ? quotationDetail?.value?.unitImages?.fullUrl : constants.staticImages.noImages" alt="" />
            <div class="associated-unit">
              <div class="model-name">
                <span>
                  {{ quotationDetail?.value?.yearMakeModel }}
                </span>
              </div>
              <div class="stockNumber">
                <span class="model-detail-label"> #Stock : </span>
                <span class="model-detail-info">
                  {{ quotationDetail?.value.stockNumber ? quotationDetail?.value.stockNumber : 'NA' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </p-card>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
      <button class="btn btn-primary" type="submit" (click)="onSubmit($event)">Save</button>
    </div>
  </form>
</div>
