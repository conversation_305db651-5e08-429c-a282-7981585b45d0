<div class="modal-title close">
    <h4 class="header-title">{{ pageTitle }}</h4>
    <fa-icon [icon]="faIcons.faTimes" (click)="closePopup()"></fa-icon>
  </div>
  <ng-container>
    <div class="quote-field-form content">
      <label class="required mt-3 mb-2">Funding Fields</label>
      <p-multiSelect
        styleClass="w-100"
        appendTo="body"
        [options]="fundingFieldConfigList"
        [(ngModel)]="selectedfields"
        optionLabel="label"
        optionValue="id"
        required
        [filter]="true"
        filterBy="label"
        placeholder="Select Fields"
        selectedItemsLabel="{0} fields selected"
        optionDisabled="isDefault"
        >
        <ng-template pTemplate="empty">
          <ng-container [ngTemplateOutlet]="emptyMessage"></ng-container>
        </ng-template>
        <ng-template let-item pTemplate="item">
          <span>{{ item.label }}</span>
        </ng-template>
      </p-multiSelect>
    </div>
  </ng-container>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="closePopup()">Cancel</button>
    <button class="btn btn-primary" type="submit" (click)="onSubmit()" [disabled]="isLoading">
      Save
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isLoading"></fa-icon>
    </button>
  </div>
  
  <ng-template #emptyMessage let-loader="loader" let-data="data">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
    <p *ngIf="!loader && !data?.length">No records found</p>
  </ng-template>
  