.view-task {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
}

.cols-btn {
  margin-right: 5px;
}

.tabs {
  position: relative;
}

.checkbox-col {
  width: 55px;
}

.light-edit-image img {
  filter: brightness(0) invert(1);
}

.actions img {
  padding: 5px;
}

.column-btn {
  margin-left: 10px;
  color: #0b0b69;
  background-color: #fff;
  border-color: #0b0b69;
  font-weight: 600;
  border: 3px solid #0b0b69;
  padding: 0px 17px !important;

  fa-icon {
    margin-left: 5px;
    font-size: 16px;
    margin-right: 9px;
  }
}

::ng-deep .inventory-search-tr th {
  .search-input {
    input {
      min-width: 4rem;
      height: 36px !important;
    }

    p-calendar.p-inputwrapper {
      span.p-calendar-w-btn {
        height: 36px !important;
      }
    }
  }

  .float-end img {
    cursor: pointer;
  }

  .reset-icon {
    fa-icon {
      color: var(--active-color) !important;
      font-size: 23px;
    }
  }
}

@media only screen and (max-width: 500px) {
  .reset-btn {
    padding: 0px 10px !important;
  }
}

@media only screen and (max-width: 860px) {
  .column-btn {
    margin-top: 1rem;
  }
}

::ng-deep .contact-tabs {
  .p-tabview-nav-content {
    margin-top: 5px;
  }
}
