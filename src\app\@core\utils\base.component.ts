import { Component, OnDestroy } from '@angular/core';
import { environment } from '@env/environment';
import { Account } from '@pages/auth/models';
import { TableLazyLoadEvent } from 'primeng/table';
import { Subject } from 'rxjs';
import { Constants } from 'src/app/@shared/constants/app.constants';
import { Page, PaginationConfig, PermissionActions } from 'src/app/@shared/models';
import { OrderByParam } from 'src/app/@shared/models/sort.model';
import { Utils } from 'src/app/@shared/services/util.service';
import { faIcons } from './font-awesome-icon.utils';
import { ROUTER_UTILS } from './router.utils';

@Component({
  selector: 'app-base',
  template: ''
})
export class BaseComponent implements OnDestroy {

  currentUser!: Account | null;
  destroy$: Subject<void> = new Subject();
  path = ROUTER_UTILS.config;
  constants = Constants;
  faIcons = faIcons;
  utils = Utils;
  pageTitle!: string;
  paginationConfig: PaginationConfig = new PaginationConfig();
  environment = environment;
  modalTransition = '0ms';
  isLoading = false;
  permissionActions = PermissionActions;
  isExporting = false;

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.unsubscribe();
  }

  get orderBy(): OrderByParam[] {
    return [{ ascending: this.paginationConfig.ascending, field: this.paginationConfig.predicate }];
  }

  onSortChange(sortEvent: TableLazyLoadEvent, callbackFn: Function): void {
    this.paginationConfig.predicate = sortEvent.sortField as string;
    this.paginationConfig.ascending = sortEvent.sortOrder === 1;
    callbackFn();
  }

  setPaginationParamsFromPageResponse<T>(pageResponse: Page<T>) {
    this.paginationConfig.totalElements = pageResponse.totalElements;
    this.paginationConfig.numberOfElements = pageResponse.numberOfElements;
    this.paginationConfig.number = pageResponse.number;
  }

  getEvaluatedExpression(expression: string, object: any): any {
    return Utils.getPropertyByString(object, expression);
  }
}
