import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { UserListItem } from '@pages/administration/models';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { firstValueFrom } from 'rxjs';
import { SettingsService } from '../../settings.service';

@Component({
  selector: 'app-account',
  templateUrl: './account.component.html',
  styleUrls: [],
})
export class AccountComponent extends BaseComponent implements OnInit {

  profileFormGroup!: UntypedFormGroup;
  @Input() userInfo!: UserListItem | null;
  @Input() isFormDisabled = false;
  isLoading = false
  constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly settingsService: SettingsService,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    this.initializeFormGroup();
    this.currentUser = await firstValueFrom(this.authService.getCurrentUser(true));
    this.updateFormGroupWithUserInfo();
  }

  initializeFormGroup(): void {
    this.profileFormGroup = this.fb.group({
      firstName: ['', [Validators.required, Validators.maxLength(50)]],
      lastName: ['', [Validators.required, Validators.maxLength(50)]],
    });
  }

  updateFormGroupWithUserInfo(): void {
    this.profileFormGroup.patchValue({
      firstName: this.currentUser?.firstName,
      lastName: this.currentUser?.lastName,
    });
  }

  onUpdateProfile(): void {
    Object.keys(this.profileFormGroup.controls).forEach(key => {
      const control = this.profileFormGroup.get(key);
      if (control) {
        // Trim the value of each control
        const trimmedValue = control.value.trim();
        control.setValue(trimmedValue);
      }
    });
    if (this.profileFormGroup.invalid) {
      this.profileFormGroup.markAllAsTouched();
      return;
    }
    this.isLoading = true;
    this.settingsService.update<Account>(this.profileFormGroup.value).subscribe(() => {
      this.toasterService.success(MESSAGES.profileUpdateSuccess);
      this.isLoading = false
      this.authService.setCurrentUser$({ ...this.currentUser, ...this.profileFormGroup.value });
    });
  }

}
