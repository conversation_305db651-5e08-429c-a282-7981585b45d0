import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { LocalStorageService, StorageItem } from './local-storage.service';

export enum THEMES {
  LIGHT = 'LIGHT',
  DARK = 'DARK',
}

export enum GLOBAL_THEMES {
  LIGHT_PURPLE = 'lara-light-purple',
  DARK_PURPLE = 'lara-dark-purple',
}
@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  idealTheme: THEMES = THEMES.LIGHT;
  readonly selectedTheme = new BehaviorSubject(
    this.getStoredTheme() || this.idealTheme
  );
  readonly selectedTheme$: Observable<THEMES> =
    this.selectedTheme.asObservable();
  constructor(
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly localStorageService: LocalStorageService) { }

  changeTheme(isDark: boolean): void {
    const body = document.getElementsByTagName('body')[0];

    if (!isDark) {
      this.selectedTheme.next(THEMES.LIGHT);
      body.classList.remove(THEMES.DARK);
      body.classList.add(THEMES.LIGHT);
      this.changeGlobalTheme(GLOBAL_THEMES.LIGHT_PURPLE);
      this.localStorageService.setItem(StorageItem.Theme, THEMES.LIGHT);
    } else {
      this.selectedTheme.next(THEMES.DARK);
      body.classList.remove(THEMES.LIGHT);
      body.classList.add(THEMES.DARK);
      this.changeGlobalTheme(GLOBAL_THEMES.DARK_PURPLE);
      this.localStorageService.setItem(StorageItem.Theme, THEMES.DARK);
    }
  }

  private changeGlobalTheme(globalTheme: GLOBAL_THEMES): void {
    const themeLink = this.document.getElementById(
      'app-theme'
    ) as HTMLLinkElement;

    if (themeLink) {
      themeLink.href = `${globalTheme}.css`;
    }
  }

  private getStoredTheme(): THEMES | null {
    return this.localStorageService.getItem(StorageItem.Theme) as THEMES | null;
  }
}
