import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { PrivilegeActionResponseDTOs } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';

@Directive({
  selector: '[appHasPermission]'
})
export class HasPermissionDirective {

  constructor(
    private readonly templateRef: TemplateRef<any>,
    private readonly viewContainerRef: ViewContainerRef,
    private readonly authService: AuthService
  ) {
  }

  @Input()
  set appHasPermission(value: Array<string>) {
    const permissions = this.authService.getRoleInfo()?.privilegeActionResponseDTOs;
    if (permissions && permissions.some((permissionItem) => permissionItem.module.name === 'Skeye')) {
      this.viewContainerRef.createEmbeddedView(this.templateRef);
      return;
    }
    if (permissions) {
      const isPermission: boolean = permissions
        .some((permissionItem: PrivilegeActionResponseDTOs) => {
          return value.includes(permissionItem.actionDto.name)
        });

      if (isPermission) {
        this.viewContainerRef.createEmbeddedView(this.templateRef);
      } else {
        this.viewContainerRef.clear()
      }
    }

  }

}
