import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { DropdownModule } from 'primeng/dropdown';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CategoryModule } from './pages/category/category.module';
import { SpecificationConfigrationRoutingModule } from './specification-configration-routing.module';
import { SpecificationConfigrationComponent } from './specification-configration.component';


@NgModule({
  declarations: [
    SpecificationConfigrationComponent
  ],
  imports: [
    CommonModule,
    DirectivesModule,
    TableModule,
    SharedComponentsModule,
    CategoryModule,
    SpecificationConfigrationRoutingModule,
    DropdownModule,
    FormsModule
  ],
  providers: [
  ],
  exports: [
  ]
})
export class SpecificationConfigrationModule { }
