import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Constants, MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { VendorCreateParam, VendorListItem } from '@pages/administration/models';
// import { Address } from 'ngx-google-places-autocomplete/objects/address';
import { takeUntil } from 'rxjs';
import { SuppliersService } from '../suppliers.service';

@Component({
  selector: 'app-supplier-add',
  templateUrl: './supplier-add.component.html',
  styleUrls: ['./supplier-add.component.scss']
})
export class SupplierAddComponent extends BaseComponent implements OnInit, OnChanges {

  title = 'Add Supplier';
  supplierFormGroup!: UntypedFormGroup;
  hasDataBeenModified = false;
  @Input() supplierInfo!: VendorListItem | null;
  @Input() isViewMode!: boolean;
  isEditMode = false;

  redirectUrl!: string;
  showGoogleMapSideBar = false;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  fullAddress!: string;
  contactNoFormat = Constants.phoneNumberMask;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(private readonly fb: UntypedFormBuilder,
    private readonly supplierService: SuppliersService,
    private readonly toasterService: AppToasterService,
    private readonly router: Router,
    private readonly cdf: ChangeDetectorRef,
    private readonly activeRoute: ActivatedRoute,
    private readonly commonSharedService: CommonSharedService) {
    super();
  }

  ngOnInit(): void {
    this.commonSharedService.setBlockUI$(true);
    this.initializeFormGroup();
    this.setSupplierInfoInFormGroup();
    this.activeRoute.queryParams.subscribe(params => {
      if (params?.returnUrl) {
        this.redirectUrl = params?.returnUrl;
      }
    });
    this.commonSharedService.setBlockUI$(false);
    this.cdf.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.supplierInfo.currentValue) {
      if (this.isViewMode) {
        this.title = 'View Supplier';
      } else {
        this.title = 'Edit Supplier';
      }
      this.isEditMode = true;
    }
  }

  initializeFormGroup(): void {
    this.supplierFormGroup = this.fb.group({
      name: new UntypedFormControl('', [Validators.required, Validators.maxLength(150)]),
      email: new UntypedFormControl('', [Validators.email]),
      phoneNumber: new UntypedFormControl(''),
      additionalDetail: new UntypedFormControl(''),
      contactPerson: this.newContactPersonFormGroup,
      address: this.newAddressFormGroup
    });
  }

  get newContactPersonFormGroup(): UntypedFormGroup {
    return this.fb.group({
      firstName: new UntypedFormControl('', [Validators.maxLength(50)]),
      lastName: new UntypedFormControl('', [Validators.maxLength(50)]),
      email: new UntypedFormControl('', [Validators.email]),
      phoneNumber: new UntypedFormControl('')
    })
  }

  get newAddressFormGroup(): FormGroup {
    return this.fb.group({
      streetAddress: new FormControl(''),
      city: new FormControl(''),
      state: new FormControl(''),
      zipcode: new FormControl(''),
      latitude: new FormControl(''),
      longitude: new FormControl('')
    })
  }

  get addressFormGroup(): UntypedFormGroup {
    return this.supplierFormGroup.get('address') as UntypedFormGroup;
  }

  get contactPersonFormGroup(): UntypedFormGroup {
    return this.supplierFormGroup.get('contactPerson') as UntypedFormGroup;
  }

  get supplierInfoCreateParams(): VendorCreateParam {
    return {
      ...this.supplierFormGroup.value,
      organizationId: 1,
      id: this.supplierInfo?.id
    };
  }

  onSubmit(close = true): void {
    if (this.supplierFormGroup.invalid) {
      this.supplierFormGroup.markAllAsTouched();
      return;
    }
    if (this.isEditMode) {
      this.editSupplier();
    } else {
      this.saveSupplier(close);
    }

  }

  saveSupplier(close = true): void {
    this.supplierService.add(this.supplierInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.supplierUpdateSuccess : MESSAGES.supplierAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.supplierFormGroup.reset({
          name: '',
          email: '',
          phoneNumber: '',
          additionalDetail: '',
          contactPerson: {
            firstName: '',
            lastName: '',
            email: '',
            phoneNumber: ''
          },
          address: {
            streetAddress: '',
            city: '',
            state: '',
            zipcode: '',
            latitude: '',
            longitude: ''
          }
        });
      }
    });
  }

  editSupplier(): void {
    this.supplierService.update(this.supplierInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.supplierUpdateSuccess : MESSAGES.supplierAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.goBack();
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  setSupplierInfoInFormGroup(): void {
    if (this.supplierInfo) {
      this.supplierFormGroup.patchValue({
        name: this.supplierInfo.name,
        email: this.supplierInfo.email,
        phoneNumber: this.supplierInfo.phoneNumber,
        additionalDetail: this.supplierInfo.additionalDetail,
        contactPerson: {
          firstName: this.supplierInfo.contactPerson.firstName,
          lastName: this.supplierInfo.contactPerson.lastName,
          email: this.supplierInfo.contactPerson.email,
          phoneNumber: this.supplierInfo.contactPerson.phoneNumber
        },
        address: {
          streetAddress: this.supplierInfo.address.streetAddress,
          city: this.supplierInfo.address.city,
          state: this.supplierInfo.address.state,
          zipcode: this.supplierInfo.address.zipcode
        }
      });

      if (this.isViewMode) {
        this.supplierFormGroup.disable();
      }
    }
  }

  toggleGoogleMapPopUp() {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getFullAddress()
  }

  handleAddressChange(address: any) {
    if (address && address.address_components) {
      this.addressFormGroup.controls['streetAddress'].setValue(address.name);
      this.addressFormGroup.controls['latitude'].setValue(address.geometry.location.lat());
      this.addressFormGroup.controls['longitude'].setValue(address.geometry.location.lng());
      address.address_components.forEach((addressComponent: any) => {
        switch (addressComponent.types[0]) {
          case 'sublocality':
            break;
          case 'locality':
            this.addressFormGroup.controls['city'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_1':
            this.addressFormGroup.controls['state'].setValue(addressComponent.long_name);
            break;
          case 'postal_code':
            this.addressFormGroup.controls['zipcode'].setValue(addressComponent.long_name);
            break;
        }
      });
    }
  }

  getFullAddress() {
    const rowAddress = []
    const address = this.addressFormGroup.value;
    rowAddress.push(address?.streetAddress ? address.streetAddress : '')
    rowAddress.push(address?.city ? address.city : '')
    rowAddress.push(address?.state ? address.state : '')
    rowAddress.push(address?.zipcode ? address.zipcode : '')
    const cleanAddress = rowAddress.filter(str => {
      return str !== ""
    })
    this.fullAddress = cleanAddress.join(", ")
  }

}
