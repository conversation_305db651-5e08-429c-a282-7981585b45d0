#!/bin/sh
YELLOW='\033[0;33m'
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color
. "$(dirname "$0")/_/husky.sh"

current_branch=$(git symbolic-ref HEAD | sed -e 's,.*/\(.*\),\1,')

# Step 1 angular build begins
echo "${YELLOW}Generating & Verifying AOT build before pushing to ${RED} $current_branch ${NC}"
ng build --c=dev
echo "${GREEN}AOT Build verified and code pushed to $current_branch. ${NC}"
# angular build ends
