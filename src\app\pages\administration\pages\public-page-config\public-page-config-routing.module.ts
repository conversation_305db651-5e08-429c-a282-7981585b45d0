import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionForChildGuard } from '@core/guards';
import { ROUTER_UTILS } from '@core/utils';
import { PermissionActions } from 'src/app/@shared/models';
import { PublicPageConfigComponent } from './public-page-config.component';

const routes: Routes = [
  {
    path: '',
    component: PublicPageConfigComponent,
    title: 'Skeye - Public Page Config',
    children: [
      {
        path: ROUTER_UTILS.config.administration.publicPageConfig.advertising.root,
        loadChildren: async () => (await import('./pages/advertising/advertising.module')).AdvertisingModule,
        canActivate: [PermissionForChildGuard],
        data: {
          permission: [PermissionActions.VIEW_ADVERTISE]
        }
      },
      {
        path: ROUTER_UTILS.config.administration.publicPageConfig.quoteForm.root,
        loadChildren: async () => (await import('./pages/quote-form/quote-form.module')).QuoteFormModule,
        canActivate: [PermissionForChildGuard],
        data: {
          permission: [PermissionActions.VIEW_QUOTE_FORM]
        }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PublicPageConfigRoutingModule { }
