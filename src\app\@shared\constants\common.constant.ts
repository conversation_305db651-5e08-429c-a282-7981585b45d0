export const USER_MENTION_CONFIG = {
  startChar: '<strong>',
  endChar: '</strong>'
};

export const MENTION_CONFIG = {
  triggerChar: '@',
  labelKey: 'label',
  allowSpace: true,
  mentionSelect: onMentionSelect,
  mentionFilter: onMentionFilter
};

export function onMentionSelect(selection: any) {
  return USER_MENTION_CONFIG.startChar + selection.label + USER_MENTION_CONFIG.endChar;
}

export function onMentionFilter(search: any, items: any) {
  return items.filter((e: any) => e.label.toLowerCase().includes(search.toLowerCase()));
}

