import { Directive } from '@angular/core';
import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';
import { Constants } from '@constants/*';

@Directive({
  selector: '[appEmailValidation]',  // Custom selector
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: EmailValidationDirective,
      multi: true,
    },
  ],
})
export class EmailValidationDirective implements Validator {

  constructor() { }

  validate(control: AbstractControl): ValidationErrors | null {
    const email = control.value;
    // Regular expression to check for an email with valid domain and extension after the dot
    const emailRegex = Constants.regexForEmailValidation;
    // If the email does not match the regex or ends with a dot without a valid extension, it's invalid
    if (email && !emailRegex.test(email)) {
      return { email: true }; // Error key to trigger validation message
    }

    return null; // If no errors, return null
  }
}
