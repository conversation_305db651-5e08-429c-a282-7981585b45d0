import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { InventorySoldComponent } from './inventory-sold.component';



@NgModule({
  declarations: [InventorySoldComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    RadioButtonModule,
    TabViewModule,
    CardModule,
    TooltipModule,
    CheckboxModule,
    MenuModule,
  ],
  exports: [
    InventorySoldComponent
  ]
})
export class InventorySoldModule { }
