import { GenericFilterParams } from './generic-filter-params.model';

export class HistoryListFilter extends GenericFilterParams {
  archived = false;
}

export interface HistoryListItem {
  id: number;
  action: string;
  module: string;
  actionUser: ActionUser;
  eventId: string;
  displayName: string;
  createdDate: string;
  eventDeleted: boolean;
}

export interface ActionUser {
  id: number
  name: string
}
