import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { map, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ReportingService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.reports.root;
  }

  getSalesPersonList<ReturnType>(endpoint?: string): Observable<ReturnType[]> {
      return this.httpClient.get<ReturnType[]>(endpoint)
        .pipe(map((list) => list.map((item) => this.fromServerModel(item))));
    }
}
