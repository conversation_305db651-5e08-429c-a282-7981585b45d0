import { AssociationsUnits } from '@pages/inventory/models';
import { IdNameModel } from 'src/app/@shared/models';

export class DriverScheduleBoardListItem {
  id!: number;
  unitId!: number;
  driver!: IdNameModel;
  reporter!: IdNameModel;
  dispatcher!: IdNameModel;
  driverScheduleStatus!: string;
  driverScheduleAddresses!: Address[];
  item?: string;
  startDate?: string | null;
  endDate?: string | null;
  createdBy!: IdNameModel;
  createdDate!: string;
  weekDay!: string;
  pickUpLocation!: string;
  destinationLocation!: string;
  driverScheduleAttachments!: Array<DriverScheduleAttachments>;
  driverInstruction!: string;
  year!: number;
  make!: string;
  model!: string;
  vin!: string;
  unitAssociations!: Array<AssociationsUnits>;
  drivers!: DriverSchedule[];

  constructor(json?: DriverScheduleBoardListItem) {
    if (json) {
      Object.assign(this, json);
    }
  }
}

export interface DriverScheduleAttachments {
  id: number;
  url: string;
  fileName: string;
  driverScheduleId: number;
  fullPath: string;
}

export interface DriverScheduleCreateParams {
  stockNumber: number;
  stockNumberId: number;
  startDate: string;
  endDate: string;
  driverId: number;
  reporterId: number;
  driverScheduleStatus: string;
  driverScheduleAddresses: Address;
  vin: string;
  year: number;
  make: string;
  model: string;
}

export class Address {
  city!: string;
  state!: string;
  streetAddress!: string;
  zipcode!: string;
  location!: string;
  contactPersonName!: string;
  contactPersonNumber!: string;
}

export class DriverList {
  id: number;
  name: string;
  email: string;
  roleName: string;
  phoneNumber!: string;
  roleId!: number;
}

export interface DriverCreateParams {
  firstName: string;
  phoneNumber: number;
  email: string;
  employeeId: string;
  dealerId: number;
  role: string;
  shopId: number;
}

export enum ScheduleForList {
  stock = 'Stock#',
  title = 'Other',
}

export enum DriverScheduleStatus {
  REQUESTED = 'REQUESTED',
  SCHEDULED = 'SCHEDULED',
  ISSUE = 'ISSUE',
  COMPLETED = 'COMPLETED'
}

export interface DriverSchedule {
  id?: number;
  driverId: number;
  driverName?: string;
}