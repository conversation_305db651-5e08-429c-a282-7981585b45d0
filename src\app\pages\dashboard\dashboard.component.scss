@import 'src/assets/scss/variables';

.widget {
  border-radius: 5px;
  padding: 15px 20px;
  margin-bottom: 10px;
  margin-top: 10px;
}
.widget.style1 h2 {
  font-size: 30px;
}
.widget h2,
.widget h3 {
  margin-top: 5px;
  margin-bottom: 0;
}

.font-bold {
  font-weight: 600;
}
.text-left {
  text-align: left !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.m-t-20 {
  margin-top: 20px;
}

.text-end {
  text-align: end !important;
}

.title {
  color: var(--content-head-color);
  font-size: 16px !important;
  font-weight: 600 !important;
  letter-spacing: 0;
  line-height: 25px !important;
  text-transform: uppercase;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 10px;
}
.child-right {
  padding-right: 2px;
}

.p-l-10 {
  padding-left: 10px;
}

.p-r-10 {
  padding-right: 10px;
}

.p-0 {
  padding: 0 !important;
}

.truck-count {
  color: $truck-count-color;
}

.monthly-count-color {
  color: $monthly-count-color;
}

.sale-count-color {
  color: $sale-count-color;
}

.incoming-truck-count-color {
  color: $incoming-truck-count-color;
}

.daily-sale-section {
  background: linear-gradient(to right bottom, $daily-sale-section-color 50%, $daily-sale-section-dual-color 50.3%);
}

.monthly-sale-section {
  background: linear-gradient(to right bottom, $monthly-sale-section-color 50%, $monthly-sale-section-dual-color 50.3%);
}

.total-inventory-section {
  background: linear-gradient(to right bottom, $truck-inventory-section-color 50%, $truck-inventory-section-dual-color 50.3%);
}

.incoming-truck-section {
  background: linear-gradient(to right bottom, $incoming-truck-section-color 50%, $incoming-truck-section-dual-color 50.3%);
}

.space-between {
  justify-content: space-between;
}

.m-t-30 {
  margin-top: 30px;
}

.m-t-25 {
  margin-top: 25px;
}

.m-t-0 {
  margin-top: 0px;
}
