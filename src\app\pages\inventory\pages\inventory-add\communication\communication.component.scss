.button {
  background-color: #101097;
  border-color: #101097;
  border: none;
  color: var(--white-color);
  padding: 11px 15px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 15px 10px 20px;
}

.button:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.button5 {
  border-radius: 50%;
}

.col-11 {
  width: 94%;
}

.col-1 {
  width: 6%;
}

::ng-deep .initials-value {
  width: 3% !important;

  .p-avatar.p-avatar-xl {
    width: 45px;
    height: 45px;
    font-size: 19px;
    background-color: var(--border-box-bg-color);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
  }
}

::ng-deep .card-content {
  .p-card {
    border: solid #92aeff;
    border-width: 0 0 0 6px;
    border-radius: 0px;
  }
}

.user-details {
  display: inline-flex;

  div:nth-child(2) {
    margin-left: 15px;
    color: #999999;
  }
}

.inventory-comments {
  margin-bottom: 10px;
}

.add-message {
  margin-bottom: 15px;
}

.users-comment {
  margin-bottom: 20px;
  margin-top: -5px;
}

.child-comments {
  .col-1 {
    top: -14px;
  }
}

.inventory-child-comments {
  .col-1 {
    width: 4% !important;
  }
}

.preview-more {
  color: var(--active-color);
  cursor: pointer;
}

.space-between {
  justify-content: space-between;
}

.m-t-30 {
  margin-top: 30px;
}

::ng-deep .p-editor-content .ql-container .ql-snow {
  height: 100px !important;
}
::ng-deep .add-message .p-editor-container .p-editor-content .ql-editor {
  height: 100px !important;
}

.m-r-10 {
  margin-right: 10px;
}

.w-90 {
  width: 90%;
}

::ng-deep .highlight-comment {
  .p-card {
    background-color: lighten(#e0edff, 3%) !important;
  }
}

@media only screen and (max-width: 500px) {
  .user-details {
    flex-wrap: wrap;
  }

  .ml-0 {
    margin-left: 0 !important;
  }

  .w-90 {
    width: 80%;
  }
}
