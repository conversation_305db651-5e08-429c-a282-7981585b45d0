import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

export enum StorageItem {
  AuthToken = 'authToken',
  RefreshToken = 'refreshToken',
  Theme = 'theme',
}

@Injectable({
  providedIn: 'root',
})
export class LocalStorageService {
  constructor() { }
  skipEncryptionFor = [StorageItem.AuthToken, StorageItem.RefreshToken];

  getItem = (itemName: StorageItem): unknown | null => {
    const item: any = this.getStorage().getItem(itemName);
    return item ? JSON.parse(item) : null;
  };

  setItem = (itemName: StorageItem, value: any): void => {
    this.getStorage().setItem(itemName, JSON.stringify(value));
    return;
  };

  removeItem = (itemName: StorageItem): void => {
    this.getStorage().removeItem(itemName);
  };

  getStorage = () => {
    return environment.persistUserSession ? localStorage : sessionStorage;
  };
}
