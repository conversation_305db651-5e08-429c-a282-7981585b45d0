<div class="add-comment mb-3">
  <button class="btn btn-primary" id="addShopBtn" type="button" *ngIf="!isAddingComment" [appImageIconSrc]="constants.staticImages.icons.addNewHover" (click)="onAddComment()">Add New Comment</button>

  <form class="form" [formGroup]="commentFormGroup" (ngSubmit)="onAddNewCommentSubmit()" [ngClass]="{ loading: isLoading }" *ngIf="isAddingComment">
    <div>
      <p-editor
        #commentEditor
        [mention]="availableMentionUsers"
        (itemSelected)="mentionSelected($event)"
        [mentionConfig]="mentionConfig"
        formControlName="comment"
        (onTextChange)="textChange($event)"
        (onSelectionChange)="selectionChange($event)"
        (onInit)="selectionChange($event)"
      >
        <ng-template pTemplate="header">
          <span class="ql-formats">
            <button type="button" #boldText class="ql-bold" aria-label="Bold"></button>
            <button type="button" class="ql-italic" aria-label="Italic"></button>
            <button type="button" class="ql-underline" aria-label="Underline"></button>
          </span>
        </ng-template>
      </p-editor>
      <app-error-messages [control]="commentFormGroup.controls?.comment"></app-error-messages>
    </div>
    <div class="actions m-t-15">
      <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
      <button class="btn btn-primary ms-3" type="submit" appShowLoaderOnApiCall>Save</button>
    </div>
  </form>
</div>
<div *ngIf="!isLoading; else loaderTemplate">
  <ul>
    <li *ngFor="let comment of comments" class="comment">
      <div [ngClass]="{ 'comment-wrapper': true, 'highlight-comment': selectedCommentId === comment.id }">
        <div class="wrap-comment">
          <span class="user"> {{ comment?.createdByName }} - </span>
          <span class="date">{{ comment.createdDate | date: constants.fullDateFormat }}</span>
          <p class="body overflow-text" [innerHTML]="comment?.comment"></p>
        </div>
        <div class="action-icons" *ngIf="currentUserId === comment.createdById">
          <img [src]="constants.staticImages.icons.edit" (click)="onEdit(comment)" alt="edit-icon" />
          <em class="pi pi-trash text-danger" appShowLoaderOnApiCall (click)="onDelete(comment, $event)" alt=""></em>
        </div>
      </div>
    </li>
  </ul>
</div>
<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="loader-icon"></fa-icon>
</ng-template>
