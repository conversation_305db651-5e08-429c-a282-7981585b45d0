import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { Constants } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CrmContactListItem } from '@pages/administration/models/crm.model';
import { QuotationKey } from '@pages/crm/models/customer-inventory.model';
import { QuotationCustomerListItem, QuotationFilterParams } from '@pages/crm/models/customer-lead-quotation.model';
import { CustomerLeadListFilter, CustomerLeadListItem, CustomerStatusList } from '@pages/crm/models/customer-lead.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { PageChangedEvent, PaginationConfig } from 'ngx-bootstrap/pagination';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { DataType, OperatorType, TreeOperatorType } from 'src/app/@shared/models';

@Component({
  selector: 'app-crm-contact-customer-lead',
  templateUrl: './crm-contact-customer-lead.component.html',
  styleUrls: ['./crm-contact-customer-lead.component.scss']
})
export class CrmContactCustomerLeadComponent extends BaseComponent {

  crmCustomerLeadList: CustomerLeadListItem[] = [];
  PaginationConfig: PaginationConfig = new PaginationConfig();
  filterParams: CustomerLeadListFilter = new CustomerLeadListFilter();
  showCreateModal = false;
  isCrmCustomerViewMode = false;
  customerStatusList = CustomerStatusList;
  constants = Constants;
  @Input() crmContactInfo!: CrmContactListItem | null;
  @Input() quotationCustomerList!: QuotationCustomerListItem[] | null;

  constructor(private readonly crmService: CrmService,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly cdf: ChangeDetectorRef) {
    super();
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }
  get getQuotationRequest(): QuotationFilterParams {
    return {
      treeOperator: TreeOperatorType.NOOP,
      values: [{
        dataType: DataType.LONG,
        key: QuotationKey.CUSTOMER_LEAD,
        operator: OperatorType.EQUAL,
        value: this.crmContactInfo?.id ? this.crmContactInfo?.id : ''
      }]
    };
  }
  getAll(): void {
    this.isLoading = true;
    this.crmService.add<QuotationFilterParams>(this.getQuotationRequest, API_URL_UTIL.admin.crm.customerFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.crmCustomerLeadList = res.content;
          this.setPaginationParamsFromPageResponse<CustomerLeadListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  onAdd(): void {
    this.showCreateModal = true;
  }

  onClose(isClose: boolean): void {
    this.showCreateModal = false;
    if (isClose) {
      this.getAll();
    }
  }
}
