::ng-deep .incoming-truck-list {
  .tab-content ul > li {
    width: 50%;
    text-align: center;
    span {
      width: 100%;
    }
  }

  p-table {
    .p-dropdown .p-dropdown-label {
      background: var(--active-color) !important;
      color: var(--white-color) !important;
      padding: 3px 0px 3px 10px !important;
      font-size: 14px;
    }

    .p-dropdown .p-dropdown-trigger {
      background: var(--active-color) !important;
      color: var(--white-color) !important;
    }

    .p-dropdown {
      background-color: var(--active-color) !important;
      height: inherit;
    }

    .pi-chevron-down:before {
      font-size: 14px;
    }
    .action-btn {
      padding-top: 3px;
    }
  }

  .dynamic-col {
    width: 15rem !important;
  }

  .w-150 {
    width: 150px !important;
  }

  .view-task {
    text-decoration: underline;
    color: var(--link-color);
    cursor: pointer;
  }

  .small-col {
    width: 100px;
    text-align: center !important;
  }

  ::ng-deep .inventory-search-tr th {
    .search-input {
      input {
        width: 4rem;
        height: 36px !important;
      }

      p-calendar.p-inputwrapper {
        span.p-calendar-w-btn {
          height: 36px !important;
        }
      }
    }
    .float-end img {
      cursor: pointer;
    }
  }

  ::ng-deep .p-multiselect-label-container {
    width: 150px;
  }

  .cross-icon {
    position: relative;
    left: 150px;
    top: -28px;
    height: 0;
    display: flex;
    color: #6c757d;
    font-size: 15px;
  }

  .stock-truck-list {
    max-width: inherit;
    overflow: auto;
  }
}

.m-l-15 {
  margin-left: 15px;
}

@media only screen and (max-width: 860px) {
  .top-header {
    .column-btn {
      margin-top: 1rem;
    }
  }
}
.top-header {
  .column-btn {
    margin-left: 10px;
    color: #0b0b69;
    background-color: #fff;
    border-color: #0b0b69;
    font-weight: 600;
    border: 3px solid #0b0b69;
    padding: 0px 17px !important;

    fa-icon {
      margin-left: 5px;
      font-size: 16px;
      margin-right: 9px;
    }
  }
}
