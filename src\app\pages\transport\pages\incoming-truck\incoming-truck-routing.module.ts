import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { IncomingTruckListComponent } from "./incoming-truck-list/incoming-truck-list.component";
import { IncomingTruckComponent } from "./incoming-truck.component";

const routes: Routes = [
  {
    path: '',
    component: IncomingTruckComponent,
    title: 'Skeye - Incoming truck board',
    children: [
      {
        path: '',
        component: IncomingTruckListComponent,
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class IncomingTruckRoutingModule { }
