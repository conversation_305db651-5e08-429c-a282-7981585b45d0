import { IdName } from '@pages/inventory/models'

export interface DailySalesListItems {
  id: number
  stockNumber: string
  category: IdName
  unitType: IdName
  totalProjectedInvestment: number
  customerFirstName: string
  customerLastName: string
  salesPerson: IdName
  sellPrice: number
  invoiceDate: string
}

export enum SalesDateRangeFilter {
  noop = 'NOOP',
  and = 'AND',
  invoiceDate = 'invoiceDate',
  transDate = 'transDate',
  createdDate = 'createdDate',
  date = 'DATE',
  greaterThanEqual = 'GREATER_THAN_OR_EQUAL',
  lessThanEqual = 'LESS_THAN_OR_EQUAL'
}
