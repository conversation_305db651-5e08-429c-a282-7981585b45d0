import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';

@Injectable({
  providedIn: 'root'
})
export class PipelineCommentService extends BaseCrudService {

  constructor(protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  getBaseAPIPath(): string {
    return API_URL_UTIL.pipelineComment.root;
  }
}
