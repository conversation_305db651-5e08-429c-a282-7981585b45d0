import { Directive, ElementRef, HostListener } from '@angular/core';

const EVENT_TARGET = '$event.target';
const MENTION_LIST = 'mention-list'

@Directive({
  selector: '[appMentionPosition]'
})
export class MentionPositionDirective {
  constructor(private readonly el: ElementRef) { }

  @HostListener('input', [EVENT_TARGET]) onInput(editor: HTMLDivElement): void {
    this.printCaretPosition(editor);
    this.showCurrentMentionList();
  }

  @HostListener('click', [EVENT_TARGET]) onClick(editor: HTMLDivElement): void {
    this.hideAllMentionLists();
    this.showCurrentMentionList();
    this.printCaretPosition(editor);
  }

  @HostListener('document:click', [EVENT_TARGET]) onDocumentClick(target: EventTarget): void {
    const clickedInsideEditor = this.el.nativeElement.contains(target);
    if (!clickedInsideEditor) {
      this.hideAllMentionLists();
    }
    if (this.el.nativeElement.parentElement.querySelector(MENTION_LIST)) {
      this.showCurrentMentionList();
    }
  }

  private hideAllMentionLists() {
    const lists = document.querySelectorAll(MENTION_LIST);
    if (lists.length) {
      lists.forEach((list) => {
        (list as HTMLElement).style.display = 'none';
      });
    }
  }

  private showCurrentMentionList() {
    const list = this.el.nativeElement.parentElement.querySelector(MENTION_LIST);
    if (list) {
      list.style.display = 'block';
    }
  }

  private printCaretPosition(editor: HTMLDivElement): void {
    const selection = window.getSelection();
    if (!selection?.anchorNode) {
      return
    }
    const range = selection.getRangeAt(0);
    const caretPosition = range.startOffset;
    const caretContainer = range.startContainer;
    const caretPositionRelativeToEditor = this.calculateCaretPositionRelativeToEditor(editor, caretContainer, caretPosition);
    const mentionList = this.el.nativeElement.parentElement?.querySelector(MENTION_LIST) as HTMLElement;
    setTimeout(() => {
      if (mentionList) {
        mentionList.style.top = `${caretPositionRelativeToEditor.top + 61}px`
      }
    }, 0);
  }

  private calculateCaretPositionRelativeToEditor(editor: HTMLDivElement, caretContainer: Node, caretPosition: number): { left: number, top: number } {
    const editorRect = editor.getBoundingClientRect();
    const caretRange = document.createRange();
    caretRange.setStart(caretContainer, caretPosition);
    caretRange.setEnd(caretContainer, caretPosition);
    const caretRect = caretRange.getBoundingClientRect();
    const left = caretRect.left - editorRect.left;
    const top = caretRect.top - editorRect.top;
    return { left, top };
  }
}
