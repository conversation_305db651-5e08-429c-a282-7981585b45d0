import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { SharedComponentsModule } from "@sharedComponents/shared-components.module";
import { DirectivesModule } from "src/app/@shared/directives/directives.module";
import { SharedLibsModule } from "src/app/@shared/shared-libs.module";
import { ForgotPasswordFinishComponent } from "./forgot-password-finish.component";

const ROUTES: Routes = [
  {
    path: '',
    component: ForgotPasswordFinishComponent
  }
]

@NgModule({
  declarations: [ForgotPasswordFinishComponent],
  imports: [
    DirectivesModule,
    FontAwesomeModule,
    SharedComponentsModule,
    SharedLibsModule,
    RouterModule.forChild(ROUTES),
  ]
})
export class ForgotPasswordFinishComponentModule { }
