import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Constants, MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { VendorCreateParam, VendorListItem } from '@pages/administration/models';
import { Address } from 'ngx-google-places-autocomplete/objects/address';
import { takeUntil } from 'rxjs';
import { VendorService } from '../vendors.service';

@Component({
  selector: 'app-vendor-add',
  templateUrl: './vendor-add.component.html',
  styleUrls: ['./vendor-add.component.scss']
})
export class VendorAddComponent extends BaseComponent implements OnInit, OnChanges {

  title = 'Add Vendor';
  redirectUrl!: string;
  vendorFormGroup!: UntypedFormGroup;
  hasDataBeenModified = false;
  @Input() vendorInfo!: VendorListItem | null;
  @Input() isViewMode!: boolean;
  isEditMode = false;
  showGoogleMapSideBar = false;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  fullAddress!: string;
  contactNoFormat = Constants.phoneNumberMask;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(private readonly fb: UntypedFormBuilder,
    private readonly vendorService: VendorService,
    private readonly toasterService: AppToasterService,
    private readonly router: Router,
    private readonly cdf: ChangeDetectorRef,
    private readonly activeRoute: ActivatedRoute,
    private readonly commonSharedService: CommonSharedService
  ) {
    super();
  }

  ngOnInit(): void {
    this.commonSharedService.setBlockUI$(true);
    this.initializeFormGroup();
    this.setVendorInfoInFormGroup();
    this.activeRoute.queryParams.subscribe(params => {
      if (params?.returnUrl) {
        this.redirectUrl = params?.returnUrl;
      }
    });
    this.commonSharedService.setBlockUI$(false);
    this.cdf.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.vendorInfo.currentValue) {
      if (this.isViewMode) {
        this.title = 'View Vendor';
      } else {
        this.title = 'Edit Vendor';
      }
      this.isEditMode = true;
    }
  }

  initializeFormGroup(): void {
    this.vendorFormGroup = this.fb.group({
      name: new UntypedFormControl('', [Validators.required, Validators.maxLength(150)]),
      email: new UntypedFormControl('', [Validators.email]),
      phoneNumber: new UntypedFormControl(''),
      additionalDetail: new UntypedFormControl(''),
      contactPerson: this.newContactPersonFormGroup,
      address: this.newAddressFormGroup
    });
  }

  get newContactPersonFormGroup(): UntypedFormGroup {
    return this.fb.group({
      firstName: new UntypedFormControl('', [Validators.maxLength(50)]),
      lastName: new UntypedFormControl('', [Validators.maxLength(50)]),
      email: new UntypedFormControl('', [Validators.email]),
      phoneNumber: new UntypedFormControl('')
    })
  }

  get newAddressFormGroup(): FormGroup {
    return this.fb.group({
      streetAddress: new FormControl(''),
      city: new FormControl(''),
      state: new FormControl(''),
      zipcode: new FormControl(''),
      latitude: new FormControl(''),
      longitude: new FormControl('')
    })
  }

  get addressFormGroup(): UntypedFormGroup {
    return this.vendorFormGroup.get('address') as UntypedFormGroup;
  }

  get contactPersonFormGroup(): UntypedFormGroup {
    return this.vendorFormGroup.get('contactPerson') as UntypedFormGroup;
  }

  get vendorInfoCreateParams(): VendorCreateParam {
    return {
      ...this.vendorFormGroup.value,
      organizationId: 1,
      id: this.vendorInfo?.id
    };
  }

  onSubmit(close = true): void {
    if (this.vendorFormGroup.invalid) {
      this.vendorFormGroup.markAllAsTouched();
      return;
    }
    if (this.isEditMode) {
      this.editVendor();
    } else {
      this.saveVendor(close);
    }
  }

  saveVendor(close = true): void {
    this.vendorService.add(this.vendorInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.vendorUpdateSuccess : MESSAGES.vendorAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.vendorFormGroup.reset(
          {
            name: '',
            email: '',
            phoneNumber: '',
            additionalDetail: '',
            contactPerson: {
              firstName: '',
              lastName: '',
              email: '',
              phoneNumber: ''
            },
            address: {
              streetAddress: '',
              city: '',
              state: '',
              zipcode: '',
              latitude: '',
              longitude: ''
            }
          }
        );
      }
    });
  }

  editVendor(): void {
    this.vendorService.update(this.vendorInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.vendorUpdateSuccess : MESSAGES.vendorAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.goBack();
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  setVendorInfoInFormGroup(): void {
    if (this.vendorInfo) {
      this.vendorFormGroup.patchValue({
        name: this.vendorInfo.name,
        email: this.vendorInfo.email,
        phoneNumber: this.vendorInfo.phoneNumber,
        additionalDetail: this.vendorInfo.additionalDetail,
        contactPerson: {
          firstName: this.vendorInfo.contactPerson.firstName,
          lastName: this.vendorInfo.contactPerson.lastName,
          email: this.vendorInfo.contactPerson.email,
          phoneNumber: this.vendorInfo.contactPerson.phoneNumber
        },
        address: {
          streetAddress: this.vendorInfo.address.streetAddress,
          city: this.vendorInfo.address.city,
          state: this.vendorInfo.address.state,
          zipcode: this.vendorInfo.address.zipcode
        }
      });

      if (this.isViewMode) {
        this.vendorFormGroup.disable();
      }
    }
  }

  toggleGoogleMapPopUp() {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getFullAddress()
  }

  handleAddressChange(address: Address | any) {
    if (address && address.address_components) {
      this.addressFormGroup.controls['streetAddress'].setValue(address.name);
      this.addressFormGroup.controls['latitude'].setValue(address.geometry.location.lat());
      this.addressFormGroup.controls['longitude'].setValue(address.geometry.location.lng());
      address.address_components.forEach((addressComponent: any) => {
        switch (addressComponent.types[0]) {
          case 'sublocality':
            break;
          case 'locality':
            this.addressFormGroup.controls['city'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_1':
            this.addressFormGroup.controls['state'].setValue(addressComponent.long_name);
            break;
          case 'postal_code':
            this.addressFormGroup.controls['zipcode'].setValue(addressComponent.long_name);
            break;
        }
      });
    }
  }

  getFullAddress() {
    const rowAddress = []
    const address = this.addressFormGroup.value;
    rowAddress.push(address?.streetAddress ? address.streetAddress : '')
    rowAddress.push(address?.city ? address.city : '')
    rowAddress.push(address?.state ? address.state : '')
    rowAddress.push(address?.zipcode ? address.zipcode : '')
    const cleanAddress = rowAddress.filter(str => {
      return str !== ""
    })
    this.fullAddress = cleanAddress.join(", ")
  }

}
