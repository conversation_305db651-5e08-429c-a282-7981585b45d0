.timeline {
  text-align: center;

  .footer {
    background-color: var(--listing-timeline-bg-color);
    width: fit-content;
    padding: 2px 20px;
    border-radius: 12px;

    .pi {
      margin-right: 5px;
    }

    span {
      font-size: 13px;
      letter-spacing: 0;
      line-height: 21px;
      color: var(--active-color);
      display: flex;
      align-items: center;
    }
  }
}

.timeline-header {
  width: 220px;
}

.pi-trash {
  font-size: 20px;

  &:hover {
    cursor: pointer;
  }
}
.w-145 {
  width: 145px !important;
}
::ng-deep .crm-task-list {
  .p-datatable .p-datatable-tbody > tr {
    background: white;
  }

  .p-datatable.p-datatable-hoverable-rows .p-datatable-tbody > tr:not(.p-highlight):hover {
    background-color: white !important;
  }

  .tab-content {
    padding: 0;
  }
    .normal-dropdowns {
      .p-dropdown {
        height: 42px;
      }
    }
    //TODO Will be removed once confirm
    // .p-dropdown .p-dropdown-label {
    //   background: var(--white-colo) !important;
    //   color: var(--white-color) !important;
    //   font-size: 14px;
    //   padding: 3px 0px 3px 10px !important;
    // }
  
    // .p-dropdown .p-dropdown-trigger {
    //   background: var(--white-colo) !important;
    //   color: var(--white-color) !important;
    // }
  
    // .p-dropdown {
    //   background-color: var(--white-colo) !important;
    //   height: inherit;
    // }
  
    .pi-chevron-down:before {
      font-size: 14px;
    }
  
    .priority-icon {
      padding: 17px !important;
    }
    .p-dropdown .p-dropdown-clear-icon{
      right: 2.4rem !important;
    }
}

.view-task {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
}

.dropdown-toggle::after {
  margin-left: 25px !important;
}

.w-150 {
  width: 150px !important;
}

::ng-deep .crm-task-list p-table p-dropdown {
  padding-bottom: 3px;
  padding-top: 3px;
}

::ng-deep .crm-task-list p-table {
  .p-dropdown .p-dropdown-label {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
    padding: 3px 0px 3px 10px !important;
    font-size: 14px;
    display: flex !important;
  }

  .p-dropdown .p-dropdown-trigger {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
  }

  .p-dropdown {
    background-color: var(--active-color) !important;
    height: inherit;
  }

  .pi-chevron-down:before {
    font-size: 14px;
  }

  .priority-icon {
    padding: 17px !important;
  }
}

.top-header {
  .column-btn {
    margin-left: 10px;
    color: #0b0b69;
    background-color: #fff;
    border-color: #0b0b69;
    font-weight: 600;
    border: 3px solid #0b0b69;
    padding: 0px 17px;

    fa-icon {
      margin-left: 5px;
      font-size: 16px;
      margin-right: 9px;
    }
  }
}

::ng-deep .inventory-search-tr th {
  .search-input {
    input {
      width: 4rem;
      height: 36px !important;
    }

    p-calendar.p-inputwrapper {
      span.p-calendar-w-btn {
        height: 36px !important;
      }
    }
  }

  .float-end img {
    cursor: pointer;
  }
}

@media only screen and (max-width: 500px) {
  .reset-btn {
    padding: 0px 10px !important;
  }
}
