<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <ng-container *ngIf="categories">
      <button class="btn btn-primary left" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAddEditCategory()" *appHasPermission="[permissionActions.CREATE_CATEGORY]">
        <span class="show-label">Add New Category</span>
      </button>
    </ng-container>
  </div>
</app-page-header>

<ng-container *ngIf="!isLoading; else pageLoaderTemplate">
  <p-table
    class="no-column-selection"
    [scrollable]="true"
    [value]="categories"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="true"
    [rowHover]="true"
    [loading]="isLoading"
    styleClass="p-datatable-gridlines"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <th pResizableColumn scope="col" pFrozenColumn>Category</th>
        <th pResizableColumn scope="col">Unit Type</th>
        <th pResizableColumn scope="col">Make</th>
        <th pResizableColumn scope="col">Model</th>
        <th pResizableColumn scope="col">Specification</th>
        <th pResizableColumn class="col">Inventory</th>
        <th pResizableColumn scope="col" *appHasPermission="[permissionActions.UPDATE_CATEGORY, permissionActions.DELETE_CATEGORY]">Actions</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-category>
      <tr>
        <td pFrozenColumn>
          {{ category?.name }}
        </td>
        <td>
          {{ category?.unitType }}
        </td>
        <td>
          {{ category?.make }}
        </td>
        <td>
          {{ category?.model }}
        </td>
        <td>
          {{ category?.specification }}
        </td>
        <td>
          {{ category?.inventoryCount }}
        </td>
        <td class="actions" *appHasPermission="[permissionActions.UPDATE_CATEGORY, permissionActions.DELETE_CATEGORY]">
          <img [src]="constants.staticImages.icons.edit" (click)="onAddEditCategory(category)" alt="" *appHasPermission="[permissionActions.UPDATE_CATEGORY]" />
          <img [src]="constants.staticImages.icons.deleteIcon" (click)="showDeleteModal(category, $event)" alt="" *appHasPermission="[permissionActions.DELETE_CATEGORY]" />
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="6" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
</ng-container>

<ng-template #pageLoaderTemplate>
  <div class="no-data mt-5">
    <em class="pi pi-spin pi-spinner"></em>
  </div>
</ng-template>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  (onHide)="closeModal()"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-category-add-update
    *ngIf="showCreateModal"
    [selectedCategory]="selectedCategory"
    (closeModal)="closeModal()"
    (addNewCategory)="addNewCategory($event)"
    (updateCategoryDetails)="updateCategory($event)"
  >
  </app-category-add-update>
</p-sidebar>

<p-confirmPopup></p-confirmPopup>
