import { GenericFilterParams, IdNameModel } from "src/app/@shared/models";
import { DealerGroup } from "./dealer.model";
import { Shop } from "./shop.model";

export class UserListFilter extends GenericFilterParams {
  archived = false;
}

export class UserListItem {
  id!: number;
  firstName!: string;
  lastName!: string;
  phoneNumber!: string;
  email!: string;
  createdDate!: number | string | null;
  employeeId!: string;
  lastActivity!: number | string | null;
  archived!: boolean;
  status!: boolean;
  authorities: Authorities[] = [];
  userDealerRoles: DealerGroup[] = [];
  shops: Shop[] = [];
  name?: string; 
  dealers?: DealerGroup[] = []; 
  shopsName?: string; 
  createdBy!: IdNameModel;
  roleId?: number;
  roleDto?: RoleDto;
}

export interface RoleDto {
  id?: number;
  name: Authorities;
}

export enum Authorities {
  ROLE_ADMIN = "ROLE_ADMIN",
  ROLE_USER = "ROLE_USER",
}

export interface UserDealerRole {
  id?: number;
  dealerId: number;
  roleId?: number;
  selectedDealerId?: number;
}

export class UserCreateParam {
  email!: string;
  phoneNumber!: string;
  employeeId!: string;
  firstName!: string;
  lastName!: string;
  userDealerRoles: UserDealerRole[] = [];
  shops: Shop[] = [];
}

export interface ChangePasswordParam {
  newPassword: string,
  userId: number
}

export class DealerRole {
  dealer!: IdNameModel;
  archivedDealer!: boolean | undefined;
  roles: IdNameModel[] = [];
  roleString!: string
}

export class DealerShop {
  dealer!: IdNameModel;
  archivedDealer!: boolean | undefined;
  shops: IdNameModel[] = [];
  shopString!: string;
}
