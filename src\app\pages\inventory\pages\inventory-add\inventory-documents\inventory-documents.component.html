<div class="content">
  <div class="title">
    <div class="parent-associate-document">
      <div class="ass-doc">
        <h4>Associated Documents</h4>
      </div>
    </div>
  </div>
  <div class="upload-doc" *ngIf="!isViewMode">
    <button class="btn btn-primary right upload-button" type="button" [appImageIconSrc]="constants.staticImages.icons.uploadImage" (click)="excelDownloadMenu.toggle($event)">
      <span class="show-label">Upload Document</span>
    </button>
    <p-menu #excelDownloadMenu [model]="excelDownloadMenuItems" [popup]="true"></p-menu>
    <input type="file" id="privateDocument" (change)="onFileSelect($event, 'PRIVATE')" class="hide-upload-input" [accept]="constants.allowedPdfFormats" />
    <input type="file" id="publicDocument" (change)="onFileSelect($event, 'PUBLIC')" class="hide-upload-input" [accept]="constants.allowedPdfFormats" />
  </div>
  <div class="add-margin">
    <p-table
      [columns]="selectedColumns"
      [value]="inventoryDocumentList"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getPublicDocumentListByUnitId.bind(this))"
      [sortField]="paginationConfig.predicate"
      [rowHover]="true"
      [loading]="isLoading"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th class="medium-col" scope="col">Title</th>
          <ng-container *ngFor="let col of columns">
            <th pReorderableColumn *ngIf="col.reorderable" [ngClass]="col.class">
              {{ col.header }}
            </th>
            <th *ngIf="!col.reorderable" [ngClass]="col.class">
              {{ col.header }}
            </th>
          </ng-container>
          <th class="medium-col" scope="col">Action</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-inventoryDocumentList let-columns="columns" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td class="view-document-enabled" (click)="openDocumentDetails(inventoryDocumentList)">
            <button class="btn pdf-icon" id="addModelBtn" type="button" [appImageIconSrc]="constants.staticImages.icons.pdfIcon"></button>
            {{ inventoryDocumentList.title }}
          </td>
          <ng-container *ngFor="let col of columns">
            <ng-container *ngIf="col.evaluationExpression">
              <td [ngClass]="col.class">
                {{ getEvaluatedExpression(col.evaluationExpression, inventoryDocumentList) }}
              </td>
            </ng-container>
            <ng-container *ngIf="!col.evaluationExpression">
              <td *ngIf="!col.isATemplate" [ngClass]="col.class">
                {{ inventoryDocumentList[col.field] }}
              </td>
            </ng-container>
          </ng-container>
          <td>
            <ng-container *ngIf="inventoryDocumentList?.isEditable; else openPdf">
              <a [href]="inventoryDocumentList?.documentUrl" target="_blank">
                <img [src]="constants.staticImages.icons.viewIcon" alt="" class="view-icon" />
              </a>
              <a (click)="downloadServerPDF(inventoryDocumentList)">
                <img [src]="constants.staticImages.icons.download" alt="" class="view-icon" />
              </a>
              <img [src]="constants.staticImages.icons.deleteIcon" alt="" class="view-icon" (click)="onDeleteDocument(inventoryDocumentList, $event)" *ngIf="!isViewMode" />
            </ng-container>
            <ng-template #openPdf>
              <a (click)="utils.viewUploadedPdf(inventoryDocumentList?.file)">
                <img [src]="constants.staticImages.icons.viewIcon" alt="" class="view-icon" />
              </a>
              <a (click)="utils.downloadUploadedPdf(inventoryDocumentList?.file)">
                <img [src]="constants.staticImages.icons.download" alt="" class="view-icon" />
              </a>
              <img [src]="constants.staticImages.icons.deleteIcon" alt="" class="view-icon" (click)="onDeleteDocument(inventoryDocumentList, $event)" *ngIf="!isViewMode" />
            </ng-template>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <div *ngFor="let fileProgress of fileUploadProgresses; let fileIndex = index">
      <div class="files" *ngIf="!fileProgress.isResolved">
        <div class="file-box-wrapper">
          <div class="file-box" [ngClass]="{ 'in-progress': !fileProgress?.isResolved }">
            <button class="btn pdf-icon" id="addModelBtn" type="button" [appImageIconSrc]="constants.staticImages.icons.pdfIcon"></button>
          </div>
          <div class="file-progress">
            {{ fileProgress?.fileProperty?.name }}
            <p-progressBar [value]="fileProgress?.progress$ | async" [showValue]="true"></p-progressBar>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showDocumentDetails"
  position="right"
  (onHide)="showDocumentDetails = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-inventory-document-details (onClose)="onAddEditPopupClose($event)" *ngIf="showDocumentDetails" [documentDetails]="documentDetails" [isViewMode]="isViewMode">
  </app-inventory-document-details>
</p-sidebar>
