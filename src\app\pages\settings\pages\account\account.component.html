<div class="wrapper">
  <form class="row" (ngSubmit)="onUpdateProfile()">
    <section [formGroup]="profileFormGroup">
      <div class="row">
        <div class="form-group col-md-6 col-12">
          <label class="required">First name</label>
          <input formControlName="firstName" type="text" class="form-control email" appNameValidation />
          <app-error-messages [control]="profileFormGroup?.controls?.firstName"></app-error-messages>
        </div>
        <div class="form-group col-md-6 col-12">
          <label class="required">Last name</label>
          <input formControlName="lastName" type="text" class="form-control email" appNameValidation />
          <app-error-messages [control]="profileFormGroup?.controls?.lastName"></app-error-messages>
        </div>
      </div>
    </section>
    <div class="action-wrapper">
      <button class="btn btn-primary" type="submit" [disabled]="isLoading">
        Update<fa-icon class="position-absolute ms-2" [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isLoading"></fa-icon>
      </button>
    </div>
  </form>
</div>
<p-confirmPopup></p-confirmPopup>
