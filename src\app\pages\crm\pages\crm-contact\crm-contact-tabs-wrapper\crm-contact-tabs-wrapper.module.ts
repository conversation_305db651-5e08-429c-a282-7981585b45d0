import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { CardModule } from 'primeng/card';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { MenuModule } from 'primeng/menu';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmCustomerAddModule } from '../../crm-customer/crm-customer-add/crm-customer-add.module';
import { CrmContactAddExtendedModule } from '../crm-customer-add-extended/crm-customer-add-extended.module';
import { CrmContactTabsWrapperComponent } from './crm-contact-tabs-wrapper.component';

@NgModule({
  declarations: [CrmContactTabsWrapperComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    SidebarModule,
    ReactiveFormsModule,
    FormsModule,
    TabViewModule,
    ConfirmPopupModule,
    DropdownModule,
    CardModule,
    TableModule,
    MenuModule,
    CrmCustomerAddModule,
    CrmContactAddExtendedModule,
  ],
})

export class CrmContactTabsWrapperModule { }
