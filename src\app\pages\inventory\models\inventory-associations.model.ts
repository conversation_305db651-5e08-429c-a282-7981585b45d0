import { UnitImages } from "@pages/crm/models/customer-inventory.model";
import { InternetOption } from "./inventory.model";

export interface AssociationsUnits {
  id: number;
  stockNumber: string;
  vin: string;
  make: string;
  model: string;
  year: string;
  yearMakeModel: string;
  unitImages: UnitImages;
  unitAssociationChildDTO: Array<AssociationsUnits>;
  categoryId: number;
  retailCost?: number;
  retailAskingPrice?: number;
  investmentCost?: number;
  quotePrice?: number;
  isSelected?: boolean;
  unitId?: number;
  owner: AssociatedUser;
  internetOption: InternetOption;
}

export interface AssociationHistory {
  id: number;
  eventType: string;
  eventDate: string;
  unit: AssociationsUnits;
  user: AssociatedUser;
  associatedUnit: AssociationsUnits;
}

export interface AssociatedUser {
  id: number;
  name: string;
  abbreviation: string;
}
