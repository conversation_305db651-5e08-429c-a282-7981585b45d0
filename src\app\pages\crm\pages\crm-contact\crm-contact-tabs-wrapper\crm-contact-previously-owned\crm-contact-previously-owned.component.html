<div class="card tabs crm-contact-previously-owned">
  <div class="tab-content">
    <p-table class="no-column-selection" [value]="previouslyOwnedFormArray.controls" responsiveLayout="stack"
      sortMode="single" [customSort]="true" [lazy]="true" [reorderableColumns]="true" [rowHover]="true"
      [loading]="isLoading" [scrollable]="true">
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th scope="col" pFrozenColumn>
            #Stock
          </th>
          <th scope="col">
            Vin
          </th>
          <th scope="col">
            Make
          </th>
          <th scope="col">
            Year
          </th>
          <th scope="col">
            Model
          </th>
          <th scope="col">
            Status
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td pFrozenColumn>
            {{rowData?.value?.stock}}
          </td>
          <td>
            {{rowData?.value?.vin}}
          </td>
          <td>
            {{rowData?.value?.make}}
          </td>
          <td>
            {{rowData?.value?.year}}
          </td>
          <td>
            {{rowData?.value?.model}}
          </td>
          <td>
            {{rowData?.value?.status}}
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="6" class="empty">No data to display</td>
      </ng-template>
    </p-table>
  </div>
</div>
