import { Component, OnInit } from '@angular/core';
import { Constants } from '@constants/*';
import { ROUTER_UTILS } from '@core/utils/router.utils';
import { distinctUntilChanged, Subject, takeUntil } from 'rxjs';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';

@Component({
  templateUrl: './not-found.component.html',
  styles: [
    `.not-found-container {
      min-height: calc(100vh - 120px);
    }`
  ]
})
export class NotFoundComponent implements OnInit {
  path = ROUTER_UTILS.config.base;
  constants = Constants;
  isDarkMode = false;
  destroy$: Subject<void> = new Subject();

  constructor(private readonly themeService: ThemeService) { }

  ngOnInit(): void {
    this.subscribeToTheme();
  }

  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        if (theme === THEMES.DARK) {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
      });
  }
}
