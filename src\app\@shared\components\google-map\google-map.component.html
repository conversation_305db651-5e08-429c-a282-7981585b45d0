<div class="modal-title">
  <h4>Google Map</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<div class="model-body">
  <div class="error-message" *ngIf="mapNotFoundMessage">
    {{ mapNotFoundMessage }}
  </div>

  <div *ngIf="overlays.length">
      <google-map [zoom]="zoom" [center]="center" width="100%" height="100vh">
        <map-marker *ngFor="let overlay of overlays" [position]="overlay.position" [title]="overlay.title"></map-marker>
      </google-map>
    <!-- TODO will be removed -->
    <!-- <p-gmap [options]="options" [overlays]="overlays" [style]="{'width':'100%','height':'calc(100vh - 58px)'}" ></p-gmap> -->
  </div>
</div>

