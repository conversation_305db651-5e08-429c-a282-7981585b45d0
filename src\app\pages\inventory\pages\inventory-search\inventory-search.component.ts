import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { BaseComponent } from '@core/utils';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { IdNameModel } from 'src/app/@shared/models';
import { DummyService } from 'src/app/@shared/services';

enum SearchOption {
  VIN = 1,
  MAKE_MODEL_YEAR = 2
}

@Component({
  selector: 'app-inventory-search',
  templateUrl: './inventory-search.component.html',
  styleUrls: ['./inventory-search.component.scss']
})
export class InventorySearchComponent extends BaseComponent implements OnInit {

  unitSearchFormGroup!: FormGroup;
  searchOptions = SearchOption;
  selectedSearchOption!: SearchOption;
  vinSearchFormGroup!: FormGroup;
  loaders = {
    makes: false,
    models: false,
    categories: false,
    years: false
  }

  makes: IdNameModel[] = [];
  models: IdNameModel[] = [];
  categories: IdNameModel[] = [];
  years: IdNameModel[] = [];

  searchResults: any[] = [];
  selectedVehicle: any;

  @Output() onVehicleSelect: EventEmitter<any> = new EventEmitter();

  constructor(private readonly formBuilder: FormBuilder, private readonly inventoryService: InventoryService) {
    super();
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getDummyValues();
  }

  initializeFormGroup(): void {
    this.unitSearchFormGroup = this.formBuilder.group({
      make: new FormControl(null),
      model: new FormControl(null),
      year: new FormControl(null),
      category: new FormControl(null),
    });
    this.vinSearchFormGroup = this.formBuilder.group({
      vinNumber: new FormControl(null, Validators.required)
    });
  }

  getDummyValues() {
    this.makes = this.inventoryService.getDummyValues('makes');
    this.models = this.inventoryService.getDummyValues('models');
    this.categories = this.inventoryService.getDummyValues('categories');
    this.years = this.inventoryService.getDummyValues('years');
  }

  onSearch(): void {
    const generatorFunction: Function = () => {
      return {
        id: DummyService.randomNumber(2),
        year: 2017,
        model: DummyService.randomString(6),
        make: DummyService.randomString(10),
        name: DummyService.randomString(15)
      }
    }
    this.searchResults = DummyService.randomDataList(generatorFunction, 3);
  }

  onSelectVehicle(vehicle: any) {
    this.selectedVehicle = vehicle;
    this.onVehicleSelect.next(this.selectedVehicle);
  }

}
