import { IdNameModel } from "src/app/@shared/models";

export interface DriverScheduleCommentListItem {
  id: number;
  driverScheduleId: number;
  skeyeUser: IdNameModel;
  comment: string;
  date: string | null;
  name: string;
  mentionUserIds: number[];
}

export interface DriverScheduleCommentCreateParam {
  driverScheduleId: number;
  comment: string;
  id?: number;
  mentionUserIds: number[];
}
