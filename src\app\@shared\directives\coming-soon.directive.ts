import { Directive, ElementRef, Renderer2 } from '@angular/core';
/**
 * @whatitdoes it adds coming soon place holder for any element
 * @howtouse  <div sflCommingSoon> // will add coming soon text and bg color to look the applied div disabled// </div>
 *
 */

@Directive({
  selector: '[appCommingSoon]'
})
export class CommingSoonDirective {

  constructor(private readonly elementRef: ElementRef, private readonly renderer: Renderer2) {
    this.renderer.addClass(this.elementRef.nativeElement, 'sfl-coming-soon-element');
    this.elementRef.nativeElement.innerHTML = '<div class="coming-soon"><h4>COMING SOON</h4></div>';
  }

}
