import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewChildren } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES, VALIDATION_CONSTANTS } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AllUserListFilter, InventoryCommentCreateParam, MentionUser } from '@pages/inventory/models/inventory-communication';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { MentionConfig } from 'angular-mentions';
import { takeUntil } from 'rxjs';
import { MENTION_CONFIG } from 'src/app/@shared/constants/common.constant';
import { MentionParam } from 'src/app/@shared/models';
type numNull = number | null | undefined;

@Component({
  selector: 'app-edit-communication',
  templateUrl: './edit-communication.component.html',
  styleUrls: ['./edit-communication.component.scss']
})
export class EditCommunicationComponent extends BaseComponent implements OnInit {
  @Input() caseNotes!: any;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  title = 'Edit Comments';
  commentFormGroup!: FormGroup;
  availableMentionUsers: MentionParam[] = [];
  mentionConfig: MentionConfig = {
    ...MENTION_CONFIG,
    items: this.availableMentionUsers
  };
  mentionedUsers: MentionUser[] = [];
  textEditor: any;
  editorChild: any;
  @ViewChild('textEditor') set content(content: any) {
    if (content) {
      this.textEditor = content;
    }
  }
  @ViewChildren('editorChild') set contents(contents: any) {
    if (contents) {
      this.editorChild = contents;
    }
  }
  @ViewChild('boldText') boldText!: ElementRef;
  @Input() filterParams: any = new AllUserListFilter();
  @Input() inventoryInfoId!: numNull;
  @Input() inventoryIncomingInfoId!: numNull;
  unitId!: numNull;
  isFirstTime = true;

  constructor(private readonly formBuilder: FormBuilder, private readonly toasterService: AppToasterService, private readonly inventoryService: InventoryService, private readonly cdf: ChangeDetectorRef, private readonly userAnnotationService: UserAnnotationService) { super() }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getAll();
    this.setCommentForm()
  }
  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.userAnnotationService.get(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          this.availableMentionUsers = res.map((user: any) => ({ id: user.id, label: user.name }));
          this.mentionConfig.items = this.availableMentionUsers;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: (err: any) => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }
  initializeFormGroup(): void {
    this.commentFormGroup = this.formBuilder.group({
      message: new FormControl(null, [Validators.required, Validators.maxLength(VALIDATION_CONSTANTS.textAreaMaxLength)]),
      unitId: new FormControl(null),
      parentCommunicationId: new FormControl(null),
      id: new FormControl(null),
      mentionUserIds: new FormControl([])
    });
  }
  onCancel() {
    this.onClose.emit(true);
  }

  editCommentSubmit() {
    if (this.commentFormGroup.invalid) {
      this.commentFormGroup.markAllAsTouched();
      return;
    }
    this.commentFormGroup.markAsUntouched();
    if (this.commentFormGroup.value.id) {
      this.editComment()
    }
  }
  get commentCreateParams(): InventoryCommentCreateParam {
    return {
      ...this.commentFormGroup.value,
    };
  }

  private removeErasedUser(comment: string, ids: number[]): number[] {
    return ids.filter(id => {
      const label = this.availableMentionUsers.find(obj => obj.id === id)?.label;
      return label && comment.toLowerCase().includes(label.toLowerCase());
    });
  }

  private editComment(): void {
    const params = this.commentCreateParams;
    params.mentionUserIds = [...new Set([...params.mentionUserIds, ...this.mentionedUsers.map(i => i.id)])];
    if (this.commentFormGroup.controls['message'].value.includes('&lt;strong')) {
      let val = this.commentFormGroup.controls['message'].value.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['message'].setValue(`${val}`);
      params.message = this.commentFormGroup.controls['message'].value
    }
    params.mentionUserIds = this.removeErasedUser(params.message, params.mentionUserIds);
    this.inventoryService.update(params, API_URL_UTIL.inventory.communications).pipe(takeUntil(this.destroy$)).subscribe(savedComment => {
      this.toasterService.success(MESSAGES.commentUpdateSuccess);
      this.onClose.emit(true);
      this.cdf.detectChanges();
    });
  }

  mentionSelected(mention: any, childIndex = -1) {
    let count = 0;
    for (const i of this.mentionedUsers) {
      if (i.id === mention.id) {
        count++;
      }
    }
    if (count === 0) {
      this.mentionedUsers.push(mention);
    }
    setTimeout(() => {
      if (childIndex > -1) {
        this.editorChild.toArray()[childIndex].getQuill().updateContents();
      } else {
        this.textEditor.getQuill().updateContents();
      }
    }, 0);
  }

  textChange(e: any) {
    if (e.htmlValue.includes('&lt;strong')) {
      let val = e.htmlValue.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['message'].setValue(`${val}`);
      setTimeout(() => {
        this.textEditor.el.nativeElement.childNodes[0].lastChild.childNodes[0].focus();
        this.textEditor.el.nativeElement.childNodes[0].lastChild.childNodes[0].click();
        this.boldText.nativeElement.click();
        this.textEditor.getQuill().setSelection(val.length + 1);
      }, 0);
    }
  }

  decodeEntities(str: any) {
    // this prevents any overhead from creating the object each time
    const element = document.createElement('div');
    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
    element.innerHTML = str;

    return element.textContent;
  }

  selectionChange(e: any) {
    if (e && e.range && e.range.index > 0 && e.source === 'api') {
      this.boldText.nativeElement.click();
    }
  }

  setCommentForm() {
    if (this.caseNotes) {
      this.commentFormGroup.patchValue({
        message: this.caseNotes.value.message,
        unitId: this.caseNotes.value.unitId,
        id: this.caseNotes.value.id,
        mentionUserIds: this.caseNotes.value.mentionUserIds,
        parentCommunicationId: this.caseNotes.value.parentCommunicationId
      })
    }
  }
}
