@import "src/assets/scss/variables";

.warranties {
  &:not(:first-child) {
    margin-top: 7px;
  }

  .radio-wrapper {
    display: flex;

    p-radiobutton {
      margin-right: 30px;
    }
  }

  .label {
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
  }
}

::ng-deep .internet-groups {
  .p-multiselect {
    width: 100%;
  }
}

.public-inventory-wrapper {
  font-size: 14px;

  a {
    color: var(--link-color);
    text-decoration: underline;
  }
}

.dummy-data-btn {
  position: fixed;
  right: 70px;
  top: 8px;
}

.sfl-coming-soon-element {
  margin-top: 16px;
}

.add-btn {
  height: 25px;
  padding: 0px 5px;
  float: right;
  margin: 3px;
}

.p-field-radiobutton {
  padding: 0 15px 0 0;
}

.p-field-radiobutton p-radiobutton {
  padding: 0 5px 0 0;
}

.mt-23 {
  margin-top: 22px;
}

.bold {
  font-weight: 600;
  font-size: 20px;
}

.displayContactDetails.company {
  font-weight: 500;
}

.displayContactIcons {
  border: none;
  background-color: transparent;
  float: right;
  margin-right: 5px;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

::ng-deep .inventory-general-pipeline {
  .p-timeline {
    width: 500px !important;
  }

  .p-timeline-horizontal .p-timeline-event-connector {
    width: 100%;
    min-width: 100px;
    background-color: var(--active-color);
  }

  .p-timeline .p-timeline-event-marker {
    border-color: $placeholder-color;
    width: 1rem;
    height: 1rem;
  }

  .p-timeline-event-connector {
    flex-grow: unset;
  }

  .p-timeline.p-timeline-horizontal .p-timeline-event-opposite,
  .p-timeline.p-timeline-horizontal .p-timeline-event-content {
    padding: 0;
  }

  .p-timeline-event-opposite {
    display: none;
  }

  .p-timeline-event-content {
    flex: unset;

    span {
      color: var(--text-color);
      font-size: 14px;
      letter-spacing: 0;
      line-height: 21px;
    }
  }

  .p-timeline.p-timeline-horizontal .p-timeline-event-opposite,
  .p-timeline.p-timeline-horizontal .p-timeline-event-content {
    width: max-content;
  }

  .p-timeline-event {
    min-height: unset;
  }

  .p-timeline-event-separator {
    span {
      border: 2px solid var(--active-color);
      background-color: var(--active-color);
      color: var(--white-color);
      border-radius: 50%;
      width: 1.5rem;
      height: 1.5rem;
      font-size: 0.8rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .p-datatable .p-datatable-tbody > tr > td {
    padding: 0.4rem 1rem;
  }

  .initials {
    left: -10px;
    width: 40px;
    height: 25px;
    background-color: var(--app-background-color);
    color: var(--text-color);
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    top: 25%;
    border: 1px solid var(--text-color);
    margin-left: 5px;
    padding: 3px;
  }
}

.pos-rel {
  position: relative;
}

.view-pipeline {
  width: 215px;
  position: absolute;
  right: 22px;
}

.financial-tab {
  position: fixed;
  right: 370px;
  top: 8px;
}

::ng-deep .inventory-general-div {
  .p-disabled {
    opacity: 0.6;
  }

  .p-disabled .p-dropdown-label,
  .task-add .p-component:disabled .p-dropdown-label {
    color: #797979;
  }

  .p-dialog .p-dialog-header .p-dialog-title {
    font-weight: 500;
    font-size: 15px;
  }

  .p-dialog .p-dialog-content {
    padding: 0;
  }
}

::ng-deep .width-80 {
  width: 80rem !important;
}
