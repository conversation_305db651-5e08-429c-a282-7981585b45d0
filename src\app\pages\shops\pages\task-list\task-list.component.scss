.card {
  box-shadow: none !important;
}

.tabs .tab-content {
  padding: 0 !important;
}

.timeline {
  text-align: center;

  .footer {
    background-color: var(--listing-timeline-bg-color);
    width: fit-content;
    padding: 2px 20px;
    border-radius: 12px;

    .pi {
      margin-right: 5px;
    }

    span {
      font-size: 13px;
      letter-spacing: 0;
      line-height: 21px;
      color: var(--active-color);
      display: flex;
      align-items: center;
    }
  }
}

.activity-log {
  display: flex;
  align-items: center;
  color: var(--active-color);
  font-size: 18px;

  &:hover {
    color: var(--active-color-lighter);
    cursor: pointer;
  }
}

.timeline-header {
  width: 220px;
}

.pi-trash {
  font-size: 20px;

  &:hover {
    cursor: pointer;
  }
}

::ng-deep .task-list {
  .p-datatable .p-datatable-tbody > tr {
    background: white;
  }

  .p-datatable.p-datatable-hoverable-rows .p-datatable-tbody > tr:not(.p-highlight):hover {
    background-color: white !important;
  }
}

.view-task {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
  z-index: 1;
}

.dropdown-toggle::after {
  margin-left: 25px !important;
}

.w-150 {
  width: 310px !important;
}

::ng-deep .task-list p-table p-dropdown {
  height: 22px;
}

::ng-deep .task-list p-table {
  .p-dropdown .p-dropdown-label {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
    padding-top: 0px;
    font-size: 14px;
  }

  .p-dropdown .p-dropdown-trigger {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
  }

  .p-dropdown {
    background-color: var(--active-color) !important;
    height: inherit;
  }

  .pi-chevron-down:before {
    font-size: 14px;
  }

  .priority-icon {
    padding: 17px !important;
  }
}

th,
td {
  width: 75px;
}
