<div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
  <h4 class="header-title">{{ title }}</h4>
  <span *ngIf="isEditMode" class="created-by">
    <span class="bold-text">#{{ customerDetails?.id }}</span>
    Created By <span class="bold-text">{{ customerDetails?.salesPerson?.name }}</span> on
    {{ customerDetails?.createdDate | date : constants.fullDateFormat }}
  </span>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>

<div class="customer-lead-div">
  <form [formGroup]="customerLeadFormGroup" (ngSubmit)="onSubmit()">
    <div class="content">
      <p-accordion class="nested-accordion" [multiple]="true">
        <div class="row">
          <div class="col-12 col-lg-4">
            <p-accordionTab [(selected)]="accordionTabs.contactInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.contactInfo }">
                  <span
                    >Contact Information
                    <span *ngIf="(displayContact?.name || displayContact?.contactName) && !accordionTabs.contactInfo">
                      ({{ displayContact?.name ? displayContact?.name : displayContact?.contactName }})
                    </span>
                  </span>
                  <em class="pi" [ngClass]="accordionTabs.contactInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="contactInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
            <p-accordionTab [(selected)]="accordionTabs.salePersonInfo" *ngIf="isEditMode || isViewMode">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.salePersonInfo }">
                  <span
                    >Salesman Information
                    <span *ngIf="customerDetails?.salesPerson?.name && !accordionTabs.salePersonInfo"> ({{ customerDetails?.salesPerson?.name }}) </span>
                  </span>
                  <em class="pi" [ngClass]="accordionTabs.salePersonInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="salesInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-lg-8 col-12">
            <p-accordionTab [(selected)]="accordionTabs.inventoryPreferenceInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.inventoryPreferenceInfo }">
                  <span class="accordion-title">
                    Inventory Preference
                    <span *ngIf="!accordionTabs.inventoryPreferenceInfo && (isEditMode || isViewMode)"> ({{ getInventoryPerference() }}) </span>
                  </span>
                  <em class="pi" [ngClass]="accordionTabs.inventoryPreferenceInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="inventoryInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-sm-12">
            <p-accordionTab [(selected)]="accordionTabs.specificationPreferenceInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.specificationPreferenceInfo }">
                  <span class="accordion-title">
                    Specification Preference
                    <span *ngIf="!accordionTabs.specificationPreferenceInfo && (isEditMode || isViewMode)"> ({{ getSpecificationPrefrecence() }}) </span>
                  </span>
                  <em class="pi" [ngClass]="accordionTabs.specificationPreferenceInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-container *ngIf="isSpecificationLoading; else showSpecification">
                <div class="text-center">
                  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
                </div>
              </ng-container>
              <ng-template #showSpecification>
                <div class="col-12" *ngFor="let specification of inventorySpecificationForm">
                  <ng-container *ngIf="specification.fields.length">
                    <p-accordionTab [(selected)]="specification.isShow">
                      <ng-template pTemplate="header">
                        <div class="accordion-header" [ngClass]="{ active: specification.isShow }">
                          <span>
                            {{ specification.sectionName }}
                          </span>
                          <em class="pi" [ngClass]="specification.isShow ? 'pi-angle-up' : 'pi-angle-down'"></em>
                        </div>
                      </ng-template>
                      <ng-template pTemplate="content">
                        <div class="row">
                          <div [ngClass]="{ 'col-12': true, ' col-sm-6 col-md-6 col-lg-3': field.dataType !== 'TextBox' }" *ngFor="let field of specification.fields">
                            <label [ngClass]="{ required: field.isRequired }">{{ field.label }}</label>
                            <ng-container *ngIf="field.dataType === 'DropDown'">
                              <p-dropdown
                                appPreventClearFilter
                                appendTo="body"
                                [name]="field.for"
                                [options]="field.options"
                                [ngModelOptions]="{ standalone: true }"
                                [(ngModel)]="field.value"
                                optionLabel="name"
                                [showClear]="true"
                                optionValue="id"
                                [filter]="true"
                                filterBy="name"
                                [disabled]="!!isViewMode"
                                [placeholder]="field.placeholder"
                                [required]="field.isRequired"
                                #dropDownField="ngModel"
                              >
                                <ng-template pTemplate="empty">
                                  <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: false, data: field.for }"></ng-container>
                                </ng-template>
                                <ng-template let-item pTemplate="item">
                                  <span>{{ item.name }}</span>
                                </ng-template>
                              </p-dropdown>
                              <small class="text-danger" *ngIf="!dropDownField.valid && dropDownField.touched"> This field is required </small>
                            </ng-container>
                            <ng-container *ngIf="field.dataType === 'TextField'">
                              <input
                                class="form-control"
                                type="text"
                                [name]="field.for"
                                [placeholder]="field.placeholder"
                                [required]="field.isRequired"
                                [(ngModel)]="field.value"
                                #textFeild="ngModel"
                                [ngModelOptions]="{ standalone: true }"
                                [disabled]="!!isViewMode"
                              />
                              <small class="text-danger" *ngIf="!textFeild.valid && textFeild.touched"> This field is required </small>
                            </ng-container>
                            <ng-container *ngIf="field.dataType === 'Number'">
                              <p-inputNumber
                                styleClass="w-100"
                                inputStyleClass="form-control"
                                [name]="field.for"
                                [placeholder]="field.placeholder"
                                [(ngModel)]="field.value"
                                [required]="field.isRequired"
                                [ngModelOptions]="{ standalone: true }"
                                #numberField="ngModel"
                                [disabled]="!!isViewMode"
                                [useGrouping]="field.for !== 'Equipment Year'"
                              >
                              </p-inputNumber>
                              <small class="text-danger" *ngIf="!numberField.valid && numberField.touched"> This field is required </small>
                            </ng-container>
                            <ng-container *ngIf="field.dataType === 'TextBox'">
                              <textarea
                                [placeholder]="field.placeholder"
                                [(ngModel)]="field.value"
                                rows="3"
                                [ngModelOptions]="{ standalone: true }"
                                [name]="field.for"
                                [required]="field.isRequired"
                                [disabled]="!!isViewMode"
                                #textAreaField="ngModel"
                              ></textarea>
                              <small class="text-danger" *ngIf="!textAreaField.valid && textAreaField.touched"> This field is required </small>
                            </ng-container>
                          </div>
                        </div>
                      </ng-template>
                    </p-accordionTab>
                  </ng-container>
                </div>
              </ng-template>
            </p-accordionTab>
          </div>
        </div>
      </p-accordion>
      <section *ngIf="isEditMode || isViewMode">
        <div class="row">
          <app-crm-customer-inventory-wrapper
            [crmId]="crmId"
            (inventoryMatchedCounts)="inventoryMatchedCount($event)"
            [crmCustomerInfoId]="crmCustomerInfo?.id"
            [crmCustomerInfo]="crmCustomerInfo"
            [customerDetails]="customerDetails"
            [inventorySpecificationForm]="inventorySpecificationForm"
          >
          </app-crm-customer-inventory-wrapper>
        </div>
      </section>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
      <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall *ngIf="!isViewMode">Save</button>
      <span *ngIf="isViewMode"><button class="btn btn-primary" type="submit" appShowLoaderOnApiCall *appHasPermission="[permissionActions.UPDATE_CUSTOMER_LEAD]">Edit</button></span>
      <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!(isEditMode || isViewMode)" appShowLoaderOnApiCall>Save & Add New</button>
    </div>
  </form>

  <ng-template #contactInfoTemplate [formGroup]="customerLeadFormGroup">
    <div class="row">
      <div class="col-lg-12 col-12 mb-3">
        <label class="required">Customer</label>
        <ng-container *appHasPermission="[permissionActions.CREATE_CONTACTS]">
          <button
            class="btn btn-primary add-btn"
            id="addMakeBtn"
            type="button"
            *ngIf="!isViewMode"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="openModel('contact')"
          ></button>
        </ng-container>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="contactList"
          formControlName="crmContactId"
          optionLabel="contactName"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="contactName"
          placeholder="Select contact"
          [itemSize]="30"
          [virtualScroll]="true"
          (onChange)="displayContactDetails($event.value)"
          [optionDisabled]="isOptionDisabled.bind(this)"
          >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.previousOwnerName, data: contactList }"> </ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span [ngClass]="{'disabled-dropdown-item': item.archived}">
              {{ item.contactName }}
              <span *ngIf="item.archived">{{ constants.archived }}</span>
            </span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="customerLeadFormGroup.controls?.crmContactId"></app-error-messages>
      </div>
    </div>
    <div class="row mt-10">
      <div class="col-lg-12 col-12 mt-4" *ngIf="displayContact">
        <p-card class="display-content">
          <div class="content-header d-flex justify-content-between">
            <div class="content-heading">
              <p class="bold m-0">{{ displayContact?.name ? displayContact?.name : displayContact.contactName }}</p>
            </div>
            <div class="location-dot" *ngIf="displayContact?.streetAddress || displayContact?.zipcode || displayContact?.city || displayContact?.state">
              <fa-icon [icon]="faIcons.faLocationDot" (click)="toggleGoogleMapPopUp(displayContact)"></fa-icon>
            </div>
          </div>
          <p class="displayContactDetails company">{{ displayContact.company }}</p>
          <p class="displayContactDetails">{{ displayContact.phoneWork }}</p>
          <p class="displayContactDetails">{{ displayContact.email }}</p>
        </p-card>
        <p-sidebar
          [closeOnEscape]="false"
          [dismissible]="false"
          [(visible)]="showGoogleMapSideBar"
          position="right"
          (onHide)="showGoogleMapSideBar = false"
          [blockScroll]="true"
          [showCloseIcon]="false"
          styleClass="p-sidebar-md"
          [baseZIndex]="10000"
          appendTo="body"
        >
          <app-google-map
            (onClose)="toggleGoogleMapPopUp(displayContact)"
            *ngIf="showGoogleMapSideBar"
            [addressGroup]="displayContact?.streetAddress"
            [address]="fullAddress"
          ></app-google-map>
        </p-sidebar>
      </div>
    </div>
  </ng-template>
  <ng-template #inventoryInfoTemplate [formGroup]="customerLeadFormGroup">
    <div class="row">
      <div class="col-lg-4 col-md-6 col-12">
        <label class="required">Category</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="categoryTypes"
          formControlName="categoryId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select category type"
          (onChange)="changeCategory($event.value)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.categoryType, data: categoryTypes }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="customerLeadFormGroup.controls.categoryId"></app-error-messages>
      </div>
      <div class="col-lg-4 col-md-6 col-12">
        <label class="required">Unit Type</label>
        <ng-container *appHasPermission="[permissionActions.CREATE_UNIT_TYPE]">
          <button
            class="btn btn-primary add-btn"
            id="addUnitTypeBtn"
            type="button"
            *ngIf="!isViewMode"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="openModel('unit-type')"
          ></button>
        </ng-container>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="unitTypes"
          formControlName="unitTypeId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select unit type"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.unitTypes, data: unitTypes }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="customerLeadFormGroup.controls.unitTypeId"></app-error-messages>
      </div>
      <div class="col-lg-4 col-md-6 col-12">
        <label>Year</label>
        <div class="year-wrapper">
          <div class="year-content from">
            <span>From: </span>
            <p-calendar
              styleClass="picker-dropdown"
              [showButtonBar]="true"
              [readonlyInput]="true"
              name="selectedFromYear"
              formControlName="minYear"
              view="year"
              dateFormat="yy"
              [maxDate]="customerLeadFormGroup.value.maxYear"
            >
            </p-calendar>
            <app-error-messages [control]="customerLeadFormGroup.controls.maxYear"></app-error-messages>
          </div>
          <div class="year-content">
            <span>To: </span>
            <p-calendar
              styleClass="picker-dropdown"
              [showButtonBar]="true"
              [readonlyInput]="true"
              name="selectedToYear"
              formControlName="maxYear"
              view="year"
              dateFormat="yy"
              [minDate]="customerLeadFormGroup.value.minYear"
            >
            </p-calendar>
            <app-error-messages [control]="customerLeadFormGroup.controls.minYear"></app-error-messages>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 col-12">
        <label>Make</label>
        <ng-container *appHasPermission="[permissionActions.CREATE_MAKE_MODEL]">
          <button
            class="btn btn-primary add-btn"
            id="addMakeBtn"
            type="button"
            *ngIf="!isViewMode"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="openModel('make')"
          ></button>
        </ng-container>
        <div class="multi-select-dropdown">
          <p-multiSelect
            appPreventClearFilter
            [options]="makes"
            defaultLabel="Select a Make"
            optionLabel="name"
            optionValue="id"
            [(ngModel)]="makeIds"
            [maxSelectedLabels]="1"
            selectedItemsLabel="{0} items selected"
            formControlName="makeId"
            appendTo="body"
            (onPanelHide)="getModels(makeIds)"
          >
            <ng-template pTemplate="empty">
              <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.makes, data: makes }"></ng-container>
            </ng-template>
          </p-multiSelect>
          <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="makeIds?.length" (click)="clearMakes()"></fa-icon>
          <app-error-messages [control]="customerLeadFormGroup.controls.makeId"></app-error-messages>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 col-12">
        <label>Model</label>
        <ng-container *appHasPermission="[permissionActions.CREATE_MAKE_MODEL]">
          <button
            class="btn btn-primary add-btn"
            id="addModelBtn"
            type="button"
            *ngIf="!isViewMode"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="openModel('model')"
          ></button>
        </ng-container>
        <div class="multi-select-dropdown">
          <p-multiSelect
            appPreventClearFilter
            [options]="models"
            defaultLabel="Select a Model"
            formControlName="unitModelId"
            optionLabel="name"
            optionValue="id"
            [maxSelectedLabels]="1"
            selectedItemsLabel="{0} items selected"
            [(ngModel)]="modelIds"
            appendTo="body"
          >
            <ng-template pTemplate="empty">
              <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.models, data: models }"></ng-container>
            </ng-template>
          </p-multiSelect>
          <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="modelIds?.length" (click)="clearModels()"></fa-icon>
          <app-error-messages [control]="customerLeadFormGroup.controls.unitModelId"></app-error-messages>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 col-12">
        <label>Designation</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="designations"
          formControlName="designationId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select designation"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.designations, data: designations }"> </ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="customerLeadFormGroup.controls.designationId"></app-error-messages>
      </div>
      <div class="col-lg-4 col-md-6 col-12">
        <label class="required">Status</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="customerStatusList"
          formControlName="status"
          optionLabel="name"
          optionValue="value"
          [filter]="true"
          filterBy="name"
          [showClear]="true"
          placeholder="Select a Status"
        >
        </p-dropdown>
        <app-error-messages [control]="customerLeadFormGroup.controls.status"></app-error-messages>
      </div>
    </div>
    <div class="row m-t-0">
      <div class="col-12">
        <label>Notes</label>
        <textarea placeholder="Enter notes" rows="3" formControlName="notes"></textarea>
        <div>
          <app-error-messages [control]="customerLeadFormGroup.controls.notes"></app-error-messages>
        </div>
      </div>
    </div>
    <div class="row m-t-0">
      <div class="col-12">
        <label>Lead Description</label>
        <textarea placeholder="Enter lead description" rows="3" formControlName="leadDescription"></textarea>
        <div>
          <app-error-messages [control]="customerLeadFormGroup.controls.leadDescription"></app-error-messages>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #salesInfoTemplate [formGroup]="customerLeadFormGroup">
    <div class="row mt-10">
      <div class="col-lg-12 col-md-12 col-12" *ngIf="customerDetails">
        <p-card class="display-content">
          <p class="bold">{{ customerDetails?.salesPerson?.name }}</p>
          <p class="displayContactDetails company">{{ customerDetails?.location?.name }}</p>
          <p class="displayContactDetails">{{ customerDetails?.salesPerson?.phoneNumber }}</p>
          <p class="displayContactDetails">{{ customerDetails?.salesPerson?.email }}</p>
        </p-card>
      </div>
    </div>
  </ng-template>
</div>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <div class="d-flex justify-content-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
    <p *ngIf="!loader && !data?.length">No records found</p>
  </div>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateMake"
  position="right"
  (onHide)="modelPopups.showCreateMake = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-make (onClose)="onAddEditPopupClose('make')" *ngIf="modelPopups.showCreateMake" [categoryId]="categoryId"> </app-add-new-make>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateModel"
  position="right"
  (onHide)="modelPopups.showCreateModel = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-model (onClose)="onAddEditPopupClose('model')" [categoryId]="categoryId" [makes]="makes" *ngIf="modelPopups.showCreateModel"> </app-add-new-model>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [fullScreen]="true"
  [(visible)]="modelPopups.showCreateContact"
  position="right"
  (onHide)="modelPopups.showCreateContact = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-contact-customer-add (onClose)="onAddEditPopupClose('contact')" (contactAddedId)="selectContactPerson($event)" *ngIf="modelPopups.showCreateContact">
  </app-crm-contact-customer-add>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateUnitType"
  position="right"
  (onHide)="modelPopups.showCreateUnitType = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-unit-type (onClose)="onAddEditPopupClose('unit-type')" [unitTypeCategoryId]="categoryId" *ngIf="modelPopups.showCreateUnitType"> </app-add-new-unit-type>
</p-sidebar>
