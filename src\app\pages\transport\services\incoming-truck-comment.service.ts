import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { Observable } from 'rxjs';
import { IncomingTruckCommentResponse } from '../models/incoming-truck-communication';

@Injectable({
  providedIn: 'root'
})
export class IncomingTruckCommentService extends BaseCrudService {


  constructor(protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  getBaseAPIPath(): string {
    return API_URL_UTIL.incomingTruckComment.root;
  }

  getIncomingTruckComments(unitId: number): Observable<IncomingTruckCommentResponse[]> {
    return this.httpClient.get<IncomingTruckCommentResponse[]>(`${API_URL_UTIL.incomingTruck.incomingTruckComment}/${unitId}/comments`);
  }
}
