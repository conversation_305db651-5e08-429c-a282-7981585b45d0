import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { ColumnItem, FilterList } from '@pages/common-table-column/models/common-table.column.model';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-save-preference',
  templateUrl: './save-preference.component.html',
  styleUrls: ['./save-preference.component.scss']
})
export class SavePreferenceComponent extends BaseComponent implements OnInit {
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() myPreference: ColumnItem[] = [];
  @Input() currentUserId!: number | null;
  @Input() selectedTabName!: string | undefined;
  title = 'Save Preference';
  filterParam!: FilterList;
  constructor(
    private readonly columnDropdownService: ColumnDropdownService,
    private readonly toasterService: AppToasterService,
  ) { super(); }
  ngOnInit(): void {
    this.filterParam = this.filterDataParam;
  }

  onCancel(): void {
    this.onClose.emit(false);
  }

  onSubmit(): void {
    this.savePreference();
  }

  private savePreference(): void {
    this.columnDropdownService.add(this.filterParam).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.preferenceMessage.replace("{action}", "saved"));
        this.onCancel();
      }
    });
  }

  get filterDataParam(): FilterList {
    return {
      module: `${this.currentUserId}_PREFERENCES`,
      data: JSON.stringify(this.myPreference),
      isDefault: false,
      skeyeUserId: this.currentUserId,
      deleted: false,
      filterName: '',
      isPrivate: true,
      filterType: "COLUMN",
      isPreference: true,
      activeTabName: this.selectedTabName
    }
  }
}
