<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button class="btn btn-primary me-3 left show-label" [appImageIconSrc]="constants.staticImages.icons.exportFile" (click)="exportUsersToExcel()">
      <span class="show-label">Export Excel</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
    <button class="btn btn-primary left" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAdd()" *appHasPermission="[permissionActions.CREATE_USERS]">
      <span class="show-label">Add New User</span>
    </button>
  </div>
</app-page-header>

<div class="card">
  <div class="tab-content">
    <p-tabView (onChange)="onTabChanged($event)" [scrollable]="true" styleClass="dynamic-tabs">
      <p-tabPanel header="Active">
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="activeTabTemplate"></ng-container>
        </ng-template>
      </p-tabPanel>
      <p-tabPanel header="Archived">
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="activeTabTemplate"></ng-container>
        </ng-template>
      </p-tabPanel>
    </p-tabView>
  </div>
</div>

<ng-template #activeTabTemplate>
  <p-table
    [columns]="selectedColumns"
    [value]="users"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="true"
    (onLazyLoad)="onSortChange($event, getAll.bind(this))"
    [sortField]="paginationConfig.predicate"
    [rowHover]="true"
    [loading]="isLoading"
    [scrollable]="true"
    styleClass="p-datatable-gridlines"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <th pResizableColumn pSortableColumn="email" id="email" pFrozenColumn>Email <p-sortIcon field="email"></p-sortIcon></th>
        <ng-container *ngFor="let col of columns">
          <th pResizableColumn [pSortableColumn]="col?.sortKey || col.field" pReorderableColumn *ngIf="col.reorderable" [ngClass]="col.class">
            {{ col.header }} <p-sortIcon [field]="col.sortKey || col.field" *ngIf="col.sortable"></p-sortIcon>
          </th>
          <th pResizableColumn [pSortableColumn]="col?.sortKey || col.field" *ngIf="!col.reorderable" [ngClass]="col.class">
            {{ col.header }} <p-sortIcon [field]="col.sortKey || col.field" *ngIf="col.sortable"></p-sortIcon>
          </th>
        </ng-container>
        <th pResizableColumn class="small-col" *appHasPermission="[permissionActions.UPDATE_USERS]">Active</th>
        <th pResizableColumn class="small-col" *appHasPermission="[permissionActions.UPDATE_USERS]">Actions</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-user let-columns="columns">
      <tr class="users-table-body">
        <td pFrozenColumn>{{ user.email }}</td>
        <ng-container *ngFor="let col of columns">
          <td *ngIf="!col.isATemplate" [ngClass]="col.class">
            {{ user[col.field] }}
          </td>

          <td *ngIf="col.field === 'dealers'" [ngClass]="col.class">
            <div *ngFor="let dealerRole of user.dealerRoles; let i = index" [ngClass]="dealerRole.archivedDealer ? 'text-reset' : 'fw-bold'">
              {{ dealerRole.dealer.name }}
              <span class="role-name">{{ dealerRole.roles?.length ? '(' + (dealerRole.roleString | titlecase) + ')' : '' }}</span>
              {{ i < user.dealerRoles.length - 1 ? ', ' : '' }}
            </div>
          </td>
          <td *ngIf="col.field === 'shopsName'" [ngClass]="col.class">
            <div *ngFor="let dealerShop of user.dealerShops; let i = index" [ngClass]="dealerShop.archivedDealer ? 'text-reset' : 'fw-bold'">
              {{ dealerShop.dealer.name }}
              <span class="role-name">{{ dealerShop.shops?.length ? '(' + (dealerShop.shopString | titlecase) + ')' : '' }}</span>
              {{ i < user.dealerShops.length - 1 ? ', ' : '' }}
            </div>
          </td>
          <td *ngIf="col.field === 'phoneNumber'">{{ user?.phoneNumber | phone }}</td>
          <td *ngIf="col.field === 'roleDto.name'">{{ user.roleDto.name }}</td>
        </ng-container>

        <td class="small-col" *appHasPermission="[permissionActions.UPDATE_USERS]">
          <ui-switch [(ngModel)]="user.status" [loading]="selectedUser?.id === user.id && isArchiveInProgress" (change)="onArchive(user, $event)">
            <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedUser?.id === user.id"></fa-icon>
          </ui-switch>
        </td>
        <td *appHasPermission="[permissionActions.UPDATE_USERS]">
          <div [ngClass]="isActiveTab && authService.isCurrentUserAdmin ? 'content-between' : 'content-center'">
            <img [src]="constants.staticImages.icons.key" alt="change-password" (click)="onChangePassword(user)" *ngIf="isActiveTab" class="image-margin" />
            <img [src]="constants.staticImages.icons.edit" (click)="onEdit(user)" alt="edit-icon" />
          </div>
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
  <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
</ng-template>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-user-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal" [roles]="roles" [userInfo]="selectedUser"></app-user-add>
</p-sidebar>

<p-sidebar
  class="dealer"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showChangePasswordModal"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  (onHide)="showChangePasswordModal = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
>
  <app-user-change-password (onClose)="onChangePasswordPopupClose()" *ngIf="showChangePasswordModal" [userInfo]="selectedUser"></app-user-change-password>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>

<p-confirmPopup appClickOutside (clickOutside)="onBackdropClick($event)"></p-confirmPopup>
