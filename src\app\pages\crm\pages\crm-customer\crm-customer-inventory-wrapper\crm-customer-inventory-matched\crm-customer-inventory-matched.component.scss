.container {
  display: inline-block;
}

.unit-image {
  width: 145px;
  height: 100%;
}

.p-card .p-card-content {
  padding: 1.25rem 0;
}

label {
  color: gray !important;
}

.rectangle {
  box-sizing: border-box;
  height: 20px;
  width: 49px;
  border: 1px solid var(--active-color);
  border-radius: 10px;
  top: 0px;
  position: relative;
  color: var(--active-color);
  font-family: Poppins;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 21px;
  text-align: center;
}

::ng-deep .customer-inventory-matched {
  .p-card-body {
    padding: 0px;
  }

  .p-card {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  }
}

.quotePriceDivWithImage {
  position: relative;
  left: -135px;
  margin-top: 10px;
}

.quotePriceDivWithoutImage {
  position: relative;
  margin-top: 10px;
}

.quotePrice {
  position: relative;
  font-weight: 500 !important;
  color: black !important;
  font-size: 16px;
}

.center {
  text-align: center;
}

.send-email-btn {
  margin-bottom: 20px;
}

.gray {
  color: var(--form-placeholder-color) !important;
  font-size: 15px;
}

.black {
  color: var(--text-color);
  font-weight: 500;
}

::ng-deep .customer-inventory-checkbox {
  .p-disabled {
    background-color: var(--active-color) !important;
  }
}

.notification-loader {
  display: flex;
  justify-content: center;
  align-content: center;
  align-items: center;
}

@media only screen and (max-width: 500px) {
  .send-quote {
    text-align: center;
    margin-top: 10px;
  }

  .content-wrapper {
    flex-direction: column;
  }
}
