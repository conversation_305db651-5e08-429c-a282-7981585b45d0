@import 'src/assets/scss/variables';

.form-control,
textarea {
  border-radius: 9px;
  background-color: var(--fields-color) !important;
  border-color: var(--table-border-color) !important;
  color: var(--text-color);

  &:focus {
    background-color: var(--fields-focus-color) !important;
    border-color: var(--active-color) !important;
    outline: 0;
    box-shadow: 0 0 0 0.05rem var(--active-color);
    color: var(--text-color);
  }
}

body.DARK {
  form {
    .form-control,
    textarea {
      &:focus {
        box-shadow: 0 0 0 0.05rem var(--text-color);
      }
    }
  }
}

textarea {
  padding: 0.375rem 0.75rem;
}

.form-control {
  height: 48px;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  box-shadow: 0 0 1px 1px var(--white-color) !important;
}

.form-group {
  margin-bottom: 25px;
}

.p-checkbox .p-checkbox-box.p-highlight,
.p-checkbox .p-checkbox-box.p-highlight:hover {
  border-color: var(--active-color) !important;
  background: var(--active-color) !important;
}

.p-radiobutton .p-radiobutton-box.p-highlight {
  border-color: var(--active-color) !important;
  background: var(--active-color) !important;
  &:hover {
    border-color: var(--active-color-lighter) !important;
    background: var(--active-color-lighter) !important;
  }
}

.p-inputtext:enabled:focus {
  box-shadow: none;
  border-color: var(--active-color);
}

.p-confirm-popup {
  box-shadow: 0 2px 7px 1px #979797;
  border-radius: 10px;

  .p-confirm-popup-message {
    max-width: 500px;
  }
}

.row {
  --bs-gutter-y: 1rem;
}

p-dropdown {
  display: block;
  width: 100%;
}

.p-dropdown {
  width: 100%;
  height: 48px;
  border-radius: 9px;
  background-color: var(--fields-color) !important;
  border: 1px solid var(--table-border-color);
  &:hover {
    border-color: var(--table-border-color) !important;
  }
  .p-dropdown-label.p-placeholder {
    color: var(--form-placeholder-color);
    font-size: 16px;
    letter-spacing: -0.28px;
    font-family: Poppins;
  }
}

.p-dropdown-filter {
  height: 36px;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item {
  font-size: 14px !important;
}

.p-dropdown-panel {
  box-shadow: var(--dropdown-box-shadow);
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
  color: var(--active-color);
  background: var(--app-background-color);
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
  color: var(--active-color);
  background: var(--app-background-color);
}

.p-dropdown-label {
  font-size: 16px;
  font-family: Poppins;
  letter-spacing: -0.28px;
  color: var(--text-color);
}

::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: var(--form-placeholder-color);
  opacity: 1;
  font-size: 16px;
  letter-spacing: -0.28px;
  font-family: Poppins;
  /* Firefox */
}

:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: var(--form-placeholder-color);
  font-size: 16px;
  letter-spacing: -0.28px;
  font-family: Poppins;
}

::-ms-input-placeholder {
  /* Microsoft Edge */
  color: var(--form-placeholder-color);
  font-size: 16px;
  letter-spacing: -0.28px;
  font-family: Poppins;
}

.form-control:disabled,
.form-control[readonly] {
  color: $disable-color;
  opacity: 0.4;
}

.p-calendar {
  height: 48px;
  width: 100%;

  input {
    border-radius: 9px;
    background-color: var(--fields-color) !important;
    border-color: var(--table-border-color) !important;
    color: var(--text-color);
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-collapse: collapse !important;

    &:focus {
      background-color: var(--fields-focus-color) !important;
      border-color: var(--active-color) !important;
      outline: 0;
    }
  }

  .p-button {
    background-color: var(--active-color) !important;
    border: 1px solid var(--table-border-color) !important;
    border-top-right-radius: 9px !important;
    border-bottom-right-radius: 9px !important;

    .p-button-icon {
      color: var(--white-color);
    }
  }
}

.drop-zone {
  height: 170px;
  width: 100%;
  padding: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  font-size: 20px;
  cursor: pointer;
  color: var(--text-color);
  border: 2px dashed #add2ff;
  border-radius: 10px;
  background-color: var(--fields-color) !important;
  position: relative;

  .title {
    color: var(--text-color);
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 27px;
    border: none !important;
    margin-bottom: 5px !important;
  }

  .pi {
    font-size: 30px;
    margin-bottom: 15px;
    color: var(--active-color);
  }

  .subtitle {
    color: var(--text-color);
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
    margin-bottom: 0 !important;
  }
}

.drop-zone--over {
  border-style: solid;
}

.drop-zone__input {
  height: 100%;
  width: 100%;
  opacity: 0;
  position: absolute;
}

.drop-zone__thumb {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
  background-color: white;
  background-size: cover;
  position: relative;
}

.drop-zone__thumb::after {
  content: attr(data-label);
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 5px 0;
  color: white;
  background: rgba(0, 0, 0, 0.75);
  font-size: 14px;
  text-align: center;
}

.position-relative {
  app-error-messages {
    left: 13px;
    bottom: -17px;
  }
}

*[class^='col-'] {
  position: relative;
}

.p-tooltip .p-tooltip-text {
  background-color: var(--primary-color);
  font-size: 14px;
  padding: 0.4rem;
}

.p-sidebar-content .content label {
  color: var(--text-color) !important;
  font-weight: 400 !important;
}

p-radiobutton,
.p-radiobutton {
  vertical-align: middle;
}

.p-multiselect-panel {
  box-shadow: var(--dropdown-box-shadow);
}

.fw-bold {
  font-weight: 500 !important;
}

.text-reset {
  opacity: 0.6;
}

.mx-w-300 {
  max-width: 300px;
}

.p-multiselect {
  background-color: var(--fields-color);
  border-color: var(--table-border-color) !important;
}

p-inputnumber,
.p-inputnumber {
  display: block !important;
}
