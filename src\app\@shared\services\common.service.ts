import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { map, Observable } from "rxjs";
import { Page } from "../models";

@Injectable({
  providedIn: 'root'
})
export class CommonService extends BaseCrudService {

  constructor(protected readonly httpClient: HttpClient) { super(httpClient); }

  getBaseAPIPath(): string {
    return '';
  }

  getList(url: string): Observable<[]> {
    return this.httpClient.get<[]>(url, {})
      .pipe(map((response) => {
        return response;
      }))
  }

  addWithoutBaseUrl<ReturnType>(resource: ReturnType, endpoint?: string): Observable<any> {
    return this.httpClient.post(`${endpoint}`, this.toServerModel(resource));
  }

  getListFromObject<ReturnType>(endpoint?: string): Observable<Page<ReturnType[]>> {
    return this.httpClient.get<Page<ReturnType>>(`${endpoint}`)
      .pipe(map((item) => {
        return item[0].masterData.columns.map((i: any) => this.fromServerModel(i))
      }));
  }
}
