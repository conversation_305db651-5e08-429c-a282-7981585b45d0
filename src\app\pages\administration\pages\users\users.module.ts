import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { NgxMaskModule } from 'ngx-mask';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ConfirmationService } from 'primeng/api';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from 'src/app/@shared/pipes/pipes.module';
import { UserAddComponent } from './user-add/user-add.component';
import { UserChangePasswordComponent } from './user-change-password/user-change-password.component';
import { UserListComponent } from './user-list/user-list.component';
import { UsersRoutingModule } from './users-routing.module';
import { UsersComponent } from './users.component';

@NgModule({
  declarations: [
    UsersComponent,
    UserListComponent,
    UserAddComponent,
    UserChangePasswordComponent,

  ],
  imports: [
    CommonModule,
    UsersRoutingModule,
    SharedComponentsModule,
    TabsModule.forRoot(),
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    CheckboxModule,
    NgxMaskModule.forRoot(),
    PipesModule,
    TabViewModule
  ],
  exports: [NgxMaskModule],
  providers: [ConfirmationService]
})
export class UsersModule { }
