import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { CrmTaskListComponent } from "./crm-task-list/crm-task-list.component";
import { CrmTaskComponent } from "./crm-task.component";

const routes: Routes = [
  {
    path: '',
    component: CrmTaskComponent,
    title: 'Skeye - Crm',
    children: [
      {
        path: '',
        component: CrmTaskListComponent,
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class CRMTaskRoutingModule { }
