import { GenericFilterParams, IdNameModel } from "src/app/@shared/models";

export interface TaskUserGroupListItem {
  id: number;
  shopName: string;
  assignees: Array<{ isTabOpen?: boolean } & IdNameModel>;
  isTabOpen?: boolean;
}

export class TaskListItem {
  id!: string;
  task!: IdNameModel;
  summary!: string;
  stockNumber!: number;
  unitId!: number;
  shop!: IdNameModel;
  assignee!: IdNameModel;
  reporter!: IdNameModel;
  taskPriority!: string;
  taskStatus!: IdNameModel;
  taskType!: IdNameModel;
  startDate!: string | null;
  endDate!: string | null;
  description!: string;
  taskAttachments: TaskAttachment[] = [];
  progress!: number;
  phaseId!: number;
  pipelineId!: number;
  customerLeadId!: string;
  crmContact!: IdNameModel;
  activity!: string;
  isActive?: boolean;
  createdDate!: string;
  archived!: boolean;
  constructor(json?: TaskListItem) {
    if (json) {
      Object.assign(this, json);
    }
  }
}

export class TaskListFilter extends GenericFilterParams {
  constructor(public shopId?: number, public assigneeId?: number, public archived = false) {
    super();
  }
}

export interface TaskCreateParams {
  taskTypeId: number;
  summary: string;
  stockNumber: number;
  unitId: number;
  shopId: number;
  assigneeId: number;
  reporterId: number;
  taskPriority: string;
  taskStatusId: number;
  startDate: string;
  endDate: string;
  description: string;
  progress: number;
  taskAttachments: TaskAttachment[];
  pipelineId: number;
  pipelineSequence: number;
  crmContactId: number;
  customerLeadId: string;
  activity: string;
}

export interface StockBasicInfo {
  id: number;
  stockNumber: number;
  unitId: number;
}

export interface TaskAttachment {
  fullTaskAttachmentUrl?: string;
  id?: number;
  taskId?: number;
  url: string;
}

export const ActivityList = [
  { value: 'Call', name: 'Call' },
  { value: 'Email', name: 'Email' },
  { value: 'Send a Quote', name: 'Send a Quote' },
  { value: 'Send a Invoice', name: 'Send a Invoice' },
  { value: 'Follow Up', name: 'Follow Up' },
  { value: 'Meeting', name: 'Meeting' },
]
