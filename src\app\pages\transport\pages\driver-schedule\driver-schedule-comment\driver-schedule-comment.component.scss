.add-comment {
  button#addShopBtn {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    scroll-margin-bottom: 100px;
    background-color: white;
    border-color: var(--active-color);
    color: var(--active-color);
    height: 41px;

    &:hover {
      color: var(--active-color-darker);
      border-color: var(--active-color-darker);
    }

    img {
      margin-right: 10px;
    }
  }

  .actions {
    display: flex;
    justify-content: flex-end;
  }

  .input-group {
    position: relative;
    margin-bottom: 10px;

    app-error-messages {
      left: 0;
      bottom: -25px;
    }
  }
}

ul {
  padding-left: 20px;

  .comment {
    margin-bottom: 10px;
    margin-left: 1rem;

    span {
      color: var(--text-color);
      font-size: 14px;
      letter-spacing: 0;
      line-height: 21px;
    }

    .user {
      font-weight: 600;
    }

    .date {
      font-weight: 400;
      font-size: 13px;
    }

    .body {
      font-weight: 400;
      margin-bottom: 0;
      font-size: 14px;
    }
  }
}

.action-icons {
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  *:hover {
    cursor: pointer;
  }
}

.comment-wrapper {
  display: flex;
  justify-content: space-between;
}

::ng-deep .p-editor-content .ql-container .ql-snow {
  height: 100px !important;
}

::ng-deep .p-editor-container .p-editor-content .ql-editor {
  height: 100px !important;
}

.m-t-15 {
  margin-top: 15px;
}

@media only screen and (max-width: 500px) {
  .date {
    display: block;
  }
}

.overflow-text {
  overflow-wrap: anywhere;
  margin-right: 1rem;
}

.wrap-comment {
  width: fit-content;
}
