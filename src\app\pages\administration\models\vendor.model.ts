import { GenericFilterParams, IdNameModel } from "src/app/@shared/models";

export class VendorList<PERSON>ilter extends GenericFilterParams {
  archived = false;
}

export interface Address {
  city: string;
  state: string;
  streetAddress: string;
  zipcode: string;
  latitude: number;
  longitude: number;
}

export interface ContactPerson {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: number;
}

export class VendorCreateParam {
  additionalDetail!: string;
  address!: Address;
  contactPerson!: ContactPerson;
  email!: string;
  name!: string;
  organizationId!: number;
  phoneNumber!: number;
}

export class VendorListItem extends VendorCreateParam {
  id!: number;
  contactPersonName?: string;
  addressCity?: string;
  addressState?: string;
  isActive?: boolean;
  archived?: boolean;
  createdBy?: IdNameModel;
  createdDate?: string;
}

export class VendorExpensesCreateParam {
  treeOperator!: string;
  values: Values[] = [];
  vendorId!: number;
}

export class Values {
  key!: string;
  value!: string;
  dataType!: string;
  operator!: string;
}

export enum PassedParamGroup {
  noop = 'NOOP',
  and = 'AND',
  transDate = 'transDate',
  date = 'DATE',
  greaterThanEqual = 'GREATER_THAN_OR_EQUAL',
  lessThanEqual = 'LESS_THAN_OR_EQUAL'
}
