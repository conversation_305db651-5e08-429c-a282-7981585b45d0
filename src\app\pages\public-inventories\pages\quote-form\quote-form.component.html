<form #quoteForm="ngForm" *ngIf="!isLoading; else loaderTemplate" name="quoteForm" (ngSubmit)="onSubmit(quoteForm)" novalidate>
  <div class="row">
    <div [ngClass]="{ 'col-md-6': !(field.dataType === 'TextBox'), 'mt-2': true }" *ngFor="let field of quoteFields">
      <label [ngClass]="{ required: field.isRequired }" for="field.for">{{ field.label }}</label>
      <ng-container *ngIf="field.dataType === 'DropDown'">
        <p-dropdown
          appendTo="body"
          [name]="field.for"
          [options]="field.options"
          [(ngModel)]="field.value"
          [showClear]="true"
          [filter]="true"
          filterBy="name"
          [placeholder]="field.placeholder ? field.placeholder : ''"
          [required]="field.isRequired"
          #dropDownField="ngModel"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: false, data: field.for }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item }}</span>
          </ng-template>
        </p-dropdown>
        <small class="text-danger" *ngIf="dropDownField.invalid && (quoteForm.submitted || dropDownField.touched)"> This field is required </small>
      </ng-container>
      <ng-container *ngIf="field.dataType === 'TextField' && !(field.for === 'Address' || field.for === 'City' || field.for === 'State' || field.for === 'Zip Code')">
        <input
          class="form-control"
          type="text"
          [name]="field.for"
          [placeholder]="field.placeholder ? field.placeholder : ''"
          [required]="field.isRequired"
          [(ngModel)]="field.value"
          #textField="ngModel"
        />
        <small class="text-danger" *ngIf="textField.invalid && (quoteForm.submitted || textField.touched)"> This field is required </small>
      </ng-container>
      <ng-container *ngIf="field.dataType === 'TextField' && (field.for === 'Address' || field.for === 'City' || field.for === 'State' || field.for === 'Zip Code')">
        <input
          class="form-control"
          type="text"
          [name]="field.for"
          ngx-google-places-autocomplete
          ngx-gp-autocomplete
          [options]="addressOptions"
          (onAddressChange)="handleAddressChange($event, quoteForm)"
          [placeholder]="field.placeholder ? field.placeholder : ''"
          [required]="field.isRequired"
          [(ngModel)]="field.value"
          #AddressTextField="ngModel"
        />
        <small class="text-danger" *ngIf="AddressTextField.invalid && (quoteForm.submitted || AddressTextField.touched)"> This field is required </small>
      </ng-container>
      <ng-container *ngIf="field.dataType === 'Number'">
        <div>
          <p-inputNumber
            styleClass="w-100"
            inputStyleClass="form-control"
            [name]="field.for"
            [placeholder]="field.placeholder ? field.placeholder : ''"
            [(ngModel)]="field.value"
            [required]="field.isRequired"
            #numberField="ngModel"
          ></p-inputNumber>
        </div>
        <small class="text-danger" *ngIf="numberField.invalid && (quoteForm.submitted || numberField.touched)"> This field is required </small>
      </ng-container>
      <ng-container *ngIf="field.dataType === 'Email'">
        <input
          class="form-control"
          type="email"
          email
          [name]="field.for"
          [placeholder]="field.placeholder ? field.placeholder : ''"
          [required]="field.isRequired"
          [(ngModel)]="field.value"
          #emailField="ngModel"
        />
        <small class="text-danger" *ngIf="emailField.errors?.required && (quoteForm.submitted || emailField.touched)">This field is required</small>
        <small class="text-danger" *ngIf="emailField.errors?.email && (quoteForm.submitted || emailField.touched)">Invalid email address</small>
      </ng-container>
      <ng-container *ngIf="field.dataType === 'PhoneNumber'">
        <p-inputMask
          styleClass="w-100 form-control"
          [name]="field.for"
          [placeholder]="field.placeholder ? field.placeholder : ''"
          [required]="field.isRequired"
          [(ngModel)]="field.value"
          [mask]="constants.phoneNumberMaskForTemplateForm"
          #phoneNumberField="ngModel"
        ></p-inputMask>
        <small class="text-danger" *ngIf="!phoneNumberField.valid && (quoteForm.submitted || phoneNumberField.touched)"> This field is required </small>
      </ng-container>
      <ng-container *ngIf="field.dataType === 'TextBox'">
        <div>
          <textarea
            class="w-100"
            [placeholder]="field.placeholder ? field.placeholder : ''"
            [(ngModel)]="field.value"
            rows="3"
            [name]="field.for"
            [required]="field.isRequired"
            #textAreaField="ngModel"
          ></textarea>
        </div>
        <small class="text-danger" *ngIf="!textAreaField.valid && (quoteForm.submitted || textAreaField.touched)"> This field is required </small>
      </ng-container>
    </div>
  </div>
  <div class="d-flex justify-content-center mt-3">
    <button class="btn public-page-action-btn">Submit</button>
  </div>
</form>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
<ng-template #loaderTemplate>
  <div class="d-flex justify-content-center align-items-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
  </div>
</ng-template>
