import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { User } from '@pages/user/models';
import { takeUntil } from 'rxjs';
import { DataTypeOptions, Field, QuoteFormData } from '../../../models/quoteForm.model';
import { QuoteFormService } from '../quote-form.service';

@Component({
  selector: 'app-quote-form-field-add-edit',
  templateUrl: './quote-form-field-add-edit.component.html',
  styleUrls: ['./quote-form-field-add-edit.component.scss']
})
export class QuoteFormFieldAddEditComponent extends BaseComponent implements OnInit {

  @Input() isEditMode!: boolean;
  @Input() isUpdateEmail!: boolean;
  @Input() selectedField!: Field | null;
  @Input() users!: User[]
  @Input() quoteFormData!: QuoteFormData
  @Output() closeModal = new EventEmitter<boolean>();
  quoteFieldFrom!: FormGroup;
  dataTypeOptions = DataTypeOptions;
  quoteFormId!: number;
  fields: Field[] = [];
  userIds: number[] = [];
  isLoading = false;
  selectedUsers: number[] = [];

  constructor(private readonly quoteFormService: QuoteFormService, private readonly toasterService: AppToasterService, private readonly userAnnotationService: UserAnnotationService,) {
    super();
  }

  ngOnInit(): void {
    this.pageTitle = this.isUpdateEmail ? 'Update Notify Email' : this.isEditMode ? 'Edit Quote Form Field' : 'Add Quote Form Field';
    this.quoteFormId = this.quoteFormData.id;
    this.fields = this.quoteFormData.formData.fields;
    this.userIds = this.selectedUsers = this.quoteFormData.userIds;
    this.updateDisabledStatusForUsers();
    if (!this.isUpdateEmail) {
      this.initializeForm();
    }
  }

  initializeForm(): void {
    this.quoteFieldFrom = new FormGroup({
      label: new FormControl(this.selectedField?.label ?? '', Validators.required),
      dataType: new FormControl(this.selectedField?.dataType ?? null, Validators.required),
      placeholder: new FormControl(this.selectedField?.placeholder ?? null),
      options: new FormControl(this.selectedField?.options ?? []),
      isRequired: new FormControl(this.selectedField?.isRequired ?? false),
      for: new FormControl(this.selectedField?.for ?? null),
      value: new FormControl(this.selectedField?.value ?? null),
      isDefault: new FormControl(this.selectedField?.isDefault ?? false),
    });
  }

  updateDisabledStatusForUsers(): void {
    this.users = this.users.map(user => ({
      ...user,
      disabled: this.selectedUsers.length >= 3 && !this.selectedUsers.includes(user.id)
    }));
  }

  getQuoteFormParams(): QuoteFormData {
    const optionsValue = this.quoteFieldFrom.controls.options.value;
    let optionsArray = [];
    const isArray = Array.isArray(optionsValue);
    if (!isArray) {
      optionsArray = optionsValue?.length ? optionsValue.split(',').map((option: string) => option.trim()).filter((option: string) => option !== '') : [];
    } else {
      optionsArray = optionsValue;
    }
    this.quoteFieldFrom.controls.options.setValue(optionsArray);
    this.quoteFieldFrom.controls.for.setValue(this.quoteFieldFrom.controls.label.value);
    let fields: Field[] = [];

    if (this.isEditMode) {
      fields = this.fields.map((field) => {
        if (field.label === this.selectedField?.label) {
          return this.quoteFieldFrom.value;
        }
        return field;
      });
    } else {
      fields = this.fields;
      fields.push(this.quoteFieldFrom.value);
    }

    return {
      id: this.quoteFormId,
      formData: {
        fields: fields
      },
      userIds: this.userIds
    }
  }

  getParamsWithUpdateEmail(): QuoteFormData {
    return {
      id: this.quoteFormId,
      formData: {
        fields: this.fields
      },
      userIds: this.selectedUsers
    }
  }

  onLabelChange(): void {
    if (this.quoteFieldFrom.controls.label.value) {
      if (this.quoteFieldFrom.get('dataType')?.value) {
        this.quoteFieldFrom.controls.placeholder.setValue(
          `${this.quoteFieldFrom.get('dataType')?.value === this.dataTypeOptions[0] ? 'Select ' : 'Enter '}`.concat(this.quoteFieldFrom.controls.label.value)
        )
      }
    } else {
      this.quoteFieldFrom.controls.placeholder.setValue(null)
    }
  }

  onDataTypeChange(dropDownOption: { value: string }): void {
    const label = this.quoteFieldFrom.controls.label.value;
    if (dropDownOption.value === this.dataTypeOptions[0]) {
      this.quoteFieldFrom.controls.options.setValidators([Validators.required]);
      this.quoteFieldFrom.controls.options.updateValueAndValidity();
      if (label) {
        this.quoteFieldFrom.controls.placeholder.setValue("Select ".concat(label))
      }
      return;
    }
    this.quoteFieldFrom.get('options')?.setValue(null);
    this.quoteFieldFrom.controls.options.setValidators(null);
    this.quoteFieldFrom.controls.options.updateValueAndValidity();
    if (label) {
      this.quoteFieldFrom.controls.placeholder.setValue("Enter ".concat(label))
    }
  }

  onModalClose(dataHasBeenModified = false): void {
    this.closeModal.emit(dataHasBeenModified);
    if (!this.isUpdateEmail) {
      this.quoteFieldFrom.controls.options.setValidators(null);
      this.quoteFieldFrom.controls.options.updateValueAndValidity();
      this.quoteFieldFrom.reset();
    }
  }

  saveQuoteForm(): void {
    this.isLoading = true;
    this.quoteFormService.update(this.isUpdateEmail ? this.getParamsWithUpdateEmail() : this.getQuoteFormParams()).pipe(takeUntil(this.destroy$)).subscribe((res: QuoteFormData) => {
      if (this.isUpdateEmail) {
        this.toasterService.success(MESSAGES.updateNotifyPersonsSuccess);
      } else {
        this.toasterService.success(this.isEditMode ? MESSAGES.updateQuoteFieldSuccess.replace('{record}', this.quoteFieldFrom.controls.label.value) : MESSAGES.addQuoteFieldSuccess.replace('{record}', this.quoteFieldFrom.controls.label.value));
      }
      this.isLoading = false;
      this.onModalClose(true);
    })
  }

  onSubmit(): void {
    if (!this.isUpdateEmail && this.quoteFieldFrom.invalid) {
      this.quoteFieldFrom.markAllAsTouched();
      return;
    }
    if (!this.isUpdateEmail && this.isEditMode) {
      const label = this.quoteFieldFrom.controls.label.value;
      if (this.fields.some((field) => field.label.toLowerCase() === label.toLowerCase() && field.for !== this.selectedField?.for)) {
        this.toasterService.error(MESSAGES.addQuoteFieldErrorMessage.replace('{record}', label));
        return;
      }
    }
    if (!this.isUpdateEmail && !this.isEditMode && this.fields.some((field) => field.label.toLowerCase() === this.quoteFieldFrom.controls.label.value.toLowerCase())) {
      this.toasterService.error(MESSAGES.addQuoteFieldErrorMessage.replace('{record}', this.quoteFieldFrom.controls.label.value));
      return;
    }
    if (this.isUpdateEmail && this.selectedUsers.length === 0) {
      return;
    }
    this.saveQuoteForm();
  }
}
