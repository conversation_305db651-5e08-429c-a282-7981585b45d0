import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { AddNewMakeModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-make/add-new-make.module';
import { AddNewModelModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-model/add-new-model.module';
import { GoogleMapModule } from '@sharedComponents/google-map/google-map.component.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { NgxMaskModule } from 'ngx-mask';
import { AccordionModule } from 'primeng/accordion';
import { CardModule } from 'primeng/card';
import { DropdownModule } from 'primeng/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmContactCustomerAddComponent } from './crm-contact-customer-add.component';

@NgModule({
  declarations: [CrmContactCustomerAddComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeIconsModule,
    AccordionModule,
    DropdownModule,
    CardModule,
    SidebarModule,
    AddNewMakeModule,
    AddNewModelModule,
    GoogleMapModule,
    NgxGpAutocompleteModule,
    NgxMaskModule.forRoot(),
  ],
  providers: [{
    provide: Loader,
    useValue: new Loader({
      apiKey: environment.googleMapApiKey,
      libraries: ['places']
    })
  }],
  exports: [CrmContactCustomerAddComponent]
})

export class CrmContactCustomerAddModule { }
