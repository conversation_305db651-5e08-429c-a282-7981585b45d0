@import "src/assets/scss/variables";

.space-between {
  justify-content: space-between;
}

.chart-content {
  justify-content: center;
}

.m-t-30 {
  margin-top: 30px;
}

.title-header {
  color: var(--text-color);
  background: var(--app-background-color);
  text-align: center;
  margin-bottom: 11px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-b-10 {
  margin-bottom: 10px;
}

.age-group {
  font-size: 14px;
  font-weight: 300;
  margin-top: 5px;
  width: max-content;
  color: var(--text-color);
}

.unit-group {
  font-size: 14px;
  font-weight: 600;
  margin-top: 5px;
  color: var(--text-color);
}

.center {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  text-align: center;
  margin-top: 4px;
}

.m-l-10 {
  margin-left: 10px;
}

.yellow-circle {
  background: $yellow-circle-color;
}

.green-circle {
  background: $green-circle-color;
}

.orange-circle {
  background: $orange-circle-color;
}

.skyblue-circle {
  background: $skyblue-circle-color;
}

.purple-circle {
  background: $purple-circle-color;
}

.blue-circle {
  background: $blue-circle-color;
}

.red-circle {
  background: $red-circle-color;
}

.m-r-20 {
  margin-right: 20px;
}

.w-70 {
  width: 70%;
}

.p-r-25 {
  padding-right: 25px;
}

.text-label {
  width: 100%;
  height: 40px;
  position: absolute;
  top: 55%;
  left: 0;
  line-height: 19px;
  text-align: center;
  color: var(--text-color);
}

@media only screen and (max-width: 900px) {
  .text-label {
    font-size: 13px;
    top: 58%;
  }

  .text-label-full {
    width: inherit !important;
    top: 38% !important;
    left: 0% !important;
  }
}

@media only screen and (min-width: 600px) {
  .chart-content {
    display: flex;
  }
}

.text-label-full {
  color: var(--text-color);
  width: 65%;
  height: 40px;
  position: absolute;
  top: 53%;
  left: -7%;
  line-height: 19px;
  text-align: center;
  font-size: larger;
}

.content {
  max-height: unset;
}

@media only screen and (min-width: 1000px) and (max-width: 1350px) {
  .text-label {
    top: 65%;
    font-size: x-small;
  }
  .text-label-full {
    top: 56%;
  }
}

@media only screen and (min-width: 900px) and (max-width: 1120px) {
  .text-label {
    top: 55%;
    font-size: xx-small;
  }
}

@media only screen and (max-width: 320px) {
  .text-label {
    top: 65%;
    font-size: xx-small;
  }
  .text-label-full {
    font-size: xx-small;
  }
}
