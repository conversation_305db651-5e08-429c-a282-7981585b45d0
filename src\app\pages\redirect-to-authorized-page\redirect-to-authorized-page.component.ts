import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseComponent } from '@core/utils';
import { MenuConfig } from '@core/utils/menu-config';
import { Role } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { PermissionActions, PermissionModules } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { MenuConfigService } from 'src/app/@shared/services/menu-config.service';

@Component({
  selector: 'app-redirect-to-authorized-page',
  templateUrl: './redirect-to-authorized-page.component.html',
  styleUrls: ['./redirect-to-authorized-page.component.scss']
})
export class RedirectToAuthorizedPageComponent extends BaseComponent implements OnInit {

  userPermissions!: Role;

  constructor(
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly menuConfigService: MenuConfigService,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super();
  }

  ngOnInit(): void {
    this.userPermissions = this.authService.getRoleInfo();
    this.redirectToAuthPage();
  }

  redirectToAuthPage() {
    this.authService.getCurrentUser().subscribe((res) => {
      this.menuConfigService.modifyMenuConfig(MenuConfig);
      if (res?.role?.name === 'ROLE_DRIVER') {
        this.router.navigate([this.path.transport.root, this.path.transport.incomingTruckBoard.root])
      } else {
        this.navigateToAuthPage();
      }
    })
  }

  navigateToAuthPage(): void {
    const permissions = this.authService.getRoleInfo()?.privilegeActionResponseDTOs;
    const permissionActions = PermissionActions;
    const permissionModule = PermissionModules;
    const redirectUrl = localStorage.getItem('redirectUrl');

    if (redirectUrl) {
      window.location.href = redirectUrl;
      localStorage.removeItem('redirectUrl');
    } else {
      if (Utils.hasModulePermission(permissions, permissionModule.DASHBOARD)) {
        this.router.navigate([this.path.base.dashboard]);
        return;
      }
    }

    const permissionRoutes = {
      [permissionActions.VIEW_CUSTOMER_LEAD]: [this.path.crm.root, this.path.crm.crmCustomer.root],
      [permissionActions.VIEW_CONTACTS]: [this.path.crm.root, this.path.crm.crmContact.root],
      [permissionActions.VIEW_INCOMING_TRUCK]: [this.path.transport.root, this.path.transport.incomingTruckBoard.root],
      [permissionActions.VIEW_DRIVER_SCHEDULE]: [this.path.transport.root, this.path.transport.driverScheduleBoard.root],
      [permissionActions.VIEW_INVENTORY]: [this.path.inventory.root],
      [permissionActions.VIEW_SOLD_PIPELINE]: [this.path.pipeline.root, this.path.pipeline.soldTruckBoard.root],
      [permissionActions.VIEW_STOCK_PIPELINE]: [this.path.pipeline.root, this.path.pipeline.stockTruckBoard.root],
      [permissionActions.VIEW_TASK]: [this.path.shops.root],
      [permissionActions.VIEW_USERS]: [this.path.administration.root, this.path.administration.users.root],
      [permissionActions.VIEW_DEALERS]: [this.path.administration.root, this.path.administration.dealers.root],
      [permissionActions.VIEW_VENDORS]: [this.path.administration.root, this.path.administration.vendors.root],
      [permissionActions.VIEW_PIPELINE_CONFIG]: [this.path.administration.root, this.path.administration.pipelineConfig.root],
      [permissionActions.VIEW_SHOPS]: [this.path.administration.root, this.path.shops.root],
      [permissionActions.VIEW_CATEGORY]: [this.path.administration.root, this.path.administration.specificationConfig.root, this.path.administration.specificationConfig.category.root],
      [permissionActions.VIEW_UNIT_TYPE]: [this.path.administration.root, this.path.administration.specificationConfig.root, this.path.administration.specificationConfig.unitType.root],
      [permissionActions.VIEW_MAKE_MODEL]: [this.path.administration.root, this.path.administration.specificationConfig.root, this.path.administration.specificationConfig.makeModel.root],
      [permissionActions.VIEW_SPECIFICATION_MASTER]: [this.path.administration.root, this.path.administration.specificationConfig.root, this.path.administration.specificationConfig.specification.root],
      [permissionActions.VIEW_VENDORS_REPORT]: [this.path.reporting.root, this.path.reporting.vendorsReport],
      [permissionActions.VIEW_SUPPLIERS_REPORT]: [this.path.reporting.root, this.path.reporting.supplierReport],
      [permissionActions.VIEW_DAILY_SALES_REPORT]: [this.path.reporting.root, this.path.reporting.crm.root, this.path.reporting.crm.dailySalesReport.root],
      [permissionActions.VIEW_ACTIVITY_REPORT]: [this.path.reporting.root, this.path.reporting.crm.root, this.path.reporting.crm.activity.root],
      [permissionActions.VIEW_INVENTORY_SALES_REPORT]: [this.path.reporting.root, this.path.reporting.crm.root, this.path.reporting.crm.inventorySales.root],
      [permissionActions.VIEW_PROFITABILITY_REPORT]: [this.path.reporting.root, this.path.reporting.inventoryReport.root, this.path.reporting.inventoryReport.profitability.root],
      [permissionActions.VIEW_INVENTORY_AGING]: [this.path.reporting.root, this.path.reporting.inventoryReport.root, this.path.reporting.inventoryReport.inventoryAging.root],
    };

    for (const permission in permissionRoutes) {
      if (Utils.hasSubModulePermission(permissions, [permission])) {
        this.router.navigate((permissionRoutes as any)[permission]);
        return;
      }
    }

    this.router.navigate([this.path.settings.root, this.path.settings.account]);
  }


}
