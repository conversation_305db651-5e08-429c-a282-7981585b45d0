import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { InventoryMatchingParams, QuotationResponse, QuotationUnitDTOS } from '@pages/crm/models/customer-inventory.model';
import { QuotationCustomerListItem, Specifications } from '@pages/crm/models/customer-lead-quotation.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { AssociationsUnits, InventoryListingSpecificationData, InventoryUnitSpecificationModel, SpecificationField } from '@pages/inventory/models';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';

@Component({
  selector: 'app-crm-quotation-resend',
  templateUrl: './crm-quotation-resend.component.html',
  styleUrls: ['./crm-quotation-resend.component.scss'],
})
export class CrmQuotationResendComponent extends BaseComponent implements OnInit {

  @Input() quotationId!: string;
  @Input() crmId!: string;
  @Input() isViewMode!: boolean;

  quotationDetails!: QuotationResponse | QuotationCustomerListItem | any;
  inventoryQuotationFormGroup!: FormGroup;
  totalQuotePrice = 0;
  totalRetailPrice = 0;
  totalInvestmentPrice = 0;
  totalSelectedInventories = 0;
  isFormLoading = false;
  customerEmail!: string;
  quotationUnitDTOS: QuotationUnitDTOS[] = [];

  @Output() closeModal = new EventEmitter<void>();
  @Output() saveQuotation = new EventEmitter<void>();

  constructor(
    private readonly fb: FormBuilder,
    private readonly crmService: CrmService,
    private readonly inventoryService: InventoryService,
    private readonly toastService: AppToasterService,
    private readonly confirmationService: ConfirmationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getQuotationDetailsById();
    this.initializeQuotationFormGroup();
  }

  initializeQuotationFormGroup(): void {
    this.inventoryQuotationFormGroup = this.fb.group({
      content: this.fb.array([])
    });
  }

  get quotationDetailsFormArray(): FormArray {
    return this.inventoryQuotationFormGroup?.get('content') as FormArray;
  }

  getQuotationDetailsById(): void {
    this.isLoading = true;
    const endpoint = `${API_URL_UTIL.admin.crm.quotations}/${API_URL_UTIL.admin.crm.list}`;
    this.crmService.get(this.quotationId, endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (quotation) => {
        this.quotationDetails = quotation;
        if (this.quotationDetails) {
          if (!this.isViewMode) {
            this.addPrimaryInventory()
            this.addAssociatedinventories();
            this.getToggleSelect(0);
          } else {
            this.totalInvestmentPrice = this.quotationDetails.totalInvestmentCost
            this.totalQuotePrice = this.quotationDetails.totalQuotePrice
            this.totalRetailPrice = this.quotationDetails.totalRetailPrice
            this.totalSelectedInventories = this.quotationDetails.quotationUnitResponseDTOList?.length
            this.addViewAssociatedinventories();
          }
        }
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  addPrimaryInventory(): void {
    if (this.quotationDetails?.generalInformation?.id) {
      this.quotationDetailsFormArray.push(this.fb.group({
        fullUrl: this.quotationDetails?.unitImages?.fullUrl,
        unitId: this.quotationDetails?.generalInformation?.id,
        yearMakeModel: `${this.quotationDetails?.generalInformation?.year} ${this.quotationDetails?.generalInformation?.make?.name} ${this.quotationDetails?.generalInformation?.unitModel?.name}`,
        stockNumber: this.quotationDetails?.generalInformation?.stockNumber,
        retailAskingPrice: this.quotationDetails?.retailAskingPrice,
        vin: this.quotationDetails?.generalInformation?.vin,
        totalProjectedInvestment: this.quotationDetails?.totalProjectedInvestment,
        isSelected: true,
        isExisting: false,
        quotePrice: this.quotationDetails?.retailAskingPrice
      }));
    }
  }

  addAssociatedinventories(): void {
    if (this.quotationDetails?.unitQuotationAssociationDTOS?.length) {
      for (const unitQuotationAssociation of this.quotationDetails?.unitQuotationAssociationDTOS) {
        this.quotationDetailsFormArray.push(this.fb.group({
          fullUrl: unitQuotationAssociation.unitImages?.fullUrl,
          unitId: unitQuotationAssociation.id,
          yearMakeModel: unitQuotationAssociation.yearMakeModel,
          stockNumber: unitQuotationAssociation.stockNumber,
          retailAskingPrice: unitQuotationAssociation.retailCost,
          vin: unitQuotationAssociation.vin,
          totalProjectedInvestment: unitQuotationAssociation.investmentCost,
          isSelected: false,
          isExisting: false,
          quotePrice: unitQuotationAssociation.retailCost,
        }));
      }
    }
  }

  addViewAssociatedinventories(): void {
    if (this.quotationDetails?.quotationUnitResponseDTOList?.length) {
      for (const unitQuotationAssociation of this.quotationDetails?.quotationUnitResponseDTOList) {
        this.quotationDetailsFormArray.push(this.fb.group({
          fullUrl: unitQuotationAssociation.unitImages?.fullUrl,
          unitId: unitQuotationAssociation.id,
          yearMakeModel: unitQuotationAssociation.yearMakeModel,
          stockNumber: unitQuotationAssociation.stockNumber,
          retailAskingPrice: unitQuotationAssociation.retailCost,
          vin: unitQuotationAssociation.vin,
          totalProjectedInvestment: unitQuotationAssociation.investmentCost,
          isSelected: false,
          isExisting: false,
          quotePrice: unitQuotationAssociation.quotedPrice,
        }));
      }
    }
  }

  onModalClose(): void {
    this.closeModal.emit();
    this.quotationDetailsFormArray?.clear();
    this.totalQuotePrice = 0;
    this.totalRetailPrice = 0;
    this.totalInvestmentPrice = 0;
    this.totalSelectedInventories = 0;
  }

  getSelectedInventoryCount(): void {
    this.totalSelectedInventories = this.quotationDetailsFormArray?.value?.filter((item: AssociationsUnits) => item.isSelected)?.length;
  }

  getTotalRetailPrice(): void {
    this.totalRetailPrice = 0
    for (const quotation of this.quotationDetailsFormArray.value) {
      if (quotation.isSelected) {
        this.totalRetailPrice += quotation.retailAskingPrice;
      }
    }
  }

  getTotalInventmentCost(): void {
    this.totalInvestmentPrice = 0;
    for (const quotation of this.quotationDetailsFormArray.value) {
      if (quotation.isSelected) {
        this.totalInvestmentPrice += quotation.totalProjectedInvestment;
      }
    }
  }

  getToggleSelect(quotationIndex: number): void {
    this.setValidatorsForQuotePrice(quotationIndex);
    this.getTotalInventmentCost();
    this.getTotalRetailPrice();
    this.getTotalQuoteCost();
    this.getSelectedInventoryCount();
  }

  setValidatorsForQuotePrice(quotationIndex: number): void {
    this.quotationDetailsFormArray.controls[quotationIndex]?.get('quotePrice')?.setValidators(
      this.quotationDetailsFormArray.controls[quotationIndex].value.isSelected && this.crmId &&
        this.quotationDetailsFormArray.controls[quotationIndex].value.retailAskingPrice ? [Validators.required] : []
    );
    this.quotationDetailsFormArray.controls[quotationIndex]?.get('quotePrice')?.markAsUntouched();
    this.quotationDetailsFormArray.controls[quotationIndex]?.get('quotePrice')?.updateValueAndValidity();
  }

  getTotalQuoteCost(): void {
    this.totalQuotePrice = 0;
    for (const quotation of this.quotationDetailsFormArray.value) {
      if (quotation.isSelected) {
        this.totalQuotePrice += quotation.quotePrice;
      }
    }
  }

  onSubmit(event: MouseEvent): void {
    const noItemSelected = this.quotationDetailsFormArray.value.every((item: AssociationsUnits) => !item.isSelected);
    if (noItemSelected) {
      this.toastService.error(MESSAGES.selectUnitToCreateQuotation);
      return;
    } else if (this.inventoryQuotationFormGroup.invalid) {
      this.inventoryQuotationFormGroup.markAllAsTouched();
      return;
    } else if (!this.crmId && !this.customerEmail) {
      this.toastService.error(MESSAGES.addCustomerEmailForShareDetails);
      return;
    } else if (!this.totalQuotePrice && this.crmId) {
      this.toastService.error(MESSAGES.addQuotationForOneStock);
      return;
    } else if (this.allInventoriesSelected) {
      this.sendQuotation();
      return;
    } else if (!this.allInventoriesSelected && !this.crmId) {
      this.sendQuotation();
      return;
    }
    if (this.crmId) {
      this.confirmationService.confirm({
        target: event?.target as EventTarget,
        message: MESSAGES.sendQuotationForSelectedItems,
        icon: icons.triangle,
        header: "Confirmation",
        accept: () => {
          this.sendQuotation();
        }
      });
    }
  }

  get allInventoriesSelected(): boolean {
    return this.quotationDetailsFormArray.value.every((inventory: AssociationsUnits) => {
      return inventory?.isSelected;
    });
  }

  sendQuotation(): void {
    this.inventoryQuotationFormGroup.markAsUntouched();
    this.isFormLoading = true
    this.getAllInventorySpecificationsData();
  }

  handleSendQuotation(): void {
    this.crmService.add(this.createQuotationParams(), API_URL_UTIL.admin.crm.quotations)
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          this.onModalClose();
          this.isFormLoading = false;
          this.saveQuotation.emit();
        }
      });
  }

  createQuotationParams(): Array<InventoryMatchingParams> {
    const params: InventoryMatchingParams = {
      totalRetailPrice: this.totalRetailPrice,
      totalInvestmentCost: this.totalInvestmentPrice,
      totalQuotePrice: this.totalQuotePrice,
      customerLeadId: this.crmId,
      sendEmail: true,
      quotationUnitDTOS: this.quotationUnitDTOS,
    };

    if (this.customerEmail) {
      params.email = this.customerEmail;
    }
    return [params];
  }

  getAllInventorySpecificationsData(): void {
    const unitIds: number[] = [];
    for (const quote of this.quotationDetailsFormArray.value) {
      if (quote.isSelected) {
        unitIds.push(quote.unitId);
      }
    }
    const endpoint = `${API_URL_UTIL.inventory.unitSpecifications}/${API_URL_UTIL.inventory.root}`;
    this.inventoryService.add(unitIds, endpoint)
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          this.transformData(res);
          this.handleSendQuotation()
        }
      });
  }

  private transformData(inventorySpecification: InventoryUnitSpecificationModel[]): void {
    const quotationUnitDTOS: QuotationUnitDTOS[] = [];
    for (const quote of this.quotationDetailsFormArray.value) {
      if (quote.isSelected) {
        quotationUnitDTOS.push({ unitId: quote.unitId, quotePrice: quote.quotePrice, includedInQuotation: true, specifications: [] });
      }
    }
    inventorySpecification.forEach((data: InventoryUnitSpecificationModel) => {
      data.specificationData.specification.forEach((specification: InventoryListingSpecificationData) => {
        const transformedSpecification: Specifications = {
          groupName: specification.sectionName,
          fields: [],
        };
        if (specification.fields.some(field => field.value)) {
          specification.fields.forEach((field: SpecificationField) => {
            if (field.value) {
              if (field.dataType === "DropDown") {
                const dropdownOption = field.options.find((option: IdNameModel) => option.id === field.value);
                transformedSpecification.fields.push({
                  key: field.label,
                  value: dropdownOption?.name ?? field.value,
                });
              }
              else {
                transformedSpecification.fields.push({
                  key: field.label,
                  value: field.value,
                });
              }
            }
          });
          const unit = quotationUnitDTOS.find((u: QuotationUnitDTOS) => u.unitId === data.unitId);
          if (unit) {
            unit.specifications.push(transformedSpecification);
          }
        }
      });
    })
    quotationUnitDTOS.forEach((unit) => {
      unit.specifications.sort((a, b) => a.groupName.localeCompare(b.groupName));
    });
    this.quotationUnitDTOS = quotationUnitDTOS;
  }
}
