<div class="modal-title sticky-top">
  <h4 class="header-title">{{ title }}</h4>
  <div>
    <img
      class="me-sm-4 me-3 cursor-pointer"
      [src]="constants.staticImages.icons.edit"
      [pTooltip]="'Edit'"
      (click)="edit()"
      alt="edit-icon"
      *appHasPermission="[permissionActions.UPDATE_INVENTORY]"
    />
    <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
  </div>
</div>
<div>
  <app-inventory-details *ngIf="selectedInventory && abbreviation" [inventoryId]="selectedInventory.id" [dealerName]="abbreviation"></app-inventory-details>
</div>
<p-sidebar [(visible)]="showCreateModal" [fullScreen]="true" (onHide)="showCreateModal = false" [blockScroll]="true" [transitionOptions]="modalTransition" [showCloseIcon]="false">
  <app-inventory-add-wrapper
    (onClose)="onAddEditPopupClose($event)"
    *ngIf="showCreateModal"
    [inventoryInfo]="selectedInventory"
    [isEditMode]="isEditMode"
    [activeIndexes]="0"
    [isViewMode]="isViewMode"
    [showSoldTabs]="showSoldTabs"
    [showHoldTabs]="showHoldTabs"
  >
  </app-inventory-add-wrapper>
</p-sidebar>
