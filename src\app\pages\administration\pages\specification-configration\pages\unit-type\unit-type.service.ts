import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Observable } from "rxjs";

@Injectable({ providedIn: 'root' })

export class UnitTypeService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.unitSpecification.root;
  }

  bulkDelete(body: Array<number>, endpoint?: string): Observable<any> {
    return this.httpClient.delete(`${this.getFullAPIUrl(endpoint)}`, {body});
  }

}
