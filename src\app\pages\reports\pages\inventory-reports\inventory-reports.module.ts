import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { InventoryReportsRoutingModule } from './inventory-reports-routing.module';
import { InventoryAgingComponent } from './pages/inventory-aging/inventory-aging.component';
import { ProfitabilityReportComponent } from './pages/profitability-report/profitability-report.component';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { MessageModule } from 'primeng/message';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { InventoryReportsComponent } from './inventory-reports.component';
import { ConfigListComponent } from './pages/inventory-aging/config-list/config-list.component';
import { ConfigUpdateComponent } from './pages/inventory-aging/config-update/config-update.component';


@NgModule({
  declarations: [
    InventoryReportsComponent,
    InventoryAgingComponent,
    ProfitabilityReportComponent,
    ConfigListComponent,
    ConfigUpdateComponent
  ],
  imports: [
    CommonModule,
    InventoryReportsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    SharedComponentsModule,
    TableModule,
    DropdownModule,
    MultiSelectModule,
    FontAwesomeIconsModule,
    CalendarModule,
    DirectivesModule,
    SidebarModule,
    MessageModule,
    TooltipModule
  ]
})
export class InventoryReportsModule { }
