import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup } from '@angular/forms';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CrmContactListItem } from '@pages/administration/models/crm.model';
import { PreviouslyOwnedParams, QuotationKey } from '@pages/crm/models/customer-inventory.model';
import { QuotationFilterParams } from '@pages/crm/models/customer-lead-quotation.model';
import { QuatationListFilter } from '@pages/crm/models/quatation.model';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { takeUntil } from 'rxjs';
import { DataType, OperatorType, TreeOperatorType } from 'src/app/@shared/models';

@Component({
  selector: 'app-crm-contact-previously-owned',
  templateUrl: './crm-contact-previously-owned.component.html',
  styleUrls: []
})
export class CrmContactPreviouslyOwnedComponent extends BaseComponent implements OnInit {

  previouslyOwnedListFormGroup!: FormGroup;
  isLoading = false;
  quotationList = [];
  filterParams: QuatationListFilter = new QuatationListFilter();
  isStatusList!: number | null;
  @Input() crmContactInfo!: CrmContactListItem | null;

  constructor(private readonly inventoryService: InventoryService,
    private readonly formBuilder: FormBuilder,
    private readonly cdf: ChangeDetectorRef) { super(); }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getQuotationDetails();
  }

  initializeFormGroup() {
    this.previouslyOwnedListFormGroup = this.formBuilder.group({
      previouslyOwned: this.formBuilder.array([])
    });
  }

  get previouslyOwnedFormArray(): FormArray {
    return this.previouslyOwnedListFormGroup.get('previouslyOwned') as FormArray;
  }

  get getQuotationRequest(): QuotationFilterParams {
    return {
      treeOperator: TreeOperatorType.NOOP,
      values: [{
        dataType: DataType.LONG,
        key: QuotationKey.PREVIOUSLY_OWNED,
        operator: OperatorType.EQUAL,
        value: this.crmContactInfo?.id ? this.crmContactInfo?.id : ''
      }]
    };
  }

  private getQuotationDetails(): void {
    this.inventoryService.add<QuotationFilterParams>(this.getQuotationRequest, API_URL_UTIL.inventory.list).pipe(takeUntil(this.destroy$)).subscribe({
      next: (quotation) => {
        const content: PreviouslyOwnedParams[] = quotation.content;
        for (const quotation of content) {
          this.previouslyOwnedFormArray.push(this.formBuilder.group({
            stock: quotation.generalInformation.stockNumber,
            vin: quotation.generalInformation.vin,
            make: quotation.generalInformation.make.name,
            year: quotation.generalInformation.year,
            model: quotation.generalInformation.unitModel.name,
            status: quotation.generalInformation.unitStatus.name
          }));
        }
      }
    });
  }
}
