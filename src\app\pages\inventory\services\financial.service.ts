import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { Observable } from 'rxjs';
import { IdNameModel, RoleNames } from 'src/app/@shared/models';
import { FinancialInformation, inventoryHoldDetails, SalesInformation, SalesPersonDetails } from '../models';

@Injectable({
  providedIn: 'root'
})
export class FinancialService extends BaseCrudService {

  constructor(protected readonly httpClient: HttpClient) { super(httpClient); }

  getBaseAPIPath(): string {
    return API_URL_UTIL.financial.root;
  }


  getAcquisitionMethod(): Observable<IdNameModel[]> {
    return this.httpClient.get<IdNameModel[]>(`${API_URL_UTIL.financial.acquisitionMethod}`);
  }

  getPurchasingAgent(): Observable<SalesPersonDetails[]> {
    const endpoint = API_URL_UTIL.admin.users.role.replace(':roleName', RoleNames.ROLE_SALESPERSON);
    return this.httpClient.get<SalesPersonDetails[]>(`${endpoint}`);
  }


  geUnitIdByFinancialInfo(inventoriesId: number[]): Observable<FinancialInformation[]> {
    return this.httpClient.post<FinancialInformation[]>(`${API_URL_UTIL.financial.unitByFinancial}/financials`, inventoriesId);
  }

  geUnitIdBySaleInfo(unitId: number): Observable<SalesInformation> {
    return this.httpClient.get<SalesInformation>(`${API_URL_UTIL.financial.unitByFinancial}/${unitId}/sell`);
  }

  getUnitIdByHoldInfo(unitId: number): Observable<inventoryHoldDetails> {
    return this.httpClient.get<inventoryHoldDetails>(`${API_URL_UTIL.financial.unitByFinancial}/${unitId}/on-hold`);
  }
}
