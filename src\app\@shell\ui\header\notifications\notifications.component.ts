import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Router } from '@angular/router';
import { MESSAGES, icons } from '@constants/*';
import { BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { QuotationFilterParams } from '@pages/crm/models/customer-lead-quotation.model';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { BehaviorSubject, distinctUntilChanged, takeUntil } from 'rxjs';
import { DataType, NotificationParams, Notifications, NotificationsStatus, OperatorType, Page, TreeOperatorType } from 'src/app/@shared/models';
import { MessagingService } from 'src/app/@shared/services';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss']
})
export class NotificationsComponent extends BaseComponent implements OnInit, OnChanges {

  @Input() unreadNotificationCount!: number;
  notifications: Array<Notifications> = [];
  pageNumber = 0;
  isDarkMode = false;
  isScrolling$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  disableInfiniteScroll = false;
  totalPages!: number;
  currentUserId!: number;
  @Output() decrementUnreadCount = new EventEmitter<void>();
  @Output() setNotificationUnreadCountToZero = new EventEmitter<void>();
  notificationStatus = NotificationsStatus;
  isLoading = true;
  activeIndex = 0;
  filterParams!: QuotationFilterParams;
  @Output() onClose = new EventEmitter<void>();

  excelDownloadMenuItems: MenuItem[] = [
    {
      label: "Mark all as read",
      command: () => this.onReadAllNotifications(),
    }
  ];

  constructor(
    private readonly messagingService: MessagingService,
    private readonly cdf: ChangeDetectorRef,
    private readonly accountService: AuthService,
    private readonly router: Router,
    private readonly confirmationService: ConfirmationService,
    private readonly themeService: ThemeService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getUserId();
    this.subscribeToTheme();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.unreadNotificationCount?.currentValue) {
      this.cdf.detectChanges();
    }
  }

  getUserId(): void {
    this.accountService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe((res) => {
      if (res?.id) {
        this.currentUserId = res.id
        this.getNotifications(true);
      }
    });
  }

  getNotifications(showLoader = false): void {
    this.isLoading = showLoader;
    this.messagingService.notificationHistory(this.currentUserId, this.pageNumber, 20, this.getNotificationParams()).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Page<Notifications>) => {
          this.notifications = [...this.notifications, ...res.content];
          this.isLoading = false;
          this.isScrolling$.next(false);
          this.cdf.detectChanges();
          this.totalPages = res.totalPages - 1;
          this.disableInfiniteScroll = res.totalPages <= this.pageNumber;
        }, error: () => {
          this.isLoading = false;
          this.isScrolling$.next(false);
          this.cdf.detectChanges();
        }
      });
  }

  getNotificationParams(): NotificationParams {
    return {
      recipientId: this.currentUserId,
      isDeleted: false,
      orderBy: [
        {
          ascending: false,
          field: "sentDate"
        }
      ],
      ...this.filterParams
    }
  }

  onScroll() {
    if (!this.disableInfiniteScroll) {
      this.isScrolling$.next(true);
      this.pageNumber++;
      this.getNotifications();
    }
  }

  onClickNotification(notificationDetails: Notifications, shouldRedirect = true): void {
    this.readNotification(notificationDetails);
    if (shouldRedirect) {
      this.initRedirectTo(notificationDetails);
    }
  }

  readNotification(notificationDetails: Notifications): void {
    if (notificationDetails.readStatus === this.notificationStatus.UNREAD) {
      this.messagingService.readNotifications(notificationDetails.id).pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            const notificationIndex: number = this.notifications?.findIndex(notification => {
              return notification.id === notificationDetails.id
            });
            this.notifications[notificationIndex].readStatus = this.notificationStatus.READ;
            this.decrementUnreadCount.emit();
          }
        });
    }
  }

  initRedirectTo(notificationDetails: Notifications): void {
    const returnUrl = this.router.routerState.snapshot.url;
    switch (notificationDetails.jsonReferenceData.parentModuleName) {
      case 'TASK':
        this.redirectToTasks(notificationDetails);
        break;
      case 'PIPELINE_SOLD':
        this.redirectToPipeline(notificationDetails, returnUrl);
        break;
      case 'DRIVER_SCHEDULE':
        this.redirectTo(`/${this.path.transport.root}/${this.path.transport.driverScheduleBoard.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&returnUrl=${returnUrl}`)
        break;
      case 'INCOMING_TRUCK':
        this.redirectToIncomingTruck(notificationDetails, returnUrl)
        break;
      case 'INVENTORY':
        this.redirectTo(`${this.path.inventory.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&commentId=${notificationDetails.jsonReferenceData.childModuleId}&returnUrl=${returnUrl}`)
        break;
      case 'PIPELINE_STOCK':
        this.redirectToStockPipeline(notificationDetails, returnUrl);
        break;
      case 'CUSTOMER_LEAD':
        this.redirectTo(`${this.path.crm.root}/${this.path.crm.crmCustomer.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&returnUrl=${returnUrl}`)
        break;
      default:
        break;
    }
  }

  redirectToTasks(notificationDetails: Notifications): void {
    if (notificationDetails.jsonReferenceData.action !== 'DELETE') {
      if (notificationDetails.jsonReferenceData.childModuleName === 'TASK_COMMENT') {
        this.redirectTo(`/${this.path.crm.root}/${this.path.crm.crmTask.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&mode=2&commentId=${notificationDetails.jsonReferenceData.childModuleId}`)
      } else {
        this.redirectTo(`/${this.path.crm.root}/${this.path.crm.crmTask.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&mode=1`)
      }
    }
  }

  redirectToIncomingTruck(notificationDetails: Notifications, returnUrl: string) {
    if (notificationDetails.jsonReferenceData.action !== 'DELETE') {
      if (notificationDetails.jsonReferenceData.childModuleName === 'INCOMING_TRUCK_COMMENT') {
        this.redirectTo(`/${this.path.transport.root}/${this.path.transport.incomingTruckBoard.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&commentId=${notificationDetails.jsonReferenceData.childModuleId}&returnUrl=${returnUrl}`)
      } else {
        this.redirectTo(`/${this.path.transport.root}/${this.path.transport.incomingTruckBoard.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&returnUrl=${returnUrl}`)
      }
    }
  }

  redirectToPipeline(notificationDetails: Notifications, returnUrl: string): void {
    if (notificationDetails.jsonReferenceData.action !== 'DELETE') {
      if (notificationDetails.jsonReferenceData.childModuleName === 'PIPELINE_COMMENT') {
        this.redirectTo(`/${this.path.pipeline.root}/${this.path.pipeline.soldTruckBoard.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&commentId=${notificationDetails.jsonReferenceData.childModuleId}&returnUrl=${returnUrl}`)
      } else {
        this.redirectTo(`/${this.path.pipeline.root}/${this.path.pipeline.soldTruckBoard.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&returnUrl=${returnUrl}`)
      }
    }
  }

  redirectToStockPipeline(notificationDetails: Notifications, returnUrl: string): void {
    if (notificationDetails.jsonReferenceData.action !== 'DELETE') {
      if (notificationDetails.jsonReferenceData.childModuleName === 'PIPELINE_COMMENT') {
        this.redirectTo(`/${this.path.pipeline.root}/${this.path.pipeline.stockTruckBoard.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&commentId=${notificationDetails.jsonReferenceData.childModuleId}&returnUrl=${returnUrl}`)
      } else {
        this.redirectTo(`/${this.path.pipeline.root}/${this.path.pipeline.stockTruckBoard.root}?id=${notificationDetails.jsonReferenceData.parentModuleId}&returnUrl=${returnUrl}`)
      }
    }
  }

  redirectTo(url: string): void {
    this.router.navigateByUrl(url)
  }

  onReadAllNotifications(): void {
    this.messagingService.readAllNotifications(this.currentUserId).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          for (const notification of this.notifications) {
            notification.readStatus = this.notificationStatus.READ;
          }
          this.setNotificationUnreadCountToZero.emit();
        }
      });
  }

  onDelete(event: Event, id: string, readStatus: string): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'notification'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.deleteNotification(id, readStatus);
        this.notifications = this.notifications.filter((notification: Notifications) => {
          return notification.id !== id
        });
      }
    });
  }

  deleteNotification(id: string, readStatus: string): void {
    this.messagingService.deleteNotification(id).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          if (readStatus === this.notificationStatus.UNREAD) {
            this.decrementUnreadCount.emit();
          }
        }
      });
  }

  handleChange(activeIndex: number) {
    this.notifications = [];
    if (activeIndex === 0) {
      this.filterParams = {} as any;
    } else if (activeIndex === 1) {
      this.filterParams = {
        treeOperator: TreeOperatorType.NOOP,
        values: [
          {
            "dataType": DataType.ENUM,
            "key": "readStatus",
            "operator": OperatorType.EQUAL,
            "value": "UNREAD",
            "enumName": "NotificationReadStatus"
          }]
      }
    } else if (activeIndex === 2) {
      this.filterParams = {
        treeOperator: TreeOperatorType.NOOP,
        values: [
          {
            "dataType": DataType.STRING,
            "key": "subject",
            "operator": OperatorType.LIKE,
            "value": "Mention"
          }]
      }
    } else {
      this.filterParams = {
        treeOperator: TreeOperatorType.NOOP,
        values: [
          {
            "dataType": DataType.STRING,
            "key": "subject",
            "operator": OperatorType.LIKE,
            "value": "Assign"
          }]
      }
    }
    this.getNotifications(true);
  }

  onModalClose() {
    this.onClose.emit();
  }

  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        if (theme === "DARK") {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
      });
  }
}
