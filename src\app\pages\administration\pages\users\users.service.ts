import { DatePipe } from "@angular/common";
import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Constants } from "@constants/*";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Observable } from "rxjs";
import { Utils } from "src/app/@shared/services";
import { ChangePasswordParam, DealerGroup, DealerRole, UserListItem } from './../../models';

@Injectable({ providedIn: 'root' })
export class UserService extends BaseCrudService {

  constructor(private readonly datePipe: DatePipe, protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.users.root;
  }

  fromServerModel(json: UserListItem): UserListItem {
    if (!json) {
      return new UserListItem();
    }
    return {
      ...json,
      name: Utils.getFullName(json.firstName, json.lastName),
      dealers: this.getUniqueDealers(json.userDealerRoles),
      lastActivity: json.lastActivity && this.datePipe.transform((json.lastActivity), Constants.fullDateFormat),
      shopsName: json.shops?.map(m => m?.name)?.join(",\n"),
    };
  }

  getUniqueDealers(dealerRoles: DealerGroup[]): DealerGroup[] {
    return dealerRoles ? dealerRoles.filter((dealer, index, self) => index === self.findIndex((t) => t.dealerId === dealer.dealerId)) : [];
  }

  changeUserPassword(params: ChangePasswordParam): Observable<void> {
    return this.httpClient.post<void>(API_URL_UTIL.admin.users.changePassword, params);
  }

  private getDealerRoles(dealers: DealerGroup[]): DealerRole[] {
    const dealerRoles: DealerRole[] = [];
    for (const dealer of dealers) {
      const existingDealer = dealerRoles.find(d => d.dealer.id === dealer.dealerId);
      if (existingDealer) {
        existingDealer.roles.push({ id: dealer.roleId, name: Utils.getRole(dealer.roleName) })
        existingDealer.roleString = existingDealer.roles.map(r => r.name).join(', ');
      } else {
        dealerRoles.push({
          dealer: { id: dealer.dealerId, name: dealer.dealerName },
          roles: [{ id: dealer.roleId, name: Utils.getRole(dealer.roleName) }],
          roleString: Utils.getRole(dealer.roleName),
          archivedDealer: dealer.archivedDealer
        })
      }
    }
    return dealerRoles;
  }

  getUsersForExport(users: UserListItem[]) {
    return users.map(user => ({
      Email: user.email,
      'First Name': user.firstName,
      'Last Name': user.lastName,
      'Phone Number': user.phoneNumber,
      'Employee ID': user.employeeId,
      'Status': user.archived ? 'Archived' : 'Active',
      "Last Activity": user.lastActivity,
      'Role': user?.roleDto?.name
    }));
  }

}
