import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { matchValidator } from 'src/app/@shared/validators';
import { SettingsService } from '../../settings.service';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {
  changePasswordFormGroup!: UntypedFormGroup;

  constructor(private readonly fb: UntypedFormBuilder,
    private readonly settingsService: SettingsService,
    private readonly toasterService: AppToasterService) { }

  async ngOnInit(): Promise<void> {
    this.initializeFormGroup();
  }

  initializeFormGroup(): void {
    this.changePasswordFormGroup = this.fb.group({
      currentPassword: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(100)]],
      newPassword: ['', [Validators.required, Validators.minLength(4), Validators.maxLength(100), matchValidator('confirmPassword', true)]],
      confirmPassword: ['', matchValidator('newPassword')],
    });
  }

  onChangePassword() {
    if (this.changePasswordFormGroup.invalid) {
      this.changePasswordFormGroup.markAllAsTouched();
      return;
    }
    this.settingsService.changePassword(this.changePasswordFormGroup.value).subscribe(res => {
      this.changePasswordFormGroup.reset();
      this.toasterService.success(MESSAGES.changePasswordSuccess);
    });
  }

}
