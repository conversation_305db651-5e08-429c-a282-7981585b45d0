import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { AuthActivateGuard, NoAuthGuard, PermissionForModuleGuard } from '@core/guards';
import { ROUTER_UTILS } from '@core/utils/router.utils';
import { CheckDealerInfo } from '@pages/public-inventories/services/check-dealer-info.resolver';
import { NotFoundComponent } from '@shell/ui/not-found/not-found.component';
import { PermissionModules } from './@shared/models';


const APP_ROUTES: Routes = [
  {
    path: ROUTER_UTILS.config.auth.root,
    loadChildren: async () => (await import('@pages/auth/auth.module')).AuthModule,
    canLoad: [NoAuthGuard],
  },
  {
    path: `${ROUTER_UTILS.config.publicInvetory.root}/:dealerName`,
    loadChildren: () => import('@pages/public-inventories/public-inventories.module').then(m => m.PublicInventoriesModule),
    resolve: { dealer: CheckDealerInfo },
  },
  {
    path: ROUTER_UTILS.config.redirectToAuth.root,
    loadChildren: () => import('@pages/redirect-to-authorized-page/redirect-to-authorized-page.module').then(m => m.RedirectToAuthorizedPageModule),
  },
  {
    path: '',
    redirectTo: ROUTER_UTILS.config.base.dashboard,
    pathMatch: 'full'
  },
  {
    path: ROUTER_UTILS.config.base.dashboard,
    loadChildren: async () => (await import('@pages/dashboard/dashboard.module')).DashboardModule,
    canActivate: [AuthActivateGuard, PermissionForModuleGuard],
    title: 'Skeye - Dashboard',
    data: {
      module: PermissionModules.DASHBOARD
    }
  },
  {
    path: ROUTER_UTILS.config.crm.root,
    loadChildren: async () => (await import('@pages/crm/crm.module')).CrmModule,
    canActivate: [AuthActivateGuard, PermissionForModuleGuard],
    data: {
      module: PermissionModules.CRM
    }
  },
  {
    path: ROUTER_UTILS.config.transport.root,
    loadChildren: async () => (await import('@pages/transport/transport.module')).TransportModule,
    canActivate: [AuthActivateGuard, PermissionForModuleGuard],
    data: {
      module: PermissionModules.TRANSPORT
    }
  },
  {
    path: ROUTER_UTILS.config.settings.root,
    loadChildren: async () => (await import('@pages/settings/settings.module')).SettingsModule,
    canActivate: [AuthActivateGuard],
  },
  {
    path: ROUTER_UTILS.config.user.root,
    loadChildren: async () => (await import('@pages/user/user.module')).UserModule,
    canActivate: [AuthActivateGuard],
  },
  {
    path: ROUTER_UTILS.config.administration.root,
    loadChildren: async () => (await import('./pages/administration/administration.module')).AdministrationModule,
    canActivate: [AuthActivateGuard, PermissionForModuleGuard],
    data: {
      module: PermissionModules.ADMIN
    }
  },
  {
    path: ROUTER_UTILS.config.reporting.root,
    loadChildren: async () => (await import('./pages/reports/reports.module')).ReportsModule,
    canActivate: [AuthActivateGuard, PermissionForModuleGuard],
    data: {
      module: PermissionModules.REPORTING
    }
  },

  // NOTE: The following modules are currently disabled per client's request.
  // {
  //   path: ROUTER_UTILS.config.shops.root,
  //   loadChildren: async () => (await import('./pages/shops/shops.module')).ShopsModule,
  //   canActivate: [AuthActivateGuard, PermissionForModuleGuard],
  //   data: {
  //     module: PermissionModules.SHOP
  //   }
  // },
  {
    path: ROUTER_UTILS.config.inventory.root,
    loadChildren: async () => (await import('./pages/inventory/inventory.module')).InventoryModule,
    canActivate: [AuthActivateGuard, PermissionForModuleGuard],
    data: {
      module: PermissionModules.INVENTORY
    }
  },
  // NOTE: The following modules are currently disabled per client's request.
  // {
  //   path: ROUTER_UTILS.config.calendar.root,
  //   loadChildren: async () => (await import('./pages/calendar/event-calendar.module')).EventCalendarModule,
  //   canActivate: [AuthActivateGuard, PermissionForModuleGuard],
  //   data: {
  //     module: PermissionModules.CALENDER
  //   }
  // },
  // {
  //   path: ROUTER_UTILS.config.pipeline.root,
  //   loadChildren: async () => (await import('./pages/pipeline/pipeline.module')).PipelineModule,
  //   canActivate: [AuthActivateGuard, PermissionForModuleGuard],
  //   data: {
  //     module: PermissionModules.PIPELINE
  //   }
  // },
  {
    path: ROUTER_UTILS.config.notAuthorized.root,
    loadChildren: async () => (await import('./pages/not-authorized/not-authorized.module')).NotAuthorizedModule,
    canActivate: [AuthActivateGuard]
  },
  {
    path: `${ROUTER_UTILS.config.notFound.root}/:module/:status`,
    loadChildren: async () => (await import('./pages/item-not-found/item-not-found.module')).ItemNotFoundModule
  },
  {
    path: '**',
    loadChildren: async () => (await import('@shell/ui/not-found/not-found.module')).NotFoundModule,
    component: NotFoundComponent,
  },
];

@NgModule({
  imports: [RouterModule.forRoot(APP_ROUTES, { preloadingStrategy: PreloadAllModules })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
