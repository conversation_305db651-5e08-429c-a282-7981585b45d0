import { GenericFilterParams } from "src/app/@shared/models";

export class InventoryCommentCreateParam {
  message!: string;
  unitId!: number;
  parentCommunicationId!: number;
  mentionUserIds: number[] = [];
  isEditable = false;
}

export enum Fields {
  CREATEDBY = 'createdBy',
  FIRSTNAME = 'firstName',
  LASTNAME = 'lastName',
  DATE = 'modifiedDate',
  MESSAGE = 'message'
}

export class InventoryCommentResponse {
  id!: number | null;
  message!: string;
  edited!: boolean;
  modifiedDate!: string;
  unitId!: number | null;
  createdBy!: CreatedBy;
  communications!: Communication[];
  mentionUserIds!: number[];
}
export class MentionUser {
  id!: number;
  label!: string;
}
export interface CreatedBy {
  id: number | null,
  firstName: string,
  lastName: string
}

export interface Communication {
  id: number | null,
  message: string,
  edited: boolean,
  modifiedDate: string,
  unitId?: number | null,
  incomingTruckId?: number | null,
  createdBy: CreatedBy
  parentCommunicationId: number | null,
  mentionUserIds: number[]
}

export class AllUserListItem {
  id!: number;
  name!: string;
  phoneNumber!: string;
  email!: string;

  constructor(json?: AllUserListItem) {
    if (json) {
      Object.assign(this, json);
    }
  }
}
export class AllUserListFilter extends GenericFilterParams {
  archived = false;
}
