import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { VendorListItem } from '@pages/administration/models';
import { Utils } from "src/app/@shared/services";

@Injectable({
  providedIn: 'root'
})
export class SuppliersService extends BaseCrudService {
  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.suppliers.root;
  }

  fromServerModel(json: VendorListItem): VendorListItem {
    if (!json) {
      return new VendorListItem();
    }
    return {
      ...json,
      contactPersonName: Utils.getFullName(json.contactPerson?.firstName, json.contactPerson?.lastName),
      addressCity: json.address?.city,
      addressState: json.address?.state,
    };
  }
}
