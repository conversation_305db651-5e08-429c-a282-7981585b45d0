import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { AddNewMakeModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-make/add-new-make.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { MakeModelRoutingModule } from './make-model-routing.module';
import { MakeModelComponent } from './make-model.component';


@NgModule({
  declarations: [
    MakeModelComponent
  ],
  imports: [
    CommonModule,
    MakeModelRoutingModule,
    DirectivesModule,
    SharedComponentsModule,
    SidebarModule,
    TableModule,
    FontAwesomeModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    FormsModule,
    DropdownModule,
    AddNewMakeModule
  ],
  providers: [
    ConfirmationService,
    MessageService
  ],
})
export class MakeModelModule { }
