import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseComponent } from '@core/utils';
import { ForgotPasswordFinishRequestParams } from '@pages/auth/models';
import { takeUntil } from 'rxjs';
import { matchValidator } from 'src/app/@shared/validators';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-forgot-password-finish',
  templateUrl: './forgot-password-finish.component.html',
  styleUrls: ['./forgot-password-finish.component.scss']
})
export class ForgotPasswordFinishComponent extends BaseComponent implements OnInit {
  forgotPasswordFormGroup!: UntypedFormGroup;
  passwordChanged = false;

  constructor(
    private readonly authService: AuthService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeForm();
    this.initializeParamListener();
  }

  initializeParamListener() {
    this.activatedRoute.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (!params.resetKey) {
        this.router.navigate([this.path.base.root, this.path.auth.root]);
      }
      this.forgotPasswordFormGroup.patchValue({
        resetKey: params.resetKey,
      });
    });
  }

  initializeForm() {
    this.forgotPasswordFormGroup = new UntypedFormGroup({
      resetKey: new UntypedFormControl(''),
      newPassword: new UntypedFormControl('', [Validators.required, Validators.minLength(1), Validators.maxLength(100), matchValidator('confirmPassword', true)]),
      confirmPassword: new UntypedFormControl('', matchValidator('newPassword')),
    });
  }

  get passwordFinishParams(): ForgotPasswordFinishRequestParams {
    return {
      key: this.forgotPasswordFormGroup.controls['resetKey']?.value,
      newPassword: this.forgotPasswordFormGroup.controls['newPassword']?.value,
    }
  }

  onForgotPasswordFinish(): void {
    if (this.forgotPasswordFormGroup.invalid) {
      this.forgotPasswordFormGroup.markAllAsTouched();
      return;
    }
    this.authService.onForgotPasswordFinish(this.passwordFinishParams)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => this.passwordChanged = true,
      });
  }

}
