import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import { BaseComponent } from '@core/utils';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { IdNameModel } from 'src/app/@shared/models';
import { IncomingTruckAddComponent } from '../incoming-truck-add/incoming-truck-add.component';
enum AddIncomingTruckStep {
  SEARCH_INCOMING = 1,
  ADD_INCOMING_TRUCK
}
@Component({
  selector: 'app-incoming-truck-add-wrapper',
  templateUrl: './incoming-truck-add-wrapper.component.html',
  styleUrls: ['./incoming-truck-add-wrapper.component.scss']
})
export class IncomingTruckAddWrapperComponent extends BaseComponent implements OnChanges {
  isEditMode = false;
  title = 'Add Incoming Truck';
  addUnitSteps = AddIncomingTruckStep;
  currentStep = AddIncomingTruckStep.ADD_INCOMING_TRUCK;
  hasDataBeenModified = false;
  selectedVehicle: any;
  index = 1;
  isPrevious = true;
  @Input() incomingTruckInfo!: IncomingTruckBoardListItem | null;
  @Input() isViewMode!: boolean;
  @Input() categoriesToDisplay!: IdNameModel[];
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild(IncomingTruckAddComponent) incomingTruckAddComponent!: IncomingTruckAddComponent;
  constructor() {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.incomingTruckInfo?.currentValue) {
      this.isEditMode = true;
      this.currentStep = AddIncomingTruckStep.ADD_INCOMING_TRUCK;
      this.title = this.isViewMode ? 'View Incoming Truck' : 'Edit Incoming Truck';
    }
    else {
      this.isViewMode = false;
    }
  }

  onSubmit(close = true): void {
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  hasBeenModified(bool: boolean) {
    this.hasDataBeenModified = bool;
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  isPreviousTab(event: any): void {
    this.isPrevious = event;
  }

  onDataModified(isModified: boolean): void {
    this.hasDataBeenModified = isModified;
  }

  onStepChange(nextStep: AddIncomingTruckStep): void {
    this.currentStep = nextStep;
  }

  onVehicleSelect(vehicle: any): void {
    this.selectedVehicle = vehicle;
  }

  onEditTitle(event: boolean) {
    if (event) {
      this.title = 'Edit Incoming Truck'
    }
  }
}
