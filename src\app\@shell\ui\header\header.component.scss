@import "/src/assets/scss/variables";
@import "/src/assets/scss/theme/mixins";

header {
  display: flex;
  align-items: center;
  background-color: var(--primary-color);

  .logo {
    display: flex;
    align-items: center;

    img {
      padding: 0px 25px;
      width: 200px;
    }

    &:hover {
      cursor: pointer;
    }
  }

  .dropdown-menu .dropdown-submenu {
    display: none;
    position: absolute;
    left: 100%;
    bottom: 0;
    background-color: var(--dropdown-panel-color);
  }

  .dropdown-menu .dropdown-submenu-left {
    right: 100%;
    left: auto;
  }

  .dropdown-menu > li:hover > .dropdown-submenu {
    display: block;
  }

  .sub-parent {
    fa-icon {
      padding: 5px;
    }
  }

  .profile {
    display: flex;

    .dropdown-menu.show {
      li a {
        color: var(--text-color);
        font-size: 13px;
        letter-spacing: 0;
        line-height: 18px;
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;

        &:hover {
          color: white;
        }

        &.username {
          color: var(--text-color);
          font-size: 14px;
          font-weight: 600;
          letter-spacing: 0;
          line-height: 19px;

          &:hover {
            cursor: default;
            color: var(--white-color);
          }
        }

        img {
          margin-right: 10px;
        }

        fa-icon {
          margin-left: 1px;
          color: #9f9f9f;
          font-size: 16px;
          margin-right: 9px;
        }
      }
    }

    ul {
      min-width: 200px;
      background-color: var(--dropdown-panel-color);
    }

    .nav-link {
      background-color: var(--primary-color);
      min-width: 75px;
      color: white;
    }
  }

  .dropdown-item,
  .nav-link {
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
  }

  .nav-pills {
    width: 100%;
    justify-content: center;
  }

  .nav-link {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    position: relative;

    img {
      margin-right: 10px;
    }
  }

  .dropdown-toggle::after {
    position: absolute;
    right: 10px;
  }

  .userprofile {
    width: 300px;
  }

  .user-name {
    width: 47px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.initials {
  position: absolute;
  left: -10px;
  width: 30px;
  height: 30px;
  background-color: var(--active-color);
  color: var(--white-color);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 25%;
}

.dropdown-menu {
  min-width: 11.5rem !important;
}

.fixed-header {
  position: fixed;
  z-index: 9;
  width: 100%;
}

button.close {
  border: none;
  background-color: var(--light-grey-color);
  font-size: 15px;
  display: flex;
  align-content: center;
  justify-content: center;

  span {
    font-size: 50px !important;
    font-weight: 100 !important;
  }
}

.modal-header {
  font-size: larger;
  font-weight: bold;
  background: #eeeeee;
}

.badge {
  background-color: var(--active-color);
  position: absolute;
  top: 50%;
  margin-top: -20px;
  margin-left: 35px;
  border-radius: 50%;
  font-size: 11px;
}

.notification-header {
  display: flex;
  align-items: center;

  .unread-count {
    background-color: var(--primary-color);
    color: white !important;
    font-size: 12px !important;
    margin-left: 15px;
    width: 21px;
    height: 23px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

::ng-deep .pipeline-config {
  .p-sidebar-content .content {
    padding-bottom: 50px;
  }

  .p-timeline-event-opposite {
    display: none;
  }

  .p-timeline {
    margin-top: 24px;
  }

  .p-timeline-event-content {
    display: none;
  }

  .p-timeline-event-separator {
    span {
      border: 2px solid var(--active-color);
      background-color: var(--active-color);
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      font-weight: bold;
      padding: 10px;
    }
  }

  .p-timeline .p-timeline-event-connector {
    background-color: var(--active-color);
  }

  .p-timeline.p-timeline-vertical .p-timeline-event-connector {
    width: 4px;
    height: 49px;
  }

  p-table {
    width: 100%;
  }
}
:host {
  .splash-screen {
    background-color: var(--card-bg-color);
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    z-index: 1000;

    img {
      margin-left: calc(100vw - 100%);
      width: 200px;
      margin-bottom: 30px;
    }

    span {
      margin-left: calc(100vw - 100%);
      margin-bottom: 30px;
    }

    ::ng-deep {
      [role="progressbar"] {
        margin-left: calc(100vw - 100%);
      }

      .mat-progress-spinner circle,
      .mat-spinner circle {
        // brand color
        stroke: var(--primary-color);
      }
    }
  }
}
.theme-icons {
  color: var(--active-color-lighter);
}

.theme-switch {
  ::ng-deep .switch.checked {
    background-color: var(--active-color-lighter);
  }
}
