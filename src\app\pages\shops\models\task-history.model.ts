export interface TaskHistoryListItem {
  eventDate: string;
  eventType: EventType;
  id: number;
  newData: string;
  oldData: string;
  parsedData?: ParsedData[];
  taskEvent: TaskEvent;
  taskId: number;
  userId: number;
  userName: string;
}

export enum EventType {
  CREATED = 'CREATED',
  MODIFIED = 'MODIFIED',
  DELETED = 'DELETED',
}

export enum TaskEvent {
  TASK = 'TASK',
  COMMENT = 'COMMENT',
  ACTIVITY_LOG = 'ACTIVITY_LOG'
}

export interface ParsedData {
  fieldLabel: string;
  oldValue: string;
  newValue: string;
}
