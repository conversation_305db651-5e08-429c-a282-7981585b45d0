<h5 class="card-title">Login</h5>
<p class="card-text">Please login to your account!</p>
<form class="form" [formGroup]="emailPasswordLoginFormGroup" (ngSubmit)="loginWithEmailPassword()">
  <div class="mt-4 mb-3">
    <div class="login-input-group">
      <label>Email</label>
      <input formControlName="username" type="email" class="form-control email" placeholder="Enter your email" />
      <app-error-messages [control]="emailPasswordLoginFormGroup.controls?.username"></app-error-messages>
    </div>
  </div>
  <div class="mt-4 mb-4">
    <div class="login-input-group">
      <label>Password</label>
      <input formControlName="password" type="password" class="form-control password" placeholder="Enter your password"
        appPasswordEye />
      <app-error-messages [control]="emailPasswordLoginFormGroup.controls?.password"></app-error-messages>
    </div>
  </div>
  <button class="btn btn-primary mt-3 mb-2 w-100" type="submit" [disabled]="emailPasswordLoginFormGroup?.invalid"
    appShowLoaderOnApiCall>
    <span>Login</span>
  </button>
  <div>
    <a class="forgot" href="javascript:;"
      [routerLink]="[path.base.root, path.auth.root, path.auth.forgotPassword.root, path.auth.forgotPassword.init]">Forgot
      your password?</a>
  </div>
</form>
