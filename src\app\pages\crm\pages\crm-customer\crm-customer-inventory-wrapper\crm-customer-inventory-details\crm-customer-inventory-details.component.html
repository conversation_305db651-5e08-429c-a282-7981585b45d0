<div class="modal-title close">
  <div class="model-content">
    <h4 class="header-title">
      {{ isViewMode ? 'Quotation' : crmId ? 'Inventory Matched' : 'Send Inventory' }}
    </h4>
    <span *ngIf="isViewMode" class="created-by">
      <span class="bold-text">#{{ quotationDetails?.id }}</span>
      Quoted By <span class="bold-text">{{ quotationDetails?.createdBy?.name }}</span> on
      {{ quotationDetails?.quotationDate | date: constants.fullDateFormat }}
    </span>
    <span class="created-by">
      {{ quotationDetails?.company }}
    </span>
  </div>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
</div>

<div class="inventory-matching-wrapper">
  <div class="inventory-quote-wrapper">
    <p-card>
      <div class="qoute-details">
        <div class="qoute-details-info">
          <ng-container [ngTemplateOutlet]="modelDetails" [ngTemplateOutletContext]="{ generalInfo: totalSelectedInventories, label: 'Total Selected Inventory' }"> </ng-container>
        </div>
        <div class="qoute-details-info">
          <ng-container
            [ngTemplateOutlet]="modelDetails"
            [ngTemplateOutletContext]="{ generalInfo: totalRetailPrice | currency: 'USD':'symbol':'1.0-0', label: 'Total Retail Price' }"
          >
          </ng-container>
        </div>
        <div class="qoute-details-info">
          <ng-container
            [ngTemplateOutlet]="modelDetails"
            [ngTemplateOutletContext]="{
              generalInfo: totalInvestmentPrice | currency: 'USD':'symbol':'1.0-0',
              label: 'Total Investment Cost'
            }"
          >
          </ng-container>
        </div>
        <div class="qoute-details-info">
          <ng-container
            [ngTemplateOutlet]="modelDetails"
            [ngTemplateOutletContext]="{ generalInfo: totalQuotePrice | currency: 'USD':'symbol':'1.0-0', label: 'Total Quote Price' }"
          >
          </ng-container>
        </div>
      </div>
    </p-card>
  </div>
  <div [ngClass]="{ 'inventory-action-label': true, gray: !isViewMode }">
    {{ isViewMode ? 'Quoted Inventory' : 'Please select inventory for quotation' }}
  </div>

  <div *ngIf="!crmId">
    <p-card>
      <label class="required" for="field.for">Customer Email</label>
      <input class="form-control" type="email" email name="email" placeholder="Enter Customer Email" required="true" [(ngModel)]="customerEmail" #emailField="ngModel" />
      <small class="text-danger" *ngIf="emailField.errors?.required && emailField.touched">This field is required</small>
      <small class="text-danger" *ngIf="emailField.errors?.email && emailField.touched">Invalid email address</small>
    </p-card>
  </div>

  <form [formGroup]="inventoryQuotationFormGroup">
    <div class="customer-inventory-matched" formGroupName="content">
      <p-card *ngFor="let quotationDetail of quotationDetailsFormArray?.controls; let quotationIndex = index">
        <div class="inventory-content-wrapper" [formGroupName]="quotationIndex">
          <div class="inventory-content">
            <div class="select-inventory-checkbox" *ngIf="!isViewMode">
              <p-checkbox class="customer-inventory-checkbox" [binary]="true" formControlName="isSelected" (onChange)="getToggleSelect(quotationIndex)"> </p-checkbox>
            </div>
            <div class="inventory-image" [ngClass]="{ 'inventory-image': true, 'inventory-image-without-checkbox': isViewMode }">
              <img [src]="quotationDetail?.value?.fullUrl ? quotationDetail?.value?.fullUrl : constants?.staticImages?.noImages" alt="" class="unit-image" />
            </div>
            <div class="inventory-info-wrapper">
              <div>
                <h6>{{ quotationDetail?.value?.yearMakeModel }}</h6>
              </div>
              <div class="inventory-info-content">
                <div class="mb-2">
                  <div>
                    <span class="gray">#Stock:</span>
                    <span class="black">
                      {{ quotationDetail?.value?.stockNumber ? quotationDetail?.value?.stockNumber : 'NA' }}
                    </span>
                  </div>
                  <div>
                    <span class="gray">Retail:</span>
                    <span class="black">
                      {{ quotationDetail.value.retailAskingPrice ? (quotationDetail.value.retailAskingPrice | currency: 'USD':'symbol':'1.0-0') : 'NA' }}
                    </span>
                  </div>
                  <div *ngIf="isViewMode">
                    <span class="gray">Quote Price: </span>
                    <span class="black">
                      {{ quotationDetail?.value?.quotePrice ? (quotationDetail?.value?.quotePrice | currency: 'USD':'symbol':'1.0-0') : 'NA' }}
                    </span>
                  </div>
                </div>
                <div>
                  <div>
                    <span class="gray">Vin:</span>
                    <span class="black">
                      {{ quotationDetail?.value?.vin ? quotationDetail?.value?.vin : 'NA' }}
                    </span>
                  </div>
                  <div>
                    <span class="gray">Investment Cost:</span>
                    <span class="black">
                      {{ quotationDetail?.value?.totalProjectedInvestment ? (quotationDetail?.value?.totalProjectedInvestment | currency: 'USD') : 'NA' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="quote">
            <div *ngIf="quotationDetail?.value?.isSelected">
              <span [ngClass]="{ required: quotationDetail?.value?.retailAskingPrice && this.crmId, quotePrice: true }">Quote Price</span>
              <input class="form-control" type="number" placeholder="Enter Quote price" formControlName="quotePrice" (input)="getTotalQuoteCost()" />
              <div class="error">
                <app-error-messages [control]="quotationDetailsFormArray.controls[quotationIndex].get('quotePrice') ?? undefined"> </app-error-messages>
              </div>
            </div>
          </div>
        </div>
      </p-card>
    </div>
    <div class="modal-footer" *ngIf="!isViewMode">
      <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
      <button class="btn btn-primary d-flex align-items-center" type="submit" (click)="onSubmit($event)">
        Send
        <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isFormLoading"></fa-icon>
      </button>
    </div>
  </form>
</div>

<ng-template #modelDetails let-generalInfo="generalInfo" let-label="label">
  <div class="model-detail-label">
    {{ label }}
  </div>
  <div class="model-detail-info">
    {{ generalInfo }}
  </div>
</ng-template>

<p-confirmPopup></p-confirmPopup>
