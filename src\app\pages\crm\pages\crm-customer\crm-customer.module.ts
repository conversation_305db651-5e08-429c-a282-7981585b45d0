import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ColumnDropdownModule } from '@pages/common-table-column/column-dropdown.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmContactModule } from '../crm-contact/crm-contact.module';
import { CrmTaskModule } from '../crm-task/crm-task.module';
import { CrmCustomerAddModule } from './crm-customer-add/crm-customer-add.module';
import { CrmCustomerListComponent } from './crm-customer-list/crm-customer-list.component';
import { CRMCustomerRoutingModule } from './crm-customer-routing.module';
import { CrmCustomerComponent } from './crm-customer.component';

@NgModule({
  declarations: [
    CrmCustomerComponent,
    CrmCustomerListComponent
  ],
  imports: [
    CommonModule,
    CRMCustomerRoutingModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    FormsModule,
    ReactiveFormsModule,
    AccordionModule,
    ConfirmPopupModule,
    SidebarModule,
    TabViewModule,
    CardModule,
    CheckboxModule,
    CrmContactModule,
    CrmTaskModule,
    CrmCustomerAddModule,
    ColumnDropdownModule,
    CalendarModule,
    MenuModule,
    MultiSelectModule
  ],
  providers: [ConfirmationService, MessageService],
})
export class CrmCustomerModule { }
