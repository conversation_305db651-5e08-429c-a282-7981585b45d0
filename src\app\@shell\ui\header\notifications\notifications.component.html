<div class="modal-header">
  <span modalHeader class="notification-header ms-3">
    <span>Notifications</span>
    <div class="unread-count" *ngIf="unreadNotificationCount">{{ unreadNotificationCount }}</div>
  </span>
  <span class="menu-actions-wrapper">
    <ng-container *ngIf="unreadNotificationCount">
      <div>
        <fa-icon [icon]="faIcons.faEllipsisH" (click)="excelDownloadMenu.toggle($event)"></fa-icon>
      </div>
      <p-menu #excelDownloadMenu [model]="excelDownloadMenuItems" [popup]="true" appendTo="body"> </p-menu>
      <span class="action-separator">|</span>
    </ng-container>
    <div>
      <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
    </div>
  </span>
</div>

<div
  class="scrollable-view-notification notification overflow-auto"
  infiniteScroll
  (scrolled)="onScroll()"
  [scrollWindow]="false"
  [infiniteScrollThrottle]="constants.infiniteScrollConfig.infiniteScrollThrottle"
  [infiniteScrollDistance]="1"
>
  <p-tabView [(activeIndex)]="activeIndex" (onChange)="handleChange($event.index)" [scrollable]="true">
    <p-tabPanel header="All"> </p-tabPanel>
    <p-tabPanel header="Unread"> </p-tabPanel>
    <p-tabPanel header="I was mentioned"> </p-tabPanel>
    <p-tabPanel header="Assigned to me"> </p-tabPanel>
  </p-tabView>
  <ng-container *ngIf="!isLoading; else loaderTemplate">
    <ng-container *ngIf="notifications?.length; else noDataFound">
      <div
        *ngFor="let notification of notifications"
        [ngClass]="{
          'unread-notification': notification?.readStatus === notificationStatus.UNREAD,
          'read-notification': notification?.readStatus !== notificationStatus.UNREAD,
          'notification-info-wrapper': true
        }"
      >
        <div (click)="onClickNotification(notification)">
          <span class="notification-info">
            <div class="subject">
              <span class="subject-info">
                {{ notification?.subject }}
              </span>
            </div>
            {{ notification?.body }}
            <div class="notification-footer">
              <span>
                {{ notification.sentDate | dateAgo }}
              </span>
              |
              <span>
                {{ notification.jsonReferenceData.parentModuleName.replace("_", " ") | titlecase }}
              </span>
            </div>
          </span>
        </div>
        <div class="notification-action-wrapper">
          <div>
            <span class="notification-actions">
              <img
                class="mark-read"
                [ngStyle]="{ filter: isDarkMode ? 'invert(1)' : '' }"
                [src]="notification?.readStatus === notificationStatus.UNREAD ? constants.staticImages.icons.circle : constants.staticImages.icons.circleCheckGreen"
                alt=""
                [pTooltip]="notification?.readStatus === notificationStatus.UNREAD ? 'Mark as read' : ''"
                tooltipPosition="left"
                (click)="onClickNotification(notification, false)"
              />
              <img [src]="constants.staticImages.icons.deleteIcon" alt="" [pTooltip]="'Delete'" tooltipPosition="left" (click)="onDelete($event, notification.id, notification.readStatus)" />
            </span>
          </div>
        </div>
      </div>
    </ng-container>
  </ng-container>
  <ng-container [ngTemplateOutlet]="(isScrolling$ | async) ? loadingMoreTemplate : null"> </ng-container>
</div>

<ng-template #noDataFound>
  <span class="notification-info display-block d-flex justify-content-center h-100">
    <span class="m-auto">No Pending Notification</span>
  </span>
</ng-template>

<ng-template #loadingMoreTemplate>
  <div class="mb-3 w-100 text-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
  </div>
</ng-template>

<ng-template #loaderTemplate>
  <div class="notification-loader">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
  </div>
</ng-template>

<p-confirmPopup></p-confirmPopup>
