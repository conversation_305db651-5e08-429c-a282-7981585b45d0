<div class="pipeline-config">
  <div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
    <h4 class="header-title">{{ title }}</h4>
    <span *ngIf="isEditMode" class="created-by">
      <span class="bold-text">#{{ stockNumber }}</span>
      Created By <span class="bold-text">{{ createdBy }}</span> on{{ createdDate | date: constants.monthDateAndYearFormat }} at
      {{ createdDate | date: constants.time }}
    </span>
    <div>
      <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
    </div>
  </div>

  <form [formGroup]="soldTruckGroup" (ngSubmit)="onSubmit()" class="task-add">
    <div class="content">
      <p-accordion class="nested-accordion" [multiple]="true">
        <p-accordionTab [(selected)]="accordionTabs.generalInfo">
          <ng-template pTemplate="header">
            <div class="accordion-header" [ngClass]="{ active: accordionTabs.generalInfo }">
              <span>General Information</span>
              <em class="pi" [ngClass]="accordionTabs.generalInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
            </div>
          </ng-template>
          <ng-template pTemplate="content">
            <ng-container [ngTemplateOutlet]="soldTruckGeneralTemplate"></ng-container>
          </ng-template>
        </p-accordionTab>
        <p-accordionTab [(selected)]="accordionTabs.pipeline">
          <ng-template pTemplate="header">
            <div class="accordion-header" [ngClass]="{ active: accordionTabs.pipeline }">
              <span>Pipeline Information</span>
              <em class="pi" [ngClass]="accordionTabs.pipeline ? 'pi-angle-up' : 'pi-angle-down'"></em>
            </div>
          </ng-template>
          <ng-template pTemplate="content">
            <ng-container [ngTemplateOutlet]="soldTruckPipelineTemplate"></ng-container>
          </ng-template>
        </p-accordionTab>
        <p-accordionTab [(selected)]="accordionTabs.task" *ngIf="isEditMode">
          <ng-template pTemplate="header">
            <div class="accordion-header" [ngClass]="{ active: accordionTabs.task }">
              <span>Task Information</span>
              <em class="pi" [ngClass]="accordionTabs.task ? 'pi-angle-up' : 'pi-angle-down'"></em>
            </div>
          </ng-template>
          <ng-template pTemplate="content">
            <ng-container [ngTemplateOutlet]="soldTaskTemplate"></ng-container>
          </ng-template>
        </p-accordionTab>
        <p-accordionTab [(selected)]="accordionTabs.communication" *ngIf="isEditMode">
          <ng-template pTemplate="header">
            <div class="accordion-header" [ngClass]="{ active: accordionTabs.communication }">
              <span>Communication</span>
              <em class="pi" [ngClass]="accordionTabs.communication ? 'pi-angle-up' : 'pi-angle-down'"></em>
            </div>
          </ng-template>
          <ng-template pTemplate="content">
            <ng-container [ngTemplateOutlet]="soldCommunicationTemplate"></ng-container>
          </ng-template>
        </p-accordionTab>
      </p-accordion>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
      <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
      <button class="btn btn-primary" type="button" appShowLoaderOnApiCall (click)="onSubmitAndAddNew()" *ngIf="!isEditMode">Save & Add New</button>
    </div>
  </form>
</div>

<ng-template #soldTruckGeneralTemplate [formGroup]="soldTruckGroup">
  <div class="row">
    <div class="col-lg-12 col-md-12 col-12">
      <section>
        <div class="row">
          <div class="col-lg-3 col-md-6 col-12" *ngIf="!isEditMode">
            <label class="required">Stock</label>
            <p-dropdown
              appendTo="body"
              [options]="stockBasicInfo"
              formControlName="stockNumber"
              optionLabel="stockNumber"
              [showClear]="true"
              optionValue="id"
              [filter]="true"
              filterBy="stockNumber"
              [virtualScroll]="true"
              [itemSize]="30"
              placeholder="Select stock"
              [disabled]="isEditMode"
              (onChange)="selectedStockNumberByDetail($event.value)"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.stockNumbers, data: stockBasicInfo }"></ng-container>
              </ng-template>
              <ng-template let-stock pTemplate="item">
                <span>{{ stock.stockNumber }}</span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="soldTruckGroup.controls?.stockNumber"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label class="required">Status</label>
            <p-dropdown
              appendTo="body"
              [options]="status"
              formControlName="status"
              optionLabel="name"
              optionValue="value"
              [filter]="true"
              filterBy="name"
              [showClear]="true"
              placeholder="Select a Status"
            >
            </p-dropdown>
            <app-error-messages [control]="soldTruckGroup.controls?.status"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label class="required">Pipeline Owner</label>
            <p-dropdown
              appendTo="body"
              [options]="pipelineOwnerList"
              formControlName="pipelineOwnerId"
              optionLabel="name"
              [showClear]="true"
              optionValue="id"
              [filter]="true"
              filterBy="stockNumber"
              [virtualScroll]="true"
              [itemSize]="30"
              placeholder="Select pipeline owner"
              (onChange)="changePipelineOwner($event.value)"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.pipelineOwner, data: pipelineOwnerList }"></ng-container>
              </ng-template>
              <ng-template let-pipelineOwner pTemplate="item">
                <span>{{ pipelineOwner.name }}</span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="soldTruckGroup?.controls?.pipelineOwnerId"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <label>Target Completed Date</label>
            <p-calendar appendTo="body" formControlName="targetCompletedDate" [showIcon]="true" [showButtonBar]="true" [readonlyInput]="true" inputId="targetCompletedDateIcon">
            </p-calendar>
            <app-error-messages [control]="soldTruckGroup?.controls?.targetCompletedDate"></app-error-messages>
          </div>
        </div>
        <div class="row mt-2">
          <div class="col-md-6 col-lg-3 col-12">
            <label>Unit Type</label>
            <div>
              <span>{{ stockGeneralBasicInfo?.unitType?.name }}</span>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 col-12">
            <label>Year</label>
            <div>
              <span>{{ stockGeneralBasicInfo?.year }}</span>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 col-12">
            <label>Make</label>
            <div>
              <span>{{ stockGeneralBasicInfo?.make?.name }}</span>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 col-12">
            <label>Model</label>
            <div>
              <span>{{ stockGeneralBasicInfo?.unitModel?.name }}</span>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 col-12">
            <label>VIN#</label>
            <div>
              <span>{{ stockGeneralBasicInfo?.vin }}</span>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 col-12">
            <label>Designation</label>
            <div>
              <span>{{ stockGeneralBasicInfo?.designation?.name }}</span>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 col-12">
            <label>Owned By</label>
            <div>
              <span>{{ stockGeneralBasicInfo?.owner?.name }}</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</ng-template>

<ng-template #soldTruckPipelineTemplate [formGroup]="soldTruckGroup">
  <div class="add-scrollbar">
    <section>
      <div class="row">
        <div class="col-lg-6 col-12">
          <div class="pipeline-title">
            <span class="required">Pipeline</span>
            <ng-container *appHasPermission="[permissionActions.CREATE_PIPELINE_CONFIG]">
              <button
                class="btn btn-primary add-btn"
                id="addMakeBtn"
                type="button"
                *ngIf="!isViewMode && !isEditMode"
                [appImageIconSrc]="constants.staticImages.icons.add"
                (click)="showPipelineConfigModal = true"
              ></button>
            </ng-container>
          </div>
          <div class="row">
            <div class="col-6">
              <p-dropdown
                appendTo="body"
                [options]="templatePipeLineInfo"
                formControlName="pipelineType"
                optionLabel="title"
                [disabled]="isEditMode"
                optionValue="id"
                [filter]="true"
                filterBy="pipelineType"
                [showClear]="true"
                placeholder="Select pipeline"
                (onChange)="selectedTemplatedByShop($event.value)"
              >
                <ng-template pTemplate="empty">
                  <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.stockNumbers, data: templatePipeLineInfo }"> </ng-container>
                </ng-template>
                <ng-template let-pipeline pTemplate="item">
                  <span>{{ pipeline.title }}</span>
                </ng-template>
              </p-dropdown>
              <app-error-messages [control]="soldTruckGroup?.controls?.pipelineType"></app-error-messages>
            </div>
          </div>
          <div class="d-flex mt-4">
            <div class="pipeline-config-info p-r-20">
              <p-timeline [value]="templatePipelinePhasesFormArray.controls | keyvalue: returnZero">
                <ng-template pTemplate="marker" let-event>
                  <span [class]="event?.value?.value?.taskStatus" appPipelineStatus>{{ getSequenceNumber(event.key) }}</span>
                </ng-template>
              </p-timeline>
            </div>
            <div>
              <p-table [value]="templatePipelinePhasesFormArray.controls" responsiveLayout="scroll" class="draggable" pDroppable="products" (onDrop)="drop()">
                <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
                  <ng-container formArrayName="phases">
                    <tr [pReorderableRow]="rowIndex" [pReorderableRowDisabled]="isEditMode && templatePipelineData[rowIndex]" [formGroupName]="rowIndex">
                      <td>
                        <span class="pi pi-bars" [ngClass]="{ hidden: isEditMode && templatePipelineData[rowIndex] }" pReorderableRowHandle></span>
                      </td>
                      <td class="pipeline-col-width">
                        <p-dropdown
                          appendTo="body"
                          [panelStyle]="{ display: 'none' }"
                          class="dropdown-disabled"
                          [options]="getAvailableShops(rowIndex)"
                          [disabled]="isEditMode && templatePipelineData[rowIndex]"
                          formControlName="shopId"
                          optionLabel="name"
                          optionValue="id"
                          [filter]="true"
                          filterBy="name"
                          placeholder="Select a shop"
                          [name]="'shop' + rowIndex"
                        >
                          <ng-template pTemplate="empty"> </ng-template>
                          <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.shops, data: getAvailableShops(rowIndex) }"> </ng-container>
                          <ng-template let-shop pTemplate="item">
                            <span>{{ shop.name }}</span>
                          </ng-template>
                        </p-dropdown>
                        <app-error-messages [control]="getCurrentTemplatePipelineFormGroup(rowIndex).controls?.shopId"> </app-error-messages>
                      </td>
                      <td class="pipeline-col-width">
                        <p-dropdown
                          appendTo="body"
                          [options]="users"
                          [showClear]="true"
                          [disabled]="isEditMode && templatePipelineData[rowIndex]"
                          formControlName="assigneeId"
                          optionLabel="name"
                          optionValue="id"
                          [filter]="true"
                          filterBy="name"
                          placeholder="Select a user"
                          [name]="'assignee' + rowIndex"
                        >
                          <ng-template pTemplate="empty">
                            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.shopUsers, data: users }"> </ng-container>
                          </ng-template>
                          <ng-template let-assignee pTemplate="item">
                            <span>{{ assignee.name }}</span>
                          </ng-template>
                        </p-dropdown>
                        <app-error-messages [control]="getCurrentTemplatePipelineFormGroup(rowIndex).controls?.assigneeId"> </app-error-messages>
                      </td>
                      <td *ngIf="!(isEditMode && templatePipelineData[rowIndex])">
                        <img
                          [src]="constants.staticImages.icons.deleteIcon"
                          alt=""
                          class="delete-shop"
                          (click)="onDeleteShop(rowIndex, templatePipelinePhasesFormArray.controls)"
                        />
                      </td>
                    </tr>
                  </ng-container>
                </ng-template>
              </p-table>
            </div>
          </div>
        </div>
        <div class="show-label col-lg-6 col-12">
          <div class="pipeline-title">
            <span>Available Shops</span>
          </div>
          <div class="d-flex outer-box">
            <div *ngFor="let shop of templateByAvailableShopInfo; let i = index">
              <div class="product-item" pDraggable="products" (onDragStart)="dragStart(shop)" (onDragEnd)="dragEnd()">
                <div class="product-list-detail">
                  <span class="pi pi-bars m-l-10 inner-box"
                    ><span class="m-l-10">{{ shop.name }}</span></span
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="m-t-10 grey-color">You can drag and drop the shop to pipeline</div>
        </div>
      </div>
    </section>
  </div>
  <div class="d-flex justify-content-end mt-2" *ngIf="isEditMode">
    <button class="btn btn-secondary" type="button" (click)="reset()">Reset</button>
    <button class="btn btn-primary m-l-10" type="button" (click)="pipelineLock()" appShowLoaderOnApiCall>Pipeline Save</button>
  </div>
</ng-template>

<ng-template #soldTaskTemplate>
  <div class="d-flex">
    <div class="pipeline-config-task p-r-20">
      <p-timeline [value]="tasks">
        <ng-template pTemplate="marker" let-event>
          <span [class]="event.taskStatus?.name" appPipelineStatus>{{ event.pipelineSequence }}</span>
        </ng-template>
      </p-timeline>
    </div>
    <div class="w-100">
      <p-table
        class="no-column-selection"
        [value]="tasks"
        responsiveLayout="scroll"
        sortMode="single"
        [customSort]="true"
        [lazy]="true"
        [reorderableColumns]="true"
        [sortField]="'id'"
        [rowHover]="true"
        [loading]="isLoading"
        [scrollable]="true"
        scrollDirection="horizontal"
      >
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th scope="col" pFrozenColumn>#Id</th>
            <th scope="col">#Stock</th>
            <th scope="col">Shop</th>
            <th scope="col">Assignee</th>
            <th scope="col">Summary</th>
            <th scope="col">Deadline</th>
            <th scope="col">Status</th>
            <th scope="col" class="small-col">Active</th>
            <th scope="col">Action</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
          <tr class="h-65px">
            <td class="pipeline-col-width view-task" (click)="onViewEdit(rowData, false)" pFrozenColumn>{{ rowData?.id }}</td>
            <td class="pipeline-col-width">{{ rowData?.stockNumber }}</td>
            <td class="pipeline-col-width">{{ rowData?.shop?.name }}</td>
            <td class="pipeline-col-width">{{ rowData?.assignee?.name }}</td>
            <td class="pipeline-col-width">{{ rowData?.summary }}</td>
            <td class="timeline pipeline-col-width">
              <div class="footer" *ngIf="rowData?.endDate">
                <span class="date"><em class="pi pi-clock"></em> {{ rowData?.endDate | date: constants.monthAndDateFormat }}</span>
              </div>
            </td>
            <td>
              <p-dropdown
                class="w-60 sold-task-list"
                appendTo="body"
                [options]="taskStatuses"
                (click)="findStatusIndex(rowIndex)"
                optionLabel="name"
                [(ngModel)]="rowData.taskStatus.id"
                optionValue="id"
                (onChange)="changeStatus(rowData, rowData?.taskStatus?.id, rowData?.id, $event)"
              >
                {{ rowData?.taskStatus?.name }}
              </p-dropdown>
            </td>
            <td class="content-center">
              <ui-switch [(ngModel)]="rowData.isActive" [loading]="selectedTask?.id === rowData.id && isArchiveInProgress" (change)="onArchive(rowData, $event)">
                <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedTask?.id === rowData.id"></fa-icon>
              </ui-switch>
            </td>
            <td>
              <div class="content-between">
                <img [src]="constants.staticImages.icons.edit" (click)="onViewEdit(rowData, true)" alt="" />
              </div>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <td [colSpan]="8" class="no-data">No data to display</td>
        </ng-template>
      </p-table>
    </div>
  </div>
</ng-template>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<ng-template #soldCommunicationTemplate>
  <app-sold-truck-board-communication [soldTruckInfo]="soldTruckInfo" [selectedCommentId]="selectedCommentId"></app-sold-truck-board-communication>
</ng-template>

<p-sidebar
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  appendTo="body"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-task-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal"> </app-task-add>
</p-sidebar>
<p-confirmPopup *ngIf="!showConfirmationDialog"></p-confirmPopup>
<p-confirmDialog header="Confirmation" styleClass="confirm-dialog" *ngIf="showConfirmationDialog" [breakpoints]="{ '960px': '60vw', '640px': '90vw' }" [style]="{ width: '30vw' }">
</p-confirmDialog>
<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showPipelineConfigModal"
  position="right"
  (onHide)="showPipelineConfigModal = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  [fullScreen]="true"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-pipeline-add *ngIf="showPipelineConfigModal" (onClose)="onPipelineAddEditPopupClose($event)"></app-pipeline-add>
</p-sidebar>
