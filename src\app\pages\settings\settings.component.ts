import { AfterViewChecked, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { BaseComponent } from '@core/utils';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { TabDirective, TabsetComponent } from 'ngx-bootstrap/tabs';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
})
export class SettingsComponent extends BaseComponent implements OnInit, AfterViewChecked {
  currentUser!: Account | null;
  @ViewChild('profileTabs') profileTabs?: TabsetComponent;

  constructor(private readonly authService: AuthService,
    private readonly router: Router,
    private readonly cdf: ChangeDetectorRef) {
    super();
    this.pageTitle = 'Profile';
  }

  async ngOnInit(): Promise<void> {
    this.authService.getCurrentUser$().pipe(takeUntil(this.destroy$)).subscribe(user => {
      this.currentUser = user;
      this.cdf.detectChanges();
    })
  }

  ngAfterViewChecked(): void {
    if (this.router.url.includes(this.path.settings.changePassword)) {
      if (this.profileTabs?.tabs[1]) {
        this.profileTabs.tabs[1].active = true;
        this.cdf.detectChanges();
      }
    } else {
      if (this.profileTabs?.tabs[0]) {
        this.profileTabs.tabs[0].active = true;
        this.cdf.detectChanges();
      }
    }
  }

  onSelectTab(data: TabDirective): void {
    if (data.heading === 'Change Password') {
      this.router.navigate(['settings', 'change-password']);
    } else {
      this.router.navigate(['settings', 'account'])
    }
  }
}
