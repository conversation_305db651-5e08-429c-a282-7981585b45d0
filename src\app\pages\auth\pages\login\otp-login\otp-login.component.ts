
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BaseComponent } from '@core/utils';
import { ROUTER_UTILS } from '@core/utils/router.utils';
import { environment } from '@env/environment';
@Component({
  templateUrl: './otp-login.component.html',
  styleUrls: ['./otp-login.component.scss'],
})
export class OtpLoginComponent extends BaseComponent {
  returnUrl: string;
  enableOtpBasedLogin = environment.enableOtpBasedLogin;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
  ) {
    super();
    this.returnUrl = this.activatedRoute.snapshot.queryParamMap.get('returnUrl') || `/${ROUTER_UTILS.config.base.dashboard}`;
  }

}
