@import "src/assets/scss/variables";

::ng-deep .search-vehicle {
  p-radiobutton {
    padding-left: 5px;
    width: 100%;

    label {
      line-height: 50px !important;
      width: 100%;
      cursor: pointer;
    }
  }
}

.radio-wrapper {
  border: 1px solid var(--border-color);
  border-radius: 9px;
}

@media (min-width: 1200px) {
  .radio-wrapper.col-xl-2 {
    width: 250px;
  }

  .col-md-2\.5 {
    width: 22%;
  }
}

.active-border {
  border-color: var(--active-color) !important;
}

.search-action {
  display: flex;
  align-items: flex-end;

  button {
    height: 48px;
  }
}

.result-title {
  color: $placeholder-color;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  margin-bottom: 0.3rem !important;
}

.result-detail {
  span {
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
    color: var(--light-black-color);
  }
}

.result-wrapper {
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 9px;

  &:hover {
    cursor: pointer;
    border-color: var(--active-color);
  }
}
