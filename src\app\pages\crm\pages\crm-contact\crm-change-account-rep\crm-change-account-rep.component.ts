import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CrmContactListItem } from '@pages/administration/models/crm.model';
import { User } from '@pages/calendar/event-calendar/models/evnet-calendar';
import { ChangeAccountRepParams } from '@pages/crm/models/crm-contact-reminder.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-crm-change-account-rep',
  templateUrl: './crm-change-account-rep.component.html',
  styleUrls: ['./crm-change-account-rep.component.scss']
})
export class CrmChangeAccountRepComponent extends BaseComponent implements OnInit {
  title = 'Change Account Reporter';
  accountRepFormGroup!: FormGroup;
  AccountRepList: User[] = [];
  loaders = {
    accountRep: false
  }

  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() selectedContacts: CrmContactListItem[] = [];

  constructor(private readonly formBuilder: FormBuilder,
    private readonly toasterService: AppToasterService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly confirmationService: ConfirmationService,
    private readonly crmService: CrmService,
  ) { super(); }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getAccountRepList();
  }

  private initializeFormGroup(): void {
    this.accountRepFormGroup = this.formBuilder.group({
      accountReporterId: new FormControl(null, Validators.required)
    });
  }

  getAccountRepList(): void {
    this.loaders.accountRep = true;
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.AccountRepList = res.filter(user => {
            return user.roleName === 'Manager' || user.roleName === 'Salesperson'
          })
          this.loaders.accountRep = false;
        },
        error: () => {
          this.loaders.accountRep = false;
        }
      });
  }

  onCancel(): void {
    this.onClose.emit(false);
  }

  get changeAccountRepParams(): ChangeAccountRepParams {
    return {
      ids: this.selectedContacts.map(contact => Number(contact.id)),
      accountRepId: this.accountRepFormGroup.value.accountReporterId
    };
  }

  onSubmit(event?: Event): void {
    if (this.accountRepFormGroup.invalid) {
      this.accountRepFormGroup.markAllAsTouched();
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.assignReporterToSelectedContactsConfirmation,
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.save();
      }
    });
  }

  save(): void {
    const endpoint = `${API_URL_UTIL.admin.crm.contact}/${API_URL_UTIL.admin.crm.reporter}`;
    this.crmService.patch(this.changeAccountRepParams, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.AccountRepUpdateSuccess);
      this.onClose.emit(true);
    });
  }
}
