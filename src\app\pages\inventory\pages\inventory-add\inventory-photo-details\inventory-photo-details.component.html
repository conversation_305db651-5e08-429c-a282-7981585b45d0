<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form [formGroup]="photoDetailFormGroup" (ngSubmit)="onSubmit()" [ngClass]="{ loading: isLoading }">
  <div class="content" *ngIf="!isLoading; else loaderTemplate">
    <section>
      <div class="row">
        <div class="col-12">
          <img [src]="imageDetails?.fileUrl ? imageDetails?.fileUrl : imageDetails?.binary" alt="" class="image-display" />
        </div>
        <div class="col-12">
          <label>Add Description</label>
          <textarea formControlName="detail" placeholder="Add Description here" rows="4"></textarea>
        </div>
        <div class="photos-switch" *ngIf="imageDetails?.photoType === inventoryPhotoType.PUBLIC_PHOTO">
          <label class="switch">
            <input type="checkbox" [ngModel]="imageDetails?.displayPicture" (change)="displayPictureToogle()" [ngModelOptions]="{ standalone: true }" />
            <span class="slider round"></span>
          </label>
          <span class="toggle-label">Thumbnail</span>
        </div>
        <div class="previous-next-btn-wrapper">
          <button class="btn btn-primary" type="button" [disabled]="shouldPreviousBtnDisable()" (click)="onPreviousImage()">Previous</button>
          <button class="btn btn-primary" type="button" [disabled]="shouldNextBtnDisable()" (click)="onNextImage()">Next</button>
        </div>
      </div>
    </section>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>
