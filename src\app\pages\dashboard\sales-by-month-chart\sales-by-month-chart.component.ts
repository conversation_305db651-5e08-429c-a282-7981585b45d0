import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { distinctUntilChanged, takeUntil } from 'rxjs';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';
import { MonthLabel, SalesByMonthColor, YearlySales } from '../dashboard.model';
import { DashboardService } from '../dashboard.service';

@Component({
  selector: 'app-sales-by-month-chart',
  templateUrl: './sales-by-month-chart.component.html',
  styleUrls: ['./sales-by-month-chart.component.scss']
})
export class SalesByMonthChartComponent extends BaseComponent implements OnInit {
  dataCountArray: any[] = [];
  isDarkMode = false;
  dataLabels: any[] = [];
  yearlySales: YearlySales[] = [];
  data!: any;
  multiAxisOptions!: any;
  @Input() isFullViewSalesByMonth!: boolean;
  saleByMonth = 'Monthly Sales Summary for all users';
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(private readonly themeService: ThemeService, private readonly dashboardService: DashboardService, private readonly cdf: ChangeDetectorRef) { super(); }

  ngOnInit(): void {
    this.subscribeToTheme();
    this.getSaleBymonth();
  }

  getSaleBymonth(): void {
    this.dashboardService.get<YearlySales[]>(API_URL_UTIL.dashboard.yearlySales)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.calculateYearlySalesData(data);
          this.cdf.detectChanges();
          this.ShowSalesByMonthChart();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  private calculateYearlySalesData(data: YearlySales[]): void {
    this.yearlySales = [];
    this.dataCountArray = [];
    for (let i = 1; i <= 12; i++) {
      const obj = { month: i, count: 0 };
      this.yearlySales.push(obj);
    }
    this.yearlySales.forEach((yearlyData) => {
      const match = data.find(element => element.month === yearlyData.month);
      if (match) {
        yearlyData.count = match.count;
      }
      this.dataCountArray.push(yearlyData.count);
    });
  }


  ShowSalesByMonthChart(): void {
    this.data = {
      labels: [MonthLabel.JAN, MonthLabel.FEB, MonthLabel.MAR, MonthLabel.APR, MonthLabel.MAY, MonthLabel.JUN, MonthLabel.JULY, MonthLabel.AUG, MonthLabel.SEPT, MonthLabel.OCT, MonthLabel.NOV, MonthLabel.DEC],
      datasets: [
        {
          label: this.saleByMonth,
          fill: true,
          borderColor: SalesByMonthColor.CHART_BORDER_COLOR,
          yAxisID: 'y',
          tension: 0.4,
          data: this.dataCountArray,
          backgroundColor: SalesByMonthColor.CHART_BACK_COLOR,
        },
      ],
    };
    this.multiAxisOptions = {
      stacked: false,
      plugins: {
        legend: {
          labels: {
            color: this.isDarkMode ? 'white' : 'black',
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: this.isDarkMode ? 'white' : 'black',
          },
          grid: {
            color: SalesByMonthColor.CHART_GRID_COLOR,
          }
        },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          ticks: {
            color: this.isDarkMode ? 'white' : 'black',
          },
          grid: {
            color: SalesByMonthColor.CHART_GRID_COLOR,
          }
        },

      }
    };
  }

  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        if (theme === THEMES.DARK) {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
        this.ShowSalesByMonthChart();
        this.cdf.detectChanges();
      });
  }

  onCancel(): void {
    this.onClose.emit(true);
  }
}
