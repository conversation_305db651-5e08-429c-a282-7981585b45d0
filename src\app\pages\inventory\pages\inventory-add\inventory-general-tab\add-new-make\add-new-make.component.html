<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form [formGroup]="makeFormGroup" (ngSubmit)="onSubmit()" [ngClass]="{ loading: isLoading }">
  <div class="content" *ngIf="!isLoading; else loaderTemplate">
    <div class="row">
      <div class="col-12" *ngIf="isAddEdit">
        <label for="unitTypeCategoryId" class="required">Category</label>
        <p-dropdown [options]="categories" placeholder="Select Category" formControlName="categoryId" optionLabel="name" optionValue="id"> </p-dropdown>
      </div>
      <div class="col-12 mb-4">
        <label class="required">Make</label>
        <input class="form-control" type="text" placeholder="Enter Make" formControlName="name" />
        <app-error-messages [control]="makeFormGroup.controls.name"></app-error-messages>
      </div>
    </div>
    <ng-container formArrayName="models">
      <label class="mb-2 required">Models</label>
      <ng-container *ngFor="let model of modelsFormArray.controls; let i = index">
        <div class="row align-items-center model-group" [formGroupName]="i">
          <div class="col-8">
            <input class="form-control" type="text" placeholder="Enter Model" formControlName="name" />
          </div>
          <div class="col-4">
            <div class="add-model">
              <img *ngIf="i || modelsFormArray.value.length !== 1" [src]="constants.staticImages.icons.deleteIcon" alt="" class="delete-shop" (click)="onDeleteModel(i)" />
              <button
                class="btn btn-primary"
                id="addShopBtn"
                type="button"
                [appImageIconSrc]="constants.staticImages.icons.addNew"
                (click)="onAddNewModel()"
                *ngIf="isAddNewModelVisible(i)"
              >
                <span class="hide-label">Add Model</span>
              </button>
            </div>
          </div>
          <small class="text-danger error-msg mt-1" *ngIf="modelsFormArray.controls[i].get('name')?.touched || modelsFormArray.controls[i].get('name')?.dirty">
            <span *ngIf="modelsFormArray.controls[i].get('name')?.hasError('required')"> This field is required </span>
            <span *ngIf="modelsFormArray.controls[i].get('name')?.errors?.maxlength"> Maximum 50 characters are allowed </span>
          </small>
        </div>
      </ng-container>
    </ng-container>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>
