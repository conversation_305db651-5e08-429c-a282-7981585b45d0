import { Constants } from "@constants/*";
export function getRefactorFileName(fileName: string): string {
  const modifiedFileName = fileName.replace(Constants.regexForSpecialCharacters, '');
  return modifiedFileName;
}
export function allowExtensions(fileName: string,supportExtension:string): boolean {
  const extensionDot = fileName?.lastIndexOf('.');
        const ext = fileName?.substring(extensionDot + 1);
        if (!supportExtension.includes(ext)) {
          return false;
        }
  return true;
}