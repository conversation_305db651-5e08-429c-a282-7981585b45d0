@media only screen and (max-width: 500px) {
  ::ng-deep .server-pagination {
    display: block !important;

    .items-per-page {
      margin-bottom: 1.5rem;
    }
  }
}

::ng-deep .server-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 25px 0px 0px;

  .pagination {
    margin: 0;
  }

  .page-item.active .page-link {
    color: var(--white-color) !important;
    background-color: var(--active-color) !important;
    border: none;
  }

  .page-link {
    color: var(--text-color);
    background-color: transparent !important;
    border-color: var(--gray-text-color);
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-radius: 4px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .page-item {
    margin: 0 5px;
  }

  .pagination-prev,
  .pagination-first,
  .pagination-next,
  .pagination-last {
    a {
      color: var(--text-color) !important;
      border: none;
      background-color: var(--app-background-color) !important;
      font-size: 25px;
      padding-top: 4px;
    }
  }

  .items-per-page {
    display: flex;
    align-items: center;

    .pagination-info {
      color: var(--text-color);
      font-size: 14px;
      letter-spacing: 0;
      line-height: 21px;
      margin-left: 15px;
    }

    label {
      font-size: 14px;
      letter-spacing: 0;
      line-height: 21px;
      color: var(--text-color);
    }

    .p-dropdown {
      height: 30px;
      margin-left: 5px;
      border-radius: 0.25rem;
      border-color: var(--gray-text-color) !important;
    }
    .p-dropdown-label {
      padding: 2.5px 0px 0px 11px;
    }
  }
  .p-dropdown-trigger {
    width: 2rem;
  }
}

::ng-deep .dropdown-menu {
  min-width: 4rem !important;
}
