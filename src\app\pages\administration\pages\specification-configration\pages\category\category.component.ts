import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { Category } from '../../models/specification.model';
import { CategoryService } from './category.service';

@Component({
  selector: 'app-category',
  templateUrl: './category.component.html',
  styleUrls: ['./category.component.scss']
})
export class CategoryComponent extends BaseComponent implements OnInit {

  categories!: Array<Category>;
  showCreateModal = false;
  selectedCategory!: Category | null;

  constructor(
    private readonly categoryService: CategoryService,
    private readonly cdf: ChangeDetectorRef,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService
  ) {
    super();
    this.pageTitle = 'Category';
  }

  ngOnInit(): void {
    this.unitTypeCategoryCounts();
  }

  unitTypeCategoryCounts(): void {
    this.isLoading = true;
    const endpoint = `${API_URL_UTIL.admin.categorySpecification.categoryCount}`
    this.categoryService.getList<Category>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.categories = res;
        this.cdf.detectChanges()
        this.isLoading = false;
      }
    });
  }

  onAddEditCategory(category?: Category): void {
    if (category?.id) {
      this.selectedCategory = category;
    }
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  addNewCategory(categoryDetails: Array<Category>): void {
    for (const category of categoryDetails) {
      this.categories.push({
        ...category,
        unitType: 0,
        make: 0,
        model: 0,
        specification: 1,
        inventoryCount: 0
      });
    }

    this.toasterService.success(MESSAGES.addSuccessMessage.replace('{item}', 'Categories'));
  }

  updateCategory(categoryDetails: Category): void {
    const selectedCategoryIndex = this.categories.findIndex((catgory: Category) => catgory.id === categoryDetails.id);
    this.categories[selectedCategoryIndex].name = categoryDetails.name;
    this.toasterService.success(MESSAGES.updateSuccessMessage.replace('{item}', 'Category'));
  }

  showDeleteModal(categoryDetails: Category, event: Event): void {
    if (categoryDetails.inventoryCount) {
      this.toasterService.warning(MESSAGES.categoryDeleteWarning);
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: `${MESSAGES.deleteWarning.replace('{record}', 'category data')} ${this.specificationMessage(categoryDetails)}`,
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteCategory(categoryDetails);
      }
    });
  }

  specificationMessage(cat: Category): string {
    return cat.unitType || cat.make || cat.model ? `It contains specification data.` : ``
  }

  onDeleteCategory(categoryDetails: Category): void {
    this.categoryService.delete(categoryDetails?.id).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.categories = this.categories.filter((catgory: Category) => catgory.id !== categoryDetails.id);
        this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('{item}', 'Category'));
      }
    });
  }

  closeModal(): void {
    this.showCreateModal = false;
    this.selectedCategory = null;
  }
}
