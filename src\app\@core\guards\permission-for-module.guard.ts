import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ROUTER_UTILS } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { Utils } from 'src/app/@shared/services';


@Injectable({
  providedIn: 'root'
})
export class PermissionForModuleGuard {

  constructor(
    private readonly authService: AuthService,
    private readonly router: Router
  ) { }

  canActivate(activatedRouteSnapshot: ActivatedRouteSnapshot): boolean {
    const permission = this.authService.getRoleInfo()?.privilegeActionResponseDTOs
    if (permission?.length) {
      if (Utils.hasModulePermission(permission, activatedRouteSnapshot.data.module)) {
        return true;
      }
    }
    this.router.navigate([ROUTER_UTILS.config.notAuthorized.root])
    return false;
  }
}
