import { Constants } from "@constants/*";
import { DropdownRouteName, MenuConfigModel } from "src/app/@shared/models";
import { ROUTER_UTILS } from "./router.utils";

export const MenuConfig: MenuConfigModel[] = [
  {
    name: 'Dashboard',
    iconPath: Constants.staticImages.icons.dashboardActive,
    hoverIconPath: Constants.staticImages.icons.dashboardHover,
    routerLink: [ROUTER_UTILS.config.base.dashboard],
  },
  {
    name: 'CRM',
    moduleKey: DropdownRouteName.CRM,
    iconPath: Constants.staticImages.icons.crmActive,
    hoverIconPath: Constants.staticImages.icons.crmHover,
    routerLink: [ROUTER_UTILS.config.crm.root],
    children: [
      {
        name: 'Customer Lead',
        routerLink: [ROUTER_UTILS.config.crm.root, ROUTER_UTILS.config.crm.crmCustomer.root],
      },
      {
        name: 'Contacts',
        routerLink: [ROUTER_UTILS.config.crm.root, ROUTER_UTILS.config.crm.crmContact.root],
      },
      {
        name: 'Tasks',
        routerLink: [ROUTER_UTILS.config.crm.root, ROUTER_UTILS.config.crm.crmTask.root],
      }
    ]
  },
  {
    name: 'Transport',
    moduleKey: DropdownRouteName.TRANSPORT,
    iconPath: Constants.staticImages.icons.transportActive,
    hoverIconPath: Constants.staticImages.icons.transportHover,
    routerLink: [ROUTER_UTILS.config.transport.root],
    children: [
      {
        name: 'Incoming Truck Tracking',
        routerLink: [ROUTER_UTILS.config.transport.root, ROUTER_UTILS.config.transport.incomingTruckBoard.root],
      },
      {
        name: 'Driver Scheduling Board',
        routerLink: [ROUTER_UTILS.config.transport.root, ROUTER_UTILS.config.transport.driverScheduleBoard.root],
      }
    ]
  },
  {
    name: 'Inventory',
    iconPath: Constants.staticImages.icons.inventoryActive,
    hoverIconPath: Constants.staticImages.icons.inventoryHover,
    routerLink: [ROUTER_UTILS.config.inventory.root],
  },
  // NOTE: The following modules are currently disabled per client's request.
  // {
  //   name: 'Pipeline',
  //   moduleKey: DropdownRouteName.PIPELINE,
  //   iconPath: Constants.staticImages.icons.pipelineActive,
  //   hoverIconPath: Constants.staticImages.icons.pipelineHover,
  //   routerLink: [ROUTER_UTILS.config.pipeline.root],
  //   children: [
  //     {
  //       name: 'Sold Truck Board',
  //       routerLink: [ROUTER_UTILS.config.pipeline.root, ROUTER_UTILS.config.pipeline.soldTruckBoard.root],
  //     },
  //     {
  //       name: 'Stock Truck Board',
  //       routerLink: [ROUTER_UTILS.config.pipeline.root, ROUTER_UTILS.config.pipeline.stockTruckBoard.root],
  //     },
  //   ]
  // },
  // {
  //   name: 'Shops',
  //   iconPath: Constants.staticImages.icons.shopActive,
  //   hoverIconPath: Constants.staticImages.icons.shopHover,
  //   routerLink: [ROUTER_UTILS.config.shops.root],
  // },
  {
    name: 'Reporting',
    moduleKey: DropdownRouteName.REPORTING,
    iconPath: Constants.staticImages.icons.reportActive,
    hoverIconPath: Constants.staticImages.icons.reportHover,
    routerLink: [ROUTER_UTILS.config.reporting.root],
    children: [
      {
        name: 'Vendors',
        routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.vendorsReport],
      },
      {
        name: 'Suppliers',
        routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.supplierReport],
      },
      {
        name: 'Customer Relationship Management',
        moduleKey: DropdownRouteName.CUSTOMER_RELATIONSHIP_MANAGEMENT,
        routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.crm.root],
        children: [
          {
            name: 'Daily Sales Report',
            moduleKey: DropdownRouteName.DAILY_SALES_REPORT,
            routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.crm.root, ROUTER_UTILS.config.reporting.crm.dailySalesReport.root],
          },
          {
            name: 'Activity Report',
            moduleKey: DropdownRouteName.ACTIVITY_REPORT,
            routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.crm.root, ROUTER_UTILS.config.reporting.crm.activity.root],
          },
          {
            name: 'Inventory Sales Report',
            moduleKey: DropdownRouteName.INVENTORY_SALES_REPORT,
            routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.crm.root, ROUTER_UTILS.config.reporting.crm.inventorySales.root],
          }
        ]
      },
      {
        name: 'Inventory',
        moduleKey: DropdownRouteName.INVENTORY_REPORTING,
        routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.inventoryReport.root],
        children: [
          {
            name: 'Profitability Report',
            moduleKey: DropdownRouteName.INVENTORY_PROFITABILITY,
            routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.inventoryReport.root, ROUTER_UTILS.config.reporting.inventoryReport.profitability.root],
          },
          {
            name: 'Inventory Aging Report',
            moduleKey: DropdownRouteName.INVENTORY_AGING,
            routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.inventoryReport.root, ROUTER_UTILS.config.reporting.inventoryReport.inventoryAging.root],
          }
        ]
      },
    ]
  },
  {
    name: 'Administration',
    moduleKey: DropdownRouteName.ADMIN,
    iconPath: Constants.staticImages.icons.adminActive,
    hoverIconPath: Constants.staticImages.icons.adminHover,
    routerLink: [ROUTER_UTILS.config.administration.root],
    children: [
      {
        name: 'Users',
        routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.users.root],
      },
      {
        name: 'Dealers',
        routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.dealers.root],
      },
      {
        name: 'Vendors',
        routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.vendors.root],
      },
      {
        name: 'Suppliers',
        routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.suppliers.root],
      },
      // NOTE: The following modules are currently disabled per client's request.
      // {
      //   name: 'Pipeline Config',
      //   routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.pipelineConfig.root],
      // },
      // {
      //   name: 'Shops',
      //   routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.shops],
      // },
      {
        name: 'Roles & Permissions',
        routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.roles],
      },
      {
        name: 'Public Page Config',
        moduleKey: DropdownRouteName.PUBLIC_PAGE_CONFIG,
        routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.publicPageConfig.root],
        children: [
          {
            name: 'Advertising Config',
            moduleKey: DropdownRouteName.ADVERTISING,
            routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.publicPageConfig.root, ROUTER_UTILS.config.administration.publicPageConfig.advertising.root],
          },
          {
            name: 'Quote Form Config',
            moduleKey: DropdownRouteName.QUOTE_FORM,
            routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.publicPageConfig.root, ROUTER_UTILS.config.administration.publicPageConfig.quoteForm.root],
          }
        ]
      },
      {
        name: 'Specifications Config',
        moduleKey: DropdownRouteName.SPECIFICATION_CONFIG,
        routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root],
        children: [
          {
            name: 'Category',
            moduleKey: DropdownRouteName.CATEGORY,
            routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root, ROUTER_UTILS.config.administration.specificationConfig.category.root],
          },
          {
            name: 'Unit Type',
            moduleKey: DropdownRouteName.UNITY_TYPE,
            routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root, ROUTER_UTILS.config.administration.specificationConfig.unitType.root],
          },
          {
            name: 'Make/Model',
            moduleKey: DropdownRouteName.MAKE_MODEL,
            routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root, ROUTER_UTILS.config.administration.specificationConfig.makeModel.root],
          },
          {
            name: 'Specification',
            moduleKey: DropdownRouteName.SPECIFICATION,
            routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root, ROUTER_UTILS.config.administration.specificationConfig.specification.root],
          }
        ]
      },

    ]
  },
]

// NOTE: The following modules are currently disabled per client's request.
// export const RightHeaderMenu: any = {
//   name: 'Calendar',
//   iconPath: Constants.staticImages.icons.calender,
//   hoverIconPath: Constants.staticImages.icons.calender,
//   routerLink: [ROUTER_UTILS.config.calendar.root],
// }
