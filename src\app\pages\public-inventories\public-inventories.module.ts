import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { FooterModule } from '@shell/ui/footer/footer.module';
import { AccordionModule } from 'primeng/accordion';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CarouselModule } from 'primeng/carousel';
import { CheckboxModule } from 'primeng/checkbox';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { EditorModule } from 'primeng/editor';
import { InputMaskModule } from 'primeng/inputmask';
import { InputNumberModule } from 'primeng/inputnumber';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SidebarModule } from 'primeng/sidebar';
import { SliderModule } from 'primeng/slider';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from 'src/app/@shared/pipes/pipes.module';
import { GoogleMapModule } from "../../@shared/components/google-map/google-map.component.module";
import { InventoryDetailsComponent } from './pages/inventory-details/inventory-details.component';
import { InventoryListComponent } from './pages/inventory-list/inventory-list.component';
import { QuoteFormComponent } from './pages/quote-form/quote-form.component';
import { PublicInventories } from './public-inventories-routing.module';
import { PublicInventoriesComponent } from './public-inventories.component';

@NgModule({
    declarations: [
        InventoryListComponent,
        InventoryDetailsComponent,
        PublicInventoriesComponent,
        QuoteFormComponent,
    ],
    imports: [
        CommonModule,
        PublicInventories,
        RadioButtonModule,
        FormsModule,
        ReactiveFormsModule,
        FooterModule,
        SharedComponentsModule,
        AccordionModule,
        CheckboxModule,
        PipesModule,
        CalendarModule,
        SidebarModule,
        CarouselModule,
        FontAwesomeIconsModule,
        DropdownModule,
        TooltipModule,
        DialogModule,
        InputNumberModule,
        InputMaskModule,
        GoogleMapModule,
        NgxGpAutocompleteModule,
        CardModule,
        SliderModule,
        EditorModule,
        DirectivesModule
    ],
    providers: [{
        provide: Loader,
        useValue: new Loader({
            apiKey: environment.googleMapApiKey,
            libraries: ['places']
        })
    }],
    exports: [
        InventoryDetailsComponent
    ]
})
export class PublicInventoriesModule { }
