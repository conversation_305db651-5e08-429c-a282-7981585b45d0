<div class="modal-title close">
  <h4 class="header-title">{{ pageTitle }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
</div>

<form [formGroup]="specificationGroupFormGroup" (ngSubmit)="onSubmit()">
  <div class="category-form">
    <div class="form-group">
      <label for="categoryName">Category</label>
      <input formControlName="categoryName" type="text" class="form-control" placeholder="Enter category" />
    </div>
    <div class="form-group">
      <label for="name" class="required">Name</label>
      <input formControlName="name" type="text" class="form-control" placeholder="Enter group name" />
      <app-error-messages [control]="specificationGroupFormGroup?.controls?.name"></app-error-messages>
    </div>
    <div class="d-flex align-items-center">
      <label for="includePreference" class="mr-5 mb-1">Multiple: </label>
      <ui-switch formControlName="multiple" name="multiple"> </ui-switch>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
    <button class="btn btn-primary d-flex align-items-center" type="submit" [disabled]="isLoading">
      Save
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isLoading"></fa-icon>
    </button>
  </div>
</form>
