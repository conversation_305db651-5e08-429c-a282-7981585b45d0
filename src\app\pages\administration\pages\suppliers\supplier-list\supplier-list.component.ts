import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { VendorListFilter, VendorListItem } from '@pages/administration/models';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { TabDirective } from 'ngx-bootstrap/tabs';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { DataType, TableColumn } from 'src/app/@shared/models';
import { PatchParam } from 'src/app/@shared/models/patch-param.model';
import { Utils } from 'src/app/@shared/services';
import { SuppliersService } from '../suppliers.service';

@Component({
  selector: 'app-supplier-list',
  templateUrl: './supplier-list.component.html',
  styleUrls: ['./supplier-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SupplierListComponent extends BaseComponent implements OnInit {
  cols: TableColumn[] = [];
  _selectedColumns: any[] = [];
  suppliers: VendorListItem[] = [];
  filterParams: VendorListFilter = new VendorListFilter();
  showCreateModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.SUPPLIERS;
  selectedSupplier!: VendorListItem | null;
  selectedSupplierExpenses!: VendorListItem | null;
  isActiveTab = true;
  isArchiveInProgress = false;
  isViewMode = false;
  showExpensesCreateModal = false;
  constructor(private readonly supplierService: SuppliersService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly activeRoute: ActivatedRoute
  ) {
    super();
    this.pageTitle = 'Suppliers';
    this.paginationConfig.predicate = 'name';
  }

  ngOnInit(): void {
    this.setTableColumns();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getSupplierById(Number(params.id));
        }
      });
  }

  getSupplierById(id: number) {
    this.supplierService.get<VendorListItem>(id).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.onEdit(res, res?.archived ? true : false);
      }
    });
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  onTabChanged(tabChangeEvent: TabDirective): void {
    if (tabChangeEvent.heading === 'Archived') {
      this.isActiveTab = false;
      this.filterParams.archived = true;
    } else {
      this.isActiveTab = true;
      this.filterParams.archived = false;
    }
    this.getAll();
  }

  setTableColumns() {
    this.cols = [
      { field: 'name', sortKey: 'name', header: 'Name', sortable: true, reorderable: true },
      { field: 'contactPersonName', header: 'Contact Person', reorderable: true },
      { field: 'email', header: 'Email', sortable: true, reorderable: true },
      { field: 'phoneNumber', header: 'Phone Number', reorderable: true, isATemplate: true },
      { field: 'addressCity', header: 'City', reorderable: true },
      { field: 'addressState', header: 'State', reorderable: true },
    ];
    this._selectedColumns = this.cols;
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.supplierService.getListWithFiltersWithPagination<VendorListFilter, VendorListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.suppliers.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.suppliers = res.content;
          this.setActiveFlagForAllSuppliers()
          this.setPaginationParamsFromPageResponse<VendorListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  private setActiveFlagForAllSuppliers() {
    this.suppliers.forEach(suppliers => suppliers.isActive = this.isActiveTab);
  }
  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedSupplier = null;
    this.isViewMode = false;
    if (refreshList) {
      this.getAll();
    }
  }

  onEdit(supplier: VendorListItem, isActiveTab: boolean): void {
    this.showCreateModal = true;
    this.selectedSupplier = supplier;
    this.isViewMode = isActiveTab ? true : false;
    this.cdf.detectChanges();
  }

  onArchive(supplier: VendorListItem, isActive: boolean): void {
    this.selectedSupplier = supplier;
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: supplier.isActive ? MESSAGES.archiveWarning.replace('{record}', 'supplier') : MESSAGES.unArchiveWarning.replace('{record}', 'supplier'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.isArchiveInProgress = true;
        this.onArchiveConfirmation(supplier);
        supplier.isActive = isActive;
      },
      reject: () => {
        supplier.isActive = !isActive;
        this.cdf.detectChanges();
      }
    });
  }

  private onArchiveConfirmation(supplier: VendorListItem): void {
    this.supplierService.patch(this.getPatchRequestParams(supplier))
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.isActiveTab ? MESSAGES.supplierArchiveSuccess : MESSAGES.supplierUnArchiveSuccess);
          this.isArchiveInProgress = false;
          this.selectedSupplier = null;
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedSupplier = null;
          this.getAll();
        }
      });
  }

  private getPatchRequestParams(supplier: VendorListItem): PatchParam {
    return {
      id: supplier.id,
      values: [{
        key: 'archived',
        value: `${this.isActiveTab}`,
        dataType: DataType.BOOLEAN
      }]
    }
  }

  onViewExpensesList(supplier: VendorListItem): void {
    this.showExpensesCreateModal = true;
    this.selectedSupplierExpenses = supplier;
  }

  onAddEditExpensePopupClose(refreshList: boolean): void {
    this.showExpensesCreateModal = false;
    this.selectedSupplierExpenses = null;
    if (refreshList) {
      this.getAll();
    }
  }

  onBackdropClick(element: HTMLElement): void {
    Utils.handlePopoverBackdropClick(element, this.selectedSupplier, 'isActive', this.isActiveTab, this.cdf);
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
