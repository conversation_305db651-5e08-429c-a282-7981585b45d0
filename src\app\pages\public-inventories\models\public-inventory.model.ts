import { FundingEmail } from '@pages/administration/models';
import { UnitImages } from "@pages/crm/models/customer-inventory.model";
import { AddInventorySpecificationParams, AssociationsUnits, GeneralInformation, InternetOption, Odometer } from "@pages/inventory/models";
import { DocumentListItem } from '@pages/inventory/models/documents.model';
import { NotesDetailsResponse } from '@pages/inventory/models/notes.model';

export interface PublicInventory {
  archived: boolean;
  createdBy: InventoryCreatedBy;
  createdDate: string;
  id: number
  incomingTruckId: number;
  retailAskingPrice: number;
  unitImages: UnitImages;
  generalInformation: GeneralInformation;
  odometer: Odometer;
  unitAssociations: Array<AssociationsUnits>;
  specifications?: AddInventorySpecificationParams;
  notes?: Array<NotesDetailsResponse>;
  documents?: Array<DocumentListItem>;
  delear: DealerDetails;
  accordionTabs: AccordianTabs;
  toggleTab: boolean;
  internetOption: InternetOption;
}

export interface AccordianTabs {
  contactInfo: boolean,
  specification: boolean,
  notes: boolean,
  documents: boolean
}

export interface InventoryCreatedBy {
  id: number;
  name: string;
}

export interface PublicImages {
  id: number;
  url: string;
  detail: string;
  photoType: string;
  componentConditionId: string;
  unitId: number;
  fullUrl: string;
  displayPicture: boolean;
  imageOrder: number;
}
export interface FilterCategories {
  id: number;
  name: string;
  parentUnitTypeCategoryId: number;
}

export interface FilterUnitTypes {
  id: number;
  name: string;
  unitTypeCategoryId: number;
  unitTypeCategoryName: string;
  inventoryCount?: number;
}

export interface FilterMakes {
  id: number;
  name: string;
  categoryId: number;
  categoryName: string;
  inventoryCount?: number;
}

export interface FilterModels {
  id: number;
  name: string;
  makeId: number;
  categoryName: string;
  makeName: string;
}

export interface SortingOptions {
  value: number;
}

export interface OrderParams {
  ascending?: boolean;
  field?: string;
}

export interface DealerDetails {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  abbreviation: string;
  address: Address;
  contactPerson: ContactPerson;
  organizationId: number;
  deleted: boolean;
  archived: boolean;
  createdBy: string;
  createdDate: string;
  fundingEmails: FundingEmail[];
}

export interface Address {
  id: number;
  streetAddress: string;
  city: string;
  state: string;
  zipcode: string;
  latitude: number;
  longitude: number;
}

export interface ContactPerson {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
}

export interface QuoteField {
  dataType: string;
  for: string;
  isDefault: boolean;
  isRequired: boolean;
  label: string;
  options: string[];
  placeholder: string;
  value: string | null;
}

export interface Field {
  key: string;
  value: any;
}