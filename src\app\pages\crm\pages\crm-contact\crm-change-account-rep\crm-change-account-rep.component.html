<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form [formGroup]="accountRepFormGroup" [ngClass]="{ loading: isLoading }">
  <div class="content" *ngIf="!isLoading; else loaderTemplate">
    <section>
      <div class="row">
        <label class="required">Account Reporter</label>
        <p-dropdown
          appendTo="body"
          [options]="AccountRepList"
          formControlName="accountReporterId"
          optionLabel="name"
          [showClear]="false"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select Account Reporter"
          [virtualScroll]="true"
          [itemSize]="30"
        >
          <ng-template pTemplate="empty">
            <ng-container
              [ngTemplateOutlet]="emptyMessage"
              [ngTemplateOutletContext]="{ loader: loaders.accountRep, data: AccountRepList }"
            ></ng-container>
          </ng-template>
          <ng-template let-pipelineOwner pTemplate="item">
            <span>{{ pipelineOwner.name }}</span>
          </ng-template>
        </p-dropdown>
      </div>
      <app-error-messages [control]="accountRepFormGroup.controls.accountReporterId"></app-error-messages>
    </section>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall (click)="onSubmit($event)">Save</button>
  </div>
</form>
<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
