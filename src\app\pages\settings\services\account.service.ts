import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { map, Observable, of } from "rxjs";
import { Account } from "../models";

@Injectable({
  providedIn: 'root'
})
export class AccountService extends BaseCrudService {

  account!: Account | null;

  getBaseAPIPath(): string {
    return 'account';
  }

  getCurrentUser(fromDb = false): Observable<Account> {
    if (fromDb || !this.account) {
      return this.httpClient.get<Account>(this.getBaseAPIPath()).pipe(map(user => this.account = user));
    }
    return of(this.account);
  }

  clearAccountDetails(): void {
    this.account = null;
  }
}
