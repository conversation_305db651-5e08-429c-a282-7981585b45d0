import { Clipboard } from '@angular/cdk/clipboard';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { AgingConfigService } from '@pages/reports/services/aging-config.service';
import { User } from '@pages/user/models';
import { takeUntil } from 'rxjs';
import { AgingConfigDto, AgingConfigUpdateDto } from '../../../models/inventory-report-model';

@Component({
  selector: 'app-config-update',
  templateUrl: './config-update.component.html',
  styleUrls: ['./config-update.component.scss']
})
export class ConfigUpdateComponent extends BaseComponent implements OnInit {
  @Output() onClose = new EventEmitter<boolean>();
  @Input() ageConfig: AgingConfigDto | null = null;
  emailConfigForm!: FormGroup;
  @Input() users: User[] = [];
  constructor(
    private readonly agingEmailService: AgingConfigService,
    private readonly fb: FormBuilder,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly clipBoard: Clipboard,
    private readonly toasterService: AppToasterService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.getUsers();
    this.initializeFormGroup();
  }

  private initializeFormGroup(): void {
    this.emailConfigForm = this.fb.group({
      subject: new FormControl(this.ageConfig?.subject, Validators.required),
      body: new FormControl(this.ageConfig?.body, Validators.required),
      toEmpId: new FormControl(this.ageConfig?.toEmailEmployee.id, Validators.required),
    })
  }

  private getUsers(): void {
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.users = res;
        }
      }
      );
  }

  closePopup(): void {
    this.onClose.emit(false);
  }

  copyToClipboard(link: string): void {
    const isCopied = this.clipBoard.copy(link);
    isCopied ? this.toasterService.success('Copied') : this.toasterService.error('Failed To Copy');
  }

  get configEmailUpdateParams(): AgingConfigUpdateDto {
    if (this.ageConfig) {
      const params: AgingConfigUpdateDto = {
        id: this.ageConfig.id,
        days: this.ageConfig.days,
        color: this.ageConfig.color,
        ...this.emailConfigForm.value
      }
      return params;
    }
    return new AgingConfigUpdateDto();
  }

  private updateEmailConfig(): void {
    this.agingEmailService.update<AgingConfigUpdateDto>(this.configEmailUpdateParams).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.emailConfigUpdated);
          this.onClose.emit(true);
        },
        error: () => {
          this.onClose.emit(true);
        }
      }
      );
  }

  onSubmit(): void {
    if (this.emailConfigForm.invalid) {
      this.emailConfigForm.markAllAsTouched();
      return;
    }
    this.updateEmailConfig();
  }
}
