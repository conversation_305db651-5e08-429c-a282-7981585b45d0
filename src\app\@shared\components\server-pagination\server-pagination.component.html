<div class="server-pagination">
  <div class="items-per-page">
    <label>Items per page: </label>
    <div class="itemsDropdown">
      <p-dropdown
        [options]="paginationParams.itemsPerPageOptions"
        [(ngModel)]="paginationParams.itemsPerPage"
        #perPage
        class="itemsPPDD"
        (onChange)="onItemsPerPageChange(perPage.value)"
        appendTo="body"
      ></p-dropdown>
    </div>
    <div class="pagination-info">
      {{ paginationInfo }}
    </div>
  </div>
  <pagination
    [boundaryLinks]="true"
    [totalItems]="paginationParams.totalElements"
    [itemsPerPage]="paginationParams.itemsPerPage"
    previousText="&lsaquo;"
    nextText="&rsaquo;"
    firstText="&laquo;"
    lastText="&raquo;"
    [(ngModel)]="paginationParams.page"
    (pageChanged)="onPageChanged($event)"
    [rotate]="false"
    [maxSize]="paginationParams.maxSize"
  >
  </pagination>
</div>
