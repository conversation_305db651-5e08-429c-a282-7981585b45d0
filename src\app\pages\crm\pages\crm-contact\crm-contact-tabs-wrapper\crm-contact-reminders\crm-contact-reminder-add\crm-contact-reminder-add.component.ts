import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CrmContactReminders } from '@pages/crm/models/crm-contact-reminder.model';
import { CrmContactRemindersService } from '@pages/crm/services/crm-contact-reminders.service';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';

@Component({
  selector: 'app-crm-contact-reminder-add',
  templateUrl: './crm-contact-reminder-add.component.html',
  styleUrls: ['./crm-contact-reminder-add.component.scss']
})
export class CrmContactReminderAddComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Reminder';
  reminderFrom!: FormGroup;
  @Input() accountRepList!: IdNameModel[];
  @Input() customerId!: string;
  @Input() reminderFor!: number;
  @Input() selectedReminder!: CrmContactReminders | null;
  @Input() isViewMode!: boolean;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() addNewReminder: EventEmitter<CrmContactReminders> = new EventEmitter<CrmContactReminders>();
  @Output() editNewReminder: EventEmitter<CrmContactReminders> = new EventEmitter<CrmContactReminders>();
  reminderHistory!: Array<CrmContactReminders>;
  showCalendar!: boolean;
  showScheduledDate!: boolean;
  minDate = new Date();
  dateOptions = [
    {
      label: '3 Months',
      months: 3
    },
    {
      label: '6 Months',
      months: 6
    },
    {
      label: '9 Months',
      months: 9
    },
    {
      label: '12 Months',
      months: 12
    },
    {
      label: 'Custom',
      months: 0
    }
  ]
  constructor(
    private readonly crmContactRemindersService: CrmContactRemindersService,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedReminder?.currentValue) {
      if (this.selectedReminder?.id) {
        this.title = this.isViewMode ? 'View Reminder' : 'Edit Reminder';
        this.initializeForm();
        this.showCalendar = true;
        if (Number(this.selectedReminder?.scheduleAfter) !== 0) {
          this.showScheduledDate = true;
        }
        this.getReminderHistory();
      }
    }
  }

  initializeForm(): void {
    this.reminderFrom = new FormGroup({
      remindToId: new FormControl(this.reminderFor, Validators.required),
      notes: new FormControl(this.selectedReminder?.notes ?? ''),
      reminderStatus: new FormControl(this.selectedReminder?.reminderStatus ?? 'REQUESTED'),
      crmContactId: new FormControl(this.customerId, Validators.required),
      reminderTime: new FormControl(this.selectedReminder?.reminderTime ? new Date(`${this.selectedReminder.reminderTime}`) : '', Validators.required),
      scheduleAfter: new FormControl(this.selectedReminder?.scheduleAfter ? Number(this.selectedReminder?.scheduleAfter) : null, Validators.required),
      reoccurring: new FormControl(this.selectedReminder?.reoccurring ?? false)
    });
    this.reminderFrom.get('remindToId')?.disable();
    if (this.isViewMode) {
      this.reminderFrom.disable();
    }
  }

  onCancel(): void {
    this.onClose.emit(true);
  }

  onSubmit(): void {
    if (this.reminderFrom.invalid) {
      this.reminderFrom.markAllAsTouched();
      return;
    }
    this.reminderFrom.markAsUntouched();
    this.selectedReminder?.id ? this.updateReminder() : this.addReminder()
  }

  addReminder(): void {
    this.crmContactRemindersService.add(this.reminderFrom.getRawValue()).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.toasterService.success(MESSAGES.reminderAddedSuccess);
      this.addNewReminder.emit(res);
      this.onCancel();
    });
  }

  updateReminder(): void {
    this.crmContactRemindersService.update({ ...this.reminderFrom.getRawValue(), id: this.selectedReminder?.id }).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.toasterService.success(MESSAGES.reminderUpdatedSuccess);
      this.editNewReminder.emit(res);
      this.showCalendar = this.showScheduledDate = false;
      this.onCancel();
    });
  }

  onDateChange(event: number): void {
    this.showCalendar = event !== null;
    if (event) {
      const date = new Date();
      date.setMonth(date.getMonth() + event);
      this.reminderFrom.get('reminderTime')?.setValue(date);
      this.showScheduledDate = true;
    } else {
      this.showScheduledDate = false;
      this.reminderFrom.get('reminderTime')?.setValue(null);
      this.reminderFrom.get('reoccurring')?.setValue(false)
    }
  }

  getReminderHistory(): void {
    if (this.selectedReminder?.id) {
      this.crmContactRemindersService.getReminderHistory(this.selectedReminder.id, API_URL_UTIL.admin.crm.reminderHistory).pipe(takeUntil(this.destroy$)).subscribe({
        next: (res: Array<CrmContactReminders>) => {
          this.reminderHistory = res;
        }
      });
    }

  }
}
