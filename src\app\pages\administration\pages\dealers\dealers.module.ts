import { NgxGpAutocompleteModule } from "@angular-magic/ngx-gp-autocomplete";
import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { GoogleMapModule } from '@sharedComponents/google-map/google-map.component.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { NgxMaskModule } from 'ngx-mask';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ConfirmationService } from 'primeng/api';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { KeyFilterModule } from 'primeng/keyfilter';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TimelineModule } from 'primeng/timeline';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from 'src/app/@shared/pipes/pipes.module';
import { DealerAddComponent } from './dealer-add/dealer-add.component';
import { DealerListComponent } from './dealer-list/dealer-list.component';
import { DealersRoutingModule } from './dealers-routing.module';
import { DealersComponent } from './dealers.component';
import { FundingEmailConfigComponent } from './funding-email-fields/funding-email-config.component';

@NgModule({
  declarations: [
    DealersComponent,
    DealerListComponent,
    DealerAddComponent,
    FundingEmailConfigComponent,
  ],
  imports: [
    CommonModule,
    DealersRoutingModule,
    SharedComponentsModule,
    TabsModule.forRoot(),
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    GoogleMapModule,
    NgxGpAutocompleteModule,
    TimelineModule,
    NgxMaskModule.forRoot(),
    PipesModule,
    KeyFilterModule
  ],
  exports: [NgxMaskModule],

  providers: [ConfirmationService, {
    provide: Loader,
    useValue: new Loader({
      apiKey: environment.googleMapApiKey,
      libraries: ['places']
    })
  }]
})
export class DealersModule { }
