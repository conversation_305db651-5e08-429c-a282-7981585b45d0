<app-page-header [pageTitle]="pageTitle" class="page-wrapper">
  <div headerActionBtn class="search-form left">
    <div class="d-flex search-form-field">
      <div class="w-260 ml-20 mr-5">
        <p-calendar
          [(ngModel)]="rangeDates"
          #rangeCalendar
          selectionMode="range"
          [showIcon]="true"
          [showButtonBar]="true"
          placeholder="Select Date Range"
          [readonlyInput]="true"
          inputId="range"
          (ngModelChange)="onChange($event, 'calender')"
        ></p-calendar>
      </div>
      <div class="w-220 ml-20">
        <p-dropdown
          [options]="users"
          [(ngModel)]="selectedUserId"
          placeholder="Select User"
          optionDisabled="inactive"
          (ngModelChange)="onChange($event, 'user')"
          optionLabel="label"
          optionValue="id"
        ></p-dropdown>
      </div>
      <div class="w-220 ml-20">
        <p-dropdown
          [options]="calendarEvent"
          [(ngModel)]="selectedType"
          optionLabel="label"
          [filter]="true"
          filterBy="label"
          [showClear]="true"
          placeholder="Select type"
          (ngModelChange)="onChange($event, 'type')"
        >
          <ng-template pTemplate="selectedItem">
            <div class="colors-menu selected-item" *ngIf="selectedType">
              <div class="color-title"><button [ngStyle]="{ 'background-color': selectedType.code }" *ngIf="selectedType.value !== 'All'"></button> {{ selectedType.label }}</div>
            </div>
          </ng-template>
          <ng-template let-country pTemplate="item">
            <div class="colors-menu">
              <div class="color-title"><button *ngIf="country.value !== 'All'" [ngStyle]="{ 'background-color': country.code }"></button> {{ country.label }}</div>
            </div>
          </ng-template>
        </p-dropdown>
      </div>
      <div class="ml-20">
        <button type="button" class="btn btn-secondary" (click)="reset()">Reset</button>
      </div>
    </div>
  </div>
</app-page-header>

<div class="e-calendar">
  <div class="model-body">
    <ng-container *ngIf="calendarOptions">
      <full-calendar [options]="calendarOptions" #calendar (click)="eventClick($event)"></full-calendar>
    </ng-container>
  </div>

  <p-dialog [header]="selectedEventInfo.title" [(visible)]="eventInfoDialog" *ngIf="eventInfoDialog">
    <div class="content">
      <div class="row d-name">
        <div>{{ selectedEventInfo.extendedProps.userName }}</div>
      </div>

      <div class="date">
        <div>{{ selectedEventInfo?.start | date: constants.dateFormat }} - {{ selectedEventInfo?.end | date: constants.dateFormat }}</div>
      </div>

      <div class="row location">
        <button type="button" class="btn" (click)="goToUrl(selectedEventInfo)">view</button>
      </div>
    </div>
  </p-dialog>
</div>
