@import "src/assets/scss/variables";

::ng-deep.inventory-table {
  .inventory-col-width-sm {
    width: 20%;
  }

  .inventory-col-width-md {
    width: 70%;
  }

  .p-datatable .p-datatable-thead > tr > th {
    border: none;
    border-width: 0;
    background: var(--table-even-color);
  }

  tbody tr:nth-child(even) > * {
    background: var(--table-even-color);
  }
}

.icon-add-image {
  margin: 5px;
  width: 48px;
  margin-right: 10px;
}

.icon-edit-image {
  margin: 5px;
  width: 50px;
}

.small-col {
  width: 200px;
}

.large-col {
  width: 1300px;
}

.view-icon {
  padding: 6px;
  border: 1px solid $blue-color;
  margin-right: 14px;
  width: 50px;
  height: 30px;
}

.drop-zone__input {
  height: 30px;
  width: 50px;
  cursor: pointer;
}

.product-image {
  height: 600px;
  width: 90%;
  object-fit: contain;
}

.p-mb-3 {
  text-align: center;
}

.close-icon {
  display: flex;
  justify-content: flex-end;
  padding: 15px;
  font-size: 25px;
}

.pointer-event-none {
  pointer-events: none;
}

.img-green {
  background-color: $green-highlight-color;
}

.img-gray {
  background-color: $gray-highlight-color;
}

::ng-deep .pi-chevron-right:before,
::ng-deep .pi-chevron-left:before {
  font-size: 30px;
}
