export const DataTypeOptions = [
  'DropDown',
  'Number',
  'TextField',
  'TextBox',
  'Email',
  'Phone Number',
]

export class Field {
  label!: string;
  dataType!: string;
  placeholder!: string;
  options!: any[];
  isRequired!: boolean;
  for!: string;
  value!: string | number | boolean | null;
  isDefault!: boolean;
}

export class QuoteFormData {
  id!: number;
  formData!: {
    fields: Field[]
  };
  userIds!: number[];
}