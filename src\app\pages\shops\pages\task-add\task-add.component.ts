import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Constants, MESSAGES, VALIDATION_CONSTANTS, icons } from '@constants/*';
import { AppToasterService, CommonService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { Shop } from '@pages/administration/models';
import { ShopService } from '@pages/administration/pages/pipeline-config/shop.service';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { StockBasicInfo, TaskAttachment, TaskCreateParams, TaskListItem } from '@pages/shops/models';
import { TaskService } from '@pages/shops/services/tasks.service';
import { User } from '@pages/user/models';
import * as saveAs from 'file-saver';
import { ConfirmationService } from 'primeng/api';
import { Observable, takeUntil, zip } from 'rxjs';
import { FileProperties, FileUploadProgress, IdNameModel } from 'src/app/@shared/models';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-task-add',
  templateUrl: './task-add.component.html',
  styleUrls: ['./task-add.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default
})
export class TaskAddComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Task';
  isArchived = false;
  taskFormGroup!: FormGroup;
  hasDataBeenModified = false;
  isEditMode = false;
  taskTypes: IdNameModel[] = [];
  taskStatuses: IdNameModel[] = [];
  shops: IdNameModel[] = [];
  stockBasicInfo: StockBasicInfo[] = [];
  assignees: User[] = [];
  currentUser!: Account | null;
  showCreateActivityModal = false;
  fetchNewActivities = false;
  fileUploadProgresses: FileUploadProgress[] = [];
  taskFileUploadPath = 'Tasks Files';
  isUploadingFile = false;
  showAttachmentsTab = true;
  isTaskInfoLoaded = false;
  taskInfo!: TaskListItem;
  taskIdNo!: string;
  showEnlargedImage = false;
  viewImageScr?: string;
  loaders = {
    taskTypes: false,
    taskStatuses: false,
    shops: false,
    stockNumbers: false,
    shopUsers: false
  }

  accordionTabs = {
    details: true,
    description: false,
    activity: true,
    attachments: true,
  }

  // @Input() taskInfo!: TaskListItem | null;
  @Input() taskId!: string;
  @Input() isViewMode!: boolean | null;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  attachmentIdsForDeletion: number[] = [];

  constructor(
    private readonly fb: FormBuilder,
    private readonly taskService: TaskService,
    private readonly toasterService: AppToasterService,
    private readonly shopService: ShopService,
    private readonly authService: AuthService,
    private readonly fileUploadService: FileUploadService,
    private readonly confirmationService: ConfirmationService,
    private readonly commonService: CommonService,
    private readonly userAnnotationService: UserAnnotationService
  ) {
    super();
  }

  ngOnInit(): void {
    if (this.isEditMode) {
      this.commonService.setBlockUI$(true);
    }
    this.initializeFormGroup();
    this.getCurrentUser();
    this.getAllMasterData();
  }

  private getAllMasterData() {
    this.getShopsByDealerId();
    this.getTaskTypes();
    this.getTaskStatuses();
    this.getStockNumbers();
    this.getTaskInfo(this.taskId);
    this.getUsers();
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user && this.taskFormGroup) {
        this.currentUser = user;
        if (!this.isEditMode) {
          const reporterControl = this.taskFormGroup.get('reporterName');
          this.taskFormGroup.get('reporterId')?.setValue(user.id);
          reporterControl?.setValue(this.utils.getFullName(user.firstName, user.lastName));
          reporterControl?.disable();
        }
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.taskId?.currentValue) {
      if (this.isViewMode) {
        this.title = 'View Task';
      } else {
        this.title = 'Edit Task';
        this.isEditMode = true;
      }
    }
  }

  private getTaskInfo(taskId: string): void {
    if (taskId) {
      this.taskService.get<TaskListItem>(taskId).pipe(takeUntil(this.destroy$)).subscribe({
        next: task => {
          this.taskInfo = task;
          this.isArchived = task.archived;
          this.isTaskInfoLoaded = true;
          this.setTaskInfoInFormGroup();
          if (this.isViewMode) {
            this.hideShowAttachmentsTab();
          }
        },
        error: () => {
          this.commonService.setBlockUI$(false);
        }
      })
    }
  }

  private initializeFormGroup(): void {
    this.taskFormGroup = this.fb.group({
      id: new FormControl(null),
      taskTypeId: new FormControl(null, [Validators.required]),
      taskStatusId: new FormControl(null, [Validators.required]),
      shopId: new FormControl(null, [Validators.required]),
      stockNumber: new FormControl(null),
      assigneeId: new FormControl(null, [Validators.required]),
      reporterId: new FormControl(null, [Validators.required]),
      reporterName: new FormControl(null),
      summary: new FormControl(null, [Validators.required, Validators.maxLength(VALIDATION_CONSTANTS.inputMaxLength)]),
      description: new FormControl(null, [Validators.maxLength(VALIDATION_CONSTANTS.descriptionMaxLength)]),
      startDate: new FormControl(null),
      endDate: new FormControl(null),
      pipelineId: new FormControl(),
      pipelineSequence: new FormControl()
    });
    this.taskFormGroup.get('taskTypeId')?.disable();
  }

  get taskInfoCreateParams(): TaskCreateParams {
    return {
      ...this.taskFormGroup.getRawValue(),
      id: this.taskInfo?.id,
      taskAttachments: this.taskAttachments,
      unitId: this.selectedUnitId,
      phaseId: this.taskInfo?.phaseId,
      pipelineId: this.taskInfo?.pipelineId
    };
  }

  get taskAttachments(): TaskAttachment[] {
    let taskAttachments: TaskAttachment[] = this.fileUploadProgresses.map(fileProgress => ({ url: fileProgress.uploadedFileUrl }));
    if (this.isEditMode && this.taskInfo?.taskAttachments?.length) {
      taskAttachments = [...this.taskInfo?.taskAttachments, ...taskAttachments];
    }
    return taskAttachments;
  }

  get selectedUnitId(): number | undefined {
    const stockNumber = this.taskFormGroup.getRawValue().stockNumber;
    return this.stockBasicInfo.find(stock => stock.stockNumber === stockNumber)?.unitId;
  }

  get isFileUploadInProgress(): boolean {
    if (this.fileUploadProgresses.some(fileProgress => fileProgress.progress$.getValue() < 100)) {
      return true;
    }
    return false;
  }

  onSubmit(close = true): void {
    if (this.isViewMode) {
      this.taskFormGroup.enable();
      this.isViewMode = false;
      this.isEditMode = true;
      this.title = 'Edit Task';
      const reporterControl = this.taskFormGroup.get('reporterName');
      reporterControl?.disable();
      this.taskFormGroup.get('stockNumber')?.disable();
      this.taskFormGroup.get('shopId')?.disable();
      this.taskFormGroup.get('assigneeId')?.disable();
      this.taskFormGroup.get('taskTypeId')?.disable();
      return;
    }
    if (this.taskFormGroup.invalid) {
      this.taskFormGroup.markAllAsTouched();
      return;
    }
    if (this.isFileUploadInProgress) {
      this.toasterService.warning(MESSAGES.fileUploadInProgress);
      return;
    }
    this.taskFormGroup.get('startDate')?.setValue(this.taskFormGroup.controls.endDate.value);
    if (this.isEditMode) {
      this.editTask();
    } else {
      this.saveTask(close);
    }
  }

  saveTask(close = true): void {
    this.taskService.add(this.taskInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.shopTaskUpdateSuccess : MESSAGES.shopTaskAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.taskFormGroup.reset();
        if (!this.isEditMode) {
          for (const taskType of this.taskTypes) {
            if (taskType.name === 'General') {
              this.taskFormGroup.get('taskTypeId')?.setValue(taskType.id);
            }
          }
          for (const taskStatuse of this.taskStatuses) {
            if (taskStatuse.name === 'To Do') {
              this.taskFormGroup.patchValue({ taskStatusId: taskStatuse.id });
            }
          }
          this.taskFormGroup.get('reporterId')?.setValue(this.currentUser?.id);
          this.taskFormGroup.get('reporterName')?.setValue(this.utils.getFullName(this.currentUser?.firstName, this.currentUser?.lastName));
          this.fileUploadProgresses = [];
        }
      }
    });
  }

  async editTask(): Promise<void> {
    if (this.attachmentIdsForDeletion.length) {
      await this.deleteAttachmentsFromServer();
    }
    this.taskService.update(this.taskInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.shopTaskUpdateSuccess : MESSAGES.shopTaskAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.isViewMode = false;
    this.onClose.emit(this.hasDataBeenModified);
  }

  async setTaskInfoInFormGroup(): Promise<void> {
    if (this.taskInfo) {
      this.taskFormGroup.patchValue({
        taskTypeId: this.taskInfo?.taskType?.id,
        taskStatusId: this.taskInfo?.taskStatus?.id,
        shopId: this.taskInfo?.shop?.id,
        summary: this.taskInfo?.summary,
        description: this.taskInfo?.description,
        unitId: this.taskInfo?.unitId,
        stockNumber: this.taskInfo?.stockNumber,
        startDate: this.taskInfo?.startDate ? new Date(`${this.taskInfo?.startDate}`) : '',
        endDate: this.taskInfo?.endDate ? new Date(`${this.taskInfo?.endDate}`) : '',
      });
      if (this.taskInfo?.shop?.id) {
        this.taskFormGroup.patchValue({
          assigneeId: this.assignees?.find(a => a.id === this.taskInfo?.assignee?.id)?.id,
          reporterId: this.taskInfo?.reporter?.id,
          reporterName: this.taskInfo?.reporter?.name,
          shopId: this.taskInfo?.shop.id
        });
        this.taskFormGroup.get('reporterName')?.disable();
      }
      if (this.isEditMode) {
        this.taskFormGroup.get('stockNumber')?.disable();
        this.taskFormGroup.get('shopId')?.disable();
        this.taskFormGroup.get('assigneeId')?.disable();
        this.taskFormGroup.get('taskTypeId')?.disable();
      }
      if (this.isViewMode) {
        this.taskFormGroup.disable();
      }
    }
    this.commonService.setBlockUI$(false);
  }

  private getTaskTypes(): void {
    this.loaders.taskTypes = true;
    this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.types).pipe(takeUntil(this.destroy$)).subscribe({
      next: (taskTypes) => {
        this.taskTypes = taskTypes;
        this.loaders.taskTypes = false;
        if (!this.isEditMode) {
          for (const taskType of this.taskTypes) {
            if (taskType.name === 'General') {
              this.taskFormGroup.get('taskTypeId')?.setValue(taskType.id);
            }
          }
        }
      },
      error: () => {
        this.loaders.taskTypes = false;
      }
    });
  }

  private getTaskStatuses(): void {
    this.loaders.taskStatuses = true;
    this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.statuses).pipe(takeUntil(this.destroy$)).subscribe({
      next: (taskStatuses) => {
        this.taskStatuses = taskStatuses;
        if (!this.isEditMode) {
          for (const taskStatuse of this.taskStatuses) {
            if (taskStatuse.name === 'To Do') {
              this.taskFormGroup.patchValue({ taskStatusId: taskStatuse.id });
            }
          }
        }
        this.loaders.taskStatuses = false;
      },
      error: () => {
        this.loaders.taskStatuses = false;
      }
    });
  }

  private getShopsByDealerId(): void {
    this.loaders.shops = true;
    this.shopService.getList<Shop>(API_URL_UTIL.admin.shops.basicInfo).pipe(takeUntil(this.destroy$)).subscribe({
      next: (shops) => {
        this.shops = shops;
        this.loaders.shops = false;
      },
      error: () => {
        this.loaders.shops = false;
      }
    });
  }

  private getStockNumbers(): void {
    this.loaders.stockNumbers = true;
    this.taskService.getList<StockBasicInfo>(`${API_URL_UTIL.tasks.stockNumbers}?pipelineType=STOCK`).pipe(takeUntil(this.destroy$)).subscribe({
      next: (stockNumbers) => {
        this.stockBasicInfo = stockNumbers;
        this.loaders.stockNumbers = false;
      },
      error: () => {
        this.loaders.stockNumbers = false;
      }
    });
  }

  getUsers(): void {
    this.loaders.shopUsers = true;
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.assignees = res;
          this.taskFormGroup.patchValue({
            assigneeId: this.assignees?.find(a => a.id === this.taskInfo?.assignee?.id)?.id
          });
          this.loaders.shopUsers = false;
        },
        error: () => {
          this.loaders.shopUsers = false;
        }
      });
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateActivityModal = false;
    setTimeout(() => {
      /*
       when activity log is opened, and on close of it, primeng remove p-overflow-hidden class.
       but since task-add is also a popup, we want to hide body scroll. hence adding below class
      */
      document.body.classList.add('p-overflow-hidden');
    }, 400);
    if (refreshList) {
      this.fetchNewActivities = true;
    }
  }

  onFileSelect(event: any) {
    for (const file of event.target.files) {
      if (file.size > this.constants.fileSize) {
        this.toasterService.warning(MESSAGES.fileUploadMessage)
      }
      else {
        if (event.target?.files?.length) {
          const extensionDot = file?.name?.lastIndexOf('.');
          const ext = file?.name?.substring(extensionDot + 1);
          if (!Constants.allowedImgFormats.includes(ext)) {
            this.toasterService.error(MESSAGES.fileTypeNotSupported);
            return;
          }
          this.uploadImage(file);
        }
      }
    }
  }

  uploadImage(file: File): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, this.taskFileUploadPath, this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.fileUploadSuccess);
        },
        error: err => {
          this.toasterService.error(MESSAGES.fileUploadError);
        }
      })
  }

  downloadImage(taskAttachment: TaskAttachment): void {
    if (taskAttachment?.url) {
      this.fileUploadService.downloadFile(taskAttachment.url, this.getFileName(taskAttachment.url)).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/image" }), this.getFileName(taskAttachment.url));
        }
      });
    }
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  deleteImageFromCloud(imageUrl: string, fileIndex: number, callbackFn: Function): void {
    this.fileUploadService.deleteFile([imageUrl]).pipe(takeUntil(this.destroy$)).subscribe(() => {
      callbackFn(fileIndex);
    })
  }

  removeFileFromUpload(fileIndex: number): void {
    const spliceImageFunction = () => {
      this.fileUploadProgresses.splice(fileIndex, 1);
    }
    const fileProgress = this.fileUploadProgresses[fileIndex];
    if (fileProgress.uploadedFileUrl) {
      this.deleteImageFromCloud(fileProgress.uploadedFileUrl, fileIndex, spliceImageFunction);
    }
  }

  onDelete(attachmentId: number | undefined, event: Event): void {
    if (!attachmentId) {
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'attachment'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.addAttachmentsForDeletion(attachmentId);
      }
    });
  }

  private addAttachmentsForDeletion(attachmentId: number): void {
    this.attachmentIdsForDeletion.push(attachmentId);
    const index = this.taskInfo?.taskAttachments.findIndex(attachment => attachment.id === attachmentId);
    if (index !== undefined && index > -1) {
      this.taskInfo?.taskAttachments.splice(index, 1);
    }
  }

  deleteAttachmentsFromServer(): Promise<void> {
    return new Promise<void>(resolve => {
      const deleteApiObservables: Observable<void>[] = [];
      for (const attachmentId of this.attachmentIdsForDeletion) {
        deleteApiObservables.push(this.taskService.deleteTaskAttachment(attachmentId));
      }
      zip(deleteApiObservables).pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.toasterService.success(MESSAGES.attachmentDeleteSuccess);
        resolve();
      });
    });
  }

  getFileName(fileUrl: string | undefined): string {
    if (!fileUrl) {
      return '';
    }
    return fileUrl.split('_')[1];
  }

  hideShowAttachmentsTab() {
    this.showAttachmentsTab = (this.taskInfo?.taskAttachments?.length && this.isViewMode) || this.isEditMode ? true : false;
  }

  onViewImage(fileUrl: string | undefined) {
    this.showEnlargedImage = true;
    if (fileUrl) {
      this.viewImageScr = fileUrl;
    }
  }
}
