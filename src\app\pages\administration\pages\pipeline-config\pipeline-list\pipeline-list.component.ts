import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { PipelineConfigListFilter, PipelineConfigListItem } from '@pages/administration/models';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { TableColumn } from 'src/app/@shared/models';
import { PipelineConfigService } from '../pipeline-config.service';

@Component({
  selector: 'app-pipeline-list',
  templateUrl: './pipeline-list.component.html',
  styleUrls: ['./pipeline-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PipelineListComponent extends BaseComponent implements OnInit {

  cols: TableColumn[] = [];
  _selectedColumns: any[] = [];
  pipelineConfigs: PipelineConfigListItem[] = [];
  filterParams: PipelineConfigListFilter = new PipelineConfigListFilter();
  showCreateModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.PIPELINE_CONFIG;
  selectedPipelineConfig!: PipelineConfigListItem | null;
  isActiveTab = true;

  constructor(private readonly pipelineConfigService: PipelineConfigService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,

    private readonly activeRoute: ActivatedRoute
  ) {
    super();
    this.pageTitle = 'Pipeline Config';
    this.paginationConfig.predicate = 'title';
  }

  ngOnInit(): void {
    this.setTableColumns();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getPipelineConfigById(Number(params.id));
        }
      });
  }

  getPipelineConfigById(id: number) {
    this.pipelineConfigService.get<PipelineConfigListItem>(id).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.onEdit(res);
        this.cdf.detectChanges();
      }
    });
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  private setTableColumns() {
    this.cols = [
      { field: 'title', sortKey: 'title', header: 'Title', sortable: true, reorderable: true },
      { field: 'pipeline', header: 'Pipeline', isATemplate: true, reorderable: true },
    ];
    this._selectedColumns = this.cols;
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.pipelineConfigService.getListWithFiltersWithPagination<PipelineConfigListFilter, PipelineConfigListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.pipelineConfig.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.pipelineConfigs = res.content;
          this.setPaginationParamsFromPageResponse<PipelineConfigListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedPipelineConfig = null;
    if (refreshList) {
      this.getAll();
    }
  }

  onAdd(): void {
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  onEdit(pipelineConfig: PipelineConfigListItem): void {
    this.showCreateModal = true;
    this.selectedPipelineConfig = pipelineConfig;
    this.cdf.detectChanges();
  }

  onDelete(pipelineConfig: PipelineConfigListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'pipeline configuration'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(pipelineConfig);
      }
    });
  }

  private onDeleteConfirmation(pipelineConfig: PipelineConfigListItem): void {
    this.pipelineConfigService.delete(pipelineConfig.id, API_URL_UTIL.admin.pipelineConfig.softDelete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.pipelineConfigDeleteSuccess);
          this.getAll();
        }
      });
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
