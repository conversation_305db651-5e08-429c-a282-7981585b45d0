import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { BaseComponent } from '@core/utils';
import { DealerListItem } from '@pages/administration/models';
import { DealerService } from '@pages/administration/pages/dealers/dealers.service';
import { InventoryListItem } from '@pages/inventory/models';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-view-inventory-details',
  templateUrl: './view-inventory-details.component.html',
  styleUrls: []
})
export class ViewInventoryDetailsComponent extends BaseComponent implements OnChanges {
  @Output() onClose = new EventEmitter<void>();
  @Output() onEdit = new EventEmitter<void>();
  @Input() selectedInventory!: InventoryListItem | null;
  @Input() isEditMode !: boolean;
  @Input() isViewMode !: boolean;
  @Input() showSoldTabs  !: boolean;
  @Input() showHoldTabs  !: boolean;
  showCreateModal = false;
  title = 'Inventory Details';
  abbreviation !: string;
  constructor(
    private readonly dealerService: DealerService,
    private readonly cdf: ChangeDetectorRef
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedInventory?.currentValue) {
      if (this.selectedInventory) {
        this.getAbbreviation(this.selectedInventory?.generalInformation?.owner?.id)
      }
    }
  }

  getAbbreviation(dealerId: number): void {
    this.dealerService.get<DealerListItem>(dealerId).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.abbreviation = res?.abbreviation ? res?.abbreviation : '';
        this.cdf.detectChanges();
      }
    })
  }

  onCancel() {
    this.selectedInventory = null;
    this.onClose.emit();
  }

  edit(): void {
    this.onEdit.emit();
    this.onCancel();
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedInventory = null;
    this.showSoldTabs = false
  }
}
