import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DealerListComponent } from './dealer-list/dealer-list.component';
import { DealersComponent } from './dealers.component';

const routes: Routes = [
  {
    path: '',
    component: DealersComponent,
    title: 'Skeye - Dealers',
    children: [
      {
        path: '',
        component: DealerListComponent,
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DealersRoutingModule { }
