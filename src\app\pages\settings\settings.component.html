<app-page-header [pageTitle]="pageTitle"></app-page-header>
<div class="wrapper">
  <div class="card user-info">
    <div class="image">
      <span>{{utils.getInitials(currentUser?.firstName, currentUser?.lastName) | uppercase}}</span>
    </div>
    <div class="name">
      <h3>{{utils.getFullName(currentUser?.firstName, currentUser?.lastName) | titlecase}}</h3>
    </div>
    <div class="content">
      <p class="email">
        <fa-icon [icon]="faIcons.faEnvelope"></fa-icon>
        <span>{{currentUser?.email}}</span>
      </p>
      <div>{{currentUser?.employeeId}}</div>
    </div>
  </div>
  <div class="account card tabs">
    <tabset #profileTabs>
      <tab heading="Personal Info" #personalInfoTab="tab" (selectTab)="onSelectTab($event)">
        <app-account name="personalInfo" *ngIf="personalInfoTab.active"></app-account>
      </tab>

      <tab heading="Change Password" #changePasswordTab="tab" (selectTab)="onSelectTab($event)">
        <app-change-password name="changePassword" *ngIf="changePasswordTab.active"></app-change-password>
      </tab>
    </tabset>
  </div>
</div>
