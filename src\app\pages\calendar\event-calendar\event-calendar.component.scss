::ng-deep .page-wrapper {
  .page-header {
    align-items: baseline !important;
    padding: 18px 0 0 0 !important;
  }
}

::ng-deep .e-calendar {
  overflow-x: auto;
  .model-body {
    height: 100%;
    padding-top: 10px;
    overflow-x: auto;
  }

  .p-dialog {
    width: 400px;
  }

  .fc-dayGridWeek-button,
  .fc-dayGridDay-button {
    background-color: #fff !important;
    color: #495057 !important;
    border: 1px solid #ced4da !important;
  }

  .fc-button.fc-dayGridWeek-button.fc-button-active,
  .fc-button.fc-dayGridDay-button.fc-button-active {
    background-color: #2563eb !important;
    border-color: #3b82f6;
    color: #ffffff !important;
  }

  .fc-button.fc-dayGridWeek-button:not(:disabled):focus,
  .fc-button.fc-dayGridDay-button:not(:disabled):focus {
    box-shadow: 0 0 0 0.2rem #bfdbfe;
  }

  .d-name {
    font-weight: 600;
    font-size: 18px;
  }

  p-dialog {
    .p-dialog-header {
      background-color: #0b0b69;
      padding: 5px 20px 5px 20px;
      color: #fff;
    }

    .p-dialog-content {
      padding: 20px;
    }

    .date {
      padding: 5px 0 15px 0;
    }

    .LIGHT-icon {
      color: #a3a2a2;
      padding: 5px;
    }

    .dark-icon {
      font-size: 20px;
      color: #827d7d;
    }

    .location {
      color: #827d7d;
      justify-content: center;

      button {
        color: #0b0b69;
        background-color: #fff;
        border-color: #0b0b69;
        font-weight: 600;
        border: 3px solid #0b0b69;
        padding: 0px 17px !important;
        width: 220px;
      }
    }
  }
}

.search-form {
  margin: 10px;

  .search-form-field {
    justify-content: end;

    .colors-menu {
      button {
        height: 15px;
        width: 25%;
      }

      .color-title {
        margin-left: 10px;
        font-size: 16px;
        align-items: center;
      }
    }
  }

  .w-220 {
    width: 220px;
  }

  .w-260 {
    width: 260px;
  }

  .ml-20 {
    margin-left: 20px;
  }

  .mr-5 {
    margin-right: 5px;
  }

  .w-250 {
    width: 250px;
  }

  .mr-5 {
    margin-right: 5px;
  }

  .w-186 {
    width: 186px;
  }
}

.selected-item {
  overflow: hidden;
}

@media only screen and (max-width: 350px) {
  .e-calendar {
    width: 777%;
  }
}

@media screen and (max-width: 1040px) and (min-width: 651px) {
  .e-calendar {
    width: 172%;
  }
}

@media screen and (max-width: 650px) and (min-width: 351px) {
  .e-calendar {
    width: 314%;
  }
}
