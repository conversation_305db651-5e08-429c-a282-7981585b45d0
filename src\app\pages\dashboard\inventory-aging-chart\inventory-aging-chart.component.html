<div class="content">
  <div *ngIf="isFullViewInventoryAging" class="title d-flex space-between">
    <div>
      <h6>Inventory Aging</h6>
    </div>
    <div class="justify-content-end">
      <fa-icon [icon]="faIcons.faTimes" *ngIf="isFullViewInventoryAging" (click)="onCancel()"></fa-icon>
    </div>
  </div>
  <div class="row">
    <div [ngClass]="{ 'chart-content': isFullViewInventoryAging, 'col-lg-6 col-md-12': true }">
      <div [ngClass]="isFullViewInventoryAging ? 'text-label-full' : 'text-label'">
        Based on <br />
        Acquisition <br />
        Date
      </div>
      <p-chart type="doughnut" [data]="data" [options]="chartOptions" class="w-70"></p-chart>
    </div>
    <div class="col-lg-6 col-md-12 m-t-30 p-r-25">
      <div class="row title-header">
        <div class="col-7 m-t-10 m-b-10">
          <div class="d-flex">Age Group</div>
        </div>
        <div class="col-5 m-t-10 m-b-10">
          <div class="d-flex justify-content-center">Unit</div>
        </div>
      </div>
      <div class="row text-center">
        <div class="col-7">
          <div class="age-group">
            <div class="d-flex">
              <div class="center yellow-circle"></div>
              <div class="m-l-10">Over 180 days</div>
            </div>
          </div>
          <div class="age-group">
            <div class="d-flex">
              <div class="center green-circle"></div>
              <div class="m-l-10"></div>
              120 to 180 days
            </div>
          </div>
          <div class="age-group">
            <div class="d-flex">
              <div class="center orange-circle"></div>
              <div class="m-l-10"></div>
              90 to 120 days
            </div>
          </div>
          <div class="age-group">
            <div class="d-flex">
              <div class="center skyblue-circle"></div>
              <div class="m-l-10"></div>
              60 to 90 days
            </div>
          </div>
          <div class="age-group">
            <div class="d-flex">
              <div class="center purple-circle"></div>
              <div class="m-l-10"></div>
              30 to 60 days
            </div>
          </div>
          <div class="age-group">
            <div class="d-flex">
              <div class="center blue-circle"></div>
              <div class="m-l-10"></div>
              Under 30 days
            </div>
          </div>
          <div class="age-group">
            <div class="d-flex">
              <div class="center red-circle"></div>
              <div class="m-l-10"></div>
              No Acq Date
            </div>
          </div>
        </div>
        <div class="col-5">
          <div class="unit-group m-r-20">{{ inventoryAging?.overOneEightyDaysCount }}</div>
          <div class="unit-group m-r-20">{{ inventoryAging?.oneTwentyDayToOneEightyDaysCount }}</div>
          <div class="unit-group m-r-20">{{ inventoryAging?.ninetyToOneTwentyDaysCount }}</div>
          <div class="unit-group m-r-20">{{ inventoryAging?.sixtyToNinetyDaysCount }}</div>
          <div class="unit-group m-r-20">{{ inventoryAging?.thirtyToSixtyDaysCount }}</div>
          <div class="unit-group m-r-20">{{ inventoryAging?.underThirtyDaysCount }}</div>
          <div class="unit-group m-r-20">{{ inventoryAging?.nullCount }}</div>
        </div>
      </div>
    </div>
  </div>
</div>
