@import "src/assets/scss/variables";

.accordion-header {
  span {
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
  }
}

::ng-deep .crm-task-add {
  p-accordion section {
    margin-bottom: 0 !important;
  }

  .activity {
    .p-accordion-content {
      padding-top: 10px;
      padding-bottom: 0;
    }
  }

  .file-box-wrapper {
    position: relative;
  }

  .file-progress {
    position: absolute;
    bottom: 0px;
    width: 100%;

    .p-progressbar {
      border-radius: 0;
      height: 20px;
    }

    .p-progressbar-label {
      font-size: 13px;
    }
  }

  .p-disabled,
  .p-component:disabled,
  textarea:disabled {
    opacity: 0.6;
  }

  .p-disabled .p-dropdown-label,
  .p-component:disabled .p-dropdown-label {
    color: #797979;
  }

  .drop-zone-disabled {
    color: $disable-color;
    background-color: $disable-background-color !important;
    opacity: 1;
  }

  .p-inline-message.p-inline-message-info {
    height: 1px;
  }

  .file-size-message {
    position: absolute !important;
    bottom: 11px;
    right: 49px;
    font-size: 15px;
    width: 268px;
  }

  .p-accordion .p-accordion-content {
    padding-bottom: 30px !important;
  }
}

form {
  height: calc(100vh - 116px);
}

.pi:hover {
  cursor: pointer;
}

.upload-size-message {
  float: right;
}

.add-btn {
  height: 25px;
  padding: 0px 5px;
  float: right;
  margin: 3px;
}

.mt-23 {
  margin-top: 22px;
}

::ng-deep .width-80 {
  width: 80% !important;
}

.close-icon {
  position: absolute;
  right: 0;
  padding: 15px;
  font-size: 25px;
}

::ng-deep .p-component-overlay {
  background-color: rgb(0, 0, 0, 0.4) !important;

  .p-dialog-content {
    padding: 0 !important;
  }
}

.view-zoomed-image {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 700px;
}

::ng-deep .photos-close-icon {
  svg {
    background-color: white;
    padding: 2px 9px;
  }
}

.file-name {
  max-width: 85px !important;
}

@media only screen and (max-width: 700px) {
  .files {
    margin: auto !important;
  }
}

@media only screen and (max-width: 500px) {
  .attachment-header {
    flex-direction: column;

    span {
      margin-bottom: 5px;
    }
  }

  .view-zoomed-image {
    width: 360px;
  }
}

.section-attachment {
  display: flex;
  justify-content: center;
}

.files {
  height: 200px;
  width: 200px;
  margin: 0 0 0 20px;

  .file-box-wrapper {
    box-shadow: var(--dropdown-box-shadow);
    border-radius: 10px;
    width: 100%;
    height: 90%;
  }

  .file-box {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;

    &.in-progress {
      opacity: 0.2;
    }
  }

  &.drop-zone {
    height: 200px;
    width: 200px;
    padding: 10px;
    margin-left: 0;

    .title {
      font-size: 14px;
    }

    .subtitle {
      font-size: 12px;
    }
  }
}

.file-footer {
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .file-name {
    max-width: 100px;
    font-size: 14px;
  }
}
