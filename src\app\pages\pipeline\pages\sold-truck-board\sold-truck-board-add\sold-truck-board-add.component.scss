@import 'src/assets/scss/variables';

.pipeline-title {
  margin-bottom: 10px !important;

  * {
    font-size: 16px !important;
    font-weight: 600 !important;
    letter-spacing: 0;
    line-height: 25px !important;
  }
}

.add-btn {
  height: 25px;
  padding: 0px 5px;
  margin: 3px;
  margin-left: 30px;
}

.left {
  position: relative !important;
  left: 38% !important;
}

::ng-deep .pipeline-config {
  .p-sidebar-content .content {
    padding-bottom: 50px;
  }

  .p-timeline-event-opposite {
    display: none;
  }

  .p-timeline {
    margin-top: 24px;
  }

  .pipeline-config-info {
    .p-timeline-event {
      min-height: 75px !important;
    }
  }

  .p-timeline-event-content {
    display: none;
  }

  .p-timeline-event-separator {
    span {
      border: 2px solid var(--active-color);
      background-color: var(--active-color);
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      font-weight: bold;
      padding: 10px;
    }
  }

  .p-timeline .p-timeline-event-connector {
    background-color: $placeholder-color;
  }

  .p-timeline.p-timeline-vertical .p-timeline-event-connector {
    width: 3px;
    height: 49px;
  }

  p-table {
    width: 100%;
  }
}

.outer-box {
  border: 1px solid var(--table-border-color);
  width: fit-content;
}

.inner-box {
  padding: 20px;
  margin: 20px;
  color: var(--text-color);
}

.m-l-10 {
  margin-left: 10px;
}

.m-t-10 {
  margin-top: 10px;
}

.grey-color {
  color: $placeholder-color;
}

::ng-deep .pipeline-config-task {
  .p-sidebar-content .content {
    padding-bottom: 50px;
  }

  .p-timeline-event-opposite {
    display: none;
  }

  .p-timeline {
    margin-top: 80px;

    .p-timeline-event {
      min-height: 64px !important;
    }
  }

  .p-timeline-event-content {
    display: none;
  }

  .p-timeline-event-separator {
    span {
      border: 2px solid var(--active-color);
      background-color: var(--active-color);
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      font-weight: bold;
      padding: 10px;
    }
  }

  .p-timeline .p-timeline-event-connector {
    background-color: $placeholder-color;
  }

  .p-timeline.p-timeline-vertical .p-timeline-event-connector {
    width: 3px;
    height: 15px;
  }

  p-table {
    width: 100%;
  }
}

.p-r-20 {
  padding-right: 20px !important;
}

::ng-deep .dropdown-disabled .p-dropdown .p-dropdown-trigger {
  display: none !important;
}

.timeline {
  text-align: center;

  .footer {
    background-color: var(--listing-timeline-bg-color);
    width: fit-content;
    padding: 2px 20px;
    border-radius: 12px;

    .pi {
      margin-right: 5px;
    }

    span {
      font-size: 13px;
      letter-spacing: 0;
      line-height: 21px;
      color: var(--active-color);
      display: flex;
      align-items: center;
    }
  }
}

.h-65px {
  height: 65px;
}

.title {
  color: black !important;
  font-weight: 500 !important;
  font-size: 16px !important;
}

.w-60 {
  width: 100% !important;
}

::ng-deep .sold-task-list {
  .p-dropdown .p-dropdown-label {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
  }

  .p-dropdown .p-dropdown-trigger {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
    width: 3rem !important;
  }

  .p-dropdown-label {
    font-size: 13px !important;
  }

  .p-dropdown {
    background-color: var(--active-color) !important;
  }
}

.view-task {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
  z-index: 1;
}

.header-title {
  top: -8px;
  position: relative;
}
.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
  position: absolute;
  top: 30px;
}
.bold-text {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

.add-scrollbar {
  overflow-x: scroll;
}

.first-col {
  z-index: 1;
}
