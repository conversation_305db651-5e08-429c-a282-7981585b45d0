import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { DealerListItem } from '@pages/administration/models';
import { Observable, catchError, map, of } from 'rxjs';
import { InventoriesService } from './inventories.service';

@Injectable({
  providedIn: 'root',
})
export class CheckDealerInfo  {
  constructor(private readonly inventoriesService: InventoriesService) { }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<DealerListItem> {
    const dealerAbbreviation = route.params['dealerName'];
    return this.inventoriesService.getDealerDetails(dealerAbbreviation).pipe(
      map((response: DealerListItem) => {
        return response;
      }),
      catchError(() => {
        return of(new DealerListItem);
      })
    );
  }
}