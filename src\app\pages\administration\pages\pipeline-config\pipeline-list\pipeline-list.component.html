<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button class="btn btn-primary left" (click)="onAdd()" [appImageIconSrc]="constants.staticImages.icons.addNew" *appHasPermission="[permissionActions.CREATE_PIPELINE_CONFIG]">
      <span class="show-label">Add New Pipeline</span>
    </button>
  </div>
</app-page-header>

<div class="card tabs pipeline-list">
  <div class="tab-content">
    <p-table
      class="no-column-selection"
      [columns]="selectedColumns"
      [value]="pipelineConfigs"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'title'"
      [rowHover]="true"
      [loading]="isLoading"
      styleClass="p-datatable-gridlines"
      [resizableColumns]="true"
      columnResizeMode="expand"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn [pSortableColumn]="col.sortable ? col?.sortKey || col.field : ''" pReorderableColumn *ngIf="col.reorderable" pResizableColumn [ngClass]="col.class">
              {{ col.header }} <p-sortIcon [field]="col.sortKey || col.field" *ngIf="col.sortable"></p-sortIcon>
            </th>
            <th pResizableColumn pResizableColumn [pSortableColumn]="col.sortable ? col?.sortKey || col.field : ''" *ngIf="!col.reorderable" [ngClass]="col.class">
              {{ col.header }} <p-sortIcon [field]="col.sortKey || col.field" *ngIf="col.sortable"></p-sortIcon>
            </th>
          </ng-container>
          <th pResizableColumn class="small-col" pResizableColumn *appHasPermission="[permissionActions.UPDATE_PIPELINE_CONFIG, permissionActions.DELETE_PIPELINE_CONFIG]">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-pipelineConfig let-columns="columns">
        <tr>
          <ng-container *ngFor="let col of columns">
            <td *ngIf="!col.isATemplate" pFrozenColumn>
              {{ pipelineConfig[col.field] | titlecase }}
            </td>
            <td *ngIf="col.isATemplate" style="min-width: 200px">
              <p-timeline [value]="pipelineConfig.templatePipelinePhases" layout="horizontal">
                <ng-template pTemplate="marker" let-pipeline>
                  <span>{{ pipeline.sequenceNumber }}</span>
                </ng-template>
                <ng-template pTemplate="content" let-pipeline>
                  <span>{{ pipeline.shop.name }}</span>
                </ng-template>
              </p-timeline>
            </td>
          </ng-container>
          <td *appHasPermission="[permissionActions.UPDATE_PIPELINE_CONFIG, permissionActions.DELETE_PIPELINE_CONFIG]">
            <div>
              <img [src]="constants.staticImages.icons.edit" (click)="onEdit(pipelineConfig)" alt="" *appHasPermission="[permissionActions.UPDATE_PIPELINE_CONFIG]" />
              <img [src]="constants.staticImages.icons.deleteIcon" (click)="onDelete(pipelineConfig, $event)" alt="" *appHasPermission="[permissionActions.DELETE_PIPELINE_CONFIG]" />
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"></app-server-pagination>
  </div>
</div>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showCreateModal"
  position="right"
  (onHide)="showCreateModal = false"
  [fullScreen]="true"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
>
  <app-pipeline-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal" [pipelineConfig]="selectedPipelineConfig"></app-pipeline-add>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>

<p-confirmPopup></p-confirmPopup>
