<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button
      class="btn btn-primary left me-3 py-2 px-4"
      (click)="onUpdateEmailList()"
      *appHasPermission="[permissionActions.UPDATE_QUOTE_FORM]"
    >
      <div class="d-flex align-items-center">
        <em class="pi pi-user-edit" style="font-size: 1.2rem"></em>
        <span class="role-label">Update Notify Person</span>
      </div>
    </button>
    <button
      class="btn btn-primary left"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      (click)="onAddEditField()"
      *appHasPermission="[permissionActions.UPDATE_QUOTE_FORM]"
    >
      <span class="role-label">Add New Field</span>
    </button>
  </div>
</app-page-header>
<div class="display-content">
  <div class="p-card p-3 mb-3">
    <h6 class="title">Notify Persons</h6>
    <p-table [value]="selectedUsers" [resizableColumns]="true" responsiveLayout="scroll">
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 2%"></th>
          <th pResizableColumn>Name</th>
          <th pResizableColumn>Email</th>
          <th pResizableColumn>Role</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td>
            {{ rowIndex + 1 }}
          </td>
          <td>{{ rowData.name }}</td>
          <td>{{ rowData.email }}</td>
          <td>{{ rowData.roleName }}</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<div class="display-content">
  <div class="p-card p-3">
    <h6 class="title">Quote Form Fields</h6>
    <p-table
      [value]="fields"
      [reorderableColumns]="true"
      [resizableColumns]="true"
      responsiveLayout="scroll"
      (onRowReorder)="onRowReorder()"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 4%" *appHasPermission="[permissionActions.UPDATE_QUOTE_FORM]"></th>
          <th pResizableColumn>Field</th>
          <th pResizableColumn>Data Type</th>
          <th pResizableColumn>Options</th>
          <th pResizableColumn>Required</th>
          <th pResizableColumn>Default</th>
          <th style="width: 7%" *appHasPermission="[permissionActions.UPDATE_QUOTE_FORM]">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr [pReorderableRow]="rowIndex">
          <td pReorderableRowHandle  *appHasPermission="[permissionActions.UPDATE_QUOTE_FORM]">
            <span class="pi pi-bars" pReorderableRowHandle ></span>
          </td>
          <td>{{ rowData.label }}</td>
          <td>{{ rowData.dataType }}</td>
          <td>{{ rowData.options }}</td>
          <td>{{ rowData.isRequired ? 'Yes' : 'No' }}</td>
          <td>{{ rowData.isDefault ? 'Yes' : 'No' }}</td>
          <td *appHasPermission="[permissionActions.UPDATE_QUOTE_FORM]">
            <img
              [src]="constants.staticImages.icons.edit"
              class="me-3"
              alt="edit"
              (click)="onAddEditField(rowData)"
              *appHasPermission="[permissionActions.UPDATE_QUOTE_FORM]"
            />
            <img
              [src]="constants.staticImages.icons.deleteIcon"
              alt="delete"
              (click)="onDelete(rowData)"
              *appHasPermission="[permissionActions.UPDATE_QUOTE_FORM]"
            />
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  class="dealer"
  [(visible)]="openModal"
  (onHide)="openModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-quote-form-field-add-edit
    *ngIf="openModal"
    (closeModal)="closeFieldModal($event)"
    [selectedField]="selectedField"
    [isEditMode]="isEditMode"
    [isUpdateEmail]="isUpdateEmail"
    [users]="users"
    [quoteFormData]="quoteFormData"
  ></app-quote-form-field-add-edit>
</p-sidebar>

<p-confirmPopup></p-confirmPopup>
