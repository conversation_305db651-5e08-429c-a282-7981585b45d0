import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { MESSAGES, Reminders, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CrmContactListItem } from '@pages/administration/models/crm.model';
import { CrmContactReminders, ReminderParams } from '@pages/crm/models/crm-contact-reminder.model';
import { CrmContactRemindersService } from '@pages/crm/services/crm-contact-reminders.service';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { DataType, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';

@Component({
  selector: 'app-crm-contact-reminders',
  templateUrl: './crm-contact-reminders.component.html',
  styleUrls: ['./crm-contact-reminders.component.scss']
})
export class CrmContactRemindersComponent extends BaseComponent implements OnInit {
  @Input() crmContactInfo!: CrmContactListItem | null;
  @Input() accountRepList!: IdNameModel[];
  @Input() customerId!: string;
  isViewMode = false;
  @Input() reminderFor!: number;
  reminders!: Array<CrmContactReminders>;
  selectedReminder!: CrmContactReminders | null;
  showCreateModal = false;
  ActiveReminders = Reminders.ActiveReminders;
  filterReminderOptions = [
    this.ActiveReminders,
    'Archived Reminders',
    'Reminders History'
  ]
  filterBy = this.ActiveReminders;
  isArchiveInProgress = false;

  constructor(
    private readonly crmContactRemindersService: CrmContactRemindersService,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCrmContactReminders();
  }

  getCrmContactReminders(params?: any): void {
    this.crmContactRemindersService.getReminders(params ?? {
      contactId: this.crmContactInfo?.id,
      deleted: false,
    }, API_URL_UTIL.inventory.list).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Array<CrmContactReminders>) => {
        this.reminders = res;
        this.isLoading = false;
      }
    });
  }

  addNewReminder(remiderDetails: CrmContactReminders): void {
    if (!this.reminders.length) {
      this.reminders = [];
    }
    if (this.filterBy === this.ActiveReminders) {
      this.reminders.push(remiderDetails);
    }
  }

  editNewReminder(remiderDetails: CrmContactReminders): void {
    const selectedReminderIndex = this.reminders.findIndex(reminder => remiderDetails.id === reminder.id);
    this.reminders[selectedReminderIndex] = remiderDetails;
    this.selectedReminder = null;
  }

  onFilterChange(filterBy: string) {
    this.isLoading = true;
    let reminderParams: ReminderParams;
    if (this.crmContactInfo?.id) {
      switch (filterBy) {
        case 'Archived Reminders':
          reminderParams = {
            contactId: this.crmContactInfo.id,
            deleted: true,
          }
          break;
        case 'Reminders History':
          reminderParams = {
            contactId: this.crmContactInfo.id,
            deleted: false,
            treeOperator: TreeOperatorType.NOOP,
            values: [
              {
                dataType: DataType.ENUM,
                key: "reminderStatus",
                operator: OperatorType.EQUAL,
                value: "SEND",
                enumName: "CrmContactReminderStatus"
              }
            ]
          };
          break;
        default:
          reminderParams = {
            contactId: this.crmContactInfo.id,
            deleted: false,
          }
          break;
      }
      this.getCrmContactReminders(reminderParams);
    }
  }

  onEdit(crmContactReminderDetails: CrmContactReminders, isViewMode = false): void {
    this.showCreateModal = true;
    this.isViewMode = isViewMode;
    this.selectedReminder = crmContactReminderDetails;
  }

  onDelete(crmContactReminderDetails: CrmContactReminders, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'reminder'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(crmContactReminderDetails);
      }
    });
  }

  onDeleteConfirmation(crmContactReminderDetails: CrmContactReminders): void {
    this.crmContactRemindersService.delete(crmContactReminderDetails.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.reminderDeletedSuccess);
          this.reminders = this.reminders.filter(reminder => crmContactReminderDetails.id !== reminder.id);
        }
      });
  }

  onClose(): void {
    this.selectedReminder = null;
    this.showCreateModal = this.isViewMode = false;
  }

  toggleReminder(reminder: CrmContactReminders, isActive: boolean): void {
    this.selectedReminder = reminder;
    this.isArchiveInProgress = true;
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: `Are you sure you want to turn ${isActive ? 'on' : 'off'} recurrence for this reminder?`,
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.isArchiveInProgress = true;
        this.toggleReminderConfirmation(reminder);
        reminder.reoccurring = isActive;
      },
      reject: () => {
        reminder.reoccurring = !isActive;
        this.isArchiveInProgress = false;
        this.cdf.detectChanges();
      }
    });
  }

  toggleReminderConfirmation(reminder: CrmContactReminders): void {
    this.crmContactRemindersService.update({
      ...reminder,
      crmContactId: reminder.crmContact.id,
      remindToId: reminder.remindTo.id
    }).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.isArchiveInProgress = false;
          this.selectedReminder = null;
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedReminder = null;
        }
      });
  }

  getReminderScheduleAfter(scheduleAfter: string): string {
    return Number(scheduleAfter) ? `${scheduleAfter} Months` : 'Custom Date';
  }

}
