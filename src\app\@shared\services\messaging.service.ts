import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AngularFireMessaging } from '@angular/fire/compat/messaging';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { BehaviorSubject, Observable, Subject, takeUntil } from 'rxjs';
import { FirebaseRegistrationParams, NotificationParams, Notifications, Page, UnreadNotificationCount } from '../models';

@Injectable({
  providedIn: 'root'
})
export class MessagingService extends BaseComponent {
  currentMessage = new BehaviorSubject(null);
  unreadNotificationCount = new Subject<void>();

  constructor(
    private readonly angularFireMessaging: AngularFireMessaging,
    private readonly http: HttpClient,
    private readonly accountService: AuthService
  ) {
    super();
    this.angularFireMessaging.messages.subscribe(
      (_messaging: any) => {
        console.log('_messaging', _messaging);
        _messaging.onMessage = (payload: any) => {
          console.log({ payload });
          console.log('in on message');
          this.getMessage(payload);
        }
      }
    )
  }

  requestPermission(): void {
    this.angularFireMessaging.requestToken.subscribe({
      next: (token: string | null) => {
      },
      error: (err: any) => {
        console.error('Unable to get permission to notify.', err);
      }
    });
  }

  getFirebaseToken(): void {
    this.accountService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.angularFireMessaging.getToken.pipe(takeUntil(this.destroy$)).subscribe({
        next: (token: string | null) => {
          if (token) {
            localStorage.setItem(this.constants.firebase.registrationToken, token);
            if (res?.id) {
              const registerParams: FirebaseRegistrationParams = {
                recipientId: (res.id).toString(),
                token
              };
              this.registerToken(registerParams).pipe(takeUntil(this.destroy$)).subscribe();
            }
          }
        },
        error: (err: any) => {
          console.error("Unable to get permission to notify.", err);
        }
      })
    })
  }

  getMessage(payload: any): void {
    const notificationOptions = {
      title: payload.notification.title,
      body: payload.notification.body
    }
    navigator.serviceWorker.getRegistration(API_URL_UTIL.firebase.notification).then((registration: any) => {
      registration.showNotification(payload?.notification?.body, notificationOptions);
    });
    this.currentMessage.next(payload);
    this.setUnreadNotificationCount();
  }

  setUnreadNotificationCount(): void {
    this.unreadNotificationCount.next();
  }

  receiveMessage(): void {
    this.angularFireMessaging.messages.subscribe({
      next: (payload: any) => {
        this.getMessage(payload);
      }
    })
  }

  registerToken(registrationParams: FirebaseRegistrationParams): Observable<void> {
    return this.http.post<void>(`${API_URL_UTIL.firebase.root}${API_URL_UTIL.firebase.recipient}${API_URL_UTIL.firebase.register}`, registrationParams);
  }

  unregisterToken(registrationParams: FirebaseRegistrationParams): Observable<void> {
    return this.http.post<void>(`${API_URL_UTIL.firebase.root}${API_URL_UTIL.firebase.recipient}${API_URL_UTIL.firebase.unregister}`, registrationParams)
  }

  notificationHistory(userId: number, pageNumber: number, pageSize: number, data: NotificationParams): Observable<Page<Notifications>> {
    const params: HttpParams = new HttpParams()
      .set('recipientId', userId.toString())
      .set('page', pageNumber.toString())
      .set('size', pageSize.toString())
      .set('orderBy', 'sentDate')
      .set('direction', 'ASC');

    return this.http.post<Page<Notifications>>(`${API_URL_UTIL.firebase.notifications}/${API_URL_UTIL.firebase.filter}`, { ...data }, { params });
  }

  getNotificationCount(userId: number): Observable<UnreadNotificationCount> {
    const params: HttpParams = new HttpParams()
      .set('recipientId', userId?.toString());
    return this.http.get<UnreadNotificationCount>(`${API_URL_UTIL.firebase.root}${API_URL_UTIL.firebase.count}`, { params });
  }

  readNotifications(id: string): Observable<void> {
    const params: HttpParams = new HttpParams()
      .set('recipientId', id);
    return this.http.put<void>(`${API_URL_UTIL.firebase.root}${API_URL_UTIL.firebase.read}/${id}`, undefined, { params });
  }

  readAllNotifications(userId: number): Observable<void> {
    const params: HttpParams = new HttpParams()
      .set('recipientId', userId?.toString());
    return this.http.get<void>(`${API_URL_UTIL.firebase.root}${API_URL_UTIL.firebase.read}`, { params });
  }

  deleteNotification(id: string): Observable<void> {
    return this.http.delete<void>(`${API_URL_UTIL.firebase.root}${API_URL_UTIL.firebase.delete}/${id}`);
  }

}
