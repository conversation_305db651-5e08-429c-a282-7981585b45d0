<div class="modal-title close">
  <h4 class="header-title">{{ pageTitle }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
</div>

<form [formGroup]="shopEditFormGroup" (ngSubmit)="onUpdateShop()" *ngIf="selectedShop?.id">
  <div class="content category-form">
    <div class="form-group">
      <label for="name" class="required">Name</label>
      <input formControlName="name" type="text" class="form-control" placeholder="Enter shop name" />
      <app-error-messages [control]="shopEditFormGroup?.controls?.name"></app-error-messages>
      <label class="required mt-3">User</label>
      <p-dropdown
        appendTo="body"
        [options]="users"
        formControlName="defaultUserId"
        optionLabel="name"
        [showClear]="true"
        optionValue="id"
        [filter]="true"
        filterBy="name"
        placeholder="Select a user"
        name="defaultUser"
      >
        <ng-template pTemplate="empty">
          <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.shopUsers }"></ng-container>
        </ng-template>
        <ng-template let-assignee pTemplate="item">
          <span>{{ assignee.name }}</span>
        </ng-template>
      </p-dropdown>
      <app-error-messages [control]="shopEditFormGroup?.controls?.defaultUserId"></app-error-messages>
      <div class="modal-footer">
        <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
        <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
      </div>
    </div>
  </div>
</form>

<form [formGroup]="shopAddFormGroup" (ngSubmit)="onAddShop()" *ngIf="!selectedShop?.id">
  <div class="content category-form">
    <ng-container formArrayName="shop">
      <ng-container *ngFor="let model of shopFormArray.controls; let i = index">
        <div class="row card model-group" [formGroupName]="i">
          <div class="col-12">
            <label class="required">Name</label>
            <input class="form-control" type="text" placeholder="Enter Name" formControlName="name" />
            <small class="text-danger error-msg" *ngIf="shopFormArray.controls[i].get('name')?.touched || shopFormArray.controls[i].get('name')?.dirty">
              <span *ngIf="shopFormArray.controls[i].get('name')?.hasError('required')"> This field is required </span>
              <span *ngIf="shopFormArray.controls[i].get('name')?.errors?.maxlength"> Maximum 50 characters are allowed </span>
            </small>
          </div>

          <div class="col-8">
            <label class="required">User</label>
            <p-dropdown
              appendTo="body"
              [options]="users"
              formControlName="defaultUserId"
              optionLabel="name"
              [showClear]="true"
              optionValue="id"
              [filter]="true"
              filterBy="name"
              placeholder="Select a user"
              [name]="'assignee' + i"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.shopUsers }"></ng-container>
              </ng-template>
              <ng-template let-assignee pTemplate="item">
                <span>{{ assignee.name }}</span>
              </ng-template>
            </p-dropdown>
            <small class="text-danger error-msg" *ngIf="shopFormArray.controls[i].get('defaultUserId')?.touched || shopFormArray.controls[i].get('defaultUserId')?.dirty">
              <span *ngIf="shopFormArray.controls[i].get('defaultUserId')?.hasError('required')"> This field is required </span>
            </small>
          </div>
          <div class="col-4">
            <div class="add-model">
              <img *ngIf="i || shopFormArray.value.length !== 1" [src]="constants.staticImages.icons.deleteIcon" alt="" class="delete-shop" (click)="onDeleteShop(i)" />
              <button class="btn btn-primary" id="addShopBtn" type="button" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAddNewShop()" *ngIf="isAddNewShopVisible(i)">
                <span class="hide-label">Add Shop</span>
              </button>
            </div>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
