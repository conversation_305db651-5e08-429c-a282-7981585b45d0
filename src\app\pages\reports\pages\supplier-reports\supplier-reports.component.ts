import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { dateFormat } from '@constants/*';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { PassedParamGroup, VendorExpensesCreateParam } from '@pages/administration/models';
import { ColumnItem } from '@pages/common-table-column/models/common-table.column.model';
import { ExpensesListFilter, ExpensesListItem, VendorSupplierBasicInfo } from '@pages/inventory/models';
import { ExpensesService } from '@pages/inventory/services/expenses.service';
import { ReportModuleConst } from '@pages/reports/models/reports-const';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';
import { SalesDateRangeFilter } from '../crm-reports/models/daily-sales';


@Component({
  selector: 'app-supplier-reports',
  templateUrl: './supplier-reports.component.html',
  styleUrls: ['./supplier-reports.component.scss']
})
export class SupplierReportsComponent extends BaseComponent implements OnInit {
  dropdownLoaders = {
    supplier: false
  };
  suppliers: IdNameModel[] = [];
  totalExpenseAmount = 0;
  dropDownColumnList: ColumnItem[] = [];
  _selectedColumns: ColumnItem[] = [];
  globalSearch = new Subject<FilterValue[]>();
  supplierExpenseFormGroup!: FormGroup;
  filterParams: ExpensesListFilter = new ExpensesListFilter();
  expenses: ExpensesListItem[] | VendorExpensesCreateParam[] = [];
  purchasedDate!: Date | null;
  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }
  set selectedColumns(val: any[]) {
    this._selectedColumns = this.dropDownColumnList.filter(col => val.includes(col));
  }

  constructor(
    private readonly expensesService: ExpensesService,
    private readonly fb: FormBuilder,
    private readonly cdf: ChangeDetectorRef,
    private readonly commonService: CommonService,
    private readonly datePipe: DatePipe,
  ) {
    super();
    this.pageTitle = 'Suppliers Report';
    this.paginationConfig.itemsPerPage = 25;
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getDropDownColumnList();
    this.getVendorSupplierList();
    this.displaySearchResult();
  }


  private initializeFormGroup(): void {
    this.supplierExpenseFormGroup = this.fb.group({
      supplierId: new FormControl(null),
      startDateGroup: this.startDateFormGroup,
      endDateGroup: this.endDateFormGroup,
    });
  }

  private getVendorSupplierList(): void {
    this.dropdownLoaders.supplier = true;
    this.expensesService.getVendorSupplierList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (vendorSupplierList: VendorSupplierBasicInfo[]) => {
        vendorSupplierList.forEach((d: VendorSupplierBasicInfo) => {
          if (d.type === 'SUPPLIER') {
            this.suppliers.push({
              id: d.id,
              name: d.name
            });
          }
        })
        if (this.suppliers?.length) {
          this.supplierExpenseFormGroup.patchValue({
            supplierId: Number(this.suppliers[0].id) ?? 0
          })
        }
        this.dropdownLoaders.supplier = false;
        this.getAll();
        this.cdf.detectChanges();
      },
      error: () => {
        this.dropdownLoaders.supplier = false;
      }
    });
  }

  private getDropDownColumnList(): void {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${ReportModuleConst.SUPPLIER_EXPENSES_REPORT}`);
    this.commonService.getListFromObject<ColumnItem[]>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        this._selectedColumns = response;
        this.cdf.detectChanges();
      });
  }

  get startDateFormGroup(): FormGroup {
    return this.fb.group({
      value: new FormControl(null, Validators.required),
      key: new FormControl(PassedParamGroup.transDate),
      dataType: new FormControl(PassedParamGroup.date),
      operator: new FormControl(PassedParamGroup.greaterThanEqual),
    })
  }

  get endDateFormGroup(): FormGroup {
    return this.fb.group({
      value: new FormControl(null, Validators.required),
      key: new FormControl(PassedParamGroup.transDate),
      dataType: new FormControl(PassedParamGroup.date),
      operator: new FormControl(PassedParamGroup.lessThanEqual),
    })
  }

  clearSearchInput(): void {
    this.supplierExpenseFormGroup?.get('startDateGroup')?.reset();
    this.supplierExpenseFormGroup?.get('endDateGroup')?.reset();
    this.purchasedDate = null;
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    if (this.filterParams?.values?.length) {
      this.filterParams.values = [];
    }
    this.filterParams.treeOperator = this.filterParams?.values?.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  get supplierEndDateFormGroup(): FormGroup {
    return this.supplierExpenseFormGroup.get('endDateGroup') as FormGroup;
  }

  get supplierStartDateFormGroup(): FormGroup {
    return this.supplierExpenseFormGroup.get('startDateGroup') as FormGroup;
  }

  private dateFilterValues(inputDate: string, isStartingDate: boolean): FilterValue {
    const date = new Date(inputDate);
    let finalDate;
    if (isStartingDate) {
      finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0).toISOString();
    } else {
      finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50).toISOString();
    }
    return {
      value: finalDate,
      key: SalesDateRangeFilter.transDate,
      dataType: DataType.DATE,
      operator: isStartingDate ? OperatorType.GREATER_THAN_OR_EQUAL : OperatorType.LESS_THAN_OR_EQUAL,
    };
  }

  onSubmit(): void {
    if (this.filterParams.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(value => {
        return value.key !== SalesDateRangeFilter.transDate;
      })
    } else {
      this.filterParams.values = []
    }
    if (this.supplierExpenseFormGroup.value.startDateGroup.value) {
      this.filterParams.values.push(this.dateFilterValues(this.supplierExpenseFormGroup.controls.startDateGroup.value.value, true));
    }
    if (this.supplierExpenseFormGroup.value.endDateGroup.value) {
      this.filterParams.values.push(this.dateFilterValues(this.supplierExpenseFormGroup.controls.endDateGroup.value.value, false));
    }
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
    this.getAll();
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.supplierId = this.supplierExpenseFormGroup.value?.supplierId;
    this.filterParams.orderBy = this.orderBy;
    this.expensesService.getListWithFiltersWithPagination<ExpensesListFilter, ExpensesListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.expenses.expensesList)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.expenses = res.content;
          this.totalExpenseAmount = 0;
          this.expenses.forEach(expense => {
            this.totalExpenseAmount = this.totalExpenseAmount + Number(expense?.amount ?? 0);
          })
          this.setPaginationParamsFromPageResponse<ExpensesListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  setEndDate(endDate: string): void {
    const setEndHours = new Date(endDate).setUTCHours(23);
    const setEndDate = new Date(setEndHours).toISOString();
    const setEndMin = new Date(setEndDate).setUTCMinutes(59);
    const setEndDateMin = new Date(setEndMin).toISOString();
    const setEndSec = new Date(setEndDateMin).setUTCSeconds(59);
    const finalEndDate = new Date(setEndSec).toISOString();
    this.supplierExpenseFormGroup.value.endDateGroup.value = finalEndDate;
  }

  setStartDate(startDate: string): void {
    const setStartHours = new Date(startDate).setUTCHours(0);
    const setStartDate = new Date(setStartHours).toISOString();
    const setStartMin = new Date(setStartDate).setUTCMinutes(0);
    const finalStartDate = new Date(setStartMin).toISOString();
    this.supplierExpenseFormGroup.value.startDateGroup.value = finalStartDate;
  }

  private displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
      this.getAll();
    });
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  clearDate() {
    this.filterParams.values = this.filterParams.values.filter((param:any) => param.key !== "transDate");
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  tableSearchByColumn(event: any, col: any): void {
    this.isLoading = true;
    this.expenses = [];
    this.totalExpenseAmount = 0;
    const searchInput = this.getEventValue(event, col);
    this.getFilterInfo(searchInput, col);
    this.setSearchEndDate(event, col);
    if (this.filterParams?.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(values => {
        if (Array.isArray(values.value) && !values.value.length) {
          return false;
        }
        return true;
      });
    }
    this.globalSearch.next(this.filterParams.values);
    this.setValueForReset(searchInput, col);
  }

  private setValueForReset(input: string, col: ColumnItem): void {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);
    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    } else {
      if (temp) {
        temp.value = input;
      }
    }
    if (temp1) {
      temp1.value = temp?.value;
    }

  }

  private getEventValue(event: any, col: ColumnItem): string {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = col?.searchKey === 'status' ? event?.value?.split()?.join('_')?.toUpperCase() : event?.value
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format)
        temp = this.setFilterStartDate(new Date(endDate).toISOString())
        break;
      case 'MULTI_DROP_DOWN':
        temp = event?.value
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  private setFilterStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0).toISOString();
  }

  private getFilterInfo(inputValue: any, col: any): void {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.searchKey)
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.searchKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        if (inputValue?.length) {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
  }

  private assignOperator(type: string): string {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
      case 'DOUBLE':
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'MULTI_DROP_DOWN':
        operatorType = OperatorType.IN
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }


  private assignDataType(col: ColumnItem): string {
    let stringDataType = '';
    switch (col.type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN;
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER;
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE;
        break;
      case 'DATE':
        stringDataType = DataType.DATE;
        break;
      case 'DROP_DOWN':
        stringDataType = col?.searchKey === 'status' ? DataType.ENUM : DataType.INTEGER
        break;
      case 'MULTI_DROP_DOWN':
        stringDataType = DataType.LONG;
        break;
      default:
        stringDataType = DataType.STRING;
        break;
    }
    return stringDataType
  }

  private setSearchEndDate(event: any, col: any): void {
    const existingDate = this.filterParams.values.find((d: any) => d.key === col.key && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.key,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setFilterEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  private setFilterEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 23, 59, 50).toISOString();
  }

  exportUsersToExcel(): void {
    this.isExporting = true;
    this.expensesService.getListWithFiltersWithPagination<ExpensesListFilter, ExpensesListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, API_URL_UTIL.expenses.expensesList)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "Suppliers_Report");
          this.isExporting = false;
        });
      });
  }

  getExcelData(data: Array<ExpensesListItem>) {
    return data.map(res => ({
      'Stock#': res?.stockNumber,
      'Supplier Name': res?.supplier?.name,
      'Amount': res?.amount,
      'Purchase Date': Utils.dateIntoUserReadableFormat(res.transDate ? new Date(res.transDate).toLocaleDateString().replace(/\//g, '-') : ''),
    }));
  }
}
