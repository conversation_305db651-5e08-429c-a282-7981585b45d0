<app-page-header class="crm-contact-task d-flex justify-content-end">
  <div headerActionBtn>
    <button
      class="btn btn-primary left upload-button"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.uploadImage"
      (click)="documentInput.click()"
      [disabled]="documentList!.length >= 10"
      appShowLoaderOnApiCall
    >
      <span class="show-label">Upload Document</span>
    </button>
    <div class="text-secondary fs-12 d-flex justify-content-end mt-1">(Maximum 10 files allowed)</div>
    <input #documentInput type="file" id="Document" (change)="onFileSelect($event)" class="hide-upload-input" [accept]="constants.allowedPdfFormats" />
  </div>
</app-page-header>

<div class="card tabs crm-task-list">
  <div class="tab-content">
    <p-table
      class="no-column-selection"
      [value]="documentList"
      responsiveLayout="stack"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
      [scrollable]="true"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th id="fileName">Name</th>
          <th id="createdDate">Upload Date</th>
          <th class="small-col text-center" id="actions" style="max-width: 10rem">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td>{{ rowData?.fileName }}</td>
          <td>{{ rowData?.createdDate | date : constants.dateFormat }}</td>
          <td style="max-width: 10rem" class="actions">
            <div class="actions-content">
              <img [src]="constants.staticImages.icons.download" (click)="onDownload(rowData)" alt="Download" />
              <em class="pi pi-trash text-danger cursor-pointer mt-1" (click)="onDelete(rowData, $event)" alt="Delete"></em>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="9" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
  </div>
</div>
