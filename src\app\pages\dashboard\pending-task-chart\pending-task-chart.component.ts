import { ApplicationConfig, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { distinctUntilChanged, takeUntil } from 'rxjs';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';
import { TaskStatus, TaskStatusChartLabels, TaskStatusLabelColors } from '../dashboard.model';
import { DashboardService } from '../dashboard.service';

@Component({
  selector: 'app-pending-task-chart',
  templateUrl: './pending-task-chart.component.html',
  styleUrls: ['./pending-task-chart.component.scss']
})
export class PendingTaskChartComponent extends BaseComponent implements OnInit {
  @Input() isFullViewPendingTask!: boolean;
  taskStatusCount!: TaskStatus;
  data: any;
  isDarkMode = false;
  chartOptions: any;
  dataCountArray: number[] = [];
  config!: ApplicationConfig;
  pendingChartLabels = TaskStatusChartLabels;
  pendingLabelColors = TaskStatusLabelColors;
  @Output() onClose = new EventEmitter<boolean>();
  constructor(
    private readonly themeService: ThemeService,
    private readonly dashboardService: DashboardService,
    private readonly cdf: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.subscribeToTheme();
    this.getAllMonthlyAndDailySaleTasksStatus();
  }

  getAllMonthlyAndDailySaleTasksStatus(): void {
    this.dashboardService.get<TaskStatus>(API_URL_UTIL.dashboard.pendingTask)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.taskStatusCount = data;
          Object.keys(this.taskStatusCount).forEach((key, index) => {
            if (index !== 3) {
              this.dataCountArray.push(this.taskStatusCount[key as keyof TaskStatus]);
            }
          });
          this.cdf.detectChanges();
          this.ShowPendingChart();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  ShowPendingChart(): void {
    this.data = {
      datasets: [
        {
          data: this.dataCountArray,
          backgroundColor: [
            this.pendingLabelColors.TO_DO_COLOR,
            this.pendingLabelColors.ISSUE_COLOR,
            this.pendingLabelColors.IN_PROGRESS_COLOR,
          ],
        },
      ],
      labels: [this.pendingChartLabels.TO_DO, this.pendingChartLabels.ISSUE, this.pendingChartLabels.IN_PROGRESS],
    };
    this.chartOptions = {
      plugins: {
        legend: {
          labels: {
            color: this.isDarkMode ? 'white' : 'black',
          }
        }
      },
      scales: {
        r: {
          grid: {
            color: 'grey'
          }
        }
      }
    }
  }

  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        if (theme === THEMES.DARK) {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
        this.ShowPendingChart();
        this.cdf.detectChanges();
      });
  }

  onCancel(): void {
    this.onClose.emit(true);
  }

  getStatus(statusCount: number | undefined): number {
    return statusCount ? (statusCount * 100) / this.taskStatusCount?.totalPendingTasks : 0;
  }
}
