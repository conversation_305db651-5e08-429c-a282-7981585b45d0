import { ContactDetails } from "@pages/administration/models/crm.model";
import { SalesPersonDetails } from "@pages/inventory/models";
import { GenericFilterParams, IdNameModel } from "src/app/@shared/models";

export class QuatationListFilter extends GenericFilterParams {
  archived = false;
}

export class QuatationListItem {
  id!: string;
  quotePrice!: number;
  quotationStatus!: string;
  quotationDate!: string;
  company!: string;
  createdBy!: SalesPersonDetails;
  reasonForRejection!: string;
  customerLeadId!: string;
  location!: string;
  crmContact!:ContactDetails;
}

export class Unit {
  id!: number;
  generalInformation!: GeneralInformation
}

export class GeneralInformation {
  id!: number;
  year!: number;
  stockNumber!: string;
  vin!: string;
  unitStatus!: IdNameModel;
  make!: IdNameModel;
  unitModel!: IdNameModel;
  unitType!: IdNameModel;
}
