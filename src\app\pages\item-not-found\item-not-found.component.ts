import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BaseComponent } from '@core/utils';
import { distinctUntilChanged, takeUntil } from 'rxjs';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';

@Component({
  selector: 'app-item-not-found',
  templateUrl: './item-not-found.component.html',
  styleUrls: ['./item-not-found.component.scss']
})
export class ItemNotFoundComponent extends BaseComponent implements OnInit {
  isDarkMode: boolean = false;
  module!: string;
  statusType!: string;
  message!: string;

  constructor(private readonly route: ActivatedRoute, private readonly themeService: ThemeService) {
    super();
  }
  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      this.module = params.get('module') ?? 'page';
      this.statusType = params.get('status') ?? 'nf';
    });
    this.getMessage();
    this.subscribeToTheme();
  }

  getItem(): string {
    switch (this.module) {
      case 'crm-contact':
        return 'contact';

      default:
        return 'page';
    }
  }

  getMessage(): void {
    if (this.statusType === 'del') {
      this.message = `The ${this.getItem()} you were looking for has been deleted.`;
    } else {
      this.message = `The ${this.getItem()} you were looking for was not found.`;
    }
  }

  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        if (theme === THEMES.DARK) {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
      });
  }
}
