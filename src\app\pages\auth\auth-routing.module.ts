import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ROUTER_UTILS } from '@core/utils/router.utils';
import { OtpLoginComponent } from './pages/login/otp-login/otp-login.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: ROUTER_UTILS.config.auth.login,
  },
  {
    path: ROUTER_UTILS.config.auth.login,
    component: OtpLoginComponent,
    title: 'Skeye - Login'
  },
  {
    path: ROUTER_UTILS.config.auth.forgotPassword.root,
    title: 'Skeye - Forgot Password',
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: ROUTER_UTILS.config.base.root + ROUTER_UTILS.config.auth.root + ROUTER_UTILS.config.base.root + ROUTER_UTILS.config.auth.login,
      },
      {
        path: ROUTER_UTILS.config.auth.forgotPassword.init,
        loadChildren: () => import('./pages/forgot-password-init/forgot-password-init.module').then(m => m.ForgotPasswordInitComponentModule),
      },
      {
        path: ROUTER_UTILS.config.auth.forgotPassword.finish,
        loadChildren: () => import('./pages/forgot-password-finish/forgot-password-finish.module').then(m => m.ForgotPasswordFinishComponentModule),
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AuthRoutingModule { }
