#!/usr/bin/env bash
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

if ! head -1 "$1" | grep -qE "^(feat|fix|ci|chore|docs|test|style|refactor)(.+?)?: .{1,}$"; then
    echo -e "${RED} Aborting commit. Your commit message is invalid.
Error:
    Commit message must include the type of commit and branch name.
    It must be any out of feat|fix|ci|chore|docs|test|style|refactor.
    ${GREEN} e.g: feat ABC-123: added logs for failed signup ${NC}" >&2
    exit 1
fi

# check minimum length commit message
if head -1 "$1" | grep -qE "^.{1,20}$"; then
    echo -e "${RED} Aborting commit. Your commit message is too short. It should be more than 20 characters ${NC}" >&2
    exit 1
fi
