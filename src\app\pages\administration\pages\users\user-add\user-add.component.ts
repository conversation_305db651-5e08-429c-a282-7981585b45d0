import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Constants, MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { Role, UserCreateParam, UserDealerRole, UserListItem } from '@pages/administration/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { takeUntil } from 'rxjs';
import { UserService } from '../users.service';

@Component({
  selector: 'app-user-add',
  templateUrl: './user-add.component.html',
  styleUrls: ['./user-add.component.scss']
})
export class UserAddComponent extends BaseComponent implements OnInit, OnChanges {

  @Input() userInfo!: UserListItem | null;

  @Input() roles: Role[] = [];
  redirectUrl!: string;
  userFormGroup!: FormGroup;
  userDealerRoles: UserDealerRole[] = [];

  title = 'Add User';
  hasDataBeenModified = false;
  isEditMode = false;
  isEmailDisabled = true;
  contactNoFormat = Constants.phoneNumberMask;

  @Output() onClose = new EventEmitter<boolean>();

  constructor(
    private readonly fb: FormBuilder,
    private readonly userService: UserService,
    private readonly toasterService: AppToasterService,
    private readonly router: Router,
    private readonly activeRoute: ActivatedRoute,
    private readonly commonSharedService: CommonSharedService,
    private readonly authService: AuthService
  ) {
    super();
  }

  ngOnInit(): void {
    this.commonSharedService.setBlockUI$(true);
    this.initializeFormGroup();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.returnUrl) {
          this.redirectUrl = params?.returnUrl;
        }
      });
    this.getCurrentUser();
    this.commonSharedService.setBlockUI$(false);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.userInfo?.currentValue) {
      this.title = 'Edit User';
      this.isEditMode = true;
    }
  }

  private initializeFormGroup(): void {
    this.userFormGroup = this.fb.group({
      firstName: new FormControl(this.userInfo?.firstName ?? '', [Validators.required, Validators.maxLength(50)]),
      lastName: new FormControl(this.userInfo?.lastName ?? '', [Validators.required, Validators.maxLength(50)]),
      email: new FormControl(this.userInfo?.email ?? '', [Validators.required, Validators.email]),
      phoneNumber: new FormControl(this.userInfo?.phoneNumber ?? ''),
      employeeId: new FormControl(this.userInfo?.employeeId ?? null, [Validators.maxLength(50)]),
      roleDto: this.fb.group({ id: new FormControl(this.userInfo?.roleDto?.id ?? null, [Validators.required]) })
    });
  }

  get roleFormGroup(): FormGroup {
    return this.userFormGroup.get('roleDto') as FormGroup;
  }

  get userInfoCreateParams(): UserCreateParam {
    return {
      ...this.userFormGroup.value,
      id: this.userInfo?.id,
      organizationId: 1,
    };
  }

  onSubmit(close = true): void {
    if (this.userFormGroup.invalid) {
      this.userFormGroup.markAllAsTouched();
      return;
    }
    const employeeId = this.userFormGroup.controls['employeeId'].value || null;
    this.userFormGroup.controls['employeeId'].setValue(employeeId);
    this.userFormGroup.markAsUntouched();
    if (this.isEditMode) {
      this.editUser();
    } else {
      this.saveUser(close);
    }
  }

  saveUser(close = true): void {
    this.userService.add(this.userInfoCreateParams, API_URL_UTIL.admin.users.register).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.userUpdateSuccess : MESSAGES.userAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.userFormGroup.reset();
        this.initializeFormGroup();
      }
    });
  }

  editUser(): void {
    this.userService.update(this.userInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.userUpdateSuccess : MESSAGES.userAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.goBack();
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      this.isEmailDisabled = user?.role.id !== 1 && user?.role.id !== 2;
    });
  }
}
