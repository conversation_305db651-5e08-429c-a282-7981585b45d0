import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { PaginationModule } from 'ngx-bootstrap/pagination';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { DropdownModule } from 'primeng/dropdown';
import { DirectivesModule } from '../directives/directives.module';
import { PipesModule } from '../pipes/pipes.module';
import { SharedLibsModule } from '../shared-libs.module';
import { ErrorHandlerComponent } from './error-handler/error-handler.component';
import { PageHeaderComponent } from './page-header/page-header.component';
import { RecentActivityComponent } from './recent-activity/recent-activity.component';
import { ServerPaginationComponent } from './server-pagination/server-pagination.component';

const COMPONENTS = [
  ErrorHandlerComponent,
  PageHeaderComponent,
  ServerPaginationComponent,
  RecentActivityComponent,
];

@NgModule({
  imports: [
    SharedLibsModule,
    DirectivesModule,
    PipesModule,
    FontAwesomeModule,
    RouterModule,
    AccordionModule.forRoot(),
    PaginationModule.forRoot(),
    BsDropdownModule,
    DropdownModule,
    InfiniteScrollModule
  ],
  declarations: [...COMPONENTS,],
  exports: [...COMPONENTS],
})
export class SharedComponentsModule { }
