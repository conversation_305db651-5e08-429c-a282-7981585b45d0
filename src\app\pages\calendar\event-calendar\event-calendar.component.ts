import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { dateFormat } from '@constants/*';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { FullCalendarComponent } from '@fullcalendar/angular';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import timeGridPlugin from '@fullcalendar/timegrid';
import { IdModel } from '@pages/inventory/models';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { takeUntil } from 'rxjs';
import { CalendarFilterParams, DataType, FilterValue, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { EventCalendarService } from './event-calendar.service';
import { CalenderEventMenipulateResponse, CalenderEventResponse, CalenderEventViewDetail, DropdownOptions, EventType, User } from './models/evnet-calendar';

@Component({
  selector: 'app-event-calendar',
  templateUrl: './event-calendar.component.html',
  styleUrls: ['./event-calendar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush

})
export class EventCalendarComponent extends BaseComponent implements OnInit {

  @ViewChild('calendar') calendarComponent!: FullCalendarComponent;
  @ViewChild('rangeCalendar') private calendar: any;

  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  closeSidebar = false;
  calendarOptions: any;
  eventInfoDialog = false;
  selectedEventInfo: any;
  eventLists: CalenderEventMenipulateResponse[] = [];
  dropdownOptions = DropdownOptions;
  selectedType!: any;
  selectedColorCode!: string
  selectedUserId!: number | null;
  rangeDates!: Date[];
  users!: IdModel[];
  todayDate: Date = new Date();
  startDate: Date = new Date(this.todayDate.getFullYear(), this.todayDate.getMonth(), 1);
  endDate: Date = new Date(this.todayDate.getFullYear(), this.todayDate.getMonth() + 1, 0);
  calendarEvent = CalenderEventViewDetail;
  filterValue!: FilterValue[]
  entityName!: string;
  constructor(
    private readonly datePipe: DatePipe,
    private readonly cdf: ChangeDetectorRef,
    private readonly eventCalendarService: EventCalendarService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly router: Router
  ) {
    super();
    this.pageTitle = 'Calendar';
  }

  ngOnInit(): void {
    this.getUsers();
    this.setDefaultAllType();
    this.setDefaultFilterValue();
    this.getEventCalenderList();
  }

  setDefaultFilterValue() {
    this.filterValue = [
      {
        dataType: DataType.DATE,
        key: "startDate",
        operator: OperatorType.GREATER_THAN_OR_EQUAL,
        value: this.setStartDate(this.dateTransform(this.startDate))
      }, {
        dataType: DataType.DATE,
        key: "startDate",
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(this.dateTransform(this.endDate))
      }
    ]
  }

  closePopup() {
    this.onClose.emit(this.closeSidebar);
  }

  onCalenderLoad() {
    this.calendarOptions = {
      initialView: 'dayGridMonth',
      headerToolbar: {
        left: 'prev,next today',
        center: 'title',
        right: 'dayGridMonth,dayGridWeek,dayGridDay'
      },
      editable: true,
      selectable: true,
      selectMirror: true,
      dayMaxEvents: true,
      contentHeight: 1000,
      initialDate: this.datePipe.transform(this.todayDate, dateFormat.format),
      plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
      events: this.eventLists,
      eventClick: this.handleEventClick.bind(this)
    }
    this.cdf.detectChanges();
  }

  handleEventClick(arg: any) {
    this.eventInfoDialog = true;
    this.selectedEventInfo = arg.event
    this.cdf.detectChanges();
  }

  setDefaultAllType() {
    this.selectedType = this.calendarEvent.find(c => c.value === EventType.ALL)
    this.entityName = this.selectedType.entity;
  }

  get requestParams(): CalendarFilterParams {
    return {
      treeOperator: TreeOperatorType.NOOP,
      entity: this.entityName ? this.entityName : EventType.ALL,
      userId: this.selectedUserId,
      left: {
        treeOperator: TreeOperatorType.AND,
        values: this.filterValue
      }
    }
  }

  dateTransform(date: Date) {
    const tempDate: any = this.datePipe.transform(date, dateFormat.format);
    return new Date(tempDate).toISOString();
  }

  setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50)).toISOString();
  }

  setStartDate(startDate: string): string {
    const date = new Date(startDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString();
  }

  getEventCalenderList() {
    this.eventCalendarService.add(this.requestParams, `${API_URL_UTIL.calendar.filter}`).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response: CalenderEventResponse[]) => {
        if (response) {
          this.manipulateResponseList(response);
          this.onCalenderLoad();
          this.cdf.detectChanges();
        }
      }
    });
  }

  manipulateResponseList(responseData: CalenderEventResponse[]) {
    this.eventLists = [];
    responseData.forEach((data: CalenderEventResponse) => {
      this.eventLists.push({
        title: data.title.concat(` ${data.userName}`),
        start: this.datePipe.transform(data.startDate, dateFormat.format),
        end: this.datePipe.transform(data.endDate, dateFormat.format),
        color: this.eventColor(data),
        userName: data.userName,
        driverScheduleId: data.driverScheduleId,
        entityName: data.entityName,
        taskType: data.taskType,
        initialTitle: data.title,
        driverScheduleStatus: data.driverScheduleStatus
      });
    });
  }

  eventColor(response: any) {
    return this.calendarEvent.find(c => c.value === response.taskType || c.value === response.entityName)?.code
  }

  onChange(event: any, field: string) {
    let apiShouldCall = false;
    if (field === 'user') {
      this.selectedUserId = event;
      apiShouldCall = true;
    }
    if (field === 'type') {
      this.selectedType = event;
      this.entityName = this.selectedType.entity;
      this.selectedColorCode = event.code;
      this.addTaskInToRequestParams(event);
      apiShouldCall = true;

    }
    if (field === 'calender') {
      this.setSelectedDateToRequest(this.rangeDates[0], this.rangeDates[1]);
      if (this.rangeDates?.[1]) {
        apiShouldCall = true;
        this.calendar.overlayVisible = false;
      }
      this.todayDate = new Date(this.rangeDates[0])
      this.calendarComponent.getApi().gotoDate(this.todayDate)
    }
    if (apiShouldCall) {
      this.getEventCalenderList();
    }

  }

  setSelectedDateToRequest(startDate: Date, endDate: Date) {
    const dates = this.filterValue.filter(f => f.key === 'startDate')
    dates[0].value = this.setStartDate(this.dateTransform(startDate))
    dates[1].value = this.setEndDate(this.dateTransform(endDate === null ? startDate : endDate))
  }

  addTaskInToRequestParams(event: any) {
    const temp = this.filterValue.find(value => value.key === "taskType.name")

    if (event.entity === EventType.TASK) {
      if (temp === undefined) {
        this.filterValue.push({
          dataType: DataType.STRING,
          key: "taskType.name",
          operator: OperatorType.EQUAL,
          value: event.value
        })
      } else {
        temp.value = event.value
      }
    } else {
      if (temp) {
        this.filterValue.splice(this.filterValue.indexOf(temp), 1)
      }
    }
  }

  eventClick(event: any) {
    if (event.target.parentElement.title === 'Next month' || event.target.parentElement.title === 'Previous month' || event.target.title === 'This month') {
      this.rangeDates = [];
      const calendarApi = this.calendarComponent.getApi();
      const currentDate = calendarApi.view.currentStart;
      this.setSelectedDateToRequest(calendarApi.view.currentStart,
        new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0))
      this.getEventCalenderList();
    }
  }

  getUsers(): void {
    this.isLoading = true;
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.users = res.map((user: any) => ({ id: user.id, label: user.name }));
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: (err: any) => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  goToUrl(detail: any) {
    const popupDetail = detail._def.extendedProps
    if (popupDetail.entityName === EventType.TASK) {
      this.navigateToTaskUrl(popupDetail.initialTitle, popupDetail.taskType)
    } else {
      this.router.navigateByUrl(`${API_URL_UTIL.transportDriverBoard}?id=${popupDetail.driverScheduleId}&status=${popupDetail.driverScheduleStatus}&redirectUrl=calendar`);
    }
  }

  navigateToTaskUrl(title: string, type: string) {
    let endpoint;
    switch (type) {
      case 'Sales': {
        endpoint = `${API_URL_UTIL.admin.crm.root}/${API_URL_UTIL.admin.crm.crmTask}`
          .concat(`?id=${title}&mode=1&redirectUrl=calendar`);
        break;
      }
      case 'General': {
        endpoint = `${API_URL_UTIL.admin.shops.root}`.concat(`?id=${title}&mode=1&redirectUrl=calendar`);
        break;
      }
      case 'Sold Pipeline': {
        endpoint = `${API_URL_UTIL.admin.shops.root}`.concat(`?id=${title}&mode=1&redirectUrl=calendar`);
        break;
      }
      case 'Stock Pipeline': {
        endpoint = `${API_URL_UTIL.admin.shops.root}`.concat(`?id=${title}&mode=1&redirectUrl=calendar`);
        break;
      }
    }
    if (endpoint) {
      this.router.navigateByUrl(endpoint);
    }
  }

  reset() {
    this.rangeDates = [];
    this.selectedType = null;
    this.selectedUserId = null;
    this.setDefaultFilterValue();
    this.setSelectedDateToRequest(new Date(this.todayDate.getFullYear(), this.todayDate.getMonth(), 1),
      new Date(this.todayDate.getFullYear(), this.todayDate.getMonth() + 1, 0))
    this.todayDate = new Date();
    this.getEventCalenderList();
  }
}
