import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NavigationService } from '@core/services';
import { BaseComponent, ROUTER_UTILS } from '@core/utils';
import { Role } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';

@Component({
  selector: 'app-not-authorized',
  templateUrl: './not-authorized.component.html',
  styleUrls: ['./not-authorized.component.scss']
})
export class NotAuthorizedComponent extends BaseComponent implements OnInit {

  userPermissions!: Role;

  constructor(
    private readonly authService: AuthService,
    private readonly navigationService: NavigationService,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.userPermissions = this.authService.getRoleInfo();
  }

  navigateToAuthorizedPage() {
    if (this.userPermissions?.name === 'ROLE_DRIVER') {
      this.router.navigate([ROUTER_UTILS.config.base.root, ROUTER_UTILS.config.transport.root, ROUTER_UTILS.config.transport.incomingTruckBoard.root])
      return;
    }
    this.navigationService.toDashboard();
  }
}
