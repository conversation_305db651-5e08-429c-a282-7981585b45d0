@import 'src/assets/scss/variables';

.acquisition-info {
  .mt-23 {
    margin-top: 22px;
  }
  .add-btn {
    height: 25px;
    padding: 0px 5px;
    float: right;
    margin: 3px;
  }
  .m-t-30 {
    margin-top: 30px;
  }

  .m-l-10 {
    margin-left: 10px;
  }
  .m-t-20 {
    margin-top: 20px;
  }
  .m-t-24 {
    margin-top: 24px;
  }

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn {
      margin-bottom: 10px;
      span {
        color: white !important;
        font-size: 15px !important;
        font-weight: 400 !important;
        text-transform: none;
      }
    }
  }

  @media only screen and (max-width: 500px) {
    .document-upload {
      flex-wrap: wrap;
      justify-content: center !important;
      align-items: end;
    }
  }
}

::ng-deep .acquisition-info {
  .p-inline-message.p-inline-message-info {
    height: 1px;
  }

  .p-dialog .p-dialog-header .p-dialog-title {
    font-weight: 500;
    font-size: 15px;
  }

  .p-dialog .p-dialog-content {
    padding: 0;
  }

  .radio-wrapper {
    display: flex;

    p-radiobutton {
      margin-right: 30px;
    }
  }

  .file-size-message {
    bottom: 11px;
    right: 49px;
    font-size: 15px;
    width: 268px;
  }

  .p-disabled,
  .p-component:disabled,
  textarea:disabled {
    color: $disable-color;
    opacity: 1;
  }

  .p-disabled .p-dropdown-label,
  .p-component:disabled .p-dropdown-label {
    color: $disable-color;
  }

  .drop-zone-disabled {
    color: $disable-color;
    background-color: $disable-background-color !important;
    opacity: 1;
  }

  .m-t-10 {
    margin-top: 10px;
  }
}
