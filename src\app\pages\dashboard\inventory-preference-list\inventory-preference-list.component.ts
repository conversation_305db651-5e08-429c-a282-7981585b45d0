import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { takeUntil } from 'rxjs';
import { InventoryListItem } from '../dashboard.model';
import { DashboardService } from '../dashboard.service';

@Component({
  selector: 'app-inventory-preference-list',
  templateUrl: './inventory-preference-list.component.html',
  styleUrls: ['./inventory-preference-list.component.scss']
})
export class InventoryPreferenceListComponent extends BaseComponent implements OnInit {
  inventoryPreference: InventoryListItem[] = [];
  @Input() isFullView!: boolean;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(private readonly dashboardService: DashboardService, private readonly cdf: ChangeDetectorRef) { super() }

  ngOnInit(): void {
    this.getAllInventoryPreference();
  }

  getAllInventoryPreference(): void {
    this.isLoading = true;
    const endpoint = `${API_URL_UTIL.dashboard.inventoryPrefernce}`;
    this.dashboardService.getList<InventoryListItem>(endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.inventoryPreference = res;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  getMakeNames(makes: any[]): string {
    return makes.map(make => make.makeName).join(', ');
  }

  getUnitModelsNames(models: any[]): string {
    return models.map(model => model.unitModelName).join(', ');
  }

  onCancel(): void {
    this.onClose.emit(true);
  }
}
