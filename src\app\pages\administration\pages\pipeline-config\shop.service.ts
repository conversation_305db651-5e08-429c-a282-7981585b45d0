import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { ShopUser } from "@pages/administration/models";
import { Observable } from "rxjs";

@Injectable({ providedIn: 'root' })
export class ShopService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.shops.root;
  }

  getShopUsers(shopId: number): Observable<ShopUser[]> {
    return this.httpClient.get<ShopUser[]>(`${API_URL_UTIL.admin.shops.users}`);
  }
}
