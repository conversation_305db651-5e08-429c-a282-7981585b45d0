import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { dateFormat } from '@constants/*';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { User } from '@pages/calendar/event-calendar/models/evnet-calendar';
import { ColumnItem } from '@pages/common-table-column/models/common-table.column.model';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { ReportingFilterParamsWithArchive } from '@pages/reports/models/reports';
import { ReportModuleConst } from '@pages/reports/models/reports-const';
import { ReportingService } from '@pages/reports/services/reporting-service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';
import { ActivityReportListItem } from '../../models/activity-model';
import { SalesDateRangeFilter } from '../../models/daily-sales';

@Component({
  selector: 'app-activity-report',
  templateUrl: './activity-report.component.html',
  styleUrls: ['./activity-report.component.scss']
})
export class ActivityReportComponent extends BaseComponent implements OnInit {
  filterParams: ReportingFilterParamsWithArchive = new ReportingFilterParamsWithArchive();
  _selectedColumns: ColumnItem[] = [];
  dropDownColumnList: ColumnItem[] = [];
  activityList: ActivityReportListItem[] = [];
  salesRepList: User[] = [];
  dateFilterFormGroup!: FormGroup;
  accountRepIds: Array<number> = [];
  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }
  set selectedColumns(val: any[]) {
    this._selectedColumns = this.dropDownColumnList.filter(col => val.includes(col));
  }
  dropdownLoaders = {
    salesRep: false
  };
  globalSearch = new Subject<FilterValue[]>();
  constructor(
    private readonly cdf: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly reportingService: ReportingService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly commonService: CommonService,
    private readonly fb: FormBuilder
  ) {
    super();
    this.pageTitle = 'Activity Report';
    this.paginationConfig.itemsPerPage = 25;
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getDropDownColumnList();
    this.getSalespersonList();
    this.displaySearchResult();
    this.onSubmit();
  }

  private initializeFormGroup(): void {
    this.dateFilterFormGroup = this.fb.group({
      startDateGroup: this.startDateFormGroup,
      endDateGroup: this.endDateFormGroup,
    });
  }

  private get startDateFormGroup(): FormGroup {
    return this.fb.group({
      value: new FormControl(new Date()),
      key: new FormControl(SalesDateRangeFilter.createdDate),
      dataType: new FormControl(SalesDateRangeFilter.date),
      operator: new FormControl(SalesDateRangeFilter.greaterThanEqual),
    })
  }

  private get endDateFormGroup(): FormGroup {
    return this.fb.group({
      value: new FormControl(new Date()),
      key: new FormControl(SalesDateRangeFilter.createdDate),
      dataType: new FormControl(SalesDateRangeFilter.date),
      operator: new FormControl(SalesDateRangeFilter.lessThanEqual),
    })
  }

  get dailySalesEndDateFormGroup(): FormGroup {
    return this.dateFilterFormGroup.get('endDateGroup') as FormGroup;
  }

  get dailySalesStartDateFormGroup(): FormGroup {
    return this.dateFilterFormGroup.get('startDateGroup') as FormGroup;
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  private getDropDownColumnList(): void {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${ReportModuleConst.ACTIVITY_REPORT}`);
    this.commonService.getListFromObject<ColumnItem[]>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        this._selectedColumns = response;
        this.cdf.detectChanges();
      });
  }

  private displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
      this.getAll();
    });
  }

  getSalespersonList(): void {
    this.dropdownLoaders.salesRep = true;
    const endpoint = API_URL_UTIL.reports.salesPersonListOfActivityReport;
    this.reportingService.getSalesPersonList(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (salesRepList:User[]) => {
        this.salesRepList = salesRepList;
        this.dropdownLoaders.salesRep = false;
      },
      error: () => {
        this.dropdownLoaders.salesRep = false;
      }
    });
  }


  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    const endpoint = `${API_URL_UTIL.reports.crmActivity}/${API_URL_UTIL.reports.filter}`;
    this.reportingService.getListWithFiltersWithPagination<ReportingFilterParamsWithArchive, ActivityReportListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.activityList = res.content;
          this.setPaginationParamsFromPageResponse<ActivityReportListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  tableSearchByColumn(event: any, col: any): void {
    this.isLoading = true;
    this.activityList = [];
    const searchInput = this.getEventValue(event, col);
    this.getFilterInfo(searchInput, col);
    this.setSearchEndDate(event, col);
    if (this.filterParams?.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(values => {
        if (Array.isArray(values.value) && !values.value.length) {
          return false;
        }
        return true;
      });
    }
    this.globalSearch.next(this.filterParams.values);
    this.setValueForReset(searchInput, col);
  }

  private setValueForReset(input: string, col: ColumnItem): void {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);
    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    } else {
      if (temp) {
        temp.value = input;
      }
    }
    if (temp1) {
      temp1.value = temp?.value;
    }

  }

  private getEventValue(event: any, col: ColumnItem): string {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = col?.searchKey === 'status' ? event?.value?.split()?.join('_')?.toUpperCase() : event?.value
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format)
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      case 'MULTI_DROP_DOWN':
        temp = event?.value
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  private setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString();
  }

  private getFilterInfo(inputValue: any, col: any): void {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.searchKey)
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.searchKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        if (inputValue?.length) {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
  }

  private assignOperator(type: string): string {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
      case 'DOUBLE':
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'MULTI_DROP_DOWN':
        operatorType = OperatorType.IN
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }


  private assignDataType(col: ColumnItem): string {
    let stringDataType = '';
    switch (col.type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN;
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER;
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE;
        break;
      case 'DATE':
        stringDataType = DataType.DATE;
        break;
      case 'DROP_DOWN':
        stringDataType = col?.searchKey === 'status' ? DataType.ENUM : DataType.INTEGER
        break;
      case 'MULTI_DROP_DOWN':
        stringDataType = DataType.LONG;
        break;
      default:
        stringDataType = DataType.STRING;
        break;
    }
    return stringDataType
  }

  private setSearchEndDate(event: any, col: any): void {
    const existingDate = this.filterParams.values.find((d: any) => d.key === col.key && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.key,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  private setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50)).toISOString();
  }

  clearSalesRep(col: ColumnItem): void {
    this.accountRepIds = [];
    this.tableSearchByColumn([], col);
  }

  clearSearchInput(): void {
    this.dateFilterFormGroup.reset();
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.accountRepIds = this.filterParams.values = [];
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  exportUsersToExcel(): void {
    this.isExporting = true;
    const endpoint = `${API_URL_UTIL.reports.crmActivity}/${API_URL_UTIL.reports.filter}`;
    this.reportingService.getListWithFiltersWithPagination<ReportingFilterParamsWithArchive, ActivityReportListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, endpoint)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "Activity_Report");
          this.isExporting = false;
        });
      });
  }

  getExcelData(data: Array<ActivityReportListItem>) {
    return data.map(res => ({
      'First Name': res?.firstName,
      'Last Name': res?.lastName,
      'Company': res?.company,
      'Salesperson': res?.salesPerson?.name,
      'Created Date': Utils.dateIntoUserReadableFormat(res?.createdDate ?? '')
    }));
  }

  private dateFilterValues(inputDate: string, isStartingDate: boolean): FilterValue {
    const date = new Date(inputDate);
    let finalDate;
    if (isStartingDate) {
      finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0).toISOString();
    } else {
      finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50).toISOString();
    }
    return {
      value: finalDate,
      key: SalesDateRangeFilter.createdDate,
      dataType: DataType.DATE,
      operator: isStartingDate ? OperatorType.GREATER_THAN_OR_EQUAL : OperatorType.LESS_THAN_OR_EQUAL,
    };
  }

  onSubmit(): void {
    if (this.filterParams.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(value => {
        return value.key !== SalesDateRangeFilter.createdDate;
      })
    } else {
      this.filterParams.values = []
    }
    if (this.dateFilterFormGroup.controls.startDateGroup.value.value) {
      this.filterParams.values.push(this.dateFilterValues(this.dateFilterFormGroup.controls.startDateGroup.value.value, true));
    }
    if (this.dateFilterFormGroup.controls.endDateGroup.value.value) {
      this.filterParams.values.push(this.dateFilterValues(this.dateFilterFormGroup.controls.endDateGroup.value.value, false));
    }
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
    this.getAll();
  }
}
