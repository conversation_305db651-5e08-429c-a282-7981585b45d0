import {
  HttpErrorResponse,
  HttpEvent, HttpHandler,
  HttpInterceptor,
  HttpRequest,
  HttpResponse
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MESSAGES } from '@constants/*';
import { AppToasterService, CommonService } from '@core/services';
import { AuthService } from '@pages/auth/services/auth.service';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ServerMessage } from '../../@shared/models';

@Injectable({ providedIn: 'root' })
export class ServerErrorInterceptor implements HttpInterceptor {
  constructor(private readonly authService: AuthService, private readonly commonService: CommonService,
    private readonly toasterService: AppToasterService) { }

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>,): Observable<HttpEvent<unknown>> {
    this.commonService.isApiCallInProgress$.next(true);
    return next.handle(request).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          this.commonService.isApiCallInProgress$.next(false);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        this.commonService.isApiCallInProgress$.next(false);
        const errorResponse: ServerMessage = error.error;
        this.toasterService.error(errorResponse.message || MESSAGES.internalServerError);
        if ([401].includes(error.status)) {
          this.authService.logOut();
          return throwError(error);
        } else if (error.status === 500) {
          console.error(error);
          return throwError(error);
        } else {
          return throwError(error);
        }
      }),
    );
  }
}

