import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { DealerListFilter, DealerUserGroupItem, Role, Shop, UserDealerRole } from '@pages/administration/models';
import { DealerService } from '@pages/administration/pages/dealers/dealers.service';
import { ShopService } from '@pages/administration/pages/pipeline-config/shop.service';
import { RoleService } from '@pages/administration/pages/users/roles.service';
import { UserService } from '@pages/administration/pages/users/users.service';
import { DriverCreateParams } from '@pages/transport/models/driver-schedule.model';
import { DriverScheduleService } from '@pages/transport/services/driver-schedule.service';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';

@Component({
  selector: 'app-driver-add',
  templateUrl: './driver-add.component.html',
  styleUrls: ['./driver-add.component.scss']
})
export class DriverAddComponent extends BaseComponent implements OnInit {
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  title = 'Add New Driver';
  hasDataBeenModified = false;
  driverFormGroup!: FormGroup;
  isEditMode = false;
  shops: IdNameModel[] = [];
  shopList: IdNameModel[] = [];
  driverNameList: IdNameModel[] = []
  loaders = {
    shops: false,
    role: false
  };
  dealers: DealerUserGroupItem[] = [];
  filterParams: DealerListFilter = new DealerListFilter();
  userDealerRoles: UserDealerRole[] = [];
  selectedShops: Shop[] = [];
  roles: Role[] = [];
  updatedValue: any[] = []
  @Input() isFormDisabled = false;
  driverId!: number;
  organizationId = 1;
  constructor(private readonly fb: FormBuilder, private readonly roleService: RoleService, private readonly userService: UserService, private readonly dealerService: DealerService, private readonly toasterService: AppToasterService, private readonly driverScheduleService: DriverScheduleService, private readonly cdf: ChangeDetectorRef, private readonly shopService: ShopService) { super() }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getAllDealers();
    this.getAllRoles();
  }

  private initializeFormGroup(): void {
    this.driverFormGroup = this.fb.group({
      firstName: new FormControl(null, [Validators.required]),
      lastName: new FormControl(null, [Validators.required]),
      phoneNumber: new FormControl(null),
      email: new FormControl(null, [Validators.required]),
      employeeId: new FormControl(null, [Validators.required]),
      role: new FormControl('Driver', [Validators.required]),
    });
    this.driverFormGroup.get('role')?.disable();
  }

  get driverCreateParams(): DriverCreateParams {
    return {
      ...this.driverFormGroup.value,
      organizationId: this.organizationId,
      roleDto: { id: 7 }
    };
  }

  getAllDealers(): void {
    this.isLoading = true;
    this.dealerService.getListWithFiltersWithPagination<DealerListFilter, DealerUserGroupItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.dealers.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.dealers = res.content;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onSubmit(close = true): void {
    this.driverFormGroup.updateValueAndValidity();
    if (this.driverFormGroup.invalid) {
      this.driverFormGroup.markAllAsTouched();
      return;
    }
    this.saveDriver(close);
  }

  saveDriver(close = true): void {
    this.userService.add(this.driverCreateParams, API_URL_UTIL.admin.users.register).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.driverAddedSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.driverFormGroup.reset();
      }
    });
  }

  onSelectShop(event: any): void {
    this.selectedShops = event.value;
  }

  getAllRoles(): void {
    this.roleService.getList<Role>().pipe(takeUntil(this.destroy$)).subscribe(res => {
      this.roles = res;
      for (const role of this.roles) {
        if (role.id === 4) {
          this.driverId = role.id;
        }
      }
    });
  }

  onSelectDealer(event: any): void {
    let shopData!: any;
    this.shopList = [];
    for (const dealerId of event.value) {
      shopData = this.dealers.find(x => x.id === dealerId);
      for (const shops of shopData?.shops) {
        if (!this.shopList.length || this.shopList.find(x => x.id !== shops.id)) {
          this.shopList.push(shops);
        }
      }
    }
    this.userDealerRoles = this.userDealerRoles.filter(d => d.dealerId !== event.itemValue);
    this.userDealerRoles.push({ dealerId: event.itemValue, roleId: this.driverId });
  }
}
