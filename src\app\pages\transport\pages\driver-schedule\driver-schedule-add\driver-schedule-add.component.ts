import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Constants, MESSAGES, driverAddress, driverScheduleStatus } from '@constants/*';
import { AppToasterService, CommonService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { allowExtensions, getRefactorFileName } from '@core/utils/fileUpaloadName.util';
import { AuthService } from '@pages/auth/services/auth.service';
import { AssociationsUnits } from '@pages/inventory/models';
import { AllUnitsAssociation } from '@pages/inventory/services/all-units.service';
import { StockBasicInfo } from '@pages/shops/models';
import { Address, DriverList, DriverSchedule, DriverScheduleAttachments, DriverScheduleBoardListItem, DriverScheduleCreateParams, ScheduleForList } from '@pages/transport/models/driver-schedule.model';
import { DriverScheduleService } from '@pages/transport/services/driver-schedule.service';
import { IncomingTruckService } from '@pages/transport/services/incoming-truck.service';
import * as saveAs from 'file-saver';
import { Address as gAddress } from 'ngx-google-places-autocomplete/objects/address';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FileProperties, FileUploadProgress, FilterValue, GenericFilterParams, IdNameModel, OperatorType, RoleNames, TreeOperatorType } from 'src/app/@shared/models';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-driver-schedule-add',
  templateUrl: './driver-schedule-add.component.html',
  styleUrls: ['./driver-schedule-add.component.scss']
})
export class DriverScheduleAddComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Driver Schedule';
  pageNumber = 0;
  isLastPage = false;
  hasDataBeenModified = false;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() driverScheduleInfo!: DriverScheduleBoardListItem | null;
  driverScheduleFormGroup!: FormGroup;
  isEditMode = false;
  stockBasicInfo: StockBasicInfo[] = [];
  loaders = {
    stockNumbers: false,
    status: false,
    role: false
  }
  filterParams = new GenericFilterParams();
  driverScheduleStatus: IdNameModel[] = [];
  showCreateModal = false;
  scheduleFor: IdNameModel[] = [];
  address!: Address;
  driverNameList: DriverList[] = [];
  statues: IdNameModel[] = [];
  showCommentCreateModal = false;
  @Input() isViewMode!: boolean | null;
  position = 'top';
  loadMoreIcon = false;
  scheduleForList = ScheduleForList;
  getStockNumber!: string;
  incomingTruckAddress!: Address;
  isRequired = false;
  showGoogleMapSideBar = false;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  today = new Date();
  showDestinationGoogleMapSideBar = false;
  pickUpFullAddress!: string;
  destinationFullAddress!: string;
  dispatcherNameList: IdNameModel[] = [];
  isDispatcherLogin: any;
  currentRole!: string;
  jsonData!: any;
  scheduleVisiblity!: boolean;
  globalSearch = new Subject<FilterValue[]>();
  requestCreatedByUserIsLoginUser = false;
  fileUploadProgresses: FileUploadProgress[] = [];
  fileName!: string;
  redirectUrl!: string;
  stockList: Array<AssociationsUnits> = [];
  driverIds!: Array<number>;
  constructor(
    private readonly fb: FormBuilder,
    private readonly incomingTruckService: IncomingTruckService,
    private readonly commonService: CommonService,

    private readonly allUnitsAssociation: AllUnitsAssociation,
    private readonly toasterService: AppToasterService,
    private readonly driverScheduleService: DriverScheduleService,
    private readonly authService: AuthService,
    private readonly cdf: ChangeDetectorRef,
    private readonly fileUploadService: FileUploadService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
  ) { super() }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getCurrentUser();
    this.getUnitInventories();
    this.getDummyData();
    this.getDriverScheduleStatuses();
    this.getRoleByDriverNameList();
    this.getUsersByDispatcherRole();
    this.scheduleVisiblity = this.isScheduleBlockDisplay(this.currentRole)
    this.activatedRoute.queryParams
      .subscribe(params => {
        if (params?.returnUrl) {
          this.redirectUrl = params.returnUrl;
        }
      })
    this.displaySearchResult();
  }

  private initializeFormGroup(): void {
    this.driverScheduleFormGroup = this.fb.group({
      scheduleFor: new FormControl('Stock#', [Validators.required]),
      stockNumber: new FormControl(null),
      title: new FormControl(null),
      item: new FormControl(null),
      driverId: new FormControl(null,[Validators.required]),
      dispatcherId: new FormControl(null, [Validators.required]),
      createdById: new FormControl(null, [Validators.required]),
      createdByName: new FormControl(null),
      driverScheduleStatus: new FormControl(null, [Validators.required]),
      startDate: new FormControl(null),
      endDate: new FormControl(null),
      pickUpLocation: this.newAddressFormGroupPickup,
      destinationLocation: this.newAddressFormGroupLocation,
      driverScheduleAddresses: this.fb.array([]),
      unitId: new FormControl(null),
      weekDay: new FormControl(null),
      driverScheduleAttachments: this.fb.array([]),
      driverInstruction: new FormControl(null),
      vin: new FormControl({ value: null, disabled: true }),
      make: new FormControl({ value: null, disabled: true }),
      model: new FormControl({ value: null, disabled: true }),
      year: new FormControl({ value: null, disabled: true }),
    });
  }

  get destinationAddressFormGroup(): FormGroup {
    return this.driverScheduleFormGroup.get('destinationLocation') as FormGroup;
  }

  get pickUpAddressFormGroup(): FormGroup {
    return this.driverScheduleFormGroup.get('pickUpLocation') as FormGroup;
  }

  getDummyData(): void {
    this.scheduleFor = [{ id: 1, name: this.scheduleForList.stock }, { id: 2, name: this.scheduleForList.title }];
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.driverScheduleInfo.currentValue) {
      this.title = 'Edit Driver Schedule';
      this.isEditMode = true;
    }
  }

  get newAddressFormGroupPickup(): FormGroup {
    return this.fb.group({
      streetAddress: new FormControl('', [Validators.required]),
      city: new FormControl('', [Validators.required]),
      state: new FormControl('', [Validators.required]),
      zipcode: new FormControl('', [Validators.required]),
      location: new FormControl(driverAddress.pickUp),
      latitude: new FormControl(''),
      longitude: new FormControl(''),
      contactPersonName: new FormControl(''),
      contactPersonNumber: new FormControl('')
    })
  }

  get newAddressFormGroupLocation(): FormGroup {
    return this.fb.group({
      streetAddress: new FormControl('', [Validators.required]),
      city: new FormControl('', [Validators.required]),
      state: new FormControl('', [Validators.required]),
      zipcode: new FormControl('', [Validators.required]),
      location: new FormControl(driverAddress.destination),
      latitude: new FormControl(''),
      longitude: new FormControl(''),
      contactPersonName: new FormControl(''),
      contactPersonNumber: new FormControl('')
    })
  }

  get fileAttachmentsFormGroup() {
    return this.driverScheduleFormGroup.controls["driverScheduleAttachments"] as FormArray;
  }

  get driverScheduleCreateParams(): DriverScheduleCreateParams {
    delete this.driverScheduleFormGroup.value.driverId;
    delete this.driverScheduleFormGroup.value.pickUpLocation;
    delete this.driverScheduleFormGroup.value.destinationLocation;
    const titleValue = this.driverScheduleFormGroup.controls['title']?.value;
    const unitIdValue = this.driverScheduleFormGroup.controls['unitId']?.value;
    const itemValue = this.driverScheduleFormGroup.controls['item']?.value;
    let item;
    if (titleValue) {
      item = titleValue;
    } else if (unitIdValue) {
      item = itemValue;
    } else {
      item = titleValue;
    }
    const drivers: DriverSchedule[] = this.setDriversParam();
    return {
      drivers,
      ...this.driverScheduleFormGroup.value,
      id: this.driverScheduleInfo?.id,
      item: item,
      vin: this.driverScheduleFormGroup.controls['vin']?.value,
      year: this.driverScheduleFormGroup.controls['year']?.value,
      make: this.driverScheduleFormGroup.controls['make']?.value,
      model: this.driverScheduleFormGroup.controls['model']?.value,
    };
  }

  private getRoleByDriverNameList(): void {
    this.loaders.role = true;
    this.driverScheduleService.getRoleByDriverNameList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (pipelineOwnerList) => {
        this.driverNameList = pipelineOwnerList;
        this.loaders.role = false;
      },
      error: () => {
        this.loaders.role = false;
      }
    });
  }


  private getDriverScheduleStatuses(): void {
    this.loaders.status = true;
    this.driverScheduleService.getDriverStatus().pipe(takeUntil(this.destroy$)).subscribe({
      next: (statusMap) => {
        for (const status in statusMap) {
          this.statues.push({ id: status, name: statusMap[status as keyof typeof statusMap] });
          if (statusMap[status as keyof typeof statusMap] === driverScheduleStatus.requested) {
            this.driverScheduleFormGroup.get('driverScheduleStatus')?.patchValue(status);
          }
        }
        this.loaders.status = false;
        if (this.isEditMode) {
          this.setDriverScheduleFormGroup();
        }
      },
      error: () => {
        this.loaders.status = false;
      }
    });
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user && this.driverScheduleFormGroup) {
        this.currentUser = user;
        this.getUserRole(this.currentUser?.currentlyActiveDealer?.role?.name)
        if (!this.isEditMode) {
          const reporterControl = this.driverScheduleFormGroup.get('createdByName');
          this.driverScheduleFormGroup.get('createdById')?.setValue(user.id);
          reporterControl?.setValue(this.utils.getFullName(user.firstName, user.lastName));
          reporterControl?.disable();
        }
      }
    });
  }

  getUserRole(roles: any) {
    this.currentRole = roles
  }

  isScheduleBlockDisplay(role: string) {
    if (role === RoleNames.ROLE_ADMIN) {
      return true
    }
    this.permissionToUpdateRequest(role)
    return this.jsonData.displayBlock;
  }

  canLoginUserUpdateRequest(role: string, status: string) {
    if (role === RoleNames.ROLE_ADMIN) {
      return true
    }
    this.permissionToUpdateRequest(role)
    return this.jsonData.canEdit
  }

  permissionToUpdateRequest(role: string) {
    switch (role) {
      case RoleNames.ROLE_DISPATCHER:
        this.jsonData = {
          displayBlock: true,
          status: 'SCHEDULER',
          canEdit: true
        }
        break;
      case RoleNames.ROLE_USER:
        this.jsonData = {
          displayBlock: false,
          status: 'REQUESTED',
          canEdit: true
        }
        break;
      case RoleNames.ROLE_DRIVER:
        this.jsonData = {
          displayBlock: true,
          status: 'REQUESTED',
          canEdit: true
        }
        break;
      default: {
        this.jsonData = {
          displayBlock: true,
          status: 'REQUESTED',
          canEdit: false
        }
        break;
      }
    }
  }

  updateDisabledStatusForDrivers(): void { 
    this.driverNameList = this.driverNameList.map(driver => ({
      ...driver,
      disabled: this.driverIds.length >= 3 && !this.driverIds.includes(driver.id)
    }));
  }

  onAdd(): void {
    this.showCreateModal = true;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    if (refreshList) {
      this.getRoleByDriverNameList();
    }
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.onClose.emit(true);
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onSubmit(close = true): void {
    if (this.isViewMode) {
      this.enableFormForEditing();
      return;
    }

    if (this.shouldAddValidators()) {
      this.addValidatorsBasedOnScheduleFor();
    }

    this.driverScheduleFormGroup.updateValueAndValidity();

    if (this.shouldShowRequiredError()) {
      this.isRequired = true;
    }
    // TODO This will be remove once complete
    // if (!this.isDispatcherLogin) {
    //   this.removeDriverIdValidator();
    // }

    if (this.driverScheduleFormGroup.invalid) {
      this.markFormGroupAsTouched();
      return;
    }

    this.disableReporterControl();
    this.updateDriverScheduleAddresses();

    if (this.isEditMode) {
      // this.handleEditMode(close);
      this.editDriverScheduleData();
    } else {
      this.saveDriverScheduleData(close);
    }
  }

  private enableFormForEditing(): void {
    this.driverScheduleFormGroup.enable();
    this.isViewMode = false;
    this.driverScheduleFormGroup.get('createdByName')?.disable();
    this.handleEnableDisableFields();
  }

  private shouldAddValidators(): boolean {
    return this.driverScheduleFormGroup.controls['scheduleFor'].value === 'Stock#' ||
      this.driverScheduleFormGroup.controls['scheduleFor'].value === 'Title';
  }

  private addValidatorsBasedOnScheduleFor(): void {
    if (this.driverScheduleFormGroup.controls['scheduleFor'].value === 'Stock#') {
      this.driverScheduleFormGroup.controls['stockNumber'].setValidators([Validators.required]);
      this.driverScheduleFormGroup.controls['stockNumber'].updateValueAndValidity();
    }
    if (this.driverScheduleFormGroup.controls['scheduleFor'].value === 'Title') {
      this.driverScheduleFormGroup.controls['title'].setValidators([Validators.required]);
    }
  }

  private shouldShowRequiredError(): boolean {
    return !this.driverScheduleFormGroup.controls['scheduleFor'].value &&
      (!this.driverScheduleFormGroup.controls['stockNumber'].value ||
        !this.driverScheduleFormGroup.controls['title'].value);
  }
// TODO This will be remove once complete
  // private removeDriverIdValidator(): void {
  //   this.driverScheduleFormGroup.controls['driverId'].clearValidators();
  //   this.driverScheduleFormGroup.controls['driverId'].updateValueAndValidity();
  // }

  private markFormGroupAsTouched(): void {
    this.driverScheduleFormGroup.markAllAsTouched();
  }

  private disableReporterControl(): void {
    const reporterControl = this.driverScheduleFormGroup.get('createdByName');
    reporterControl?.disable();
  }

  private updateDriverScheduleAddresses(): void {
    this.driverScheduleFormGroup.value.driverScheduleAddresses.push(this.driverScheduleFormGroup.value.pickUpLocation);
    this.driverScheduleFormGroup.value.driverScheduleAddresses.push(this.driverScheduleFormGroup.value.destinationLocation);
  }

  private handleEditMode(close: boolean): void {
    if (this.driverScheduleInfo?.driverScheduleStatus) {
      const userCanEdit = this.canLoginUserUpdateRequest(this.currentRole, this.driverScheduleInfo.driverScheduleStatus);
      if (this.currentUser) {
        this.requestCreatedByUserIsLoginUser = this.currentUser.id === this.driverScheduleFormGroup.value.createdById;
      }
      if (this.requestCreatedByUserIsLoginUser || userCanEdit) {
        this.editDriverScheduleData();
      } else {
        this.toasterService.error(MESSAGES.formEditPermissionMessage);
      }
    }
  }

  saveDriverScheduleData(close = true): void {
    this.driverScheduleService.add(this.driverScheduleCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.driverScheduleDataUpdateSuccess : MESSAGES.driverScheduleDataAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
        this.router.navigate([this.path.transport.root, this.path.transport.driverScheduleBoard.root]);
      } else {
        this.driverScheduleFormGroup.reset();
        this.driverScheduleFormGroup.patchValue({
          scheduleFor: 'Stock#',
          driverScheduleStatus: this.statues[0].id
        })
        this.destinationAddressFormGroup.patchValue({
          location: driverAddress.destination
        })
        this.getCurrentUser();
      }
    });
  }

  editDriverScheduleData(): void {
    this.driverScheduleService.update(this.driverScheduleCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.driverScheduleDataUpdateSuccess : MESSAGES.driverScheduleDataAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
      this.router.navigate([this.path.transport.root, this.path.transport.driverScheduleBoard.root]);
    });
  }

  searchStock(event: any): void {
    this.setFilteredStockParam(event.filter);
    this.globalSearch.next(this.filterParams.values);
  }


  displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(500)).subscribe(() => {
      this.stockList = [];
      this.pageNumber = 0;
      this.isLastPage = false;
      this.getUnitInventories();
    });
  }

  getUnitInventories(): void {
    this.loaders.stockNumbers = true;
    if (this.filterParams?.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(value => value.value)
    }
    this.filterParams.treeOperator = this.filterParams?.values?.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
    this.filterParams.isIncomingTruck = true;
    this.allUnitsAssociation.getListWithFiltersWithPagination<GenericFilterParams, AssociationsUnits>(this.filterParams, this.pageNumber + 1, 50).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.isLastPage = res.last;
        this.stockList = [...this.stockList, ...res.content];
        this.isLoading = false;
        this.loaders.stockNumbers = false;
        this.loadMoreIcon = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.isLoading = false;
        this.loaders.stockNumbers = false;
        this.cdf.detectChanges();
      }
    })
  }

  get stockParam(): FilterValue {
    return {
      dataType: DataType.STRING,
      key: 'generalInformation.stockNumber',
      operator: OperatorType.LIKE,
      value: ''
    }
  }

  private setFilteredStockParam(str: string): void {
    const stockParamValue = this.stockParam;
    stockParamValue.value = str;
    this.filterParams.values = this.filterParams?.values?.length ? this.filterParams?.values : [];
    const stockFilterValue = this.filterParams.values.find(value => value.key === 'generalInformation.stockNumber');
    if (stockFilterValue) {
      stockFilterValue.value = str;
    } else {
      this.filterParams.values.push(stockParamValue);
    }
  }

  selectedStockNumberByAddress(unitId: number): void {
    this.pickUpAddressFormGroup.patchValue({
      location: driverAddress.pickUp
    })
    const stock = this.stockList.find(i => i.id === unitId)
    this.driverScheduleFormGroup.patchValue({
      item: stock?.stockNumber,
      unitId: Number(unitId),
      vin: stock?.vin,
      make: stock?.make,
      model: stock?.model,
      year: stock?.year,
    })
    if (unitId) {
      const endpoint = API_URL_UTIL.driverSchedule.incomingAddress.replace(':unitId', unitId.toString())
      this.incomingTruckService.get<Address>(endpoint).pipe(takeUntil(this.destroy$)).subscribe(inventoryAddress => {
        if (!this.isEmptyObject(inventoryAddress)) {
          this.address = inventoryAddress;
          this.pickUpAddressFormGroup.patchValue({
            streetAddress: inventoryAddress.streetAddress,
            city: inventoryAddress.city,
            state: inventoryAddress.state,
            zipcode: inventoryAddress.zipcode,
            location: driverAddress.pickUp
          })
        }
      });
    }
  }

  onLoadMore(): void {
    if (!this.isLastPage) {
      this.loadMoreIcon = true;
      this.pageNumber++;
      this.getUnitInventories();
    }
  }

  addVendorAddress(unitId: number): void {
    this.driverScheduleService.getStockNumberByUnitId(Number(unitId)).pipe(takeUntil(this.destroy$)).subscribe(address => {
      if (!this.isEmptyObject(address)) {
        this.address = address;
        this.pickUpAddressFormGroup.patchValue({
          streetAddress: address.streetAddress,
          city: address.city,
          state: address.state,
          zipcode: address.zipcode,
          location: driverAddress.pickUp
        })
      }
    });
  }

  isEmptyObject(obj: Address): boolean {
    if ((obj.city === null && obj.state === null && obj.streetAddress === null && obj.zipcode === null)) {
      return true;
    }
    else {
      return false;
    }
  }

  private handleEnableDisableFields(): void {
    const controlsToReset = ['vin', 'year', 'make', 'model'];
    for (const controlName of controlsToReset) {
      const control = this.driverScheduleFormGroup.get(controlName);
      if (control) {
        if (this.driverScheduleInfo?.unitId) {
          control.disable();
        } else {
          control.enable();
        }
      }
    }
  }

  private handleUnitFields(unit: string) {
    const controlsToReset = ['vin', 'year', 'make', 'model'];
    for (const controlName of controlsToReset) {
      const control = this.driverScheduleFormGroup.get(controlName);
      if (control) {
        if (unit === this.scheduleForList.stock) {
          control.disable();
        } else {
          control.enable();
        }
        control.reset(null);
      }
    }
  }

  onChange(unit: string): void {
    this.handleUnitFields(unit)
    this.isRequired = false;
    if (this.isEditMode) {
      if (this.driverScheduleFormGroup.controls['unitId'].value) {
        this.driverScheduleFormGroup.patchValue({
          unitId: null
        })
      }
      else if (this.driverScheduleFormGroup.controls['title'].value) {
        this.driverScheduleFormGroup.patchValue({
          title: null
        })
      }
    }
    if (unit === this.scheduleForList.stock) {
      this.driverScheduleFormGroup.patchValue({
        title: null
      })
      this.pickUpAddressFormGroup.reset();
      this.pickUpAddressFormGroup.patchValue({
        location: driverAddress.pickUp
      })
      this.driverScheduleFormGroup.controls.title.removeValidators([Validators.required]);
      this.driverScheduleFormGroup.controls.title.updateValueAndValidity();
      this.driverScheduleFormGroup.controls.stockNumber.setValidators([Validators.required]);
      this.driverScheduleFormGroup.controls.stockNumber.updateValueAndValidity();
    }
    else if (unit === this.scheduleForList.title) {
      this.driverScheduleFormGroup.patchValue({
        unitId: null,
        stockNumber: null
      })
      this.driverScheduleFormGroup.controls.stockNumber.removeValidators([Validators.required]);
      this.driverScheduleFormGroup.controls.stockNumber.updateValueAndValidity();
      this.driverScheduleFormGroup.controls.title.setValidators([Validators.required]);
      this.driverScheduleFormGroup.controls.title.updateValueAndValidity();
      this.pickUpAddressFormGroup.reset();
      this.pickUpAddressFormGroup.patchValue({
        location: driverAddress.pickUp
      })
    }
  }

  setDriverScheduleFormGroup(): void {
    let pickUpLocation!: Address;
    let destinationLocation!: Address;

    if (this.driverScheduleInfo) {
      const pickUpLocationCandidate = this.findLocation(this.driverScheduleInfo.driverScheduleAddresses, driverAddress.pickUp);
      if (pickUpLocationCandidate) {
        pickUpLocation = pickUpLocationCandidate;
      }
      const destinationLocationCandidate = this.findLocation(this.driverScheduleInfo.driverScheduleAddresses, driverAddress.destination);
      if (destinationLocationCandidate) {
        destinationLocation = destinationLocationCandidate;
      }
      this.patchDriverScheduleForm(this.driverScheduleInfo, pickUpLocation, destinationLocation);
      this.addValidatorsBasedOnSchedule(this.driverScheduleInfo.unitId);
      if (this.isViewMode) {
        this.driverScheduleFormGroup.disable();
      }
    }
    this.commonService.setBlockUI$(false);
  }

  private findLocation(addresses: any[], locationType: string): Address | undefined {
    return addresses.find((address: any) => address.location === locationType);
  }

  private patchDriverScheduleForm(
    driverScheduleInfo: DriverScheduleBoardListItem,
    pickUpLocation: Address | undefined,
    destinationLocation: Address | undefined
  ): void {
    const formValue = {
      // TODO This will be remove once complete
      // driverId: driverScheduleInfo?.driver?.id,
      createdById: driverScheduleInfo?.createdBy?.id,
      createdByName: driverScheduleInfo?.createdBy?.name,
      startDate: driverScheduleInfo?.startDate ? new Date(`${driverScheduleInfo?.startDate}`) : '',
      endDate: driverScheduleInfo?.endDate ? new Date(`${driverScheduleInfo?.endDate}`) : '',
      scheduleFor: driverScheduleInfo?.unitId ? this.scheduleForList.stock : this.scheduleForList.title,
      title: !driverScheduleInfo?.unitId ? driverScheduleInfo.item : '',
      stockNumber: driverScheduleInfo?.unitId ? driverScheduleInfo.unitId : '',
      unitId: driverScheduleInfo.unitId,
      item: driverScheduleInfo?.unitId ? driverScheduleInfo.item : '',
      driverScheduleStatus: driverScheduleInfo?.driverScheduleStatus,
      dispatcherId: driverScheduleInfo?.dispatcher?.id,
      driverInstruction: driverScheduleInfo?.driverInstruction,
      pickUpLocation: {
        streetAddress: pickUpLocation?.streetAddress,
        city: pickUpLocation?.city,
        state: pickUpLocation?.state,
        zipcode: pickUpLocation?.zipcode,
        contactPersonName: pickUpLocation?.contactPersonName,
        contactPersonNumber: pickUpLocation?.contactPersonNumber,
      },
      destinationLocation: {
        streetAddress: destinationLocation?.streetAddress,
        city: destinationLocation?.city,
        state: destinationLocation?.state,
        zipcode: destinationLocation?.zipcode,
        contactPersonName: destinationLocation?.contactPersonName,
        contactPersonNumber: destinationLocation?.contactPersonNumber,
      },
      weekDay: driverScheduleInfo?.weekDay,
      year: driverScheduleInfo?.year,
      make: driverScheduleInfo?.make,
      model: driverScheduleInfo?.model,
      vin: driverScheduleInfo?.vin,
    };
    this.driverIds = this.setIds('drivers', 'driverId');
    this.driverScheduleFormGroup.patchValue(formValue);
    if (this.driverScheduleInfo?.driverScheduleAttachments?.length) {
      this.patchFileAttachments(this.driverScheduleInfo.driverScheduleAttachments);
    }
    if (this.currentUser) {
      this.requestCreatedByUserIsLoginUser = this.currentUser.id === driverScheduleInfo.createdBy.id;
    }
    this.handleEnableDisableFields();
    this.driverScheduleFormGroup.get('createdByName')?.disable();
  }

  private addValidatorsBasedOnSchedule(unitId: number | undefined): void {
    const scheduleForControl = this.driverScheduleFormGroup.controls['scheduleFor'];
    const stockNumberControl = this.driverScheduleFormGroup.controls['stockNumber'];
    const titleControl = this.driverScheduleFormGroup.controls['title'];
    if (scheduleForControl.value === this.scheduleForList.stock) {
      stockNumberControl.setValidators([Validators.required]);
      stockNumberControl.updateValueAndValidity();
    } else if (scheduleForControl.value === this.scheduleForList.title) {
      titleControl.setValidators([Validators.required]);
      titleControl.updateValueAndValidity();
    }
  }

  private patchFileAttachments(attachments: any[]): void {
    for (const file of attachments) {
      this.fileAttachmentsFormGroup.push(this.fb.group({
        fileName: [file.fileName],
        url: [file.url],
        fullPath: [file.fullPath],
      }));
    }
  }

  toggleGoogleMapPopUp() {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getPickUpFullAddress()
  }

  handleAddressChange(address: gAddress | any) {
    if (address?.address_components) {
      this.pickUpAddressFormGroup.controls['streetAddress'].setValue(address.name);
      this.pickUpAddressFormGroup.controls['latitude'].setValue(address.geometry.location.lat());
      this.pickUpAddressFormGroup.controls['longitude'].setValue(address.geometry.location.lng());
      address.address_components.forEach((addressComponent: any) => {
        switch (addressComponent.types[0]) {
          case 'sublocality':
            break;
          case 'locality':
            this.pickUpAddressFormGroup.controls['city'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_1':
            this.pickUpAddressFormGroup.controls['state'].setValue(addressComponent.long_name);
            break;
          case 'postal_code':
            this.pickUpAddressFormGroup.controls['zipcode'].setValue(addressComponent.long_name);
            break;
        }
      });
    }
  }

  toggleDestinationGoogleMapPopUp() {
    this.showDestinationGoogleMapSideBar = !this.showDestinationGoogleMapSideBar;
    this.getDestinationFullAddress()
  }

  handleDestinationAddressChange(address: gAddress | any) {
    if (address?.address_components) {
      this.destinationAddressFormGroup.controls['streetAddress'].setValue(address.name);
      this.destinationAddressFormGroup.controls['latitude'].setValue(address.geometry.location.lat());
      this.destinationAddressFormGroup.controls['longitude'].setValue(address.geometry.location.lng());
      address.address_components.forEach((addressComponent: any) => {
        switch (addressComponent.types[0]) {
          case 'sublocality':
            break;
          case 'locality':
            this.destinationAddressFormGroup.controls['city'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_1':
            this.destinationAddressFormGroup.controls['state'].setValue(addressComponent.long_name);
            break;
          case 'postal_code':
            this.destinationAddressFormGroup.controls['zipcode'].setValue(addressComponent.long_name);
            break;
        }
      });
    }
  }

  getPickUpFullAddress() {
    return this.getAddress(this.pickUpAddressFormGroup.value, 'PICK_UP')
  }

  getDestinationFullAddress() {
    return this.getAddress(this.destinationAddressFormGroup.value, 'DESTINATION')
  }

  getAddress(address: any, type: string) {
    const rowAddress = []
    rowAddress.push(address?.streetAddress ? address.streetAddress : '')
    rowAddress.push(address?.city ? address.city : '')
    rowAddress.push(address?.state ? address.state : '')
    rowAddress.push(address?.zipcode ? address.zipcode : '')
    const cleanAddress = rowAddress.filter(str => str !== "").join(", ")
    if (type === 'PICK_UP') {
      this.pickUpFullAddress = cleanAddress
    } else {
      this.destinationFullAddress = cleanAddress
    }
  }

  private getUsersByDispatcherRole(): void {
    this.loaders.role = true;
    this.driverScheduleService.getRoleByDispatcherList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (pipelineOwnerList) => {
        this.dispatcherNameList = pipelineOwnerList;
        this.loaders.role = false;
      },
      error: () => {
        this.loaders.role = false;
      }
    });
  }

  onEndDateChange(event: Date): void {
    this.driverScheduleFormGroup.get('weekDay')?.setValue(this.constants.weekDays[event.getDay()]);
    this.driverScheduleFormGroup.get('startDate')?.setValue(event);
  }

  clearDate(): void {
    this.driverScheduleFormGroup.get('weekDay')?.setValue(null);
  }

  onFileSelect(event: any) {
    if (event.target?.files?.length) {
      for (const file of event.target.files) {
        if (file.size > this.constants.fileSize) {
          this.toasterService.warning(MESSAGES.fileUploadMessage)
          return
        }
        const modifiedFileName = getRefactorFileName(file.name);
        const isExtensionSupported = allowExtensions(modifiedFileName,Constants.allowedPdfFormats)
        if (isExtensionSupported) {
          const modifiedFile = new File([file], modifiedFileName, { type: file.type });
          this.uploadDocument(modifiedFile);
        } else {
          this.toasterService.error(MESSAGES.fileTypeSupportedPDF);
          return
        }
      }
    }
  }

  uploadDocument(file: File): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, 'Tasks Files', this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          this.fileName = file.name;
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileAttachmentsFormGroup.push(this.fb.group({
            fileName: [file.name],
            url: [fileUrl.url],
            file: file
          }));
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.documentUploadSuccess);
          this.cdf.detectChanges();
        },
        error: err => {
          this.toasterService.error(MESSAGES.fileUploadError);
          this.cdf.detectChanges();
        }
      })
  }

  downloadServerPDF(file: DriverScheduleAttachments): void {
    if (file?.url) {
      this.fileUploadService.downloadFile(file.url, file.fileName).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/pdf" }), file.fileName);
        }
      });
    }
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  deleteImageFromCloud(imageUrl: string): void {
    this.fileUploadService.deleteFile([imageUrl]).pipe(takeUntil(this.destroy$)).subscribe(() => {
      const index = this.fileAttachmentsFormGroup.controls.findIndex(control => control.get('url')?.value === imageUrl);
      // TODO Will be remove once complete
      // this.fileAttachmentsFormGroup.clear();
      this.fileAttachmentsFormGroup.removeAt(index);

    });
  }
  setDriversParam(): DriverSchedule[] {
      let drivers: DriverSchedule[] = this.driverScheduleInfo?.drivers ?? [];
      if (drivers.length) {
        drivers = drivers.filter((driver: DriverSchedule) => {
          const existingMake = this.driverIds.includes(driver.driverId);
          if (existingMake) {
            this.driverIds = this.driverIds.filter(id => id !== driver.driverId)
          }
          return existingMake;
        })
      }
      const newDrivers: DriverSchedule[] = this.driverIds.map((id: number) => {
        return {
          driverId: id,
        };
      });
      return [...drivers, ...newDrivers];
  }
  setIds(type: string, key: string): Array<number> {
    if (this.driverScheduleInfo[type]) {
      return this.driverScheduleInfo[type].map((type: any) => type[key]);
    }
    return [];
  }
  clearDrivers() {
    this.driverIds = [];
  }
}
