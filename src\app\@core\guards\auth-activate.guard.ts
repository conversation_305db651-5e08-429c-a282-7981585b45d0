import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ROUTER_UTILS } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthActivateGuard  {

  constructor(private readonly authService: AuthService,
    private readonly router: Router) {
  }

  canActivate(route: ActivatedRouteSnapshot): boolean {
    const isLoggedIn = this.authService.isLoggedIn;
    if (isLoggedIn) {
      return true
    }
    let queryString = "";
    if (route.queryParams && Object.keys(route.queryParams).length > 0) {
      Object.keys(route.queryParams).forEach(function (key, idx) {
        if (idx === 0) {
          queryString += `?${key}=${route.queryParams[key]}`
        } else {
          queryString += `&${key}=${route.queryParams[key]}`
        }
      });
    }
    let returnUrl = new URL(window.location.href).pathname;

    returnUrl += queryString;

    const { root, login } = ROUTER_UTILS.config.auth;

    localStorage.setItem('redirectUrl', returnUrl);

    this.router.navigate(['/', root, login], {
      queryParams: { returnUrl },
    });

    return false;
  }

}
