import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Observable } from "rxjs";

@Injectable({ providedIn: 'root' })
export class GeneralInfoService extends BaseCrudService {
  constructor(protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  getBaseAPIPath(): string {
    return API_URL_UTIL.inventory.root
  }

  add<ReturnType>(resource: ReturnType, endpoint?: string): Observable<any> {
    return this.httpClient.post(`${this.getFullAPIUrl(endpoint)}`, this.toServerModel(resource));
  }

  delete(id: string | number, endpoint?: string): Observable<any> {
    return this.httpClient.delete(`${this.getFullAPIUrl(endpoint)}/${id}`);
  }

  update<ReturnType>(resource: ReturnType, endpoint?: string) {
    return this.httpClient.put(`${this.getFullAPIUrl(endpoint)}`, this.toServerModel(resource));
  }

  patch<ReturnType>(resource: ReturnType, endpoint?: string) {
    return this.httpClient.patch(`${this.getFullAPIUrl(endpoint)}`, this.toServerModel(resource));
  }

  updateDisplayOnWeb<ReturnType>(resource: ReturnType, endpoint?: string) {
    return this.httpClient.patch(`${endpoint}`, this.toServerModel(resource));
  }
}
