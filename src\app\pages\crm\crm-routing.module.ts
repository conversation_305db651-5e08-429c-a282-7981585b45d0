import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { PermissionForChildGuard } from "@core/guards";
import { ROUTER_UTILS } from "@core/utils";
import { PermissionActions } from "src/app/@shared/models";

const routes: Routes = [
  {
    path: ROUTER_UTILS.config.crm.crmContact.root,
    loadChildren: async () => (await import('./pages/crm-contact/crm-contact.module')).CrmContactModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_CONTACTS]
    }
  },
  {
    path: ROUTER_UTILS.config.crm.crmTask.root,
    loadChildren: async () => (await import('./pages/crm-task/crm-task.module')).CrmTaskModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_SALES_TASK]
    }
  },
  {
    path: ROUTER_UTILS.config.crm.crmCustomer.root,
    loadChildren: async () => (await import('./pages/crm-customer/crm-customer.module')).CrmCustomerModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_CUSTOMER_LEAD]
    }
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CRMRoutingModule { }
