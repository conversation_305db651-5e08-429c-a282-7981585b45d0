::ng-deep .crm-customer-task .page-header {
  justify-content: left;
}

.card {
  box-shadow: none !important;
}

.tabs .tab-content {
  padding: 0 !important;
}

.timeline {
  text-align: center;

  .footer {
    background-color: var(--listing-timeline-bg-color);
    width: fit-content;
    padding: 2px 20px;
    border-radius: 12px;

    .pi {
      margin-right: 5px;
    }

    span {
      font-size: 13px;
      letter-spacing: 0;
      line-height: 21px;
      color: var(--active-color);
      display: flex;
      align-items: center;
    }
  }
}

.timeline-header {
  width: 220px;
}

.pi-trash {
  font-size: 20px;

  &:hover {
    cursor: pointer;
  }
}

::ng-deep .crm-task-list {
  .p-datatable .p-datatable-tbody > tr {
    background: white;
  }

  .p-datatable.p-datatable-hoverable-rows .p-datatable-tbody > tr:not(.p-highlight):hover {
    background-color: white !important;
  }
}

.view-task {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
  z-index: 1;
}

.dropdown-toggle::after {
  margin-left: 25px !important;
}

.w-150 {
  width: 150px !important;
}

::ng-deep .crm-task-list {
  .p-dropdown .p-dropdown-label {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
    font-size: 14px;
  }

  .p-dropdown .p-dropdown-trigger {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
  }

  .p-dropdown {
    height: inherit;
    background: var(--active-color) !important;
    color: var(--white-color) !important;
  }

  .pi-chevron-down:before {
    font-size: 14px;
  }

  .priority-icon {
    padding: 17px !important;
  }

  td.empty {
    display: flex;
    width: 100%;
    justify-content: center;
  }
}
