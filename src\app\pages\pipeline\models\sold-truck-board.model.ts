import { GenericFilterParams, IdNameModel } from 'src/app/@shared/models';

export class SoldTruckBoardListFilter extends GenericFilterParams {
  deleted = false
}

export class SoldTruckBoardListItem {
  id!: number;
  stockNumber!: number;
  unitId!: number;
  pipelineType!: string;
  templatePipelineId!: number
  dealerId!: number
  phases: TemplatePipelinePhases[] = [];
  status!: string;
  timeLine!: Timeline;
  taskPercentage!: number;
  tasks!: TemplatePipelineTaskList;
  pipelineOwner!: IdNameModel;
  targetCompletedDate!: Date;
  pipeline!: string;
  createdBy!: string;
  createdDate!: Date;
  deleted!: boolean;
  isLocked!: boolean
  constructor(json?: SoldTruckBoardListItem) {
    if (json) {
      Object.assign(this, json);
    }
  }
}

export interface Timeline {
  assignee: IdNameModel,
  startTime: string;
  endTime: string
}

export class TemplatePipelinePhase {
  sequenceNumber!: number;
  assigneeId!: number;
  shopId!: number;
  id!: number | null;
}

export interface StockBasicInfo {
  unitId: number;
  stockNumber: number;
  id: number
}

export interface SoldTruckCreateParams {
  stockNumber: number;
  unitId: number;
  pipelineType: string;
  templatePipelineId: number;
  dealerId: number;
  status: string;
  pipelineOwnerId: number;
  phases: TemplatePipelinePhase[]
}

export class GeneralStockNumberBasicInfo {
  designation!: IdNameModel
  make!: IdNameModel;
  owner!: IdNameModel;
  stockNumber!: string;
  unitModel!: IdNameModel;
  unitStatus!: IdNameModel;
  vin!: string;
  year!: string;
  unitType!: IdNameModel;
}

export class PipelineConfigListItem {
  id!: number;
  title!: string;
  dealerName!: string;
  dealerId!: number;
  templatePipelinePhases: TemplatePipelinePhase[] = [];
}

export class PipelineGetDetail {
  stockNumber!: number;
  dealerId!: number;
  deleted!: boolean;
  id!: number;
  isLocked!: boolean
  phases!: TemplatePipelinePhases[];
  pipelineType!: string;
  status!: string;
  taskPercentage!: number;
  templatePipelineId!: number;
  timeLine!: string;
  unitId!: number;
  tasks!: TemplatePipelineTaskList[];
  createdBy!: string;
  createdDate!: string;
  pipelineOwner!: IdNameModel;
  targetCompletedDate!: string
}

export class TemplatePipelineTaskList {
  id!: number;
  summary!: string;
  stockNumber!: number;
  unitId!: number;
  shop!: IdNameModel;
  assignee!: IdNameModel;
  reporter!: IdNameModel;
  taskPriority!: string;
  taskStatus!: IdNameModel;
  startDate!: string | null;
  endDate!: string | null;
  description!: string;
  progress!: number;
  taskType!: IdNameModel;
  pipelineId!: number;
  pipelineSequence!: number;
  isActive!: boolean
}

export class TemplatePipelinePhases {
  id!: number;
  sequenceNumber!: number
  assignee!: AssigneeModel;
  shop!: IdNameModel;
  taskStatus!: string;
  altId!: boolean;
}

export class AssigneeModel {
  id!: number;
  firstName!: string;
  lastName!: string
}

export class PipelineLockItem {
  id!: number;
  sequenceNumber!: number;
  assigneeId!: number;
  shopId!: number;
  pipelineId!: number;
  taskStatus!: string
}

export const StatusList = [
  { value: 'COMPLETED', name: 'Completed' },
  { value: 'IN_STAGING', name: 'In Staging' },
  { value: 'WAITING_ON_PARTS', name: 'Waiting On Parts' },
  { value: 'SCHEDULE', name: 'Schedule' },
]
export const StatusListCopy = [
  { value: 'ALL', name: 'All' },
  { value: 'COMPLETED', name: 'Completed' },
  { value: 'IN_STAGING', name: 'In Staging' },
  { value: 'WAITING_ON_PARTS', name: 'Waiting On Parts' },
  { value: 'SCHEDULE', name: 'Schedule' },
]
export class DragAvailableShop {
  id!: number;
  name!: string;
  defaultUserId!: number;
  defaultUserName!: string;
}

