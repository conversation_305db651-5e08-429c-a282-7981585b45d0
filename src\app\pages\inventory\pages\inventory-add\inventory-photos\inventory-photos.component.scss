@import 'src/assets/scss/variables';

.inventory-images-public {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: inherit;

  .img-wrapper,
  .img-wrapper img {
    height: 150px;
    width: 200px;
    border-radius: 3px;
    position: relative;

    &.highlight {
      border: 3px solid var(--success-color) !important;
      min-height: 156px;
    }

    &.highlights {
      border: 3px solid var(--success-color) !important;
    }

    &:hover {
      cursor: pointer;
      border: 1px solid var(--active-color);
    }

    &.highlights {
      border: 3px solid var(--success-color) !important;
    }

    &.active {
      border: 2px solid var(--active-color) !important;
    }
  }

  .img-wrapper {
    min-height: 119px;
  }

  .file-wrapper {
    width: 100%;
  }

  .cdk-drop-list {
    min-height: 180px;
    flex-wrap: wrap;
  }

  .img-wrapper:not(.drop-zone) {
    border: 1px solid transparent;
  }

  .photo-detail {
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;
    height: 30px;
    padding-left: 5px;
    padding-right: 5px;
  }

  .photo-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .delete-icon {
    position: absolute;
    top: 5px;
    right: 70px;
    padding: 4px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;

    .pi {
      font-size: 12px;
      color: red;
      margin: 3px;
      background-color: var(--white-color);
      border-radius: 50%;
      padding: 5px;
    }

    &:hover {
      .pi {
        font-size: 13px;
      }
    }
  }

  .img-checkbox {
    top: 5px;
    left: 5px;
    position: absolute;

    ::ng-deep .p-checkbox .p-checkbox-box {
      border-radius: 50%;
      width: 17px;
      height: 17px;
    }
  }
}

.inventory-images-internal {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: inherit;

  .img-wrapper,
  .img-wrapper img {
    height: 150px;
    width: 200px;
    border-radius: 3px;
    position: relative;

    &:hover {
      cursor: pointer;
      border: 1px solid var(--active-color);
    }

    &.active {
      border: 2px solid var(--active-color) !important;
    }
  }

  .img-wrapper {
    min-height: 119px;
  }

  .img-wrapper:not(.drop-zone) {
    border: 1px solid transparent;
  }

  .photo-detail {
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;
    height: 30px;
    padding-left: 5px;
    padding-right: 5px;
  }

  .file-wrapper {
    width: 100%;
  }

  .cdk-drop-list {
    min-height: 180px;
    flex-wrap: wrap;
  }

  .photo-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .delete-icon {
    position: absolute;
    top: 5px;
    right: 70px;
    padding: 4px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;

    .pi {
      font-size: 12px;
      color: red;
      margin: 3px;
      background-color: var(--white-color);
      border-radius: 50%;
      padding: 5px;
    }

    &:hover {
      .pi {
        font-size: 13px;
      }
    }
  }

  .img-checkbox {
    top: 5px;
    left: 5px;
    position: absolute;

    ::ng-deep .p-checkbox .p-checkbox-box {
      border-radius: 50%;
      width: 17px;
      height: 17px;
    }
  }
}

.drop-zone {
  .title,
  .subtitle {
    font-size: 11px;
    line-height: 1;
  }
}

.header-span {
  color: var(--active-color) !important;
  font-size: 17px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 27px;
}

.photos-switch {
  // Toggle button css
  .switch {
    position: relative;
    display: inline-block;
    width: 35px;
    height: 19px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: 0.4s;
    transition: 0.4s;
  }

  .slider:before {
    position: absolute;
    content: '';
    height: 15px;
    width: 15px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: 0.4s;
  }

  input:checked + .slider {
    background-color: var(--success-color);
  }

  input:focus + .slider {
    box-shadow: 0 0 1px var(--success-color);
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(16px);
    -ms-transform: translateX(16px);
    transform: translateX(16px);
  }

  /* Rounded sliders */
  .slider.round {
    border-radius: 34px;
  }

  .slider.round:before {
    border-radius: 50%;
  }

  .switch {
    border: none;
  }

  .toggle-label {
    margin: 0px 5px 0px 5px;
  }
}

.toggle-label {
  font-size: 16px !important;
  font-weight: 600 !important;
}

::ng-deep .inventory-photos {
  .p-panel .p-panel-header {
    border-top: none;
    border-left: none;
    border-right: none;
  }

  .p-panel .p-panel-content {
    border-bottom: none;
    border-left: none;
    border-right: none;
  }
}

.file-progress {
  position: absolute;
  bottom: 0px;
  width: 100%;

  .p-progressbar {
    border-radius: 0;
    height: 20px;
  }

  .p-progressbar-label {
    font-size: 13px;
  }
}

.file-box-wrapper {
  position: relative;
}

.files {
  width: 200px;
  height: 150px;
  margin: 0px 5px;

  .img-wrapper {
    width: auto;
  }
}

.img-checkbox-display-picture {
  bottom: 5px;
  left: 5px;
  position: absolute;

  ::ng-deep .p-checkbox .p-checkbox-box {
    border-radius: 50%;
    width: 17px;
    height: 17px;
  }
}

.preview-picture {
  background-color: var(--white-color);
}

.rectangle-green {
  height: 25px;
  width: 35px;
  border-radius: 12.5px;
  background-color: var(--success-color);
  margin-left: 10px;
  text-align: center;
  color: var(--white-color);
}

.rectangle-gray {
  height: 25px;
  width: 35px;
  border-radius: 12.5px;
  background-color: $gray-rectangle-color;
  margin-left: 10px;
  text-align: center;
  color: var(--white-color);
}

.public-photos,
.internal-photos {
  display: flex;
}

.ml-5 {
  margin-left: 5px;
}

.close-icon {
  position: absolute;
  right: 0;
  padding: 15px;
  font-size: 25px;
}

::ng-deep .inventory-photos .p-component-overlay {
  background-color: rgb(0, 0, 0, 0.4) !important;

  .p-dialog-content {
    padding: 0 !important;
  }
}

.view-zoomed-image {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 700px;
}

::ng-deep .photos-close-icon {
  svg {
    background-color: white;
    padding: 2px 9px;
  }
}

.btn-p {
  padding: 5px !important;
}

.image-main-wrapper {
  min-width: calc(100% - 154px);
}

.show-drop-zone {
  border: 2px dashed #add2ff;
}

.cdk-drop-list {
  width: 100%;
  z-index: 25;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-box {
  height: 150px;
  width: 200px;
  cursor: move;
  z-index: 1;
  transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1);
  margin: 5px;
}

.example-box:active {
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
  opacity: 0.6;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag.example-box.ng-star-inserted.cdk-drag-preview {
  z-index: 10000 !important;
  background-color: $gray-highlight-color !important;

  .img-wrapper {
    display: none !important;
  }
}

@media only screen and (max-width: 500px) {
  .accordion-header {
    flex-direction: column;

    .public-photos,
    .internal-photos {
      margin-bottom: 10px;
    }
  }

  .switch {
    margin-right: 10px;
  }

  .cdk-drop-list {
    justify-content: center;
  }
}

@media only screen and (max-width: 500px) {
  .view-zoomed-image {
    width: 360px;
  }
}
