import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { takeUntil } from 'rxjs';
import { DaliyMontlySales, InventoryListItem, ModelType, TruckInventory } from './dashboard.model';
import { DashboardService } from './dashboard.service';

@Component({
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent extends BaseComponent implements OnInit {
  dailySales!: number;
  monthlySales!: number;
  totalTruckInvenotryCount!: number;
  incomingTruckInventoryCount!: number;
  dailyMonthlySalesVisible = false;
  inventoryPrefrence: InventoryListItem[] = [];
  modelPopups = {
    isFullViewInventory: false,
    isFullViewTask: false,
    isFullViewInventoryAging: false,
    isFullViewSaleByMonth: false,
    isFullViewTaskAlerts: false,
    isFullViewRecentInventory: false,
  };

  constructor(private readonly dashboardService: DashboardService, private readonly cdf: ChangeDetectorRef, private readonly authService: AuthService, private readonly router:Router
  ) {
    super();
  }

  ngOnInit() {
    this.getCurrentUser();
    this.getAllMonthlyAndDailySale();
    this.getTotalTruckInventoryCount();
    this.getIncomingInventoryCount();
  }

  getAllMonthlyAndDailySale(): void {
    this.dashboardService.get<DaliyMontlySales>(API_URL_UTIL.dashboard.dailyMontlySales).pipe(takeUntil(this.destroy$)).subscribe(data => {
      this.dailySales = data.dailySales;
      this.monthlySales = data.monthlySales;
      this.cdf.detectChanges();
    });
  }

  getTotalTruckInventoryCount(): void {
    this.dashboardService.get<TruckInventory>(API_URL_UTIL.dashboard.truckInventory).pipe(takeUntil(this.destroy$)).subscribe(data => {
      this.totalTruckInvenotryCount = data.totalTruckInventoryCount;
      this.cdf.detectChanges();
    });
  }

  getIncomingInventoryCount(): void {
    this.dashboardService
      .get<TruckInventory>(API_URL_UTIL.dashboard.incomingTruckInventory)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.incomingTruckInventoryCount = data.totalTruckInventoryCount;
        this.cdf.detectChanges();
      });
  }

  expandViewModel(modelType: string): void {
    switch (modelType) {
      case ModelType.INVENTORY_PREFERENCE:
        this.modelPopups.isFullViewInventory = true;
        break;
      case ModelType.TASK:
        this.modelPopups.isFullViewTask = true;
        break;
      case ModelType.INVENTORYAGING:
        this.modelPopups.isFullViewInventoryAging = true;
        break;
      case ModelType.SALES_BY_MONTH:
        this.modelPopups.isFullViewSaleByMonth = true;
        break;
      case ModelType.TASK_ALERTS:
        this.modelPopups.isFullViewTaskAlerts = true;
        break;
      case ModelType.RECENT_INVENTORY:
        this.modelPopups.isFullViewRecentInventory = true;
        break;
      default:
        break;
    }
  }

  onAddEditPopupClose(modelType: string): void {
    this.modelPopups.isFullViewInventory = false;
    this.modelPopups.isFullViewTask = false;
    this.modelPopups.isFullViewInventoryAging = false;
    this.modelPopups.isFullViewSaleByMonth = false;
    this.modelPopups.isFullViewTaskAlerts = false;
    this.modelPopups.isFullViewRecentInventory = false;
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      this.dailyMonthlySalesVisible = user?.role.id === 1 || user?.role.id === 2 || user?.role.id === 4;
    });
  }
  onClickDailySales(): void {
    this.router.navigate(['reporting', 'customer-relationship', 'daily-sales']);
  };

  onClickInventoryCount(): void {
    this.router.navigate(['inventory']);
  };

  onClickTruckCount(): void {
    this.router.navigate(['transport','incoming-truck-board'])
  };
}
