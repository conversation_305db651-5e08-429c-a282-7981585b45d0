import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES, PipelineTypeList, SoldStockPipelineStatusList, Status, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { Shop, ShopUser } from '@pages/administration/models';
import { ShopService } from '@pages/administration/pages/pipeline-config/shop.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { DragAvailableShop, GeneralStockNumberBasicInfo, PipelineConfigListItem, PipelineGetDetail, PipelineLockItem, SoldTruckBoardListItem, SoldTruckCreateParams, StatusList, StockBasicInfo, TemplatePipelinePhase, TemplatePipelineTaskList } from '@pages/pipeline/models/sold-truck-board.model';
import { TaskListItem } from '@pages/shops/models';
import { TaskService } from '@pages/shops/services/tasks.service';
import { User } from '@sentry/angular';
import { ConfirmationService } from 'primeng/api';
import { Subscription, takeUntil } from 'rxjs';
import { IdNameModel, ViewMode } from 'src/app/@shared/models';
import { SoldTruckService } from '../sold-truck.service';

@Component({
  selector: 'app-sold-truck-board-add',
  templateUrl: './sold-truck-board-add.component.html',
  styleUrls: ['./sold-truck-board-add.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default
})

export class SoldTruckBoardAddComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Sold Truck';
  soldTruckGroup!: FormGroup;
  stockBasicInfo: StockBasicInfo[] = [];
  stockGeneralBasicInfo!: GeneralStockNumberBasicInfo;
  templatePipeLineInfo!: PipelineConfigListItem[];
  taskStatuses: IdNameModel[] = [];
  shops: Shop[] = [];
  shopUsersMap: Map<number, ShopUser[]> = new Map();
  showCreateModal = false;
  isEditMode = false;
  showConfirmationDialog = false;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() soldTruckInfo!: SoldTruckBoardListItem | PipelineGetDetail | null;
  hasDataBeenModified = false;
  isPipelineTemplateShow = false
  loaders = {
    shops: false,
    stockNumbers: false,
    shopUsers: false,
    pipelineOwner: false
  }
  accordionTabs = {
    generalInfo: true,
    pipeline: false,
    task: false,
    communication: true
  }
  availableShop!: DragAvailableShop;
  status = StatusList;
  templateByAvailableShopInfo: Shop[] = [];
  showPipelineConfigModal = false;
  pipelineDetail!: PipelineGetDetail;
  pipelineLockItem!: PipelineLockItem[];
  tasks!: TemplatePipelineTaskList[];
  templatePipelineData!: boolean[];
  subscriptionManager = new Subscription();
  selectedTask!: TaskListItem | null;
  pipelineOwnerList: IdNameModel[] = [];
  stockNumber!: number;
  createdBy!: string;
  createdDate!: string;
  isStatusList!: number | null;
  isViewMode = false;
  isActiveTab = true;
  isArchiveInProgress = false;
  selectedCommentId!: number;
  redirectUrl!: string;
  users: User[] = [];
  constructor(
    private readonly router: Router,
    private readonly confirmationService: ConfirmationService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly fb: FormBuilder,
    private readonly toasterService: AppToasterService,
    private readonly taskService: TaskService,
    private readonly cdf: ChangeDetectorRef,
    private readonly shopService: ShopService,
    private readonly soldTruckService: SoldTruckService,
    private readonly inventoryService: InventoryService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly commonSharedService: CommonSharedService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.getStockNumbers();
    this.getPipelineOwnerName();
    this.initializeFormGroup();
    this.getTaskStatuses();
    this.getUsers();
    if (this.isEditMode) {
      this.getPipelineDetailById(Number(this?.soldTruckInfo?.id));
      this.activatedRoute.queryParams
        .subscribe(params => {
          if (params?.commentId) {
            this.selectedCommentId = Number(params.commentId);
          }
          if (params?.returnUrl) {
            this.redirectUrl = params.returnUrl;
          }
        })
    }
    this.commonSharedService.setBlockUI$(false);
  }

  getPipelineDetailById(id: number): void {
    this.soldTruckService.getPipelineDetailById(id).pipe(takeUntil(this.destroy$)).subscribe(pipelineDetail => {
      this.pipelineDetail = pipelineDetail;
      this.tasks = pipelineDetail.tasks;
      this.stockNumber = this.pipelineDetail.stockNumber;
      this.createdBy = this.pipelineDetail.createdBy;
      this.createdDate = this.pipelineDetail.createdDate;
      this.setActiveFlagForAll()
      if (this.tasks?.length) {
        this.accordionTabs.task = true;
      }
      this.setPipelineConfigInFormGroup();
      this.cdf.detectChanges();
    });
  }
  private setActiveFlagForAll() {
    this.tasks.forEach(task => task.isActive = this.isActiveTab);
  }
  private initializeFormGroup(): void {
    this.soldTruckGroup = this.fb.group({
      stockNumber: new FormControl(null, [Validators.required]),
      status: new FormControl(SoldStockPipelineStatusList.in_staging, [Validators.required]),
      id: new FormControl(null),
      unitId: new FormControl(null),
      pipelineType: new FormControl(null, [Validators.required]),
      templatePipelineId: new FormControl(null, [Validators.required]),
      dealerId: new FormControl(null),
      phases: this.fb.array([]),
      pipelineOwnerId: new FormControl(null, [Validators.required]),
      targetCompletedDate: new FormControl(null)
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.soldTruckInfo.currentValue) {
      this.title = 'Edit Sold Truck';
      this.isEditMode = true;
    }
  }

  get templatePipelinePhasesFormArray(): FormArray {
    return this.soldTruckGroup.get('phases') as FormArray;
  }

  get newTemplatePipelinePhaseFormGroup(): FormGroup {
    return this.fb.group({
      assigneeId: new FormControl(null, [Validators.required]),
      sequenceNumber: new FormControl(null),
      shopId: new FormControl(null, [Validators.required]),
      id: new FormControl(null),
      taskStatus: new FormControl(null),
      altId: new FormControl(null)
    });
  }

  getCurrentTemplatePipelineFormGroup(index: number): FormGroup {
    return this.templatePipelinePhasesFormArray.at(index) as FormGroup;
  }

  get pipelineConfigCreateParams(): SoldTruckCreateParams {
    return {
      ...this.soldTruckGroup.value,
      status: this.soldTruckGroup.controls['status']?.value,
      id: this.soldTruckInfo?.id,
      pipelineType: PipelineTypeList.soldPipelineType,
      phases: this.templatePipelinePhasesFormArray.value
    };
  }

  get pipelinePhasesCreateParams(): PipelineLockItem[] {
    const phaseFormValues = this.templatePipelinePhasesFormArray.value;
    return phaseFormValues.map((phase: PipelineLockItem) => ({
      ...phase,
      pipelineId: this.pipelineDetail.id
    }))
  }

  get selectedDealerId(): number {
    return this.soldTruckGroup.get('dealerId')?.value;
  }

  getSequenceNumber(index: number): number {
    return Number(index) + 1;
  }

  onDeleteShop(index: number, data: any): void {
    if (data.length > 1) {
      this.templatePipelinePhasesFormArray.removeAt(index);
    }
    else if (data.length === 1) {
      this.toasterService.error(MESSAGES.atleastOneShopRequired);
    }
  }

  private getStockNumbers(): void {
    this.loaders.stockNumbers = true;
    this.taskService.getList<StockBasicInfo>(`${API_URL_UTIL.tasks.stockNumbers}?pipelineType=SOLD`).pipe(takeUntil(this.destroy$)).subscribe({
      next: (stockBasicInfo) => {
        this.stockBasicInfo = stockBasicInfo;
        this.loaders.stockNumbers = false;
      },
      error: () => {
        this.loaders.stockNumbers = false;
      }
    });
  }

  selectedStockNumberByDetail(event: number): void {
    this.accordionTabs.pipeline = true;
    this.loaders.stockNumbers = true;
    if (this.soldTruckGroup.get('phases')) {
      this.soldTruckGroup.controls.phases = this.fb.array([]);
    }
    if (!this.isEditMode) {
      this.soldTruckGroup.patchValue({
        pipelineType: null
      })
    }
    this.templateByAvailableShopInfo = [];
    this.soldTruckGroup.patchValue({
      unitId: Number(event)
    })
    const endpoint = API_URL_UTIL.inventory.generalInfo.replace(':unitId', event.toString());
    this.inventoryService.get<GeneralStockNumberBasicInfo>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (stockInfo) => {
        this.stockGeneralBasicInfo = stockInfo;
        this.getDealerIdByTemplatePipeline();
        this.getShopsByDealerId();
        this.loaders.stockNumbers = false;
      },
      error: () => {
        this.loaders.stockNumbers = false;
      }
    });
  }

  private getShopsByDealerId(): void {
    this.loaders.shops = true;
    this.shopService.getList<Shop>().pipe(takeUntil(this.destroy$)).subscribe(shops => {
      this.shops = shops;
      this.templateByAvailableShopInfo = shops;
      this.loaders.shops = false;
    });
  }

  getAvailableShops(index: number): Shop[] {
    const phases: TemplatePipelinePhase[] = this.templatePipelinePhasesFormArray.value;
    const selectedShops = this.shops.filter(shop => phases.some(phase => phase.shopId === shop.id));
    const phase: TemplatePipelinePhase = this.templatePipelinePhasesFormArray.at(index).value;
    const otherShops: Shop[] = this.shops.filter(shop => !selectedShops.some(selectedShop => selectedShop.id === shop.id));
    const currentShop = this.shops.find(shop => shop.id === phase.shopId);
    if (currentShop) {
      otherShops.unshift(currentShop);
    }
    return otherShops;
  }

  getUsers(): void {
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.users = res;
        },
        error: () => {
          this.loaders.shopUsers = false;
        }
      }
      );
  }

  getShopUsersByShopId(formArrayIndex: number): ShopUser[] {
    const templatePipelinePhase = this.templatePipelinePhasesFormArray.at(formArrayIndex).value;
    if (!templatePipelinePhase) {
      return [];
    }
    return this.shopUsersMap.get(templatePipelinePhase.shopId) || [];
  }

  getDealerIdByTemplatePipeline(): void {
    this.soldTruckService.getDealerByTemplate().pipe(takeUntil(this.destroy$)).subscribe((stockInfo: any) => {
      this.templatePipeLineInfo = stockInfo;
    },
    );
  }

  async selectedTemplatedByShop(event: number): Promise<void> {
    if (this.soldTruckGroup.get('phases')) {
      this.soldTruckGroup.controls.phases = this.fb.array([]);
    }
    const template = this.templatePipeLineInfo.find(id => id.id === event);
    this.soldTruckGroup.patchValue({
      templatePipelineId: Number(event),
      dealerId: Number(this.stockGeneralBasicInfo?.owner?.id),
    })
    this.getShopsByDealerId();
    template?.templatePipelinePhases.forEach(async templatePipelinePhase => {
      templatePipelinePhase.id = this.isEditMode ? templatePipelinePhase.id : null;
      const pipelinePhaseFormGroup = this.newTemplatePipelinePhaseFormGroup;
      pipelinePhaseFormGroup.patchValue(templatePipelinePhase);
      this.templatePipelinePhasesFormArray.push(pipelinePhaseFormGroup);
    });
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.onClose.emit(true);
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  dragStart(shop: DragAvailableShop): void {
    this.availableShop = shop;
  }

  drop(): void {
    const shop = this.shops.find(shop => shop.id === this.availableShop.id);
    if (this.availableShop) {
      const formGroup = this.newTemplatePipelinePhaseFormGroup;
      if (shop) {
        formGroup.get('assigneeId')?.patchValue(shop?.defaultUserId);
      }
      formGroup.get('shopId')?.patchValue(this.availableShop.id);
      this.templatePipelinePhasesFormArray.push(formGroup);
      this.updateNameInTemplatePipelinePhases();
      this.availableShop = new DragAvailableShop();
    }
  }

  dragEnd(): void {
    this.availableShop = new DragAvailableShop();
  }

  onSubmit(close = true): void {
    this.soldTruckGroup.updateValueAndValidity();
    if (this.soldTruckGroup.invalid) {
      this.soldTruckGroup.markAllAsTouched();
      return;
    }
    if (this.isEditMode) {
      this.editSoldTruckData(this.soldTruckGroup.controls['status']?.value);
    } else {
      this.updateNameInTemplatePipelinePhases();
      this.saveSoldTruckData(close);
    }
  }

  saveSoldTruckData(close = true): void {
    this.soldTruckService.add(this.pipelineConfigCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.soldTruckDataUpdateSuccess : MESSAGES.soldTruckDataAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.soldTruckGroup.reset();
        this.soldTruckGroup.patchValue({
          status: SoldStockPipelineStatusList.in_staging
        })
        if (this.soldTruckGroup.get('phases')) {
          this.soldTruckGroup.controls.phases = this.fb.array([]);
        }
      }
    });
  }

  onSubmitAndAddNew(): void {
    this.stockGeneralBasicInfo = new GeneralStockNumberBasicInfo();
    this.templateByAvailableShopInfo = [];
    this.onSubmit(false);
  }

  private updateNameInTemplatePipelinePhases(): void {
    for (const [index, pipelinePhaseControl] of this.templatePipelinePhasesFormArray.controls.entries()) {
      const pipelinePhaseFormGroup = pipelinePhaseControl as FormGroup;
      const nameControl = pipelinePhaseFormGroup.get('name');
      const shopId = pipelinePhaseFormGroup.get('shopId')?.value;
      const shop = this.shops.find(shop => shop.id === shopId);
      if (shop && nameControl) {
        nameControl.patchValue(shop.name);
      }
      pipelinePhaseFormGroup.get('sequenceNumber')?.patchValue(index + 1);
    }
  }

  reset(): void {
    if (this.soldTruckGroup.get('phases')) {
      this.soldTruckGroup.controls.phases = this.fb.array([]);
    }
    this.getPipelineDetailById(Number(this?.soldTruckInfo?.id))
  }

  private async setPipelineConfigInFormGroup(): Promise<void> {
    this.templatePipelineData = [];
    if (this.pipelineDetail) {
      this.soldTruckGroup.patchValue({
        stockNumber: this.pipelineDetail.unitId.toString(),
        unitId: this.pipelineDetail.unitId,
        status: this.pipelineDetail.status,
        pipelineType: this.pipelineDetail.templatePipelineId,
        dealerId: this.pipelineDetail.dealerId,
        templatePipelineId: this.pipelineDetail.templatePipelineId,
        pipelineOwnerId: this.pipelineDetail.pipelineOwner.id,
        targetCompletedDate: new Date(this.pipelineDetail.targetCompletedDate),
      });
      this.selectedStockNumberByDetail(this.pipelineDetail.unitId);
      this.getShopsByDealerId();
      for (const templatePipelinePhase of this.pipelineDetail.phases) {
        const pipelinePhaseFormGroup = this.newTemplatePipelinePhaseFormGroup;
        pipelinePhaseFormGroup.get('shopId')?.patchValue(templatePipelinePhase.shop.id);
        pipelinePhaseFormGroup.get('assigneeId')?.patchValue(templatePipelinePhase.assignee.id);
        pipelinePhaseFormGroup.get('id')?.patchValue(templatePipelinePhase.id);
        pipelinePhaseFormGroup.get('taskStatus')?.patchValue(templatePipelinePhase.taskStatus);

        if (templatePipelinePhase.taskStatus === Status.inProgress || templatePipelinePhase.taskStatus === Status.done) {
          this.templatePipelineData.push(true);
        }
        else {
          this.templatePipelineData.push(false);
        }
        pipelinePhaseFormGroup.patchValue(templatePipelinePhase);
        this.templatePipelinePhasesFormArray.push(pipelinePhaseFormGroup);
        this.isLoading = false;
      }
      this.cdf.detectChanges();
    }
  }

  editSoldTruckData(status: string): void {
    if (this.isEditMode) {
      const endpoint = API_URL_UTIL.pipeline.status.replace(':pipelineId', this.pipelineDetail.id.toString());
      const params = {
        targetCompletedDate: this.soldTruckGroup.get('targetCompletedDate')?.value,
        status: status,
        targetCompletedDateFlag: true
      }
      this.soldTruckService.update(params, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.toasterService.success(this.isEditMode ? MESSAGES.soldTruckDataUpdateSuccess : MESSAGES.soldTruckDataAddSuccess);
        this.hasDataBeenModified = true;
        this.onClose.emit(true);
      })
    }
  }

  pipelineLock(): void {
    if (this.soldTruckGroup.controls['phases'].invalid) {
      this.soldTruckGroup.markAllAsTouched();
      return;
    }
    this.templatePipelinePhasesFormArray.updateValueAndValidity();
    this.updateNameInTemplatePipelinePhases();
    const endpoint = API_URL_UTIL.pipeline.phases.replace(':unitId', this.pipelineDetail.id.toString());
    this.soldTruckService.update(this.pipelinePhasesCreateParams, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.pipelineLocked);
      this.getPipelineDetailById(this.pipelineDetail.id);
    });
  }

  onEdit(task: TaskListItem): void {
    this.showCreateModal = true;
    this.selectedTask = task;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedTask = null;
    if (refreshList) {
      this.getPipelineDetailById(this.pipelineDetail.id);
    }
  }

  onPipelineAddEditPopupClose(refreshList: boolean): void {
    this.showPipelineConfigModal = false;
    if (refreshList) {
      setTimeout(() => {
        this.getDealerIdByTemplatePipeline();
      }, 0);
    }
  }

  returnZero = (a: any) => {
    return a;
  }

  private getPipelineOwnerName(): void {
    this.loaders.pipelineOwner = true;
    this.soldTruckService.getPipelineOwnerList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (pipelineOwnerList) => {
        this.pipelineOwnerList = pipelineOwnerList;
        this.loaders.pipelineOwner = false;
      },
      error: () => {
        this.loaders.pipelineOwner = false;
      }
    });
  }

  private getTaskStatuses(): void {
    this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.statuses).pipe(takeUntil(this.destroy$)).subscribe({
      next: (taskStatuses) => {
        this.taskStatuses = taskStatuses;
      },
    });
  }

  findStatusIndex(index: number): void {
    this.isStatusList = index;
  }

  onViewEdit(task: TaskListItem, isEdit: boolean): void {
    this.isViewMode = isEdit ? false : true;
    this.router.navigate([API_URL_UTIL.admin.shops.shops], { queryParams: { id: task.id, mode: isEdit ? ViewMode.EDIT : ViewMode.READ } });
    this.selectedTask = task;
  }

  changePipelineOwner(pipelineOwnerId: number): void {
    if (this.isEditMode && pipelineOwnerId !== null) {
      const endpoint = API_URL_UTIL.pipeline.pipelineowner.replace(':pipelineId', this.pipelineDetail.id.toString());
      const fullEndpoint = `${endpoint}pipelineOwnerId=${pipelineOwnerId}`;
      this.soldTruckService.patch(Number(null), fullEndpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.toasterService.success(MESSAGES.pipelineOwnerChangeSuccess);
      })
    }
  }

  changeStatus(task: TemplatePipelineTaskList, status: number, id: number, isActive: any): void {
    const statusParam = {
      statusId: status
    }
    const endpoint = API_URL_UTIL.tasks.status.replace(':taskId', id.toString());
    this.taskService.update(statusParam, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.statusChangeSuccess);
      this.isStatusList = null;
      this.getPipelineDetailById(this.pipelineDetail.id);
    });
    if (status === 4) {
      const message = task.isActive
        ? MESSAGES.archiveWarning.replace('{record}', 'task')
        : MESSAGES.unArchiveWarning.replace('{record}', 'vendor');
      this.handleTaskConfirmation(task, isActive, message, true);
    }
  }

  onArchive(task: any, isActive: boolean): void {
    this.selectedTask = task;
    const message = task.isActive
      ? MESSAGES.archiveWarning.replace('{record}', 'task')
      : MESSAGES.unArchiveWarning.replace('{record}', 'task');
    this.handleTaskConfirmation(task, isActive, message);
  }

  private handleTaskConfirmation(task: any, isActive: boolean, message: string, isDialog = false): void {
    this.showConfirmationDialog = isDialog;
    this.cdf.detectChanges();
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: message,
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.isArchiveInProgress = true;
        this.showConfirmationDialog = false;
        this.onArchiveConfirmation(task);
        task.isActive = isActive;
      },
      reject: () => {
        task.isActive = !isActive;
        this.showConfirmationDialog = false;
        this.cdf.detectChanges();
      }
    });
  }

  private onArchiveConfirmation(task: any): void {
    this.taskService.archivedTask((task.id).toString())
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.isActiveTab ? MESSAGES.taskArchiveSuccess : MESSAGES.taskUnArchiveSuccess);
          this.isArchiveInProgress = false;
          this.selectedTask = null;
          this.getPipelineDetailById(this.pipelineDetail.id);
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedTask = null;
          this.getPipelineDetailById(this.pipelineDetail.id);
        }
      });
  }
}
