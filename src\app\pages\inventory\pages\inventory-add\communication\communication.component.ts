import { ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild, ViewChildren } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { MESSAGES, VALIDATION_CONSTANTS, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { InventoryListItem } from '@pages/inventory/models';
import { AllUserListFilter, Communication, Fields, InventoryCommentCreateParam, InventoryCommentResponse, MentionUser } from '@pages/inventory/models/inventory-communication';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { MentionConfig } from 'angular-mentions';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { MENTION_CONFIG } from 'src/app/@shared/constants/common.constant';
import { MentionParam } from 'src/app/@shared/models';


@Component({
  selector: 'app-communication',
  templateUrl: './communication.component.html',
  styleUrls: ['./communication.component.scss']
})
export class CommunicationComponent extends BaseComponent implements OnInit {
  commentFormGroup!: FormGroup;
  addedCommentsForm!: FormGroup;
  isFirstTime = true;
  fieldsName = Fields;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  unitId!: number | undefined;
  mentionedUsers: MentionUser[] = [];
  availableMentionUsers: MentionParam[] = [];
  mentionConfig: MentionConfig = {
    ...MENTION_CONFIG,
    items: this.availableMentionUsers
  };
  @Input() filterParams: any = new AllUserListFilter();
  isNoteEditing !: any[];
  currentNoteIndex!: number;
  updatedNote: InventoryCommentCreateParam = new InventoryCommentCreateParam();
  updatedDescription = '';
  caseNotes: InventoryCommentCreateParam[] = [];
  currentUserId!: number;
  editor: any;
  editorChild: any;
  @ViewChild('editor') set content(content: any) {
    if (content) {
      this.editor = content;
    }
  }
  @ViewChildren('editorChild') set contents(contents: any) {
    if (contents) {
      this.editorChild = contents;
    }
  }
  @Input() isViewMode!: boolean;
  @ViewChild('bold') bold!: ElementRef;
  boldChild: any;
  @ViewChildren('boldChild') set contentsBtn(contents: any) {
    if (contents) {
      this.boldChild = contents;
    }
  }
  showCreateModal = false;
  selectedCommentId!: number;
  constructor(private readonly formBuilder: FormBuilder,
    private readonly inventoryService: InventoryService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly confirmationService: ConfirmationService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly accountService: AuthService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getUserId();
    this.initializeCommentSubCommentFormGroup();
    this.getCommunicationList();
    this.getAll();
    this.isLoading = true;
    this.activatedRoute.queryParams
      .subscribe(params => {
        if (params?.commentId) {
          this.selectedCommentId = Number(params.commentId);
        }
      });
  }

  getUserId(): void {
    this.accountService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe((res) => {
      if (res?.id) {
        this.currentUserId = res.id;
      }
    });
  }

  textChange(e: any) {
    if (e.htmlValue.includes('&lt;strong')) {
      let val = e.htmlValue.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['message'].setValue(`${val}`);
      setTimeout(() => {
        this.editor.el.nativeElement.childNodes[0].lastChild.childNodes[0].focus();
        this.editor.el.nativeElement.childNodes[0].lastChild.childNodes[0].click();
        this.bold.nativeElement.click();
        this.editor.getQuill().setSelection(val.length + 1);
      }, 0);
    }
  }

  selectionChange(e: any) {
    if (e.range && e.range.index > 0 && e.source === 'api') {
      this.bold.nativeElement.click();
    }
  }

  selectionChildChange(e: any, childIndex: number) {
    if (e.range && e.range.index > 0) {
      this.boldChild.toArray()[childIndex].nativeElement.click();
    }
  }

  childTextChange(e: any, childIndex: number) {
    if (e.htmlValue.includes('&lt;strong')) {
      let val = e.htmlValue.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentsFormArray.at(childIndex).get('childMessage')?.setValue(`${val}`);
      setTimeout(() => {
        this.editorChild.toArray()[childIndex].el.nativeElement.childNodes[0].lastChild.childNodes[0].focus();
        this.editorChild.toArray()[childIndex].el.nativeElement.childNodes[0].lastChild.childNodes[0].click();
        this.boldChild.toArray()[childIndex].nativeElement.click();
        this.editorChild.toArray()[childIndex].getQuill().setSelection(val.length + 1);
      }, 0);
    }
  }

  initializeFormGroup(): void {
    this.commentFormGroup = this.formBuilder.group({
      message: new FormControl(null, [Validators.required, Validators.maxLength(VALIDATION_CONSTANTS.textAreaMaxLength)]),
      unitId: new FormControl(this.inventoryInfo?.id ? this.inventoryInfo?.id : this.inventoryIncomingInfo?.unitId),
      parentCommunicationId: new FormControl(null),
      id: new FormControl(null),
      mentionUserIds: new FormControl([])
    });
  }

  decodeEntities(str: any) {
    // this prevents any overhead from creating the object each time
    const element = document.createElement('div');
    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
    element.innerHTML = str;

    return element.textContent;
  }

  initializeCommentSubCommentFormGroup(): void {
    this.addedCommentsForm = this.formBuilder.group({
      parent: this.newAddedParentCommentsFormArray,
    });

  }

  get newAddedParentCommentsFormArray(): FormArray {
    return this.formBuilder.array([this.addedParentCommentsFormGroup])
  }

  get addedParentCommentsFormGroup(): FormGroup {
    return this.formBuilder.group({
      createdBy: new FormControl(null),
      edited: new FormControl(null),
      id: new FormControl(null),
      message: new FormControl(null),
      modifiedDate: new FormControl(null),
      unitId: new FormControl(null),
      parentCommunicationId: new FormControl(null),
      childComments: this.addedChildCommentsFormArray,
    });
  }

  get addedChildCommentsFormArray(): FormArray {
    return this.formBuilder.array([this.addedChildCommentsFormGroup])
  }

  get addedChildCommentsFormGroup(): FormGroup {
    return this.formBuilder.group({
      createdBy: new FormControl(this.createdByFormGroup),
      edited: new FormControl(null),
      id: new FormControl(null),
      messageControl: new FormControl(null, [Validators.required]),
      message: new FormControl(null),
      modifiedDate: new FormControl(null),
      unitId: new FormControl(null),
      parentCommunicationId: new FormControl(null),
    });
  }

  get createdByFormGroup(): FormGroup {
    return this.formBuilder.group({
      id: new FormControl(null),
      firstName: new FormControl(null),
      lastName: new FormControl(null),
    });
  }

  get commentsFormArray(): FormArray {
    return this.addedCommentsForm.get('parent') as FormArray;
  }

  setisPrevieClickedValue(parentIndex: number): void {
    this.commentsFormArray.at(parentIndex)?.get('isPrevieClicked')?.setValue(true);
  }

  getChildCommentsFormArrayByParentCommunicationId(id: number): FormArray {
    return this.commentsFormArray.at(id)?.get('childComments') as FormArray;
  }

  getChildCommentFormControl(parentIndex: number): FormControl {
    return this.commentsFormArray.at(parentIndex)?.get('childMessage') as FormControl;
  }

  addNewParentCommentSubmit() {
    if (this.commentFormGroup.invalid) {
      this.commentFormGroup.markAllAsTouched();
      return;
    }
    this.commentFormGroup.markAsUntouched();
    if (this.inventoryInfo?.id || this.inventoryIncomingInfo?.unitId) {
      this.addComment(true);
    }
  }

  addNewChildCommentSubmit(parentId: number, parentCommunicationId: number) {
    if (!this.commentsFormArray.at(parentId)?.get('childMessage')?.dirty) {
      this.commentsFormArray.at(parentId)?.get('childMessage')?.markAsTouched();
      return;
    }
    const userIds = this.mentionedUsers.map(i => i.id);
    if (this.commentsFormArray.at(parentId)?.get('childMessage')?.value.includes('&lt;strong')) {
      let val = this.commentsFormArray.at(parentId)?.get('childMessage')?.value.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentsFormArray.at(parentId)?.get('childMessage')?.setValue(`${val}`);
    }
    if (this.inventoryInfo?.id || this.inventoryIncomingInfo?.unitId) {
      if (this.inventoryInfo?.id) {
        const childParams: InventoryCommentCreateParam = {
          message: this.commentsFormArray.at(parentId)?.get('childMessage')?.value,
          unitId: this.inventoryInfo?.id,
          parentCommunicationId: parentCommunicationId,
          mentionUserIds: userIds,
          isEditable: false
        }
        this.addComment(false, childParams, parentId);
      }
      else if (this.inventoryIncomingInfo?.unitId) {
        const childParams: InventoryCommentCreateParam = {
          message: this.commentsFormArray.at(parentId)?.get('childMessage')?.value,
          unitId: this.inventoryIncomingInfo?.unitId,
          parentCommunicationId: parentCommunicationId,
          mentionUserIds: userIds,
          isEditable: false
        }
        this.addComment(false, childParams, parentId);
      }
    }
  }

  get commentCreateParams(): InventoryCommentCreateParam {
    return {
      ...this.commentFormGroup.value,
    };
  }
  private getCommunicationList() {
    if (this.inventoryIncomingInfo) {
      this.unitId = this.inventoryIncomingInfo.unitId;
    }
    else {
      this.unitId = this.inventoryInfo?.id;
    }
    const endpoint = API_URL_UTIL.inventory.communicationByUnitId.replace(':unitId', String(this.unitId))
    this.inventoryService.getList<InventoryCommentResponse>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (communicationData) => {
        if (this.isFirstTime) {
          this.commentsFormArray.clear();
        }
        this.isFirstTime = false;
        this.commentsFormArray.clear();
        for (const communicationList of communicationData) {
          this.commentsFormArray.push(this.formBuilder.group({
            createdBy: communicationList.createdBy,
            edited: communicationList.edited,
            id: communicationList.id,
            messageControl: null,
            message: communicationList.message,
            modifiedDate: communicationList.modifiedDate,
            parentCommunicationId: null,
            unitId: communicationList.unitId,
            childMessage: new FormControl(null, [Validators.required]),
            isPrevieClicked: false,
            childComments: this.formBuilder.array([]),
            mentionUserIds: [communicationList.mentionUserIds],
          }));
          this.cdf.detectChanges();
          if (communicationList.communications.length) {
            for (const communication of communicationList.communications) {
              const commentArrayIndex = this.commentsFormArray.value.findIndex((commentsArray: Communication) => commentsArray.id === communication.parentCommunicationId);
              if (commentArrayIndex !== -1) {
                this.getChildCommentsFormArrayByParentCommunicationId(commentArrayIndex).push(this.formBuilder.group({
                  createdBy: communication.createdBy,
                  edited: communication.edited,
                  id: communication.id,
                  message: communication.message,
                  modifiedDate: communication.modifiedDate,
                  parentCommunicationId: communication.parentCommunicationId,
                  unitId: communication.unitId,
                  mentionUserIds: [communication.mentionUserIds],
                }));
              }
            }
          }
        }
        this.cdf.detectChanges();
      }
    });
  }

  private removeErasedUser(comment: string, ids: number[]): number[] {
    return ids.filter(id => {
      const label = this.availableMentionUsers.find(obj => obj.id === id)?.label;
      return label && comment.toLowerCase().includes(label.toLowerCase());
    });
  }

  private addComment(isParentComment: boolean, childParams: InventoryCommentCreateParam | null = null, parentId = 0): void {
    let params;
    if (isParentComment) {
      params = this.commentCreateParams;
      params.mentionUserIds = this.mentionedUsers.map(i => i.id);
      if (this.commentFormGroup.controls['message'].value.includes('&lt;strong')) {
        let val = this.commentFormGroup.controls['message'].value.split('&lt;strong');
        const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
        let d = val[index].split('/strong&gt;');
        d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
        d = d.join(' ');
        val[index] = d;
        val = val.join(' ');
        this.commentFormGroup.controls['message'].setValue(`${val}`);
        params.message = this.commentFormGroup.controls['message'].value;
      }
    } else {
      if (childParams) {
        params = childParams;
      }
    }
    if (params) {
      params.mentionUserIds = this.removeErasedUser(params.message, params.mentionUserIds);
    }
    this.inventoryService.add(params, API_URL_UTIL.inventory.communications).pipe(takeUntil(this.destroy$)).subscribe(savedComment => {
      this.toasterService.success(MESSAGES.commentAddSuccess);
      this.commentCreateParams.mentionUserIds = [];
      this.mentionedUsers = [];
      if (this.isFirstTime) {
        this.commentsFormArray.clear();
      }
      if (isParentComment) {
        this.commentsFormArray.push(this.formBuilder.group({
          createdBy: {
            id: savedComment.createdBy.id,
            firstName: savedComment.createdBy.firstName,
            lastName: savedComment.createdBy.lastName
          },
          edited: savedComment.savedComment,
          id: savedComment.id,
          messageControl: null,
          message: savedComment.message,
          modifiedDate: savedComment.modifiedDate,
          parentCommunicationId: savedComment.parentCommunicationId,
          unitId: savedComment.unitId,
          childMessage: new FormControl(null, [Validators.required]),
          isPrevieClicked: false,
          childComments: this.formBuilder.array([])
        }));
        this.isFirstTime = false;
        this.commentFormGroup.get('message')?.reset();
        this.addedCommentsForm.updateValueAndValidity();
      }
      else {
        this.getChildCommentsFormArrayByParentCommunicationId(parentId).push(this.formBuilder.group({
          createdBy: {
            id: savedComment.createdBy.id,
            firstName: savedComment.createdBy.firstName,
            lastName: savedComment.createdBy.lastName
          },
          edited: savedComment.savedComment,
          id: savedComment.id,
          message: savedComment.message,
          modifiedDate: savedComment.modifiedDate,
          parentCommunicationId: savedComment.parentCommunicationId,
          unitId: savedComment.unitId,
        }));
        this.commentsFormArray.at(parentId)?.get('isMultipleComments')?.setValue(this.commentsFormArray.at(parentId)?.get('childComments')?.value.length > 1 ? true : false);
        this.commentsFormArray.at(parentId)?.get('childMessage')?.reset();
      }
      this.cdf.detectChanges();
    });
  }

  mentionSelected(mention: any, childIndex = -1) {
    let count = 0;
    for (const i of this.mentionedUsers) {
      if (i.id === mention.id) {
        count++;
      }
    }
    if (count === 0) {
      this.mentionedUsers.push(mention);
    }
    setTimeout(() => {
      if (childIndex > -1) {
        this.editorChild.toArray()[childIndex].getQuill().updateContents();
      } else {
        this.editor.getQuill().updateContents();
      }
    })
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.userAnnotationService.get(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          this.availableMentionUsers = res.map((user: any) => ({ id: user.id, label: user.name }));
          this.mentionConfig.items = this.availableMentionUsers;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: (err: any) => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onDelete(expenses: any, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      header: 'Confirmation',
      message: MESSAGES.deleteWarning.replace('{record}', 'comment'),
      icon: icons.triangle,
      accept: () => {
        this.onDeleteConfirmation(expenses);
      }
    });
  }

  onDeleteConfirmation(expenses: any): void {
    const endpoint = API_URL_UTIL.inventory.communicationDeleteById;
    this.inventoryService.delete(expenses.value.id, endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.commentDeleteSuccess);
          this.getCommunicationList();
          this.cdf.detectChanges()
        }
      });
  }

  trackByFunction(index: number, element: any) {
    return element ? index : null;
  }

  onEdit(caseNote: any, index: number): void {
    this.showCreateModal = true;
    this.caseNotes = caseNote;
    this.cdf.detectChanges();
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.caseNotes = [];
    this.getCommunicationList();
    if (refreshList) {
      this.getAll();
    }
  }
}
