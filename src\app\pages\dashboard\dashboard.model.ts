import { TaskAttachment } from "@pages/shops/models";
import { IdNameModel } from "src/app/@shared/models";

export interface DaliyMontlySales {
  dailySales: number;
  monthlySales: number;
}

export interface TruckInventory {
  totalTruckInventoryCount: number;
}

export class InventoryListItem {
  id!: number;
  contactName!: string;
  unitType!: string;
  reporter!: IdNameModel;
  make!: IdNameModel;
  year!: string;
  unitModel!: IdNameModel;
  createdDate!: string;
}

export interface TaskStatus {
  toDoCount: number;
  inProgressCount: number;
  issueCount: number;
  totalPendingTasks: number;
}

export enum ModelType {
  INVENTORY_PREFERENCE = 'inventory-Preference',
  TASK = 'task',
  INVENTORYAGING = 'Inventory aging',
  SALES_BY_MONTH = 'Sales by month',
  TASK_ALERTS = 'Task Alerts',
  RECENT_INVENTORY = 'Recent Inventory'
}

export interface InventoryAging {
  nullCount: number;
  underThirtyDaysCount: number;
  thirtyToSixtyDaysCount: number;
  sixtyToNinetyDaysCount: number;
  ninetyToOneTwentyDaysCount: number;
  oneTwentyDayToOneEightyDaysCount: number;
  overOneEightyDaysCount: number;
}

export interface YearlySales {
  count: number;
  month: number;
}

export enum InventoryLabels {
  NULL = 'No Acq Date',
  UNDER_30 = 'Under 30 days',
  DAY_30_TO_60 = '30 to 60 days',
  DAY_60_TO_90 = '60 to 90 days',
  DAY_90_TO_120 = '90 to 120 days',
  DAY_120_TO_180 = '120 to 180 days',
  OVER_180 = 'Over 180 days'
}

export enum TaskStatusChartLabels {
  ISSUE = 'Issue',
  IN_PROGRESS = 'In Progress',
  TO_DO = 'To Do',
}

export enum TaskStatusLabelColors {
  ISSUE_COLOR = 'rgba(220, 93, 61, 0.6)',
  IN_PROGRESS_COLOR = 'rgba(250, 177, 51, 0.6)',
  TO_DO_COLOR = 'rgba(25, 123, 222, 0.6)'
}

export enum InventoryAgingLabelColors {
  NULL_COLR = '#e15b60',
  UNDER_30_COLOR = '#587afc',
  DAY_30_TO_60_COLOR = '#9465de',
  DAY_60_TO_90_COLOR = '#25d9fc',
  DAY_90_TO_120_COLOR = '#e88843',
  DAY_120_TO_180_COLOR = '#34c97c',
  OVER_180_COLOR = '#ffd24a'
}

export enum MonthLabel {
  JAN = 'Jan',
  FEB = 'Feb',
  MAR = 'Mar',
  APR = 'Apr',
  MAY = 'May',
  JUN = 'Jun',
  JULY = 'July',
  AUG = 'Aug',
  SEPT = 'Sept',
  OCT = 'Oct',
  NOV = 'Nov',
  DEC = 'Dec'
}

export class TaskAlertListItem {
  id!: number;
  summary!: string;
  startDate!: string;
  endDate!: string;
  description!: string;
  taskPriority!: string;
  taskType!: IdNameModel;
  taskStatus!: IdNameModel;
  reporter!: IdNameModel;
  assignee!: IdNameModel;
  unitId!: number;
  shop!: IdNameModel;
  pipelineId!: number;
  pipelineSequence!: string;
  phaseId!: number;
  taskAttachments: TaskAttachment[] = [];
  stockNumber!: number;
  deleted!: boolean;
  crmContact!: IdNameModel;
  customerLeadId!: string;
  activity!: string;
  createdDate!: string;
}

export enum SalesByMonthColor {
  CHART_BORDER_COLOR = '#4662D0',
  CHART_BACK_COLOR = 'rgba(128, 182, 244, 0.6)',
  CHART_MULTI_AXIS_COLOR = '#495057',
  CHART_GRID_COLOR = '#ebedef'
}
