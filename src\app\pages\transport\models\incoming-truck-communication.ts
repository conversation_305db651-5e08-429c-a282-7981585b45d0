import { Communication, CreatedBy } from '@pages/inventory/models/inventory-communication';

export interface IncomingTruckCommunication {
  message: string;
  incomingTruckId: number;
  parentCommunicationId: number;
  mentionUserIds: number[];
}
export class IncomingTruckCommentResponse {
  id!: number | null;
  message!: string;
  edited!: boolean;
  modifiedDate!: string;
  unitId!: number | null;
  createdBy!: CreatedBy;
  communications!: Communication[];
  mentionUserIds!: number[];
  deleted!: boolean;
}
