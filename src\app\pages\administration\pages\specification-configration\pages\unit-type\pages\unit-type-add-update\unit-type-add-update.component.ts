import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, UntypedFormArray, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { UnitTypeCategory } from '@pages/administration/pages/specification-configration/models/specification.model';
import { UnitType } from '@pages/inventory/models';
import { takeUntil } from 'rxjs';
import { UnitTypeService } from '../../unit-type.service';

@Component({
  selector: 'app-unit-type-add-update',
  templateUrl: './unit-type-add-update.component.html',
  styleUrls: ['./unit-type-add-update.component.scss']
})
export class UnitTypeAddUpdateComponent extends BaseComponent implements OnChanges, OnInit {

  @Input() selectedUnitType!: UnitType;
  @Input() categories!: Array<UnitTypeCategory>;
  @Input() selectedCategory!: number;

  pageTitle!: string;
  unitTypeEditFormGroup!: FormGroup;
  unitTypeAddFormGroup!: FormGroup;

  @Output() closeModal = new EventEmitter<void>();
  @Output() addNewUnitType = new EventEmitter<Array<UnitType>>();
  @Output() updateUnitTypeDetails = new EventEmitter<UnitType>();

  constructor(
    private readonly unitTypeService: UnitTypeService,
    private readonly formBuilder: FormBuilder,
    private readonly toasterService: AppToasterService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeAddForm();
    this.addUnitTypeConfiguration();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedUnitType?.currentValue) {
      this.setPageConfigurations();
    }

    if (changes.selectedCategory?.currentValue) {
      this.addUnitTypeConfiguration();
    }
  }

  setPageConfigurations(): void {
    if (this.selectedUnitType?.id) {
      this.pageTitle = 'Edit Unit Type';
      this.initializeEditForm();
      this.setUnitTypeFormValues();
      return;
    }
    this.addUnitTypeConfiguration();
  }

  setUnitTypeFormValues(): void {
    this.unitTypeEditFormGroup.patchValue({
      name: this.selectedUnitType?.name,
      unitTypeCategoryId: this.selectedUnitType?.unitTypeCategoryId
    })
  }

  addUnitTypeConfiguration(): void {
    this.unitTypeAddFormGroup.get('unitTypeCategoryId')?.setValue(this.selectedCategory);
    this.pageTitle = 'Add Unit Type';
  }

  initializeEditForm(): void {
    this.unitTypeEditFormGroup = new FormGroup({
      name: new FormControl('', [Validators.required, Validators.maxLength(50)]),
      unitTypeCategoryId: new FormControl(null, Validators.required)
    });
  }

  initializeAddForm(): void {
    this.unitTypeAddFormGroup = this.formBuilder.group({
      unitTypeCategoryId: new FormControl(null, Validators.required),
      unitTypes: this.newUnitTypeFormArray
    });
  }

  get newUnitTypeFormArray(): UntypedFormArray {
    return this.formBuilder.array([this.newUnitTypeFormGroup]);
  }

  get unitTypeFormArray(): UntypedFormArray {
    return this.unitTypeAddFormGroup.get('unitTypes') as UntypedFormArray;
  }

  get newUnitTypeFormGroup(): FormGroup {
    return this.formBuilder.group({
      name: new FormControl('', [Validators.required, Validators.maxLength(50)]),
      unitTypeCategoryId: new FormControl(null)
    });
  }

  onDeleteUnitType(index: number): void {
    this.unitTypeFormArray.removeAt(index);
  }

  isAddNewCategoryVisible(index: number): boolean {
    return index === this.unitTypeFormArray.length - 1;
  }

  onAddNewUnitType(): void {
    this.unitTypeFormArray.push(this.newUnitTypeFormGroup);
    document.getElementById('addModelBtn')?.scrollIntoView({
      behavior: 'smooth',
    });
  }

  onModalClose(): void {
    this.closeModal.emit();
    this.selectedUnitType?.id ? this.unitTypeEditFormGroup?.reset() : this.clearAddUnitTypeForm();
  }

  clearAddUnitTypeForm(): void {
    this.unitTypeAddFormGroup?.reset();
    this.unitTypeFormArray?.clear();
    this.unitTypeFormArray.push(this.newUnitTypeFormGroup);
  }

  onAddUnitType(): void {
    if (this.unitTypeAddFormGroup.invalid) {
      this.unitTypeAddFormGroup.markAllAsTouched();
      return;
    }
    if (this.checkIfNamesAreUnique) {
      this.addUnitType();
    } else {
      this.toasterService.error(MESSAGES.namesMustBeUnique);
    }
    return
  }

  private get checkIfNamesAreUnique(): boolean {
    const unitTypeArray = this.unitTypeAddFormGroup.get('unitTypes')?.value ?? [];
    const unitTypeNames = unitTypeArray.map((model: any) => model.name);
    const uniqueUnitType = new Set(unitTypeNames);
    return unitTypeNames?.length === uniqueUnitType?.size;
  }

  onUpdateUnitType(): void {
    if (this.unitTypeEditFormGroup.invalid) {
      this.unitTypeEditFormGroup.markAllAsTouched();
      return;
    }
    this.updateUnitType();
  }

  addUnitType(): void {
    this.unitTypeService.add<Array<UnitType>>
      (this.unitTypeFormArray.value.map((item: UnitType) => {
        return {
          ...item, unitTypeCategoryId: this.unitTypeAddFormGroup.getRawValue().unitTypeCategoryId
        }
      })
        , `${API_URL_UTIL.admin.unitSpecification.unitTypes}/all`)
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: (res: Array<UnitType>) => {
          this.addNewUnitType.emit(res);
          this.onModalClose();
        }
      });
  }

  updateUnitType(): void {
    this.unitTypeService.update<UnitType>({
      ...this.unitTypeEditFormGroup.getRawValue(), id: this.selectedUnitType.id
    },
      API_URL_UTIL.admin.unitSpecification.unitTypes).pipe(takeUntil(this.destroy$)).subscribe({
        next: (res: UnitType) => {
          this.updateUnitTypeDetails.emit(res);
          this.onModalClose();
        }
      });
  }

}
