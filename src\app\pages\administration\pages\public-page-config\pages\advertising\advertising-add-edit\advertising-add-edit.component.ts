import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { takeUntil } from 'rxjs';
import { BannerListItem } from '../advertising.model';
import { AdvertisingService } from '../advertising.service';

@Component({
  selector: 'app-advertising-add-edit',
  templateUrl: './advertising-add-edit.component.html',
  styleUrls: ['./advertising-add-edit.component.scss']
})
export class AdvertisingAddEditComponent extends BaseComponent implements OnInit {
  @Output() onClose = new EventEmitter<boolean>();
  @Input() banner!: BannerListItem | null;
  @Input() isEditMode!: boolean;
  redirectUrl!: string;
  NEW_LISTING_BANNER_ID = 2;
  bannerForm !: FormGroup;
  title !: string;
  constructor(
    private readonly fb: FormBuilder,
    private readonly advertisingService: AdvertisingService,
    private readonly toasterService: AppToasterService,
    private readonly activeRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly commonSharedService: CommonSharedService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.title = this.isEditMode ? 'Edit Banner' : 'Add Banner';
    this.initializeFormGroup();
    this.activeRoute.queryParams.subscribe(params => {
      if (params?.returnUrl) {
        this.redirectUrl = params?.returnUrl;
      }
    });
    this.commonSharedService.setBlockUI$(false);
  }

  private initializeFormGroup(): void {
    this.bannerForm = this.fb.group({
      id: new FormControl(this.isEditMode ? this.banner?.id : null),
      name: new FormControl(this.isEditMode ? this.banner?.name : null, [Validators.required, Validators.maxLength(25)]),
      bgColor: new FormControl(this.isEditMode ? this.banner?.bgColor : '#000000', Validators.required),
      fontColor: new FormControl(this.isEditMode ? this.banner?.fontColor : '#000000', Validators.required),
      systemDefault: new FormControl(this.isEditMode ? this.banner?.systemDefault : false),
      clearAfter: (this.isEditMode && this.banner?.id === 2) ? new FormControl(this.isEditMode ? this.banner?.clearAfter : null, [Validators.required]) : null
    });
  }


  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  onCancel(bool = false): void {
    this.onClose.emit(bool);
    this.goBack()
  }

  private saveBanner(): void {
    this.advertisingService.add(this.bannerForm.value).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.BannerAddSuccess);
        this.onClose.emit(true);
      }
    })
  }
  private editBanner(): void {
    this.advertisingService.update(this.bannerForm.value).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.BannerEditSuccess);
        this.onClose.emit(true);
      }
    })
  }

  onSubmit(): void {
    if (this.bannerForm.invalid) {
      this.bannerForm.markAllAsTouched();
      return;
    }
    if (this.isEditMode) {
      this.editBanner();
    }
    else {
      this.saveBanner();
    }
  }
}
