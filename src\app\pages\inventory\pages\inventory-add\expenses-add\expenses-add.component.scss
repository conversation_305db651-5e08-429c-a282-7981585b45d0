.m-t-5 {
  margin-top: 5px;
}

.space-between {
  justify-content: space-between;
}

.add-btn-vendor {
  background: var(--active-color);
  border: none;
  color: white;
}

.m-t-10 {
  margin-top: 10px;
}

.m-l-10 {
  margin-left: 10px;
}

::ng-deep .expense-add {
  .p-inline-message.p-inline-message-info {
    height: 1px;
  }

  .file-size-message {
    bottom: 11px;
    right: 49px;
    font-size: 15px;
    width: 268px;
  }
}

.cursor-pointer {
  cursor: pointer;
}

.add-btn {
  height: 25px;
  padding: 0px 5px;
  float: right;
  margin: 3px;
}

::ng-deep .width-80 {
  width: 80rem !important;
}
