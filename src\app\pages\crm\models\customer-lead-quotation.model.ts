import { AssociationsUnits } from "@pages/inventory/models";
import { GenericFilterParams, IdNameModel, TreeOperatorType } from "src/app/@shared/models";
import { QuotationResponse } from "./customer-inventory.model";

export interface QuotationFilterParams {
  treeOperator?: TreeOperatorType,
  values?: QuotationValues[],
}

export interface QuotationValues {
  dataType: string,
  key: string,
  operator: string,
  value: string,
  enumName?: string;
}

export enum QuotationCustomerLeadKey {
  CRM_CUSTOMER = 'customerLead.crmContact.id',
  CRM_CONTACT = 'customerLead.id',
}

export class QuotationCustomerListFilter extends GenericFilterParams {
  deleted = false
}

export class QuotationCustomerListItem {
  company!: string;
  createdBy!: CreatedBy;
  crmContact!: CrmContact
  customerLeadId!: string;
  id!: string;
  location!: string;
  quotationDate!: string;
  quotationStatus!: string | null;
  quotePrice!: number;
  reasonForRejection!: string | null;
  sendEmail!: boolean;
  unit!: QuotationResponse;
  quotationUnitResponseDTOList!: Array<AssociationsUnits>;
  totalRetailPrice!: number;
  totalInvestmentCost !: number;
  totalQuotePrice !: number;
}

export interface CreatedBy {
  email: string,
  id: number,
  name: string,
  phoneNumber: number
}

export interface CrmContact {
  city: string,
  company: string,
  contactName: null | string,
  country: string,
  creator: IdNameModel,
  email: string,
  id: number,
  phoneWork: number,
  state: string,
  streetAddress: string,
  zipcode: string,
}

export enum QuotationStatus {
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED'
}

export interface Specifications {
  groupName: string,
  fields: Fields[]
}

export class Fields {
  key!: string;
  value!: string | number
}
