<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form [formGroup]="documentDetailsFormGroup" (ngSubmit)="onSubmit()" [ngClass]="{ loading: isLoading }">
  <div class="content" *ngIf="!isLoading; else loaderTemplate">
    <section>
      <ng-container *ngIf="documentDetails?.isEditable">
        <div class="row">
          <div class="col-12">
            <label>Add Title</label>
            <input class="form-control" type="text" placeholder="Add Title" formControlName="title" />
          </div>
        </div>
        <div class="row">
          <div class="col-12 position-relative">
            <label>Add Description</label>
            <textarea placeholder="Add Description" rows="3" formControlName="description"></textarea>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <label>Select Documents</label>
            <input class="form-control disabled" type="text" placeholder="Select Documents" formControlName="documentType" />
          </div>
        </div>
        <p-divider></p-divider>
        <div class="row">
          <div class="col-12">
            <label>Document Visibility</label>
            <div class="row">
              <div class="p-field-radiobutton col-2">
                <p-radioButton name="sharing" [value]="SharingType.PRIVATE" inputId="internal" formControlName="sharing"> </p-radioButton>
                <label for="internal">Private</label>
              </div>
              <div class="p-field-radiobutton col-2">
                <p-radioButton name="sharing" [value]="SharingType.PUBLIC" inputId="public" formControlName="sharing"> </p-radioButton>
                <label for="public">Public</label>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <section [ngClass]="{ 'document-other-details': documentDetails?.isEditable }">
        <div class="row">
          <div>
            <span class="document-label">Filename:</span>
            <div>
              <span class="document-label-detail">{{ documentDetails?.fileName }}</span>
            </div>
          </div>
        </div>
        <div class="row">
          <div>
            <span class="document-label">Added:</span>
            <div>
              <span class="document-label-detail">{{ documentDetails?.createdDate | date: constants.fullDateFormat }}</span>
            </div>
          </div>
        </div>
        <div class="row">
          <div>
            <span class="document-label">File Size:</span>
            <div>
              <span class="document-label-detail">{{ documentDetails?.fileSize }} KB</span>
            </div>
          </div>
        </div>
      </section>
    </section>
  </div>

  <div class="modal-footer" *ngIf="documentDetails?.isEditable">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall *ngIf="!isViewMode">Save</button>
  </div>
</form>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>
