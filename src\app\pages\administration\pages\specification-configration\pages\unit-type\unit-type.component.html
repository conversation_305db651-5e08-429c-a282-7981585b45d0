<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button
      class="btn btn-primary left"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      (click)="onAddEditUnitType()"
      *appHasPermission="[permissionActions.CREATE_UNIT_TYPE]"
    >
      <span class="show-label">Add New Unit Type</span>
    </button>
  </div>
</app-page-header>

<div class="content">
  <section>
    <div class="d-flex justify-content-between block">
      <p-dropdown
        [options]="categories"
        [(ngModel)]="selectedCategory"
        optionLabel="name"
        optionValue="id"
        (onChange)="onCategoryChange($event.value)"
      ></p-dropdown>
      <button
        class="btn btn-primary left"
        (click)="showDeleteModal(undefined, $event, true)"
        *appHasPermission="[permissionActions.DELETE_UNIT_TYPE]"
        [disabled]="!selectedUnitTypes.length"
      >
        <span>Delete Unit Types ({{ getSelectedUnitTypeCount() }})</span>
      </button>
    </div>
  </section>
</div>

<ng-container *ngIf="!isLoading; else pageLoaderTemplate">
  <p-table
    class="no-column-selection"
    [value]="unitTypes"
    [(selection)]="selectedUnitTypes"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="true"
    [rowHover]="true"
    [loading]="isLoading"
    styleClass="p-datatable-gridlines"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <ng-container *appHasPermission="[permissionActions.DELETE_UNIT_TYPE]">
          <th pResizableColumn style="width: 4rem" *ngIf="getUnitTypeCount()">
            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
          </th>
        </ng-container>
        <th pResizableColumn scope="col">Name</th>
        <th pResizableColumn scope="col">Inventory</th>
        <th pResizableColumn scope="col" *appHasPermission="[permissionActions.UPDATE_UNIT_TYPE, permissionActions.DELETE_UNIT_TYPE]">
          Actions
        </th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-unitType>
      <tr>
        <ng-container *appHasPermission="[permissionActions.DELETE_UNIT_TYPE]">
          <td *ngIf="getUnitTypeCount()">
            <p-tableCheckbox *ngIf="!unitType?.inventoryCount" [value]="unitType"></p-tableCheckbox>
          </td>
        </ng-container>
        <td>
          {{ unitType?.name }}
        </td>
        <td>
          {{ unitType?.inventoryCount }}
        </td>
        <td class="actions" *appHasPermission="[permissionActions.DELETE_UNIT_TYPE, permissionActions.UPDATE_UNIT_TYPE]">
          <img
            [src]="constants.staticImages.icons.edit"
            (click)="onAddEditUnitType(unitType)"
            alt=""
            *appHasPermission="[permissionActions.UPDATE_UNIT_TYPE]"
          />
          <img
            [src]="constants.staticImages.icons.deleteIcon"
            (click)="showDeleteModal(unitType, $event)"
            alt=""
            *appHasPermission="[permissionActions.DELETE_UNIT_TYPE]"
          />
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="6" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
</ng-container>

<ng-template #pageLoaderTemplate>
  <div class="no-data mt-5">
    <em class="pi pi-spin pi-spinner"></em>
  </div>
</ng-template>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  (onHide)="closeModal()"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-unit-type-add-update
    [selectedUnitType]="selectedUnitType"
    [selectedCategory]="selectedCategory"
    [categories]="categories"
    (closeModal)="closeModal()"
    (addNewUnitType)="addNewUnitType($event)"
    (updateUnitTypeDetails)="updateUnitType($event)"
  >
  </app-unit-type-add-update>
</p-sidebar>
<p-confirmPopup></p-confirmPopup>
