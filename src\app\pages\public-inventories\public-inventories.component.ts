import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BaseComponent } from '@core/utils';
import { DealerListItem } from '@pages/administration/models';

@Component({
  selector: 'app-public-inventories',
  templateUrl: './public-inventories.component.html',
  styleUrls: ['./public-inventories.component.scss']
})
export class PublicInventoriesComponent extends BaseComponent implements OnInit {
  isDealerActive = false;
  dealer!: DealerListItem;
  constructor(private readonly activatedRoute: ActivatedRoute) {
    super();
  }

  ngOnInit(): void {
    this.activatedRoute.data.subscribe((response: any) => {
      this.dealer = response.dealer;
      if (this.dealer) {
        this.isDealerActive = !this.dealer.archived;
      }
    });
  }
}
