.add-shop {
  width: 200px;

  button {
    display: flex;
    flex-direction: row-reverse;
    height: 100%;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    scroll-margin-bottom: 100px;

    img {
      margin-right: 10px;
    }
  }
}

.shop-group {
  margin-bottom: 15px;
}

.delete-shop {
  margin-top: 13px;

  &:hover {
    cursor: pointer;
  }
}

::ng-deep .pipeline-config {
  .p-sidebar-content .content {
    padding-bottom: 50px;
  }

  .p-timeline-event-opposite {
    display: none;
  }

  .p-timeline {
    margin-top: 24px;
  }

  .p-timeline-event-content {
    display: none;
  }

  .p-timeline-event-separator {
    span {
      border: 2px solid var(--active-color);
      background-color: var(--active-color);
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      font-weight: bold;
      padding: 10px;
    }
  }

  .p-timeline .p-timeline-event-connector {
    background-color: var(--active-color);
  }

  .p-timeline.p-timeline-vertical .p-timeline-event-connector {
    width: 4px;
    height: 49px;
  }

  p-table {
    width: 100%;
  }
}

.delete-shop {
  margin-top: 0;
}

.pi-bars {
  margin-top: 1px;
}

.pipeline-col-width {
  width: 42%;
}
.header-title {
  top: -8px;
  position: relative;
}
.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
  position: absolute;
  top: 30px;
}
.bold-text {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

.modal-footer {
  width: -webkit-fill-available !important;
}
