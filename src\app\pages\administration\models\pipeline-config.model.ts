import { GenericFilterParams, IdNameModel } from "src/app/@shared/models";

export class PipelineConfigListFilter extends GenericFilterParams {
  deleted = false;
}

export class PipelineConfigListItem {
  id!: number;
  title!: string;
  dealerName!: string;
  dealerId!: number;
  templatePipelinePhases: TemplatePipelinePhase[] = [];
  createdBy!: IdNameModel;
  createdDate!: string
}


export class PipelineConfigCreateParam {
  id?: number;
  dealerId!: string;
  title!: string;
  templatePipelinePhases: TemplatePipelinePhase[] = [];
}

export class TemplatePipelinePhase {
  id!: number;
  name!: string;
  assigneeName!: string;
  sequenceNumber!: number;
  templatePipelineId!: number;
  assigneeId!: number;
  shopId!: number;
  shop!: IdNameModel;
}
