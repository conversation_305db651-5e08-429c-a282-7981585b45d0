<div class="modal-title close">
  <div class="model-content">
    <h4 class="header-title">Duplicate Inventory</h4>
  </div>
  <fa-icon [icon]="faIcons.faTimes" (click)="onModalClose()"></fa-icon>
</div>

<div class="inventory-matching-wrapper">
  <form [formGroup]="duplicateInventoryFormGroup" (ngSubmit)="onSubmit()">
    <div class="row">
      <div class="col-6">
        <div class="card primary-inventories">
          <div class="title">
            <h4>Source Inventory</h4>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-6">
                <img [src]="getPrimaryInventoryImage()" alt="" />
              </div>
              <div class="col-6">
                <div class="model-name">
                  {{ inventoryInfo?.generalInformation?.year }}
                  {{ inventoryInfo?.generalInformation?.make?.name }}
                  {{ inventoryInfo?.generalInformation?.unitModel?.name }}
                </div>
                <div class="model-details">
                  <div>
                    <ng-container [ngTemplateOutlet]="modelDetails" [ngTemplateOutletContext]="{ generalInfo: inventoryInfo?.generalInformation?.stockNumber, label: 'Stock#' }"> </ng-container>
                  </div>
                  <div>
                    <ng-container [ngTemplateOutlet]="modelDetails" [ngTemplateOutletContext]="{ generalInfo: inventoryInfo?.generalInformation?.vin, label: 'Vin' }"> </ng-container>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-6">
        <div class="card">
          <div class="title">
            <h4>Duplication Options</h4>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-12">
                <p-dropdown
                  appendTo="body"
                  [options]="inventoryStatuses"
                  formControlName="inventoryStatusId"
                  optionLabel="name"
                  [showClear]="true"
                  optionValue="id"
                  placeholder="Select Account Reporter"
                >
                </p-dropdown>
              </div>
              <div class="col-12">
                <p-checkbox name="groupname" class="m-r-9" value="specification" formControlName="specification" label="Specification" [binary]="true"></p-checkbox>
                <p-checkbox name="groupname" class="m-r-9" value="photos" formControlName="photos" label="Photos" [binary]="true"></p-checkbox>
                <p-checkbox name="groupname" value="condition" formControlName="condition" label="Condition" [binary]="true"> </p-checkbox>
                <p-checkbox name="groupname" value="notes" formControlName="notes" label="Notes" [binary]="true"> </p-checkbox>
                <p-checkbox name="groupname" value="documents" formControlName="documents" label="Documents" [binary]="true"> </p-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="card">
          <div class="title">
            <h4>Units</h4>
          </div>
          <div class="card-body">
            <div class="add-duplicate">
              <button type="button" class="btn btn-primary left" (click)="addNewInventory()" [appImageIconSrc]="constants.staticImages.icons.addNew">
                <span>Add new unit</span>
              </button>
            </div>
            <ng-container *ngIf="clonedInventoryFormArray?.controls">
              <p-table [value]="clonedInventoryFormArray.controls" responsiveLayout="scroll" class="no-column-selection" pDroppable="products">
                <ng-template pTemplate="header">
                  <tr>
                    <th scope="col" class="required">Stock</th>
                    <th scope="col">Vin</th>
                    <th scope="col">Odometer</th>
                    <th scope="col">Hours</th>
                    <th scope="col">Retail</th>
                    <th scope="col">Wholesale</th>
                    <th scope="col" class="contact-vendor">Contact/Vendor/Supplier</th>
                    <th scope="col">Actions</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
                  <ng-container formArrayName="inventories">
                    <tr [formGroupName]="rowIndex">
                      <td>
                        <p-cellEditor>
                          <ng-template pTemplate="output">
                            <span class="search-input">
                              <input type="text" class="form-control form-control-sm" formControlName="stock" />
                              <app-error-messages [control]="getCurrentClonedInventoryFormGroup(rowIndex).controls.stock"> </app-error-messages>
                            </span>
                          </ng-template>
                        </p-cellEditor>
                      </td>
                      <td>
                        <p-cellEditor>
                          <ng-template pTemplate="output">
                            <span class="search-input">
                              <input type="text" class="form-control form-control-sm" formControlName="vin" />
                            </span>
                          </ng-template>
                        </p-cellEditor>
                      </td>
                      <td>
                        <p-cellEditor>
                          <ng-template pTemplate="output">
                            <span class="search-input">
                              <input type="text" class="form-control form-control-sm" formControlName="odometer" />
                            </span>
                          </ng-template>
                        </p-cellEditor>
                      </td>
                      <td>
                        <p-cellEditor>
                          <ng-template pTemplate="output">
                            <span class="search-input">
                              <input type="text" class="form-control form-control-sm" formControlName="hours" />
                            </span>
                          </ng-template>
                        </p-cellEditor>
                      </td>
                      <td>
                        <p-cellEditor>
                          <ng-template pTemplate="output">
                            <span class="search-input">
                              <input type="text" class="form-control form-control-sm" formControlName="retail" />
                            </span>
                          </ng-template>
                        </p-cellEditor>
                      </td>
                      <td>
                        <p-cellEditor>
                          <ng-template pTemplate="output">
                            <span class="search-input">
                              <input type="text" class="form-control form-control-sm" formControlName="wholesale" />
                            </span>
                          </ng-template>
                        </p-cellEditor>
                      </td>
                      <td class="contact-vendor">
                        <p-cellEditor>
                          <ng-template pTemplate="output">
                            <span class="search-input">
                              <p-dropdown
                                appendTo="body"
                                [options]="vendors"
                                formControlName="previousOwnerContactId"
                                optionLabel="name"
                                [showClear]="true"
                                optionValue="id"
                                [filter]="true"
                                filterBy="name"
                                [virtualScroll]="true"
                                [itemSize]="30"
                                placeholder="Select"
                                (onChange)="displayContactDetails($event, rowIndex)"
                              >
                                <ng-template pTemplate="empty">
                                  <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.previousOwnerName, data: vendors }"> </ng-container>
                                </ng-template>
                                <ng-template let-item pTemplate="item">
                                  <span>{{ item.name }}</span>
                                </ng-template>
                              </p-dropdown>
                            </span>
                          </ng-template>
                        </p-cellEditor>
                      </td>
                      <td>
                        <div class="d-flex justify-content-center">
                          <img [src]="constants.staticImages.icons.deleteIcon" alt="" [pTooltip]="'Delete'" tooltipPosition="left" (click)="onDelete(rowIndex, $event)" />
                        </div>
                      </td>
                    </tr>
                  </ng-container>
                </ng-template>
              </p-table>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary" type="button" (click)="onModalClose()">Cancel</button>
      <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
    </div>
  </form>
</div>

<ng-template #modelDetails let-generalInfo="generalInfo" let-label="label">
  <span class="model-detail-label"> {{ label }} : </span>
  <span class="model-detail-info">
    {{ generalInfo ? generalInfo : "NA" }}
  </span>
</ng-template>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
