import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { ROUTER_UTILS } from "@core/utils";

@Injectable({ providedIn: 'root' })
export class NavigationService {
  constructor(private readonly router: Router) { }

  toDashboard() {
    this.router.navigate([ROUTER_UTILS.config.base.root, ROUTER_UTILS.config.base.dashboard]);
  }

  toProfile() {
    this.router.navigate([ROUTER_UTILS.config.base.root, ROUTER_UTILS.config.settings.root, ROUTER_UTILS.config.settings.account]);
  }

  toChangePassword() {
    this.router.navigate([ROUTER_UTILS.config.base.root, ROUTER_UTILS.config.settings.root, ROUTER_UTILS.config.settings.changePassword]);
  }

  toAdministrationUsers() {
    this.router.navigate([ROUTER_UTILS.config.base.root, ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.users]);
  }

  toAdministrationDealers() {
    this.router.navigate([ROUTER_UTILS.config.base.root, ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.dealers]);
  }

  toAdministrationPipelineConfig() {
    this.router.navigate([ROUTER_UTILS.config.base.root, ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.pipelineConfig]);
  }

  toAdministrationVendors() {
    this.router.navigate([ROUTER_UTILS.config.base.root, ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.vendors]);
  }
}
