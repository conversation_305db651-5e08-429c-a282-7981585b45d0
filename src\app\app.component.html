<app-splash-screen></app-splash-screen>
<ng-container *ngIf="!isPublicPage; else publicPage">
  <app-authorized-layout *ngIf="isLoggedIn$ | async; else notAuthorized">
    <router-outlet></router-outlet>
  </app-authorized-layout>

  <ng-template #notAuthorized>
    <app-layout>
      <router-outlet></router-outlet>
    </app-layout>
  </ng-template>
</ng-container>

<ng-template #publicPage>
  <router-outlet></router-outlet>
</ng-template>
<p-toast [baseZIndex]="5000" position="bottom-right" [preventOpenDuplicates]="true"></p-toast>

<p-blockUI [blocked]="isUIBlocked">
  <em class="pi pi-spin pi-spinner" style="font-size: 3rem; color: var(--active-color)"></em>
  <div class="spinner-message">{{ message }}</div>
</p-blockUI>
