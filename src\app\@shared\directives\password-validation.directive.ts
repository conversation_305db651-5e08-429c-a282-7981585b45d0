import { Directive, ElementRef, HostListener, Renderer2 } from '@angular/core';

@Directive({
  selector: '[appPasswordValidation]'
})
export class PasswordValidationDirective {
  constructor(private el: ElementRef, private renderer: Renderer2) { }

  @HostListener('input', ['$event'])
  onInputChange(event: Event): void {
    const inputElement = this.el.nativeElement as HTMLInputElement;

    // Remove spaces from the input value
    inputElement.value = inputElement.value.replace(/\s/g, '');  // Regex to remove all spaces
  }
  // Listen for keydown events to prevent space character
  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    const inputElement = this.el.nativeElement as HTMLInputElement;
    if (event.key === ' ') {
      inputElement.setCustomValidity('Password cannot contain spaces');
      inputElement.reportValidity();
      event.preventDefault();  // Prevent the space key from being typed
    } else {
      inputElement.setCustomValidity('');
    }
  }
}