import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { BaseComponent } from '@core/utils';
import { AssociationsUnits, ExpensesListFilter, FinancialInformation, InventoryListItem } from '@pages/inventory/models';
import { InventoryAssociationsService } from '@pages/inventory/services';
import { FinancialService } from '@pages/inventory/services/financial.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { takeUntil } from 'rxjs';
import { FinancialComponent } from '../financial/financial.component';

@Component({
  selector: 'app-financial-wrapper',
  templateUrl: './financial-wrapper.component.html',
  styleUrls: ['./financial-wrapper.component.scss']
})
export class FinancialWrapperComponent extends BaseComponent implements OnInit {
  @Input() filterParams: ExpensesListFilter = new ExpensesListFilter();
  @Input() salePrice!: number;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() isViewMode!: boolean;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Output() isAssociateUnitRetailPrice = new EventEmitter<boolean>(false);

  @ViewChild(FinancialComponent) financialComponent!: FinancialComponent;
  financialInformationResponse: FinancialInformation[] = [];
  financialInformation!: FinancialInformation;
  associatedUnits: Array<AssociationsUnits> = [];
  activeAssociatedUnit!: AssociationsUnits;
  activeIndex = 0;
  constructor(
    private readonly inventoryAssociationsService: InventoryAssociationsService,
    private readonly financialService: FinancialService,
    private readonly cdf: ChangeDetectorRef

  ) {
    super();
    this.paginationConfig.itemsPerPageOptions = [5, 10, 25, 50, 100];
    this.paginationConfig.itemsPerPage = 5;
  }

  ngOnInit(): void {
    this.getAssociatedUnits();
  }

  getAssociatedUnits(): void {
    let unitAssociationId = 0;
    if (this.inventoryInfo && (this.inventoryInfo?.unitAssociationId || this.inventoryInfo?.unitAssociation)) {
      unitAssociationId = Number(this.inventoryInfo.unitAssociationId) || Number(this.inventoryInfo?.unitAssociation !== undefined ? this.inventoryInfo?.unitAssociation?.id : 0);
    }
    else {
      unitAssociationId = this.inventoryIncomingInfo?.unit.unitAssociation.id ?? 0;
    }
    this.inventoryAssociationsService.get<Array<AssociationsUnits>>(unitAssociationId).pipe(takeUntil(this.destroy$)).subscribe(
      {
        next: (res: Array<AssociationsUnits>) => {
          this.associatedUnits = res
          if (this.associatedUnits.length) {
            this.movePrimaryInventroyToFirstPosition();
          }
          this.getAllUnitIdsFinancial();
        }
      }
    )
  }

  movePrimaryInventroyToFirstPosition(): void {
    const id: number = Number(this.inventoryInfo?.generalInformation?.id || this.inventoryIncomingInfo?.unit?.generalInformation?.id)
    const index = this.associatedUnits.findIndex(unit => unit.id === id);
    if (index > -1) {
      const [unit] = this.associatedUnits.splice(index, 1);
      this.associatedUnits.unshift(unit);
    }
  }

  getAllUnitIdsFinancial(): void {
    this.isLoading = true;
    const unitIds = this.associatedUnits.length ? this.associatedUnits.map(item => item.id) : [Number(this.inventoryInfo?.generalInformation?.id || this.inventoryIncomingInfo?.unit?.generalInformation?.id)]
    this.financialService.geUnitIdByFinancialInfo(unitIds).pipe(takeUntil(this.destroy$)).subscribe(financialInformation => {
      this.financialInformationResponse = this.getTotalInvenstmentAmount(financialInformation);
      this.isLoading = false;
      this.cdf.detectChanges();
    });
  }

  getActiveFinancialInformation(unitId: number): void {
    const financialInformation = this.financialInformationResponse.find((item: any) => item.unitId === unitId);
    if (financialInformation) {
      this.financialInformation = financialInformation;
    }
  }

  onFincancialSubmit(): void {
    if (this.financialComponent?.financialFormGroup?.invalid) {
      return;
    }
    this.financialComponent?.onSubmit();
  }

  getTotalInvenstmentAmount(financialInformation: FinancialInformation[]): FinancialInformation[] {
    let totalAmount = 0
    let retailAskingPrice = false;
    financialInformation.forEach(financialInfo => {
      const unitId = Number(this.inventoryInfo?.generalInformation?.id || this.inventoryIncomingInfo?.unit?.generalInformation?.id)
      totalAmount += financialInfo.acquisitionCost ? financialInfo.acquisitionCost : 0;
      totalAmount += financialInfo.actualExpenses ? financialInfo.actualExpenses : 0;
      if (financialInformation.length > 1 && Number(financialInfo.retailAskingPrice) === 0) {
        if (financialInfo.unitId !== unitId) {
          retailAskingPrice = true;
        }
      }
      this.isAssociateUnitRetailPrice.emit(retailAskingPrice)
    })
    financialInformation.forEach((financialInfo: FinancialInformation) => {
      financialInfo.grandTotal = totalAmount;
    })
    return financialInformation;
  }

  handleExpensesPopupClosed(): void {
    this.getAllUnitIdsFinancial();
  }
}
