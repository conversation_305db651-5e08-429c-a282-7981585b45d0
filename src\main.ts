import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { environment } from './environments/environment';


if (environment.production) {
  enableProdMode();
}

if (environment.sentryKey) {
  // Sentry.init({
  //   dsn: environment.sentryKey,
  //   integrations: [
  //     // Registers and configures the Tracing integration,
  //     // which automatically instruments application to monitor its
  //     // performance, including custom Angular routing instrumentation
  //     new Integrations.BrowserTracing({
  //       tracingOrigins: Constants.environmentsForErrorTracing,
  //       // routingInstrumentation: Sentry.routingInstrumentation,
  //     }),
  //   ],

  //   // Set tracesSampleRate to 1.0 to capture 100%
  //   // of transactions for performance monitoring.
  //   // values range from 0 to 1, 0.2 signifies 20% of transactions will be captured.
  //   tracesSampleRate: 1.0,
  // });
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
