import { DatePipe } from "@angular/common";
import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { QuotationUnitDTOS } from '@pages/crm/models/customer-inventory.model';
import { BehaviorSubject, Observable } from "rxjs";
import { IdNameModel } from "src/app/@shared/models";
import { DummyService } from "src/app/@shared/services";

@Injectable({ providedIn: 'root' })
export class InventoryService extends BaseCrudService {

  constructor(private readonly datePipe: DatePipe, protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  unitId: BehaviorSubject<number> = new BehaviorSubject(Number(null));
  associatedId: BehaviorSubject<number> = new BehaviorSubject(Number(null));
  associatedUnitId: BehaviorSubject<number> = new BehaviorSubject(Number(null));

  public imageUploaded: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public contactFinancialLoaded: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public contactGeneralLoaded: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);


  getBaseAPIPath(): string {
    return API_URL_UTIL.inventory.root;
  }
  getDummyValues(phrase: string, size = 10): IdNameModel[] {
    const dataGeneratorFunction = (): IdNameModel => {
      return { id: DummyService.randomNumber(2), name: `${phrase} ${DummyService.randomString(2)}` };
    };
    return DummyService.randomDataList<IdNameModel>(dataGeneratorFunction, size);
  }

  getTaskPriorities(): Observable<{}> {
    return this.httpClient.get<{}>(`${this.getFullAPIUrl()}/${API_URL_UTIL.inventory.unitType}`);
  }

  setUnitId$(unitId: number) {
    if (unitId) {
      this.unitId.next(unitId);
    }
  }

  setImageUploaded(isImageUploaded: boolean) {
    this.imageUploaded.next(isImageUploaded);
  }

  getUnitId$(): Observable<number> {
    return this.unitId.asObservable()
  }

  setAssociateId$(associatedId: number) {
    if (associatedId) {
      this.associatedId.next(associatedId);
    }
  }

  setAssociatedUnitId$(unitId: number) {
    this.associatedUnitId.next(unitId);
  }

  getAssociateId$(): Observable<number> {
    return this.associatedId.asObservable();
  }

  deletePhotoAttachment(attachmentId: number): Observable<void> {
    return this.httpClient.delete<void>(`${this.getFullAPIUrl()}/${API_URL_UTIL.inventory.images}/${attachmentId}`);
  }

  bulkDelete(body: Array<number>): Observable<any> {
    return this.httpClient.delete(`${this.getFullAPIUrl()}`, { body });
  }


  getCategoryType(): Observable<IdNameModel[]> {
    return this.httpClient.get<IdNameModel[]>(`${API_URL_UTIL.inventory.categoryType}`);
  }

  getCategoryByMakeList(categoryId: number | string): Observable<IdNameModel[]> {
    return this.httpClient.get<IdNameModel[]>(`${API_URL_UTIL.inventory.categoryByMakeList}/${categoryId}`);
  }

  downloadPdf(resource: QuotationUnitDTOS): Observable<any> {
    return this.httpClient.post(`${API_URL_UTIL.inventory.pdfGenerator}`, this.toServerModel(resource), {
      responseType: "arraybuffer"
    });
  }

  setContactFinancialLoaded(isContactLoaded: boolean) {
    this.contactFinancialLoaded.next(isContactLoaded);
  }
  setContactGeneralLoaded(isContactLoaded: boolean) {
    this.contactGeneralLoaded.next(isContactLoaded);
  }
}
