@import "src/assets/scss/variables";

.user-img {
  width: 110px;
  height: 110px;
  border-radius: 50%;
}

.card.user-info {
  width: 30%;
  height: max-content;

  .image {
    display: flex;
    justify-content: center;
    padding: 3em 3em 1.5em;

    span {
      width: 100px;
      height: 100px;
      border: 2px solid $placeholder-color;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30px;
    }
  }

  .name {
    text-align: center;
    color: var(--text-color);
  }

  .content {
    padding: 10px 25px;
    text-align: center;

    fa-icon {
      margin-right: 8px;
      color: $placeholder-color;
    }

    span {
      color: var(--text-color);
    }
  }
}

.wrapper {
  display: flex;
}

::ng-deep .account {
  margin-left: 20px;
  width: 70%;
  padding: 15px 30px;

  .nav-item {
    margin: 0 10px;

    &:first-of-type {
      margin-left: 0;

      a {
        padding-left: 0;
      }
    }
  }

  .nav-link {
    padding: 10px;
  }

  .password-eye {
    top: 29px !important;
    right: 10px !important;
  }

  .wrapper {
    margin: 20px 0 10px;
  }

  .nav-tabs {
    padding: 0;
  }

  .tab-content {
    padding: 0;
  }
}

@media (max-width: 767px) {
  .wrapper {
    flex-direction: column;
  }

  ::ng-deep .account {
    width: 100%;
    margin-left: 0;

    .nav-item {
      margin: 0 !important;
    }
  }

  .card.user-info {
    width: 100%;
    margin-bottom: 20px;
  }
}

@media (max-width: 375px) {
  ::ng-deep .account {
    .nav-link {
      padding-left: 0 !important;
    }
  }
}
