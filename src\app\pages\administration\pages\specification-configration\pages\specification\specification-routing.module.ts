import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { SpecificationComponent } from "./specification.component";

const routes: Routes = [
    {
      path: '',
      component: SpecificationComponent,
      title: 'Skeye - Specification Category',
      children: [
        {
          path: '',
          component: SpecificationComponent,
          pathMatch: 'full'
        }
      ]
    }
  ];

  @NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
  })

  export class SpecificationRoutingModule { }
