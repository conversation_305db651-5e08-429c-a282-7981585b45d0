<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form [formGroup]="modeFormGroup" (ngSubmit)="onSubmit()" [ngClass]="{ loading: isLoading }">
  <div class="content" *ngIf="!isLoading; else loaderTemplate">
    <section>
      <div class="row">
        <div class="col-12" *ngIf="makes.length">
          <label class="required">Make</label>
          <p-dropdown appendTo="body" [options]="makes" formControlName="makeId" optionLabel="name" [showClear]="true" optionValue="id" [filter]="true" filterBy="name" placeholder="Select Make">
          </p-dropdown>
          <app-error-messages [control]="modeFormGroup.controls.makeId"></app-error-messages>
        </div>
        <div class="col-12">
          <label class="required">Model</label>
          <input class="form-control" type="text" placeholder="Enter Model" formControlName="name" />
          <app-error-messages [control]="modeFormGroup.controls.name"></app-error-messages>
        </div>
      </div>
    </section>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>
