import { getItem, StorageItem } from "@core/utils";
import { LoginResponse } from "@pages/auth/models";
import { firstValueFrom } from "rxjs";
import { RefreshTokenService } from "./refresh-token.service";

export function refreshTokenInitializer(refreshTokenService: RefreshTokenService) {
  return async (): Promise<LoginResponse | null> => {
    if (!getItem(StorageItem.AuthToken)) {
      return Promise.resolve(null)
    }
    const refreshTokenResponse = await firstValueFrom(refreshTokenService.refreshToken()).catch(err => {
      // do nothing here. it is added just to catch
    });
    return refreshTokenResponse || Promise.resolve(null);
  };
}
