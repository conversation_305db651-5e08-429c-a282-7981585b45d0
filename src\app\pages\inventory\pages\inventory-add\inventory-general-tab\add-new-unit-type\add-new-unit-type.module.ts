import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService } from 'primeng/api';
import { DropdownModule } from 'primeng/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { AddNewUnitTypeComponent } from './add-new-unit-type.component';
@NgModule({
  declarations: [AddNewUnitTypeComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeIconsModule,
    AccordionModule,
    DropdownModule,
    SidebarModule,
  ],
  providers: [ConfirmationService],
  exports: [AddNewUnitTypeComponent]

})
export class AddNewUnitTypeModule {}
