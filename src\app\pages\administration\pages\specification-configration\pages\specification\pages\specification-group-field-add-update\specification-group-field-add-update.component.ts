import { <PERSON>Case<PERSON>ipe } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES, specificationDetails } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { DataTypeOptions, UnitTypeCategory } from '@pages/administration/pages/specification-configration/models/specification.model';
import { ColumnItem } from '@pages/common-table-column/models/common-table.column.model';
import { InventorySpecification, InventorySpecificationFields, InventorySpecificationResponse } from '@pages/inventory/models';
import { takeUntil } from 'rxjs';
import { SpecificationService } from '../../specification.service';

@Component({
  selector: 'app-specification-group-field-add-update',
  templateUrl: './specification-group-field-add-update.component.html',
  styleUrls: ['./specification-group-field-add-update.component.scss']
})
export class SpecificationGroupFieldAddUpdateComponent extends BaseComponent implements OnInit, OnChanges {

  @Input() categories!: Array<UnitTypeCategory>;
  @Input() selectedCategory!: number;
  @Input() isEditMode!: boolean;
  @Input() specificationList!: InventorySpecificationResponse;
  @Input() groupAndFieldNames: string[] = [];
  @Input() selectedSpecificationFields!: InventorySpecificationFields;
  @Input() selectedSpecificationGroup!: InventorySpecification;
  @Input() specificationDropDownColumnList: ColumnItem[] = [];
  @Output() fieldToBeAddedInColumnMaster = new EventEmitter<{ data: ColumnItem, isEdit: boolean }>();
  specificationGroupFieldFrom!: FormGroup;
  newColumnMasterObject!: ColumnItem;
  isLoading = false;
  dataTypeOptions = DataTypeOptions;
  @Output() closeModal = new EventEmitter<{ dataHasBeenModified: boolean, newName: string }>();
  @Output() addSpecificationGroup = new EventEmitter<InventorySpecificationResponse>();
  constructor(
    private readonly specificationService: SpecificationService,
    private readonly toasterService: AppToasterService,
    private readonly titleCasePipe: TitleCasePipe,
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeForm();
    this.addSpecificationGroupConfiguration();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!changes.isEditMode?.currentValue) {
      this.addSpecificationGroupConfiguration();
    }

    if (changes.isEditMode?.currentValue) {
      this.setPageConfigurations();
    }
  }

  setPageConfigurations() {
    this.pageTitle = 'Edit Specification Group Field';
    this.selectCategoryName();
    this.setSpecificationFieldFormValues();
  }

  setSpecificationFieldFormValues(): void {
    this.specificationGroupFieldFrom.patchValue({
      id: this.selectedSpecificationFields?.id,
      for: this.selectedSpecificationFields?.for,
      label: this.selectedSpecificationFields?.label,
      parentId: this.selectedSpecificationGroup.id,
      dataType: this.selectedSpecificationFields?.dataType,
      options: this.selectedSpecificationFields.options?.map(o => o.name)?.join(","),
      placeholder: this.selectedSpecificationFields?.placeholder,
      isRequired: this.selectedSpecificationFields?.isRequired,
      includePreference: this.selectedSpecificationFields?.includePreference
    });
    this.specificationGroupFieldFrom.get('parentId')?.disable();
  }

  addSpecificationGroupConfiguration(): void {
    this.selectCategoryName();
    this.pageTitle = 'Add Specification Group Field';
  }

  initializeForm(): void {
    this.specificationGroupFieldFrom = new FormGroup({
      label: new FormControl('', Validators.required),
      categoryName: new FormControl(null, Validators.required),
      dataType: new FormControl(null, Validators.required),
      placeholder: new FormControl(null),
      options: new FormControl(null),
      includePreference: new FormControl(false),
      isRequired: new FormControl(false),
      parentId: new FormControl(null, Validators.required),
      id: new FormControl(null),
      for: new FormControl(null)
    });
  }

  selectCategoryName(): void {
    if (this.selectedCategory) {
      const selectedCategoryName = this.categories?.find((category) => category.id === this.selectedCategory)?.name
      this.specificationGroupFieldFrom.get('categoryName')?.setValue(selectedCategoryName);
      this.specificationGroupFieldFrom.get('categoryName')?.disable();
      this.specificationGroupFieldFrom.get('parentId')?.enable();
    }
  }

  onDataTypeChange(dropDownOption: { value: string }): void {
    const label = this.specificationGroupFieldFrom.controls.label.value;
    if (dropDownOption.value === this.dataTypeOptions[0]) {
      this.specificationGroupFieldFrom.controls.options.setValidators([Validators.required]);
      this.specificationGroupFieldFrom.controls.options.updateValueAndValidity();
      if (label) {
        this.specificationGroupFieldFrom.controls.placeholder.setValue("Select ".concat(label))
      }
      return;
    }
    this.specificationGroupFieldFrom.get('options')?.setValue(null);
    this.specificationGroupFieldFrom.controls.options.setValidators(null);
    this.specificationGroupFieldFrom.controls.options.updateValueAndValidity();
    if (label) {
      this.specificationGroupFieldFrom.controls.placeholder.setValue("Enter ".concat(label))
    }
  }

  onLabelChange(): void {
    if (this.specificationGroupFieldFrom.controls.label.value) {
      if (this.specificationGroupFieldFrom.get('dataType')?.value) {
        this.specificationGroupFieldFrom.controls.placeholder.setValue(
          `${this.specificationGroupFieldFrom.get('dataType')?.value === this.dataTypeOptions[0] ? 'Select ' : 'Enter '}`.concat(this.specificationGroupFieldFrom.controls.label.value)
        )
      }
    } else {
      this.specificationGroupFieldFrom.controls.placeholder.setValue(null)
    }
  }

  onModalClose(dataHasBeenModified = false): void {
    this.closeModal.emit({ dataHasBeenModified, newName: this.specificationGroupFieldFrom.get('label')?.value?.toLowerCase() ?? '' });
    this.specificationGroupFieldFrom.controls.options.setValidators(null);
    this.specificationGroupFieldFrom.controls.options.updateValueAndValidity();
    this.specificationGroupFieldFrom.reset();
  }

  onSubmit(): void {
    if (this.specificationGroupFieldFrom.invalid) {
      this.specificationGroupFieldFrom.markAllAsTouched();
      return;
    }
    const newLabel = this.specificationGroupFieldFrom.get('label')?.value.toLowerCase();
    if (this.groupAndFieldNames.includes(newLabel)) {
      this.toasterService.error(MESSAGES.labelWithSameNameExists)
      return;
    }
    if (this.isEditMode) {
      this.updateGroupField();
    } else {
      this.addNewGroupField();
    }
  }

  private isTextField(): boolean {
    return this.specificationGroupFieldFrom.get('dataType')?.value === DataTypeOptions[2] || this.specificationGroupFieldFrom.get('dataType')?.value === DataTypeOptions[3]
  }

  addNewGroupField(): void {
    const specificationGroupIndex = this.specificationList.masterData.specification.findIndex(
      (specificationData) => {
        return specificationData.id === this.specificationGroupFieldFrom.get('parentId')?.value
      }
    );

    const lastFieldIndexOfSelectedSpecificationGroup = this.specificationList.masterData.specification[specificationGroupIndex]?.fields?.length - 1;
    this.specificationList.masterData.specification[specificationGroupIndex].fields.push({
      for: this.specificationGroupFieldFrom.get('label')?.value,
      label: this.specificationGroupFieldFrom.get('label')?.value,
      value: this.isTextField() ? "" : null,
      dataType: this.specificationGroupFieldFrom.get('dataType')?.value,
      order: !this.specificationList.masterData.specification[specificationGroupIndex]?.fields?.length ? 1 :
        this.specificationList.masterData.specification[specificationGroupIndex]?.fields[lastFieldIndexOfSelectedSpecificationGroup].order + 1,
      isRequired: this.specificationGroupFieldFrom.get('isRequired')?.value,
      placeholder: this.specificationGroupFieldFrom.get('placeholder')?.value,
      options: this.specificationGroupFieldFrom.get('options')?.value ?
        ([...new Set(this.specificationGroupFieldFrom.get('options')?.value?.split(','))] as string[])
          .map((optionItem: string, index: number) => {
            return { name: this.titleCasePipe.transform(optionItem), id: index + 1 };
          }) : [],
      id: !this.specificationList.masterData.specification[specificationGroupIndex]?.fields?.length ? 1 :
        this.specificationList.masterData.specification[specificationGroupIndex]?.fields[lastFieldIndexOfSelectedSpecificationGroup].id + 1,
      includePreference: this.specificationGroupFieldFrom.get('includePreference')?.value
    });
    const groupId = this.specificationGroupFieldFrom.get('parentId')?.value;
    const groupName = this.specificationList.masterData.specification.find(group => group.id === groupId)?.sectionName;
    const key = `${specificationDetails}.${groupName}.${this.specificationGroupFieldFrom.get('label')?.value}`
    this.newColumnMasterObject = {
      default: false,
      disable: false,
      id: null,
      key: key,
      name: this.specificationGroupFieldFrom.get('label')?.value,
      shorting: false,
      shortingKey: null,
      type: this.columnDataType,
      value: null,
      parentIndex: specificationGroupIndex,
      childIndex: lastFieldIndexOfSelectedSpecificationGroup + 1,
      isSpecificationField: true
    }
    this.manageFieldCount();
    this.updateSpecification();
  }

  get columnDataType(): string {
    if (this.specificationGroupFieldFrom.get('dataType')?.value === this.dataTypeOptions[0]) {
      return 'MULTI_DROP_DOWN';
    } else if (this.specificationGroupFieldFrom.get('dataType')?.value === this.dataTypeOptions[1]) {
      return 'INTEGER';
    } else {
      return 'STRING';
    }
  }

  manageFieldCount() {
    let count = 0
    this.specificationList.masterData.specification.forEach(s => {
      count = count + s.fields.length
    });
    this.specificationList.fieldCount = count;
  }

  updateGroupField(): void {
    const specificationGroupIndex = this.specificationList.masterData.specification.findIndex(
      (specificationData) => {
        return specificationData.id === this.specificationGroupFieldFrom.get('parentId')?.value
      }
    );

    const fieldIndex = this.specificationList.masterData.specification[specificationGroupIndex]?.fields?.findIndex(
      (field) => {
        return field.id === this.selectedSpecificationFields.id
      }
    );

    const field = this.specificationList.masterData.specification[specificationGroupIndex]?.fields[fieldIndex];
    field.for = this.specificationGroupFieldFrom.get('label')?.value;
    field.label = this.specificationGroupFieldFrom.get('label')?.value;
    field.value = this.isTextField() ? "" : null;
    field.dataType = this.specificationGroupFieldFrom.get('dataType')?.value;
    field.order = this.specificationGroupFieldFrom.get('order')?.value;
    field.isRequired = this.specificationGroupFieldFrom.get('isRequired')?.value;
    field.placeholder = this.specificationGroupFieldFrom.get('placeholder')?.value;
    field.options = this.specificationGroupFieldFrom.get('options')?.value ?
      ([...new Set(this.specificationGroupFieldFrom.get('options')?.value?.split(','))] as string[])
        .map((optionItem: string, index: number) => {
          return { name: this.titleCasePipe.transform(optionItem), id: index + 1 };
        }) : [];
    field.id = this.specificationGroupFieldFrom.get('id')?.value;
    field.includePreference = this.specificationGroupFieldFrom.get('includePreference')?.value;
    const groupId = this.specificationGroupFieldFrom.get('parentId')?.value;
    const groupName = this.specificationList.masterData.specification.find(group => group.id === groupId)?.sectionName;
    const key = `${specificationDetails}.${groupName}.${this.specificationGroupFieldFrom.get('label')?.value}`
    this.newColumnMasterObject = {
      default: false,
      disable: false,
      id: null,
      key,
      name: this.specificationGroupFieldFrom.get('label')?.value,
      shorting: false,
      shortingKey: null,
      type: this.columnDataType,
      value: null,
      parentIndex: specificationGroupIndex,
      childIndex: fieldIndex,
      isSpecificationField: true
    }
    this.updateSpecification();
  }

  updateSpecification(): void {
    this.isLoading = true;
    this.specificationService.update<InventorySpecificationResponse>(this.specificationList).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: InventorySpecificationResponse) => {
        this.addSpecificationGroup.emit(res);
        this.fieldToBeAddedInColumnMaster.emit({ data: this.newColumnMasterObject, isEdit: this.isEditMode });
        this.isEditMode ?
          this.toasterService.success(MESSAGES.updateSuccessMessage.replace('{item}', 'Specification group field')) :
          this.toasterService.success(MESSAGES.addSuccessMessage.replace('{item}', 'Specification group field'));
        this.onModalClose(true);
        this.isLoading = false;
      }
    });
  }
}
