::ng-deep .pipeline-list {
  .p-timeline-horizontal .p-timeline-event-connector {
    width: 100%;
    min-width: 70px;
    background-color: var(--active-color);
  }

  .p-timeline .p-timeline-event-marker {
    border-color: var(--active-color);
    width: 1rem;
    height: 1rem;
  }

  .p-timeline-event-connector {
    flex-grow: unset;
  }

  .p-timeline-horizontal .p-timeline-event {
    flex: unset;
  }

  .p-timeline.p-timeline-horizontal .p-timeline-event-opposite,
  .p-timeline.p-timeline-horizontal .p-timeline-event-content {
    padding: 0;
  }

  .p-timeline-event-opposite {
    display: none;
  }

  .p-timeline-event-content {
    flex: unset;
    margin-right: 12px;

    span {
      color: var(--text-color);
      font-size: 14px;
      letter-spacing: 0;
      line-height: 21px;
    }
  }

  .p-timeline.p-timeline-horizontal .p-timeline-event-opposite,
  .p-timeline.p-timeline-horizontal .p-timeline-event-content {
    width: max-content;
  }

  .p-timeline-event {
    min-height: unset;
  }

  .p-timeline-event-separator {
    span {
      border: 2px solid var(--active-color);
      background-color: var(--active-color);
      color: white;
      border-radius: 50%;
      width: 1.5rem;
      height: 1.5rem;
      font-size: 0.8rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .p-datatable .p-datatable-tbody > tr > td {
    padding: 0.4rem 1rem;
  }
}
