<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form class="form" [formGroup]="commentFormGroup" [ngClass]="{ loading: isLoading }">
  <div class="row m-0">
    <div class="col-10 m-t-30 m-l-20">
      <p-editor
        appMentionPosition
        #textEditor
        [mention]="availableMentionUsers"
        (itemSelected)="mentionSelected($event)"
        [mentionConfig]="mentionConfig"
        formControlName="message"
        (onTextChange)="textChange($event)"
        (onSelectionChange)="selectionChange($event)"
        (onInit)="selectionChange($event)"
      >
        <ng-template pTemplate="header">
          <span class="ql-formats">
            <button type="button" #boldText class="ql-bold" aria-label="Bold"></button>
            <button type="button" class="ql-italic" aria-label="Italic"></button>
            <button type="button" class="ql-underline" aria-label="Underline"></button>
          </span>
        </ng-template>
      </p-editor>
      <app-error-messages [control]="commentFormGroup.controls.message"></app-error-messages>
    </div>
    <div class="col-1">
      <button class="button button5" (click)="editCommentSubmit()" appShowLoaderOnApiCall>
        <em class="pi pi-reply"></em>
      </button>
    </div>
  </div>
</form>
