<div (clickOutside)="isCollapsed = true" class="nav w-100 fixed-header">
  <nav class="w-100" id="navbar navbar-inverse" role="navigation">
    <div class="navbar-header" id="top-navbar-sm">
      <div class="d-flex justify-content-space-between">
        <div class="d-flex">
          <div>
            <button type="button" class="navbar-toggle collapsed" (click)="isCollapsed = !isCollapsed" aria-expanded="false">
              <fa-icon class="icon" [icon]="faIcons.faBars"></fa-icon>
            </button>
          </div>
          <img [src]="constants.applicationLogoUrl" class="img-fluid navbar-brand skyLogo" [routerLink]="[path.base.dashboard]" alt="Company Logo" />
        </div>
        <div class="d-flex w-100 justify-content-end">
          <div class="d-flex">
            <!-- NOTE: The following modules are currently disabled per client's request. -->
            <!-- <div>
              <a
                class="nav-link cal-icon"
                (click)="isCollapsed = true"
                [routerLink]="rightMenuConfig.routerLink"
                [appImageIconSrc]="constants.staticImages.icons.calender"
              ></a>
            </div> -->
          </div>
          <div class="d-flex">
            <div>
              <a class="nav-link bell-icon mr-25" [appImageIconSrc]="constants.staticImages.icons.bellNotification" (click)="getNotificationCount(); showModal = true"></a>
            </div>
          </div>
          <div>
            <ul class="navbar-nav">
              <li class="nav-item dropdown" dropdown>
                <div class="position-relative" [ngClass]="{ 'pt-24': isIOS }">
                  <a id="profile" dropdownToggle type="button" aria-controls="dropdown-animated" role="button" aria-expanded="false">
                    <div class="initials">
                      {{ utils.getInitials(currentUser?.firstName, currentUser?.lastName) | uppercase }}
                    </div>
                  </a>
                </div>
                <div class="dropdown-menu dropdown-menu-right position-absolute" *dropdownMenu aria-labelledby="system">
                  <a class="dropdown-item">{{ utils.getFullName(currentUser?.firstName, currentUser?.lastName) | titlecase }} </a>
                  <a class="dropdown-item text" href="javascript:;" (click)="navigationService.toProfile()" [appImageIconSrc]="constants.staticImages.icons.loginUser"
                    ><span class="m-l-sm">My Profile</span></a
                  >
                  <a class="dropdown-item text" href="javascript:;" (click)="navigationService.toChangePassword()" [appImageIconSrc]="constants.staticImages.icons.lockPassword">
                    <span class="m-l-sm">Change Password</span></a
                  >
                  <div class="divider"></div>
                  <a class="dropdown-item text" (click)="onClickLogOut()">
                    <span class="m-l-sm">Logout</span>
                    <fa-icon [icon]="faIcons.faSignOutAlt"></fa-icon>
                  </a>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="collapse navbar-collapse" [collapse]="isCollapsed">
      <ul class="nav navbar-nav">
        <ng-container *ngFor="let menu of menuConfig">
          <li *ngIf="!menu.children?.length">
            <a
              (click)="isCollapsed = !isCollapsed"
              class="navbar-nav nav-link"
              [routerLink]="menu.routerLink"
              routerLinkActive="active"
              [appImageIconSrc]="menu.iconPath"
              [hoverImgSrc]="menu.hoverIconPath"
            >
              {{ menu.name }}
            </a>
          </li>
          <li class="nav-item dropdown" dropdown *ngIf="menu.children?.length">
            <a
              class="navbar-nav nav-link dropdown-toggle"
              id="profile"
              dropdownToggle
              type="button"
              role="button"
              aria-expanded="false"
              [appImageIconSrc]="menu.iconPath"
              [hoverImgSrc]="menu.hoverIconPath"
              [ngClass]="{ active: currentActiveParentRoute === dropdownOpts.ADMIN }"
              ><span class="color-white">{{ menu.name }}</span>
            </a>
            <ul *dropdownMenu class="dropdown-menu w-100" role="menu" aria-labelledby="button-animated" id="dropdown-animated">
              <ng-container *ngFor="let childMenu of menu.children">
                <li *ngIf="!childMenu.children?.length">
                  <a
                    (click)="isCollapsed = !isCollapsed"
                    class="dropdown-item navbar-nav nav-link"
                    href="javascript:;"
                    [routerLink]="childMenu.routerLink"
                    routerLinkActive="active"
                    >{{ childMenu.name }}</a
                  >
                </li>
                <li class="nav-item dropdown" dropdown *ngIf="childMenu.children?.length">
                  <a
                    class="navbar-nav nav-link dropdown-item"
                    id="profile"
                    dropdownToggle
                    type="button"
                    role="button"
                    aria-expanded="false"
                    (click)="$event.stopPropagation()"
                    [ngClass]="{ active: currentActiveParentRoute === dropdownOpts.ADMIN }"
                    >{{ childMenu.name }}
                  </a>
                  <ul *dropdownMenu class="dropdown-menu submenu" role="menu" aria-labelledby="button-animated">
                    <li *ngFor="let subChild of childMenu?.children">
                      <a
                        (click)="isCollapsed = !isCollapsed"
                        class="dropdown-item navbar-nav nav-link"
                        href="javascript:;"
                        [routerLink]="subChild.routerLink"
                        routerLinkActive="active"
                        >{{ subChild.name }}
                      </a>
                    </li>
                  </ul>
                </li></ng-container
              >
            </ul>
          </li>
        </ng-container>
      </ul>
    </div>
  </nav>
</div>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showModal"
  position="right"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-notifications
    *ngIf="showModal"
    (decrementUnreadCount)="decrementUnreadCount()"
    [unreadNotificationCount]="unreadNotificationCount"
    (setNotificationUnreadCountToZero)="setNotificationUnreadCountToZero()"
    (onClose)="onCloseClick()"
  >
  </app-notifications>
</p-sidebar>
