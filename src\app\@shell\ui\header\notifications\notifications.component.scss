@import "/src/assets/scss/variables";
@import "/src/assets/scss/theme/mixins";

.notification {
  height: calc(100vh - 120px);
  margin-top: 10px;

  ::ng-deep .p-tabview-panels {
    padding: 1px !important;
  }
}

.subject {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  line-height: 24px;

  .subject-info {
    font-size: 16px;
    font-weight: 600;
    letter-spacing: -0.32px;
    line-height: 19px;
  }

  .close {
    border: none;
    background-color: inherit;
    font-size: 15px;
    display: flex;
    align-content: center;
    justify-content: center;

    span {
      font-size: 35px !important;
      font-weight: 100 !important;
    }
  }
}

.unread-notification {
  border-left: 3px solid #4686ff;
  padding: 16px;
  border-top: none;
  margin-bottom: 1px;
  cursor: pointer;
}

.read-notification {
  padding: 16px;
  border-top: none;
  margin-bottom: 1px;
  cursor: pointer;
}

.read-all {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: -0.28px;
  line-height: 18px;
  margin: 5px;
  display: flex;
  justify-content: flex-end;
}

.notification-loader {
  display: flex;
  justify-content: center;
  align-content: center;
  align-items: center;
  height: calc(100vh - 110px);
}

.menu-actions-wrapper {
  display: flex;
  align-items: center;
  // TODO remove once completed
  // justify-content: center;

  //New css added
  width: 100%;
  justify-content: end;
  margin-right: 2%;

  fa-icon {
    font-size: 20px;
    font-weight: 100;
  }

  .action-separator {
    font-size: 50px;
    font-weight: 100;
    color: lightgray;
    margin: 0 10px;
  }
}

.modal-header {
  font-size: larger;
  font-weight: bold;
}

.badge {
  background-color: var(--active-color);
  position: absolute;
  top: 50%;
  margin-top: -20px;
  margin-left: 150px;
  border-radius: 50%;
  font-size: 11px;
}

.notification-header {
  display: flex;
  align-items: center;

  .unread-count {
    background-color: var(--primary-color);
    color: white !important;
    font-size: 12px !important;
    margin-left: 15px;
    width: 21px;
    height: 23px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

button.close {
  border: none;
  background-color: var(--light-grey-color);
  font-size: 15px;
  display: flex;
  align-content: center;
  justify-content: center;

  span {
    font-size: 50px !important;
    font-weight: 100 !important;
  }
}

.notification-footer {
  font-size: 12px;
  margin-top: 2px;
}

.notification-info-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  .notification-actions {
    display: flex;
    align-items: center;

    fa-icon {
      margin-right: 5px;
      font-size: 17px;
      margin-top: 5px;
    }

    .mark-read {
      height: 20px;
      margin-right: 7px;
      margin-top: 3px;
    }
  }

  .close {
    border: none;
    background-color: inherit;
    font-size: 15px;
    display: flex;
    align-content: center;
    justify-content: center;

    span {
      font-size: 35px !important;
      font-weight: 100 !important;
    }
  }
}

.notification-action-wrapper {
  .close-wrapper {
    display: flex;
    justify-content: end;
  }
}
