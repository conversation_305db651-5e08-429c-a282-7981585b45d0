import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { BannerListItem } from './advertising.model';
import { AdvertisingService } from './advertising.service';

@Component({
  selector: 'app-advertising',
  templateUrl: './advertising.component.html',
  styleUrls: ['./advertising.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdvertisingComponent extends BaseComponent implements OnInit {
  banners: BannerListItem[] = [];
  selectedBanner!: BannerListItem | null;
  openModal = false;
  isEditMode = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.ADVERTISING;
  constructor(
    private readonly advertisingService: AdvertisingService,
    private readonly cdf: ChangeDetectorRef,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService,
    private readonly activeRoute: ActivatedRoute,
  ) {
    super();
    this.pageTitle = 'Advertising Config';
  }

  async ngOnInit(): Promise<void> {
    await this.getAll();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          const selectedBanner = this.banners.find(banner => banner.id === Number(params.id));
          this.onAddEditBanner(selectedBanner)
        }
      })
  }

  onAddEditBanner(banner?: BannerListItem): void {
    if (banner) {
      this.selectedBanner = banner;
      this.isEditMode = true;
    }
    this.openModal = true;
  }

  onDelete(banner: BannerListItem): void {
    if (banner.systemDefault) {
      this.toasterService.warning(MESSAGES.disableDeleteRole.replace('role', 'Banner'));
    } else {
      this.confirmationService.confirm({
        target: event?.target as EventTarget,
        message: MESSAGES.deleteWarning.replace('{record}', 'banner'),
        icon: icons.triangle,
        header: "Confirmation",
        accept: () => {
          this.onDeleteConfirmation(banner);
        }
      });
    }
  }

  private onDeleteConfirmation(banner: BannerListItem): void {
    this.advertisingService.delete(banner?.id).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('{item}', 'Banner'));
        this.getAll();
      }
    });
  }

  closeModal(bool: boolean): void {
    this.openModal = false;
    this.selectedBanner = null;
    this.isEditMode = false;
    this.cdf.detectChanges();
    if (bool) {
      this.getAll();
    }
  }

  async getAll(): Promise<void> {
    this.isLoading = true;
    this.advertisingService.getList<BannerListItem>().pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: BannerListItem[]) => {
        this.banners = res;
        this.isLoading = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.isLoading = false;
        this.cdf.detectChanges();
      }
    })
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }

}
