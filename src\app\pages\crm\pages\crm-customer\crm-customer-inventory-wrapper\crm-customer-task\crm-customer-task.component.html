<app-page-header class="crm-customer-task">
  <div headerActionBtn>
    <button
      class="btn btn-primary left"
      (click)="onAdd()"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      type="button"
      *appHasPermission="[permissionActions.CREATE_SALES_TASK]"
    >
      <span class="show-label">Add New Task</span>
    </button>
  </div>
</app-page-header>

<div class="card tabs crm-task-list">
  <div class="tab-content">
    <p-table
      class="no-column-selection"
      [value]="tasks"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
      [scrollable]="true"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th id="id" pFrozenColumn>#Id</th>
          <th id="stockNumber">#Stock</th>
          <th id="LeadId">Lead Id</th>
          <th id="summary">Summary</th>
          <th id="taskType">Type</th>
          <th class="timeline-header" id="timelineHeader">Deadline</th>
          <th id="reported">Created By</th>
          <th id="taskStatus">Status</th>
          <th scope="col" class="small-col">Active</th>
          <th class="small-col text-center" id="actions" *appHasPermission="[permissionActions.UPDATE_SALES_TASK, permissionActions.DELETE_SALES_TASK]">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td (click)="onViewEdit(rowData, false)" class="view-task" pFrozenColumn>{{ rowData?.id }}</td>
          <td>{{ rowData?.stockNumber }}</td>
          <td>{{ rowData?.customerLeadId }}</td>
          <td>{{ rowData?.summary }}</td>
          <td>{{ rowData?.taskType?.name }}</td>
          <td class="timeline">
            <div class="footer">
              <span class="date"><em class="pi pi-clock"></em>{{ rowData?.endDate | date : constants.monthAndDateFormat }}</span>
            </div>
          </td>
          <td>{{ rowData?.reporter?.name }}</td>
          <td>
            <p-dropdown
              class="w-150 task-list"
              appendTo="body"
              [options]="taskStatuses"
              (click)="findStatusIndex(rowIndex)"
              optionLabel="name"
              [(ngModel)]="rowData.taskStatus.id"
              optionValue="id"
              (onChange)="changeStatus(rowData, rowData?.taskStatus?.id, rowData?.id, $event)"
              [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_SALES_TASK])"
            >
            </p-dropdown>
          </td>
          <td class="actions">
            <div class="actions-content">
              <ui-switch [(ngModel)]="rowData.isActive" [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_SALES_TASK])" [loading]="selectedTask?.id === rowData.id && isArchiveInProgress" (change)="onArchive(rowData, $event)">
                <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedTask?.id === rowData.id"></fa-icon>
              </ui-switch>
            </div>
          </td>
          <td *appHasPermission="[permissionActions.UPDATE_SALES_TASK, permissionActions.DELETE_SALES_TASK]" class="actions">
            <div class="actions-content">
              <img [src]="constants.staticImages.icons.edit" (click)="onViewEdit(rowData, true)" alt="" *appHasPermission="[permissionActions.UPDATE_SALES_TASK]" />
              <em class="pi pi-trash text-danger" appShowLoaderOnApiCall (click)="onDelete(rowData, $event)" *appHasPermission="[permissionActions.DELETE_SALES_TASK]" alt=""></em>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="9" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
  </div>
</div>

<p-sidebar
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false; taskId = ''"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-task-add
    (onClose)="onAddEditPopupClose($event)"
    *ngIf="showCreateModal"
    [taskId]="taskId"
    [isViewMode]="isViewMode"
    [crmCustomerInfoId]="crmCustomerInfoId"
  ></app-crm-task-add>
</p-sidebar>

<p-confirmPopup *ngIf="!showConfirmationDialog" appClickOutside (clickOutside)="onBackdropClick($event)"></p-confirmPopup>
<p-confirmDialog header="Confirmation" styleClass="confirm-dialog" *ngIf="showConfirmationDialog" [breakpoints]="{ '960px': '60vw', '640px': '90vw' }" [style]="{ width: '30vw' }">
</p-confirmDialog>
