.tabs {
  .nav-tabs .nav-link.active,
  .nav-tabs .nav-item.show .nav-link {
    color: var(--active-color) !important;
    font-weight: 500 !important;
    border: none !important;
    background-color: transparent !important;
    border-bottom: 3px solid var(--active-color) !important;
  }

  .nav-link {
    padding: 10px 25px;
    border-radius: 0 !important;
    color: darken($placeholder-color, 10%);
    font-size: 16px;
    font-weight: 500;
    letter-spacing: -0.32px;
    line-height: 25px;
    transition: none;
  }

  .nav-tabs .nav-link:hover,
  .nav-tabs .nav-link:focus {
    border-color: transparent;
    isolation: isolate;
    font-weight: 500;
    color: darken($placeholder-color, 20%);
  }

  .nav-tabs {
    padding: 0 20px;
  }

  .tab-content {
    padding: 13px 20px 7px;
  }
}
.p-tabview-ink-bar {
  display: none !important;
}

@media (max-width: 767px) {
  .tabs .nav-item {
    margin: 0;
  }
}

.p-accordion .p-accordion-header .p-accordion-header-link .p-accordion-toggle-icon {
  display: none;
}

.accordion-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.p-accordion .p-accordion-header:not(.p-disabled) .p-accordion-header-link:focus {
  box-shadow: var(--card-box-shadow);
}

.p-accordion .p-accordion-header .p-accordion-header-link {
  background-color: var(--card-bg-color);
  color: var(--text-color) !important;
  font-size: 17px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 27px;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 6px;
  text-decoration: none;
}

.p-accordion p-accordiontab .p-accordion-tab {
  margin-bottom: 15px;
}

.nested-accordion {
  .p-accordion .p-accordion-header .p-accordion-header-link {
    background-color: var(--card-bg-color) !important;
    font-size: 15px;
    border: none;
    border-radius: 0;
    box-shadow: var(--card-box-shadow);
    text-decoration: none;
  }

  .p-accordion-tab-active .p-accordion-header-link {
    box-shadow: none !important;
    border-bottom: 1px solid var(--table-border-color) !important;
  }

  .p-accordion .p-accordion-content {
    border: none;
    box-shadow: var(--card-box-shadow);
    background-color: var(--card-bg-color);
  }

  .p-accordion p-accordiontab .p-accordion-tab {
    margin-bottom: 10px;
  }
}

.p-progressbar {
  background-color: $progress-bar-bg;
}

.p-tabview .p-tabview-nav {
  background-color: var(--card-bg-color);
  border-color: var(--table-border-color);
}

.p-tabview .p-tabview-nav {
  li {
    &.p-highlight .p-tabview-nav-link {
      color: var(--active-color);
      border-color: var(--active-color);
      background-color: var(--card-bg-color);
    }
    .p-tabview-nav-link {
      background-color: var(--card-bg-color);
      border-color: transparent transparent var(--table-border-color) transparent;
    }
  }
}

.p-card {
  background: var(--fields-color);
  color: var(--text-color);
}

body.DARK {
  .p-tabview .p-tabview-nav li:not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link {
    background-color: var(--primary-color-lighter);
    color: var(--text-color);
  }
}

.p-tabview .p-tabview-nav li .p-tabview-nav-link {
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 23px;
  color: var(--text-color);
  text-transform: uppercase;
  border-width: 0 0 3px 0;
  padding: 0.8rem 1.5rem;
  text-decoration: none;
}

.p-tabview .p-tabview-nav li .p-tabview-nav-link:not(.p-disabled):focus {
  box-shadow: none;
}
