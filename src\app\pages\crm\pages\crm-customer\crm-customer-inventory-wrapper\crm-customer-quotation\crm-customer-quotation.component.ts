import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { QuotationCustomerLeadKey, QuotationCustomerListFilter, QuotationCustomerListItem, QuotationFilterParams, QuotationStatus } from '@pages/crm/models/customer-lead-quotation.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { AssociationsUnits } from '@pages/inventory/models';
import { takeUntil } from 'rxjs';
import { DataType, OperatorType, TreeOperatorType } from 'src/app/@shared/models';

@Component({
  selector: 'app-crm-customer-quotation',
  templateUrl: './crm-customer-quotation.component.html',
  styleUrls: ['./crm-customer-quotation.component.scss']
})
export class CrmCustomerQuotationComponent extends BaseComponent {
  isLoading = false;
  quotationList = [];
  quotationValue = '1';
  filterParams: QuotationCustomerListFilter = new QuotationCustomerListFilter();
  quotationStatus = QuotationStatus;
  @Input() crmId!: string;
  @Input() categoryId!: number;
  @Input() quotationCustomerList!: QuotationCustomerListItem[] | null;
  @Output() onQuotationAccepted: EventEmitter<boolean> = new EventEmitter<boolean>()
  showRejectModel = false;
  selectedQuotationId!: string;
  showCreateModal = false;
  selectedQuotation!: QuotationCustomerListItem;
  constructor(private readonly crmService: CrmService,
    private readonly formBuilder: FormBuilder,
    private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef) {
    super();
  }

  get getQuotationRequest(): QuotationFilterParams {
    return {
      treeOperator: TreeOperatorType.NOOP,
      values: [{
        dataType: DataType.STRING,
        key: QuotationCustomerLeadKey.CRM_CONTACT,
        operator: OperatorType.EQUAL,
        value: this.crmId.toString()
      }]
    };
  }

  private getQuotationDetails(): void {
    this.crmService.add<QuotationFilterParams>(this.getQuotationRequest, API_URL_UTIL.admin.crm.quatationFilter).pipe(takeUntil(this.destroy$)).subscribe({
      next: (quotation) => {
        this.quotationCustomerList = quotation.content;
      }
    });
  }

  get acceptedParams() {
    return {
      quotationStatus: "ACCEPTED"
    }
  }

  onAccepted(selectedQuotationId: string): void {
    const endpoint = API_URL_UTIL.admin.crm.acceptQuotation.replace(':quotationId', selectedQuotationId);
    this.crmService.patch(this.acceptedParams, endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.quotationAccepted);
        this.onQuotationAccepted.next(true);
      }
    });
  }

  onReject(id: string): void {
    this.showRejectModel = true;
    this.selectedQuotationId = id;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showRejectModel = false;
    if (refreshList) {
      this.getQuotationDetails();
      this.cdf.detectChanges();
    }
  }

  onView(rowData: any) {
    this.showCreateModal = true;
    this.selectedQuotation = rowData;
  }

  onCloseModal() {
    this.selectedQuotation = {} as QuotationCustomerListItem;
    this.showCreateModal = false;
  }

  getPrimaryStocks(rowData: QuotationCustomerListItem): AssociationsUnits | undefined {
    return rowData.quotationUnitResponseDTOList.find(item => item.categoryId === this.categoryId);
  }

  getAssociatedStocks(rowData: QuotationCustomerListItem): Array<AssociationsUnits> {
    return rowData.quotationUnitResponseDTOList.filter(item => item.categoryId !== this.categoryId);
  }
}
