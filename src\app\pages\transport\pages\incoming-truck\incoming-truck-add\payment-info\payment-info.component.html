<div class="general-info">
  <form>
    <section [formGroup]="paymentFormGroup">
      <div class="row m-t-10">
        <div class="col-lg-6 col-md-6 col-12">
          <label class="required">Stock</label>
          <input class="form-control" type="text" placeholder="Enter stock number" formControlName="stockNumber" />
          <app-error-messages [control]="paymentFormGroup.controls.stockNumber"></app-error-messages>
        </div>
        <div class="col-lg-6 col-md-6 col-12">
          <label class="required">Purchase Date</label>
          <div>
            <p-calendar appendTo="body" formControlName="transDate" [showIcon]="true" [showButtonBar]="true" [readonlyInput]="true" inputId="dateIcon"></p-calendar>
          </div>
          <app-error-messages [control]="paymentFormGroup.controls.transDate"></app-error-messages>
        </div>
      </div>
      <div class="row align-items-center mt-3">
        <div class="col-lg-3 col-md-4 col-12">
          <label>
            <span class="required">Select assignee</span>
            <span class="ms-2 text-primary text-decoration-underline cursor-pointer" (click)="assignToMe()">Assign to me</span>
          </label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="users"
            formControlName="assigneeId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select assignee"
          >
          </p-dropdown>
          <app-error-messages [control]="paymentFormGroup.controls.assigneeId"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-4 col-12">
          <div class="field-checkbox m-t-24">
            <div class="d-flex">
              <p-checkbox [binary]="true" inputId="generalCheckbox" formControlName="enable" [(ngModel)]="enablePickupTab" (ngModelChange)="onCheckedChangeGeneral()"></p-checkbox>
              <label for="generalCheckbox"><span class="ms-2 cursor-pointer">Check to fill pickup information</span></label>
            </div>
          </div>
        </div>
      </div>
      <div class="row align-items-center mt-3">
        <div class="col-lg-3 col-md-6 col-12">
          <div class="d-flex align-items-center pt-3">
            <ui-switch (change)="changeSendEmailFlag()" formControlName="sendFundingEmail" [disabled]="isViewMode"> </ui-switch>
            <div class="ms-3 mb-1">
              <span *ngIf="!incomingTruckDetails?.sentEmail">Send Funding Email</span>
              <span *ngIf="incomingTruckDetails?.sentEmail">Resend Funding Email</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </form>
</div>
