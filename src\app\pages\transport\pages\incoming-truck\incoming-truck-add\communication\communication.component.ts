import { ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild, ViewChildren } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { MESSAGES, VALIDATION_CONSTANTS, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { AllUserListFilter, Communication, MentionUser } from '@pages/inventory/models/inventory-communication';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { IncomingTruckCommunication } from '@pages/transport/models/incoming-truck-communication';
import { IncomingTruckDetails } from '@pages/transport/models/incoming-truck.model';
import { IncomingTruckCommentService } from '@pages/transport/services/incoming-truck-comment.service';
import { MentionConfig } from 'angular-mentions';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { MENTION_CONFIG } from 'src/app/@shared/constants/common.constant';
import { MentionParam } from 'src/app/@shared/models';

@Component({
  selector: 'app-communication',
  templateUrl: './communication.component.html',
  styleUrls: ['./communication.component.scss']
})
export class CommunicationComponent extends BaseComponent implements OnInit {
  @Input() isViewMode!: boolean;
  @Input() inventoryIncomingInfo!: IncomingTruckDetails | null;
  @Input() filterParams: any = new AllUserListFilter();
  @ViewChild('bold') bold!: ElementRef;
  commentFormGroup!: FormGroup;
  addedCommentsForm!: FormGroup;
  availableMentionUsers: MentionParam[] = [];
  mentionedUsers: MentionUser[] = [];
  isNoteEditing !: any[];
  currentUserId!: number;
  isFirstTime = true;
  editor: any;
  editorChild: any;
  boldChild: any;
  Id!: number;
  @ViewChildren('boldChild') set contentsBtn(contents: any) {
    if (contents) {
      this.boldChild = contents;
    }
  }
  @ViewChildren('editorChild') set contents(contents: any) {
    if (contents) {
      this.editorChild = contents;
    }
  }
  @ViewChild('editor') set content(content: any) {
    if (content) {
      this.editor = content;
    }
  }
  selectedCommentId!: number;

  mentionConfig: MentionConfig = {
    ...MENTION_CONFIG,
    items: this.availableMentionUsers
  };

  constructor(private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef,
    private readonly formBuilder: FormBuilder,
    private readonly incomingTruckService: IncomingTruckCommentService,
    private readonly confirmationService: ConfirmationService,
    private readonly accountService: AuthService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly userAnnotationService: UserAnnotationService,

  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getUserId();
    this.initializeCommentSubCommentFormGroup();
    this.getCommunicationList();
    this.getAll();
    this.isLoading = true;
    this.activatedRoute.queryParams
      .subscribe(params => {
        if (params?.commentId) {
          this.selectedCommentId = Number(params.commentId);
        }
      });
  }

  initializeFormGroup(): void {
    this.commentFormGroup = this.formBuilder.group({
      message: new FormControl(null, [Validators.required, Validators.maxLength(VALIDATION_CONSTANTS.textAreaMaxLength)]),
      incomingTruckId: new FormControl(this.inventoryIncomingInfo?.id),
      parentCommunicationId: new FormControl(null),
      id: new FormControl(null),
      mentionUserIds: new FormControl([])
    });
  }

  initializeCommentSubCommentFormGroup(): void {
    this.addedCommentsForm = this.formBuilder.group({
      parent: this.newAddedParentCommentsFormArray,
    });
  }

  get addedParentCommentsFormGroup(): FormGroup {
    return this.formBuilder.group({
      createdBy: new FormControl(null),
      edited: new FormControl(null),
      id: new FormControl(null),
      message: new FormControl(null),
      modifiedDate: new FormControl(null),
      incomingTruckId: new FormControl(null),
      parentCommunicationId: new FormControl(null),
      childComments: this.addedChildCommentsFormArray,
    });
  }

  get addedChildCommentsFormArray(): FormArray {
    return this.formBuilder.array([this.addedChildCommentsFormGroup])
  }

  get addedChildCommentsFormGroup(): FormGroup {
    return this.formBuilder.group({
      createdBy: new FormControl(this.createdByFormGroup),
      edited: new FormControl(null),
      id: new FormControl(null),
      messageControl: new FormControl(null, [Validators.required]),
      message: new FormControl(null),
      modifiedDate: new FormControl(null),
      incomingTruckId: new FormControl(null),
      parentCommunicationId: new FormControl(null),
    });
  }

  get createdByFormGroup(): FormGroup {
    return this.formBuilder.group({
      id: new FormControl(null),
      firstName: new FormControl(null),
      lastName: new FormControl(null),
    });
  }

  get newAddedParentCommentsFormArray(): FormArray {
    return this.formBuilder.array([this.addedParentCommentsFormGroup])
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.userAnnotationService.get(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          this.availableMentionUsers = res.map((user: any) => ({ id: user.id, label: user.name }));
          this.mentionConfig.items = this.availableMentionUsers;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: (err: any) => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }


  getUserId(): void {
    this.accountService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe((res) => {
      if (res?.id) {
        this.currentUserId = res.id;
      }
    });
  }

  mentionSelected(mention: any, childIndex = -1) {
    let count = 0;
    for (const i of this.mentionedUsers) {
      if (i.id === mention.id) {
        count++;
      }
    }
    if (count === 0) {
      this.mentionedUsers.push(mention);
    }
    setTimeout(() => {
      if (childIndex > -1) {
        this.editorChild.toArray()[childIndex].getQuill().updateContents();
      } else {
        this.editor.getQuill().updateContents();
      }
    })
  }

  textChange(e: any) {
    if (e.htmlValue.includes('&lt;strong')) {
      let val = e.htmlValue.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['message'].setValue(`${val}`);
      setTimeout(() => {
        this.editor.el.nativeElement.childNodes[0].lastChild.childNodes[0].focus();
        this.editor.el.nativeElement.childNodes[0].lastChild.childNodes[0].click();
        this.bold.nativeElement.click();
        this.editor.getQuill().setSelection(val.length + 1);
      }, 0);
    }
  }


  decodeEntities(str: any) {
    // this prevents any overhead from creating the object each time
    const element = document.createElement('div');
    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
    element.innerHTML = str;

    return element.textContent;
  }


  selectionChange(e: any) {
    if (e.range && e.range.index > 0 && e.source === 'api') {
      this.bold.nativeElement.click();
    }
  }

  get commentCreateParams(): IncomingTruckCommunication {
    return {
      ...this.commentFormGroup.value,
    };
  }

  addNewParentCommentSubmit() {
    if (this.commentFormGroup.invalid) {
      this.commentFormGroup.markAllAsTouched();
      return;
    }
    this.commentFormGroup.markAsUntouched();
    if (this.inventoryIncomingInfo?.id) {
      this.addComment(true);
    }
  }

  get commentsFormArray(): FormArray {
    return this.addedCommentsForm.get('parent') as FormArray;
  }

  private addComment(isParentComment: boolean, childParams: IncomingTruckCommunication | null = null, parentId = 0): void {
    let params;
    if (isParentComment) {
      params = this.commentCreateParams;
      params.mentionUserIds = this.mentionedUsers.map(i => i.id);
      if (this.commentFormGroup.controls['message'].value.includes('&lt;strong')) {
        let val = this.commentFormGroup.controls['message'].value.split('&lt;strong');
        const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
        let d = val[index].split('/strong&gt;');
        d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
        d = d.join(' ');
        val[index] = d;
        val = val.join(' ');
        this.commentFormGroup.controls['message'].setValue(`${val}`);
        params.message = this.commentFormGroup.controls['message'].value;
      }
    } else {
      if (childParams) {
        params = childParams;
      }
    }
    if (params) {
      params.mentionUserIds = this.removeErasedUser(params.message, params.mentionUserIds);
    }
    this.incomingTruckService.add(params).pipe(takeUntil(this.destroy$)).subscribe(savedComment => {
      this.toasterService.success(MESSAGES.commentAddSuccess);
      this.commentCreateParams.mentionUserIds = [];
      this.mentionedUsers = [];
      if (this.isFirstTime) {
        this.commentsFormArray.clear();
      }
      if (isParentComment) {
        this.commentsFormArray.push(this.formBuilder.group({
          createdBy: {
            id: savedComment.createdBy.id,
            firstName: savedComment.createdBy.firstName,
            lastName: savedComment.createdBy.lastName
          },
          edited: savedComment.savedComment,
          id: savedComment.id,
          messageControl: null,
          message: savedComment.message,
          modifiedDate: savedComment.modifiedDate,
          parentCommunicationId: savedComment.parentCommunicationId,
          incomingTruckId: savedComment.id,
          childMessage: new FormControl(null, [Validators.required]),
          isPrevieClicked: false,
          childComments: this.formBuilder.array([])
        }));
        this.isFirstTime = false;
        this.commentFormGroup.get('message')?.reset();
        this.addedCommentsForm.updateValueAndValidity();
      }
      else {
        this.getChildCommentsFormArrayByParentCommunicationId(parentId).push(this.formBuilder.group({
          createdBy: {
            id: savedComment.createdBy.id,
            firstName: savedComment.createdBy.firstName,
            lastName: savedComment.createdBy.lastName
          },
          edited: savedComment.savedComment,
          id: savedComment.id,
          message: savedComment.message,
          modifiedDate: savedComment.modifiedDate,
          parentCommunicationId: savedComment.parentCommunicationId,
          incomingTruckId: savedComment.incomingTruckId,
        }));
        this.commentsFormArray.at(parentId)?.get('isMultipleComments')?.setValue(this.commentsFormArray.at(parentId)?.get('childComments')?.value.length > 1 ? true : false);
        this.commentsFormArray.at(parentId)?.get('childMessage')?.reset();
      }
      this.cdf.detectChanges();
    });
  }

  getChildCommentsFormArrayByParentCommunicationId(id: number): FormArray {
    return this.commentsFormArray.at(id)?.get('childComments') as FormArray;
  }

  private removeErasedUser(comment: string, ids: number[]): number[] {
    return ids.filter(id => {
      const label = this.availableMentionUsers.find(obj => obj.id === id)?.label;
      return label && comment.toLowerCase().includes(label.toLowerCase());
    });
  }

  trackByFunction(index: number, element: any) {
    return element ? index : null;
  }

  onDelete(expenses: any, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'comment'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(expenses);
      }
    });
  }


  setisPrevieClickedValue(parentIndex: number): void {
    this.commentsFormArray.at(parentIndex)?.get('isPrevieClicked')?.setValue(true);
  }

  onDeleteConfirmation(expenses: any): void {
    this.incomingTruckService.delete(expenses.value.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.commentDeleteSuccess);
          this.getCommunicationList();
          this.cdf.detectChanges()
        }
      });
  }
  private getCommunicationList() {
    if (this.inventoryIncomingInfo) {
      this.Id = this.inventoryIncomingInfo.id;
    }
    this.incomingTruckService.getIncomingTruckComments(this.Id).pipe(takeUntil(this.destroy$)).subscribe({
      next: (communicationData) => {
        if (this.isFirstTime) {
          this.commentsFormArray.clear();
        }
        this.isFirstTime = false;
        this.commentsFormArray.clear();
        for (const communicationList of communicationData) {
          this.commentsFormArray.push(this.formBuilder.group({
            createdBy: communicationList.createdBy,
            edited: communicationList.edited,
            id: communicationList.id,
            messageControl: null,
            message: communicationList.message,
            modifiedDate: communicationList.modifiedDate,
            parentCommunicationId: null,
            incomingTruckId: communicationList.unitId,
            childMessage: new FormControl(null, [Validators.required]),
            isPrevieClicked: false,
            childComments: this.formBuilder.array([]),
            mentionUserIds: [communicationList.mentionUserIds],
          }));
          this.cdf.detectChanges();
          if (communicationList.communications.length) {
            for (const communication of communicationList.communications) {
              const commentArrayIndex = this.commentsFormArray.value.findIndex((commentsArray: Communication) => commentsArray.id === communication.parentCommunicationId);
              if (commentArrayIndex !== -1) {
                this.getChildCommentsFormArrayByParentCommunicationId(commentArrayIndex).push(this.formBuilder.group({
                  createdBy: communication.createdBy,
                  edited: communication.edited,
                  id: communication.id,
                  message: communication.message,
                  modifiedDate: communication.modifiedDate,
                  parentCommunicationId: communication.parentCommunicationId,
                  unitId: communication.unitId,
                  mentionUserIds: [communication.mentionUserIds],
                }));
              }
            }
          }
        }
        this.cdf.detectChanges();
      }
    });
  }

  childTextChange(e: any, childIndex: number) {
    if (e.htmlValue.includes('&lt;strong')) {
      let val = e.htmlValue.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentsFormArray.at(childIndex).get('childMessage')?.setValue(`${val}`);
      setTimeout(() => {
        this.editorChild.toArray()[childIndex].el.nativeElement.childNodes[0].lastChild.childNodes[0].focus();
        this.editorChild.toArray()[childIndex].el.nativeElement.childNodes[0].lastChild.childNodes[0].click();
        this.boldChild.toArray()[childIndex].nativeElement.click();
        this.editorChild.toArray()[childIndex].getQuill().setSelection(val.length + 1);
      }, 0);
    }
  }

  selectionChildChange(e: any, childIndex: number) {
    if (e.range && e.range.index > 0) {
      this.boldChild.toArray()[childIndex].nativeElement.click();
    }
  }

  getChildCommentFormControl(parentIndex: number): FormControl {
    return this.commentsFormArray.at(parentIndex)?.get('childMessage') as FormControl;
  }

  addNewChildCommentSubmit(parentId: number, parentCommunicationId: number) {
    if (!this.commentsFormArray.at(parentId)?.get('childMessage')?.dirty) {
      this.commentsFormArray.at(parentId)?.get('childMessage')?.markAsTouched();
      return;
    }
    const userIds = this.mentionedUsers.map(i => i.id);
    if (this.commentsFormArray.at(parentId)?.get('childMessage')?.value.includes('&lt;strong')) {
      let val = this.commentsFormArray.at(parentId)?.get('childMessage')?.value.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentsFormArray.at(parentId)?.get('childMessage')?.setValue(`${val}`);
    }
    if (this.inventoryIncomingInfo?.id) {
      const childParams: IncomingTruckCommunication = {
        message: this.commentsFormArray.at(parentId)?.get('childMessage')?.value,
        incomingTruckId: this.inventoryIncomingInfo?.id,
        parentCommunicationId: parentCommunicationId,
        mentionUserIds: userIds,
      }
      this.addComment(false, childParams, parentId);
    }
  }

}
