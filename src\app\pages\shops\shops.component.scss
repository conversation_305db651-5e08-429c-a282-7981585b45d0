.mr-5 {
  margin-right: 5px;
}

.w-186 {
  width: 186px;
}

.expand-btn {
  padding: 0 15px !important;
}

.m-t-80 {
  margin-top: 80px;
}

::ng-deep .shops-list .parent-accordion {
  .p-accordion-header {
    .p-accordion-header-link {
      background-color: var(--app-background-color) !important;
      color: var(--text-color) !important;
    }
    &:hover .p-accordion-header-link {
      background-color: var(--app-background-color) !important;
      color: var(--text-color) !important;
    }
  }
}
::ng-deep .shops-list .nested-accordion {
  .p-accordion-header {
    .p-accordion-header-link {
      background-color: var(--card-bg-color) !important;
      color: var(--text-color) !important;
    }
    &:hover .p-accordion-header-link {
      background-color: var(--card-bg-color) !important;
      color: var(--text-color) !important;
    }
  }
}
