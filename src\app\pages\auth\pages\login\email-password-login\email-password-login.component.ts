import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseComponent } from '@core/utils';
import { LoginEmailPasswordParams } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { takeUntil } from 'rxjs';
import { MessagingService } from 'src/app/@shared/services';
import { MenuConfigService } from 'src/app/@shared/services/menu-config.service';


@Component({
  selector: 'app-email-password-login',
  styleUrls: ['./email-password-login.component.scss'],
  templateUrl: './email-password-login.component.html',
})
export class EmailPasswordLoginComponent extends BaseComponent implements OnInit {
  emailPasswordLoginFormGroup!: UntypedFormGroup;

  constructor(
    private readonly router: Router,
    private readonly authService: AuthService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly messagingService: MessagingService,
    private readonly menuConfigService: MenuConfigService,
    private readonly cdf: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  initializeForm() {
    this.emailPasswordLoginFormGroup = new UntypedFormGroup({
      username: new UntypedFormControl('', [Validators.required, Validators.minLength(1), Validators.maxLength(50), Validators.email]),
      password: new UntypedFormControl('', [Validators.required, Validators.minLength(4), Validators.maxLength(50)]),
    });

  }

  get loginEmailPasswordParams(): LoginEmailPasswordParams {
    return {
      ...this.emailPasswordLoginFormGroup.value,
    };
  }

  loginWithEmailPassword(): void {
    if (this.emailPasswordLoginFormGroup.invalid) {
      this.emailPasswordLoginFormGroup.markAllAsTouched();
      return;
    }
    this.menuConfigService.resetMenuConfig();
    this.cdf.detectChanges();
    this.authService.loginWithEmailPassword(this.loginEmailPasswordParams).pipe(takeUntil(this.destroy$)).subscribe(loginRes => {
      this.messagingService.requestPermission();
      this.messagingService.getFirebaseToken();
      this.messagingService.receiveMessage();
      this.messagingService.currentMessage.subscribe(res => {
        if (res) {
          console.log('message recieved', res);
        }
      });

      this.router.navigate([this.path.redirectToAuth.root]).then()
    });
  }

}
