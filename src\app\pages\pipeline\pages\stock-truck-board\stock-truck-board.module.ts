import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { ColumnDropdownModule } from '@pages/common-table-column/column-dropdown.module';
import { ShopsModule } from '@pages/shops/shops.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { MentionModule } from 'angular-mentions';
import { UiSwitchModule } from 'ngx-ui-switch';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService, MessageService, TreeDragDropService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DragDropModule } from 'primeng/dragdrop';
import { DropdownModule } from 'primeng/dropdown';
import { EditorModule } from 'primeng/editor';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { ProgressBarModule } from 'primeng/progressbar';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TimelineModule } from 'primeng/timeline';
import { TooltipModule } from 'primeng/tooltip';
import { TreeModule } from 'primeng/tree';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { StockTruckBoardComponent } from '../stock-truck-board/stock-truck-board.component';
import { StockTruckRoutingModule } from '../stock-truck-board/stock-truck-board.routing.module';
import { StockTruckBoardAddComponent } from './stock-truck-board-add/stock-truck-board-add.component';
import { StockTruckBoardCommunicationComponent } from './stock-truck-board-communication/stock-truck-board-communication.component';
import { StockTruckBoardListComponent } from './stock-truck-board-list/stock-truck-board-list.component';
import { PipelineConfigModule } from '@pages/administration/pages/pipeline-config/pipeline-config.module';

@NgModule({
  declarations: [StockTruckBoardComponent,
    StockTruckBoardAddComponent,
    StockTruckBoardListComponent,
    StockTruckBoardCommunicationComponent],

  imports: [
    CommonModule,
    StockTruckRoutingModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    ConfirmDialogModule,
    TimelineModule,
    ProgressBarModule,
    AccordionModule,
    TabViewModule,
    TooltipModule,
    CheckboxModule,
    ShopsModule,
    TreeModule,
    DragDropModule,
    SidebarModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    ColumnDropdownModule,
    CalendarModule,
    EditorModule,
    MentionModule,
    MenuModule,
    PipelineConfigModule
  ],

  providers: [ConfirmationService, TreeDragDropService, MessageService],
  exports: [StockTruckBoardAddComponent]
})

export class StockTruckBoardModule { }
