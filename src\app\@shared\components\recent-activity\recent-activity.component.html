<div class="modal-title">
  <h4>Activities</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="closePopup()"></fa-icon>
</div>

<div
  class="scrollable-view-activity activity-list overflow-auto"
  infiniteScroll
  (scrolled)="onScroll()"
  [scrollWindow]="false"
  [infiniteScrollThrottle]="constants.infiniteScrollConfig.infiniteScrollThrottle"
  [infiniteScrollDistance]="1"
>
  <div class="py-3 px-4">
    <input class="form-control" placeholder="Search by {{ searchPlaceholder }}" (input)="searchHistory($event.target)" />
  </div>
  <ng-container *ngIf="!isLoading; else loaderTemplate">
    <ng-container *ngIf="historyList?.length; else noDataFound">
      <ng-container *ngFor="let history of historyList; let i = index; trackBy: trackByFunction">
        <div
          class="p-3 history-item d-flex flex-column ajustify-content-center"
          [ngClass]="{ even: i % 2 === 0 }"
          (click)="onClickHistory(history)"
        >
          <div class="history-info-div">
            <span class="user-name">{{ history.actionUser.name }}</span
            >&nbsp;has&nbsp;<span class="action-name">{{ getAction(history.action) }}</span
            >&nbsp;
            <span class="display-name text-break fw-bolder text-decoration-underline">{{ history.displayName }}</span>
            &nbsp;<span *ngIf="isIncomingTruckToInventory(history)">to Inventory</span>
          </div>
          <div class="time-div">
            <span class="created-date">{{ history.createdDate | dateAgo }}</span>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </ng-container>
  <ng-container [ngTemplateOutlet]="(isScrolling$ | async) ? loadingMoreTemplate : null"> </ng-container>
</div>

<ng-template #loaderTemplate>
  <div class="history-loader">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
  </div>
</ng-template>

<ng-template #loadingMoreTemplate>
  <div class="mb-3 w-100 text-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
  </div>
</ng-template>

<ng-template #noDataFound>
  <span class="notification-info display-block d-flex justify-content-center h-100">
    <span class="m-auto">No Recent Activities</span>
  </span>
</ng-template>
