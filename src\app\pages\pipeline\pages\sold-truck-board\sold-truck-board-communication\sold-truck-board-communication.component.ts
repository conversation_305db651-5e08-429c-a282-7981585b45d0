import { ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES, VALIDATION_CONSTANTS, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { AllUserListFilter, MentionUser } from '@pages/inventory/models/inventory-communication';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { PipelineGetDetail, SoldCommentCreateParam, SoldCommentListItem, SoldTruckBoardListItem } from '@pages/pipeline/models';
import { MentionConfig } from 'angular-mentions';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { MENTION_CONFIG } from 'src/app/@shared/constants/common.constant';
import { MentionParam } from 'src/app/@shared/models';
import { PipelineCommentService } from '../pipeline-comment.service';

@Component({
  selector: 'app-sold-truck-board-communication',
  templateUrl: './sold-truck-board-communication.component.html',
  styleUrls: ['./sold-truck-board-communication.component.scss']
})
export class SoldTruckBoardCommunicationComponent extends BaseComponent implements OnInit {
  @Input() soldTruckInfo!: SoldTruckBoardListItem | PipelineGetDetail | null;
  @Input() selectedCommentId!: number;
  isAddingComment = false;
  commentFormGroup!: FormGroup;
  mentionedUsers: MentionUser[] = [];
  availableMentionUsers: MentionParam[] = [];
  mentionConfig: MentionConfig = {
    ...MENTION_CONFIG,
    items: this.availableMentionUsers
  };
  comments: SoldCommentListItem[] = [];
  currentUserId!: number;

  commentEditor: any;
  @ViewChild('commentEditor') set content(content: any) {
    if (content) {
      this.commentEditor = content;
    }
  }
  @Input() filterParams: any = new AllUserListFilter();

  @ViewChild('boldText') boldText!: ElementRef;
  isFirstTime = true;
  constructor(private readonly formBuilder: FormBuilder,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly confirmationService: ConfirmationService,
    private readonly accountService: AuthService,
    private readonly toasterService: AppToasterService,
    private readonly pipelineCommentService: PipelineCommentService) { super() }

  ngOnInit(): void {
    this.getPipelineComments();
    this.initializeFormGroup();
    this.getUserId();
    this.getAll();
  }

  initializeFormGroup(): void {
    this.commentFormGroup = this.formBuilder.group({
      comment: new FormControl(null, [Validators.required, Validators.maxLength(VALIDATION_CONSTANTS.textAreaMaxLength)]),
      pipelineId: new FormControl(this.soldTruckInfo?.id),
      id: new FormControl(null),
      mentionUserIds: new FormControl([]),
      createdById: new FormControl(null)
    });
  }

  getUserId(): void {
    this.accountService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe((res) => {
      if (res?.id) {
        this.currentUserId = res.id;
      }
    });
  }

  get soldCommentCreateParams(): SoldCommentCreateParam {
    return this.commentFormGroup.value;
  }

  onAddNewCommentSubmit() {
    if (this.commentFormGroup.invalid) {
      this.commentFormGroup.markAllAsTouched();
      return;
    }
    this.commentFormGroup.markAsUntouched();
    if (this.soldCommentCreateParams?.id) {
      this.edit();
    } else {
      this.add();
    }
  }

  removeErasedUser(comment: string, ids: number[]): number[] {
    return ids.filter(id => {
      const label = this.availableMentionUsers.find(obj => obj.id === id)?.label;
      return label && comment.toLowerCase().includes(label.toLowerCase());
    });
  }

  add(): void {
    const params = this.soldCommentCreateParams;
    params.mentionUserIds = this.mentionedUsers.map(i => i.id);
    if (this.commentFormGroup.controls['comment'].value.includes('&lt;strong')) {
      let val = this.commentFormGroup.controls['comment'].value.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['comment'].setValue(`${val}`);
      params.comment = this.commentFormGroup.controls['comment'].value
    }
    params.mentionUserIds = this.removeErasedUser(params.comment, params.mentionUserIds);
    this.pipelineCommentService.add<SoldCommentCreateParam>(params).pipe(takeUntil(this.destroy$)).subscribe(savedComment => {
      this.toasterService.success(MESSAGES.commentAddSuccess);
      this.comments.push(savedComment);
      this.commentFormGroup.reset();
      this.mentionedUsers = [];
      this.isAddingComment = false;
    });
  }

  edit(): void {
    const params = this.soldCommentCreateParams
    params.mentionUserIds = [...new Set([...params.mentionUserIds, ...this.mentionedUsers.map(i => i.id)])];
    if (this.commentFormGroup.controls['comment'].value.includes('&lt;strong')) {
      let val = this.commentFormGroup.controls['comment'].value.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['comment'].setValue(`${val}`);
      params.comment = this.commentFormGroup.controls['comment'].value
    }
    params.mentionUserIds = this.removeErasedUser(params.comment, params.mentionUserIds);
    this.pipelineCommentService.update<SoldCommentCreateParam>(params).pipe(takeUntil(this.destroy$)).subscribe(savedComment => {
      this.toasterService.success(MESSAGES.commentUpdateSuccess);
      const index = this.comments.findIndex(comment => comment.id === (savedComment as SoldCommentCreateParam)?.id);
      if (index !== undefined && index > -1) {
        this.comments[index] = savedComment as SoldCommentListItem;
      }
      this.commentFormGroup.reset();
      this.mentionedUsers = [];
      this.isAddingComment = false;
    });
  }

  onAddComment() {
    this.commentFormGroup.patchValue({
      id: null,
      comment: null,
      pipelineId: this.soldTruckInfo?.id,
      mentionUserIds: null
    });
    this.isAddingComment = true;
  }

  onCancel(): void {
    this.isAddingComment = false;
    this.mentionedUsers = [];
  }

  getPipelineComments(): void {
    this.isLoading = true;
    const url = API_URL_UTIL.pipelineComment.pipeline.replace(':pipelineId', this.soldTruckInfo?.id.toString() || '');
    this.pipelineCommentService.getList<SoldCommentListItem>(url).pipe(takeUntil(this.destroy$)).subscribe({
      next: comments => {
        this.isLoading = false;
        this.comments = comments;
        if (!this.comments?.length) {
          this.isAddingComment = true;
        }
      },
      error: () => {
        this.isLoading = false;
      }
    })
  }

  onEdit(pipelineComment: SoldCommentListItem): void {
    this.mentionedUsers = [];
    this.commentFormGroup.patchValue({
      id: pipelineComment.id,
      comment: pipelineComment.comment,
      pipelineId: pipelineComment.pipelineId,
      mentionUserIds: pipelineComment.mentionUserIds,
      createdById: pipelineComment.createdById
    })
    this.isAddingComment = true;
  }

  onDelete(comment: SoldCommentListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'comment'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(comment);
      }
    });
  }

  onDeleteConfirmation(comment: SoldCommentListItem): void {
    this.pipelineCommentService.delete(comment.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.commentDeleteSuccess);
          const index = this.comments.findIndex((c: any) => c.id === comment.id);
          if (index !== undefined && index > -1) {
            this.comments.splice(index, 1);
          }
        }
      });
  }
  mentionSelected(mention: any, childIndex = -1) {
    let count = 0;
    for (const i of this.mentionedUsers) {
      if (i.id === mention.id) {
        count++;
      }
    }
    if (count === 0) {
      this.mentionedUsers.push(mention);
    }
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.userAnnotationService.get(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          this.availableMentionUsers = res.map((user: any) => ({ id: user.id, label: user.name }));
          this.mentionConfig.items = this.availableMentionUsers;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: (err: any) => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  textChange(e: any) {
    if (e.htmlValue.includes('&lt;strong')) {
      let val = e.htmlValue.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['comment'].setValue(`${val}`);
      setTimeout(() => {
        this.commentEditor.el.nativeElement.childNodes[0].lastChild.childNodes[0].focus();
        this.commentEditor.el.nativeElement.childNodes[0].lastChild.childNodes[0].click();
        this.boldText.nativeElement.click();
        this.commentEditor.getQuill().setSelection(val.length + 1);
      }, 0);
    }
  }

  selectionChange(e: any) {
    if (e && e.range && e.range.index > 0 && e.source === 'api') {
      this.boldText.nativeElement.click();
    }
  }

  decodeEntities(str: any) {
    // this prevents any overhead from creating the object each time
    const element = document.createElement('div');
    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
    element.innerHTML = str;
    return element.textContent;
  }
}
