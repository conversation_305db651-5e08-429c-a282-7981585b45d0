export enum FilterModuleName {
  INVENTORY_MODULE = 'Inventory',
  PIPELINE_CONFIG_MODULE = 'PipelineConfig',
  USERS_MODULE = 'Users',
  DEALERS_MODULE = 'Dealers',
  VENDORS_MODULE = 'Module',
  SHOP_TASK = 'ShopTask',
  PENDING_INCOMING_TRUCK_TRACKING = 'PENDING_INCOMING_TRUCK_TRACKING',
  READY_FOR_PICKUP_INCOMING_TRUCK_TRACKING = 'READY_FOR_PICKUP_INCOMING_TRUCK_TRACKING',
  DRIVER_SCHEDULE_MODULE = 'DRIVER_SCHEDULING_BOARD',
  CRM_CONTACT_MODULE = 'CONTACTS',
  CRM_CUSTOMER_MODULE = 'CUSTOMER_LEAD',
  CRM_TASK_MODULE = 'TASKS',
  SOLD_TRUCK_BOARD = 'SoldTruckBoard',
  STOCK_TRUCK_BOARD = 'StockTruckBoard',
  CUSTOMER_LEAD = 'CustomerLead',
}

export interface FilterList {
  data?: string;
  deleted?: boolean;
  filterName?: string;
  id?: number;
  isDefault?: boolean;
  module: string;
  skeyeUserId?: number | null;
  dealerId?: string | number | null;
  isPrivate?: boolean;
  filterType?: string;
  hideField?: string | null;
  activeTabName?: string | null;
  isPreference?: boolean;
}

export interface ColumnItem {
  id: number | null;
  name: string | null;
  key: string | null;
  disable: boolean;
  default: boolean;
  shorting: boolean;
  type: string | null;
  value: null | number | string | number[];
  shortingKey: string | null;
  searchKey?: string | null;
  parentIndex?: number;
  childIndex?: number;
  isSpecificationField?: boolean;
}
