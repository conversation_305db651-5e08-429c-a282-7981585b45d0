<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>

<div class="model-body">
  <form [formGroup]="reminderFrom" class="specification-form" (ngSubmit)="onSubmit()">
    <div class="content">
      <div class="form-group">
        <label class="required">Reminder for</label>
        <p-dropdown appendTo="body" [options]="accountRepList" formControlName="remindToId" optionLabel="name" [showClear]="true" optionValue="id" placeholder="Select Reporter">
        </p-dropdown>
        <app-error-messages [control]="reminderFrom?.controls?.remindToId"></app-error-messages>
      </div>
      <div class="row">
        <div [ngClass]="showCalendar ? 'col-6' : 'col-12'">
          <div class="form-group">
            <label class="required">Schedule after</label>
            <p-dropdown
              appendTo="body"
              [options]="dateOptions"
              optionLabel="label"
              [showClear]="true"
              formControlName="scheduleAfter"
              optionValue="months"
              placeholder="Select Duration"
              (onChange)="onDateChange($event?.value)"
            >
            </p-dropdown>
            <app-error-messages *ngIf="!showCalendar" [control]="reminderFrom?.controls?.scheduleAfter"> </app-error-messages>
          </div>
        </div>
        <div class="col-6" *ngIf="showCalendar">
          <div class="form-group">
            <div *ngIf="showScheduledDate">
              <label for="">Scheduled on</label>
              <div class="schedule-on-content">
                {{ reminderFrom?.controls?.reminderTime?.value | date: constants.dateFormat }}
              </div>
            </div>
            <div *ngIf="!showScheduledDate">
              <label for="" class="required">Schedule on</label>
              <p-calendar
                appendTo="body"
                formControlName="reminderTime"
                [showIcon]="true"
                [showButtonBar]="true"
                [readonlyInput]="true"
                inputId="endDateIcon"
                placeholder="Select Date"
                [minDate]="minDate"
              >
              </p-calendar>
            </div>
            <app-error-messages [control]="reminderFrom?.controls?.reminderTime"></app-error-messages>
          </div>
        </div>
      </div>
      <div class="d-flex align-items-center form-group" *ngIf="reminderFrom?.controls?.scheduleAfter?.value">
        <label for="reoccurring" class="mr-5">Recurrence reminder: </label>
        <ui-switch formControlName="reoccurring" name="reoccurring" class="mt-1"> </ui-switch>
      </div>
      <div class="form-group">
        <label>Notes</label>
        <textarea placeholder="Enter notes" rows="5" formControlName="notes" class="form-control"></textarea>
      </div>
    </div>
    <div class="history" *ngIf="isViewMode">
      <div class="title">Reminder History</div>
      <div class="main-content-wrapper">
        <ng-container *ngIf="reminderHistory?.length; else noHistoryFound">
          <div class="content-wrapper" *ngFor="let history of reminderHistory">
            Reminder triggered on
            <span class="history-bold-text">
              {{ history.reminderTime | date: constants.dateFormat }}
            </span>
            for
            <span class="history-bold-text">
              {{ history.remindToName }}
            </span>
          </div>
        </ng-container>

        <ng-template #noHistoryFound>
          <div class="m-4 d-flex justify-content-center">No history found</div>
        </ng-template>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
      <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
    </div>
  </form>
</div>
