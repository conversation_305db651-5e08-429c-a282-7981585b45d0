import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NgForm } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { Field, QuoteField } from '@pages/public-inventories/models';
import { InventoriesService } from '@pages/public-inventories/services';
import { Address as gAddress } from 'ngx-google-places-autocomplete/objects/address';

@Component({
  selector: 'app-quote-form',
  templateUrl: './quote-form.component.html',
  styleUrls: ['./quote-form.component.scss']
})
export class QuoteFormComponent extends BaseComponent implements OnInit {

  @Input() stockNumber!: string;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  quoteId!: number;
  quoteFields!: QuoteField[];
  fields: Field[] = [];
  isLoading = false;
  addressOptions: any = {
    componentRestrictions: { country: 'US' }
  }

  constructor(private readonly inventoriesService: InventoriesService, private readonly toasterService: AppToasterService) {
    super();
  }

  ngOnInit(): void {
    this.getQuoteFormData();
  }

  getQuoteFormData() {
    this.isLoading = true;
    this.inventoriesService.getQuoteFormData().subscribe((res) => {
      this.quoteId = res[0].id
      this.quoteFields = res[0].formData.fields;
      this.isLoading = false;
    })
  }

  handleAddressChange(address: gAddress | any, quoteForm: NgForm) {
    if (address?.address_components) {
      quoteForm.controls['Address'].setValue(address.name);
      quoteForm.controls['latitude']?.setValue(address.geometry.location.lat());
      quoteForm.controls['longitude']?.setValue(address.geometry.location.lng());
      address.address_components.forEach((addressComponent: any) => {
        switch (addressComponent.types[0]) {
          case 'sublocality':
            break;
          case 'locality':
            quoteForm.controls['City'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_1':
            quoteForm.controls['State'].setValue(addressComponent.long_name);
            break;
          case 'postal_code':
            quoteForm.controls['Zip Code'].setValue(addressComponent.long_name);
            break;
        }
      });
    }
  }

  onSubmit(form: NgForm): void {
    if (form.invalid) {
      return;
    }
    for (const [key, value] of Object.entries(form.value)) {
      if (value) {
        this.fields.push({
          "key": key,
          "value": value
        })
      }
    }
    const quoteParams = {
      id: this.quoteId,
      stockNumber: this.stockNumber,
      fields: this.fields
    }
    this.inventoriesService.submitQuoteFormData(quoteParams).subscribe((res) => {
      this.toasterService.success(MESSAGES.quoteFormSubmitted)
    })
    form.reset();
    this.onClose.emit(true);
  }
}
