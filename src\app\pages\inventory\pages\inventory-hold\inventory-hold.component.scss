.inventory-matching-wrapper {
  height: calc(100vh - 115px);
  overflow-y: auto;

  ::ng-deep .p-card {
    margin: 15px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);

    .p-card-content {
      padding: 0;
    }

    .p-card-body {
      padding: 10px;
    }
  }

  .gray {
    color: var(--form-placeholder-color) !important;
  }

  .inventory-quote-wrapper {
    .qoute-details {
      display: flex;

      div {
        margin-right: 5px;
      }

      .qoute-details-info {
        border-right: 1px solid #e3e3e3;
      }

      .qoute-details-info:last-child {
        border-right: none;
      }

      .model-detail-label {
        color: var(--form-placeholder-color) !important;
      }

      .model-detail-info {
        font-weight: 500;
      }
    }
  }

  .inventory-action-label {
    margin: 20px;
    font-weight: 500;
  }

  .customer-inventory-matched {
    .inventory-content-wrapper {
      .content {
        padding: 5px;
        display: flex;
        justify-content: space-between;

        img {
          height: 50px;
        }

        .link-off {
          height: 20px;
          cursor: pointer;
        }

        .associated-unit {
          width: 80%;
        }
      }
    }
  }
}

.modal-title {
  align-items: center;

  .model-content {
    display: flex;
    flex-direction: column;
  }

  fa-icon {
    height: fit-content;
    line-height: 27px;
  }
}

.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
}

.bold-text {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

.modal-title * {
  line-height: 22px;
}

::ng-deep p-confirmdialog {
  .p-dialog-mask-scrollblocker {
    z-index: 99999 !important;
  }
}