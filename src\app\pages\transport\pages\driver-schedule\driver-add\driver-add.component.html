<div class="modal-title">
  <div>
    <h4>{{ title }}</h4>
  </div>
  <div>
    <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
  </div>
</div>

<form [formGroup]="driverFormGroup" (ngSubmit)="onSubmit()" [ngClass]="{ loading: isLoading }">
  <div class="content" *ngIf="!isLoading; else loaderTemplate">
    <section>
      <div class="row">
        <div class="col-lg-4 col-md-6 col-12">
          <label class="required">First Name</label>
          <input class="form-control" type="text" placeholder="Enter name" formControlName="firstName" />
          <app-error-messages [control]="driverFormGroup.controls.firstName"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label class="required">Last Name</label>
          <input class="form-control" type="text" placeholder="Enter name" formControlName="lastName" />
          <app-error-messages [control]="driverFormGroup.controls.lastName"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>Phone Number</label>
          <input class="form-control" type="text" placeholder="Enter phone number" formControlName="phoneNumber" [mask]="constants.phoneNumberMask" />
          <app-error-messages [control]="driverFormGroup.controls.phoneNumber"></app-error-messages>
          <span class="text-danger f-s-12" *ngIf="driverFormGroup.controls.phoneNumber.errors?.mask">Please enter valid phone number</span>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label class="required">Email</label>
          <input class="form-control" type="text" placeholder="Enter email" formControlName="email" />
          <app-error-messages [control]="driverFormGroup.controls.email"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label class="required">Employee Id</label>
          <input class="form-control" type="text" placeholder="Enter employee id" formControlName="employeeId" />
          <app-error-messages [control]="driverFormGroup.controls.employeeId"></app-error-messages>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label class="required">Role</label>
          <input class="form-control" type="text" placeholder="Enter role" formControlName="role" />
          <app-error-messages [control]="driverFormGroup.controls.role"></app-error-messages>
        </div>
      </div>
    </section>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
    <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!isEditMode" appShowLoaderOnApiCall>Save & Add New</button>
  </div>
</form>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
