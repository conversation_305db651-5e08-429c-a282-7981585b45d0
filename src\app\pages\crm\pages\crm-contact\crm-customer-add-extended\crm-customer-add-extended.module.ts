import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { AddNewMakeModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-make/add-new-make.module';
import { AddNewModelModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-model/add-new-model.module';
import { AddNewUnitTypeModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-unit-type/add-new-unit-type.module';
import { GoogleMapModule } from '@sharedComponents/google-map/google-map.component.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { AccordionModule } from 'primeng/accordion';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmContactCustomerAddModule } from '../../crm-customer/crm-contact-customer-add/crm-contact-customer-add.module';
import { CrmContactAddExtendedComponent } from './crm-customer-add-extended.component';

@NgModule({
  declarations: [CrmContactAddExtendedComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    SidebarModule,
    ReactiveFormsModule,
    FormsModule,
    TabViewModule,
    ConfirmPopupModule,
    DropdownModule,
    CardModule,
    TableModule,
    AccordionModule,
    CrmContactCustomerAddModule,
    AddNewModelModule,
    AddNewMakeModule,
    AddNewUnitTypeModule,
    GoogleMapModule,
    NgxGpAutocompleteModule,
    CalendarModule,
    InputNumberModule
  ],
  providers: [{
    provide: Loader,
    useValue: new Loader({
      apiKey: environment.googleMapApiKey,
      libraries: ['places']
    })
  }],
  exports: [CrmContactAddExtendedComponent]
})

export class CrmContactAddExtendedModule { }
