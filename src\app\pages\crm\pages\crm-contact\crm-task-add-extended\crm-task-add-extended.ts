import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Constants, MESSAGES, VALIDATION_CONSTANTS, icons } from '@constants/*';
import { AppToasterService, CommonService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ShopUser, UserListFilter, UserListItem } from '@pages/administration/models';
import { ContactDetails } from '@pages/administration/models/crm.model';
import { ShopService } from '@pages/administration/pages/pipeline-config/shop.service';
import { UserService } from '@pages/administration/pages/users/users.service';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { CustomerLeadListFilter, CustomerLeadListItem } from '@pages/crm/models/customer-lead.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { ActivityList, StockBasicInfo, TaskAttachment, TaskCreateParams, TaskListItem } from '@pages/shops/models';
import { TaskService } from '@pages/shops/services/tasks.service';
import * as saveAs from 'file-saver';
import { ConfirmationService } from 'primeng/api';
import { Observable, catchError, forkJoin, map, of, takeUntil, tap } from 'rxjs';
import { FileProperties, FileUploadProgress, IdNameModel, Page } from 'src/app/@shared/models';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-crm-task-add-extended',
  templateUrl: './crm-task-add-extended.html',
  styleUrls: ['./crm-task-add-extended.scss']
})
export class CrmTaskAddExtendedComponent extends BaseComponent implements OnInit {

  @Input() contactId!: any;
  title = 'Add Task';
  taskFormGroup!: FormGroup;
  hasDataBeenModified = false;
  isEditMode = false;
  taskTypes: IdNameModel[] = [];
  taskStatuses: IdNameModel[] = [];
  shops: IdNameModel[] = [];
  stockBasicInfo: StockBasicInfo[] = [];
  assignees: ShopUser[] = [];
  currentUser!: Account | null;
  showCreateActivityModal = false;
  fetchNewActivities = false;
  fileUploadProgresses: FileUploadProgress[] = [];
  taskFileUploadPath = 'Tasks Files';
  isUploadingFile = false;
  showAttachmentsTab = true;
  activityList = ActivityList;
  loaders: { [key: string]: boolean } = {
    taskTypes: false,
    taskStatuses: false,
    stockNumbers: false,
    shopUsers: false,
    previousOwnerName: false,
    customerLead: false
  }

  accordionTabs = {
    details: true,
    description: false,
    activity: true,
    attachments: true,
  }
  contactList: ContactDetails[] = [];
  taskInfo!: TaskListItem | null;
  @Input() taskId!: string | undefined;
  isViewMode!: boolean | null;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() filterParams: CustomerLeadListFilter = new CustomerLeadListFilter();
  customerLeadList: CustomerLeadListItem[] = [];
  users: UserListItem[] = [];
  isActiveTab = true;
  showCreateContact = false;
  maxFileSizeAllowed = MESSAGES.maxFileSizeAllowed;
  showEnlargedImage = false;
  viewImageScr?: string;

  constructor(private readonly fb: FormBuilder,
    private readonly taskService: TaskService,
    private readonly toasterService: AppToasterService,
    private readonly shopService: ShopService,
    private readonly authService: AuthService,
    private readonly fileUploadService: FileUploadService,
    private readonly confirmationService: ConfirmationService,
    private readonly commonService: CommonService,
    private readonly crmService: CrmService,
    private readonly cdf: ChangeDetectorRef,
    private readonly userService: UserService,
    private readonly activeRoute: ActivatedRoute
  ) { super() }

  ngOnInit(): void {
    if (this.isEditMode) {
      this.commonService.setBlockUI$(true);
      this.commonService.setBlockUI$(false);
    }
    this.initializeFormGroup();
    this.getCurrentUser();
    this.getAllMasterData();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.mode) {
          if (params?.mode === '2') {
            this.title = 'Edit Task';
            this.isEditMode = true;
          } else {
            this.title = 'View Task';
            this.isViewMode = true;
          }
        }
      });
  }

  private getAllMasterData() {
    forkJoin([
      this.getTaskTypes(),
      this.getTaskStatuses(),
      this.getStockNumbers(),
      this.getContactInfo(),
      this.getAllCustomerLead(),
      this.getAllAssignee(),
      this.getTaskInfo(this.taskId)
    ]).subscribe({
      next: () => {
        this.setTaskInfoInFormGroup();
      }
    });
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user && this.taskFormGroup) {
        this.currentUser = user;
        if (!this.isEditMode) {
          const reporterControl = this.taskFormGroup.get('reporterName');
          this.taskFormGroup.get('reporterId')?.setValue(user.id);
          reporterControl?.setValue(this.utils.getFullName(user.firstName, user.lastName));
          reporterControl?.disable();
        }
      }
    });
  }

  private getTaskInfo(taskId: string | undefined): Observable<TaskListItem | null> {
    if (!taskId) {
      return of(null);
    }
    return this.taskService.get<TaskListItem>(taskId).pipe(
      takeUntil(this.destroy$),
      catchError((error) => {
        this.commonService.setBlockUI$(false);
        return of(null);
      }),
      tap((task) => {
        if (task) {
          this.taskInfo = task;
        }
      })
    );
  }

  private initializeFormGroup(): void {
    this.taskFormGroup = this.fb.group({
      id: new FormControl(null),
      taskTypeId: new FormControl(null, [Validators.required]),
      taskStatusId: new FormControl(null, [Validators.required]),
      stockNumber: new FormControl(null),
      assigneeId: new FormControl(null, [Validators.required]),
      reporterId: new FormControl(null, [Validators.required]),
      reporterName: new FormControl(null),
      summary: new FormControl(null, [Validators.required, Validators.maxLength(VALIDATION_CONSTANTS.inputMaxLength)]),
      description: new FormControl(null, [Validators.maxLength(VALIDATION_CONSTANTS.descriptionMaxLength)]),
      startDate: new FormControl(null),
      endDate: new FormControl(null),
      pipelineId: new FormControl(),
      pipelineSequence: new FormControl(),
      crmContactId: new FormControl(this.contactId ?? null, [Validators.required]),
      activity: new FormControl(null, [Validators.required]),
      customerLeadId: new FormControl(null),
    });
    this.taskFormGroup.get('taskTypeId')?.disable();
  }

  get taskInfoCreateParams(): TaskCreateParams {
    return {
      ...this.taskFormGroup.getRawValue(),
      id: this.taskInfo?.id,
      taskAttachments: this.taskAttachments,
      unitId: this.selectedUnitId,
      phaseId: this.taskInfo?.phaseId,
      pipelineId: this.taskInfo?.pipelineId
    };
  }

  get taskAttachments(): TaskAttachment[] {
    let taskAttachments: TaskAttachment[] = this.fileUploadProgresses.map(fileProgress => ({ url: fileProgress.uploadedFileUrl }));
    if (this.isEditMode && this.taskInfo?.taskAttachments?.length) {
      taskAttachments = [...this.taskInfo?.taskAttachments, ...taskAttachments];
    }
    return taskAttachments;
  }

  get selectedUnitId(): number | undefined {
    const stockNumber = this.taskFormGroup.getRawValue().stockNumber;
    return this.stockBasicInfo.find(stock => stock.stockNumber === stockNumber)?.unitId;
  }

  get isFileUploadInProgress(): boolean {
    if (this.fileUploadProgresses.some(fileProgress => fileProgress.progress$.getValue() < 100)) {
      return true;
    }
    return false;
  }

  onSubmit(close = true): void {
    if (this.isViewMode) {
      this.taskFormGroup.enable();
      this.isViewMode = false;
      const reporterControl = this.taskFormGroup.get('reporterName');
      reporterControl?.disable();
      this.taskFormGroup.get('stockNumber')?.disable();
      this.taskFormGroup.get('assigneeId')?.disable();
      this.taskFormGroup.get('taskTypeId')?.disable();
      return;
    }
    if (this.taskFormGroup.invalid) {
      this.taskFormGroup.markAllAsTouched();
      return;
    }
    if (this.isFileUploadInProgress) {
      this.toasterService.warning(MESSAGES.fileUploadInProgress);
      return;
    }
    this.taskFormGroup.get('startDate')?.setValue(this.taskFormGroup.controls.endDate.value);
    if (this.isEditMode) {
      this.editTask();
    } else {
      this.saveTask(close);
    }
  }

  saveTask(close = true): void {
    this.taskService.add(this.taskInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.shopTaskUpdateSuccess : MESSAGES.shopTaskAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.taskFormGroup.reset();
        this.getCurrentUser();
        if (!this.isEditMode) {
          for (const taskType of this.taskTypes) {
            if (taskType.name === 'Sales') {
              this.taskFormGroup.get('taskTypeId')?.setValue(taskType.id);
            }
          }
          for (const taskStatuse of this.taskStatuses) {
            if (taskStatuse.name === 'To Do') {
              this.taskFormGroup.patchValue({ taskStatusId: taskStatuse.id });
            }
          }
          this.taskFormGroup.get('reporterId')?.setValue(this.currentUser?.id);
          this.taskFormGroup.get('crmContactId')?.setValue(this.contactId);
          this.fileUploadProgresses = [];
        }
      }
    });
  }

  editTask(): void {
    this.taskService.update(this.taskInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.shopTaskUpdateSuccess : MESSAGES.shopTaskAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  async setTaskInfoInFormGroup(): Promise<void> {
    if (this.taskInfo) {
      this.taskFormGroup.patchValue({
        taskTypeId: this.taskInfo?.taskType?.id,
        taskStatusId: this.taskInfo?.taskStatus?.id,
        summary: this.taskInfo?.summary,
        description: this.taskInfo?.description,
        unitId: this.taskInfo?.unitId,
        stockNumber: this.taskInfo?.stockNumber,
        startDate: this.taskInfo?.startDate ? new Date(`${this.taskInfo?.startDate}`) : '',
        endDate: this.taskInfo?.endDate ? new Date(`${this.taskInfo?.endDate}`) : '',
        crmContactId: this.taskInfo?.crmContact?.id,
        customerLeadId: this.taskInfo?.customerLeadId,
        activity: this.taskInfo?.activity,
        assigneeId: this.users?.find(a => a.id === this.taskInfo?.assignee?.id)?.id,
        reporterId: this.taskInfo?.reporter?.id,
        reporterName: this.taskInfo?.reporter?.name,
      });
      this.taskFormGroup.get('reporterName')?.disable();
      if (this.isEditMode) {
        this.taskFormGroup.get('stockNumber')?.disable();
        this.taskFormGroup.get('assigneeId')?.disable();
      }
      if (this.isViewMode) {
        this.taskFormGroup.disable();
      }
    }
    this.commonService.setBlockUI$(false);
  }

  private getTaskTypes(): Observable<IdNameModel[]> {
    this.loaders.taskTypes = true;
    return this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.types).pipe(
      takeUntil(this.destroy$),
      tap((taskTypes) => {
        this.taskTypes = taskTypes;
        this.loaders.taskTypes = false;
        if (!this.isEditMode) {
          for (const taskType of this.taskTypes) {
            if (taskType.name === 'Sales') {
              this.taskFormGroup.get('taskTypeId')?.setValue(taskType.id);
            }
          }
        }
      }),
      catchError((error) => {
        this.loaders.taskTypes = false;
        return of([]);
      })
    );
  }

  private getTaskStatuses(): Observable<IdNameModel[]> {
    this.loaders.taskStatuses = true;
    return this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.statuses).pipe(
      takeUntil(this.destroy$),
      tap((taskStatuses) => {
        this.taskStatuses = taskStatuses;
        if (!this.isEditMode) {
          for (const taskStatus of this.taskStatuses) {
            if (taskStatus.name === 'To Do') {
              this.taskFormGroup.patchValue({ taskStatusId: taskStatus.id });
            }
          }
        }
        this.loaders.taskStatuses = false;
      }),
      catchError((error) => {
        this.loaders.taskStatuses = false;
        return of([]);
      })
    );
  }


  private getContactInfo(): Observable<ContactDetails[]> {
    this.loaders.previousOwnerName = true;
    this.commonService.setBlockUI$(true);
    return this.crmService.getList<ContactDetails>(API_URL_UTIL.admin.crm.contact).pipe(
      takeUntil(this.destroy$),
      tap((contactList) => {
        this.contactList = contactList;
        this.loaders.previousOwnerName = false;
        this.commonService.setBlockUI$(false);
        this.cdf.detectChanges();
      }),
      catchError((error) => {
        this.loaders.previousOwnerName = false;
        this.commonService.setBlockUI$(false);
        this.cdf.detectChanges();
        return of([]);
      })
    );
  }


  private getStockNumbers(): Observable<StockBasicInfo[]> {
    this.loaders.stockNumbers = true;
    return this.taskService.getList<StockBasicInfo>(API_URL_UTIL.tasks.stockNumbers).pipe(
      takeUntil(this.destroy$),
      tap((stockNumbers) => {
        this.stockBasicInfo = stockNumbers;
        this.loaders.stockNumbers = false;
      }),
      catchError((error) => {
        this.loaders.stockNumbers = false;
        return of([]);
      })
    );
  }

  onFileSelect(event: any) {
    for (const file of event.target.files) {
      if (file.size > this.constants.fileSize) {
        this.toasterService.warning(MESSAGES.fileUploadMessage)
      }
      else {
        if (event.target?.files?.length) {
          const extensionDot = file?.name?.lastIndexOf('.');
          const ext = file?.name?.substring(extensionDot + 1);
          if (!Constants.allowedImgFormats.includes(ext)) {
            this.toasterService.error(MESSAGES.fileTypeNotSupported);
            return;
          }
          this.uploadImage(file);
        }
      }
    }
  }

  uploadImage(file: File): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, this.taskFileUploadPath, this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.fileUploadSuccess);
        },
        error: err => {
          this.toasterService.error(MESSAGES.fileUploadError);
        }
      })
  }

  downloadImage(taskAttachment: TaskAttachment): void {
    if (taskAttachment?.url) {
      this.fileUploadService.downloadFile(taskAttachment.url, this.getFileName(taskAttachment.url)).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/image" }), this.getFileName(taskAttachment.url));
        }
      });
    }
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  deleteImageFromCloud(imageUrl: string, fileIndex: number, callbackFn: Function): void {
    this.fileUploadService.deleteFile([imageUrl]).pipe(takeUntil(this.destroy$)).subscribe(() => {
      callbackFn(fileIndex);
    })
  }

  removeFileFromUpload(fileIndex: number): void {
    const spliceImageFunction = () => {
      this.fileUploadProgresses.splice(fileIndex, 1);
    }
    const fileProgress = this.fileUploadProgresses[fileIndex];
    if (fileProgress.uploadedFileUrl) {
      this.deleteImageFromCloud(fileProgress.uploadedFileUrl, fileIndex, spliceImageFunction);
    }
  }

  onDelete(attachmentId: number | undefined, event: Event): void {
    if (!attachmentId) {
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'attachment'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(attachmentId);
      }
    });
  }

  onDeleteConfirmation(attachmentId: number): void {
    this.taskService.deleteTaskAttachment(attachmentId).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.photoDeleteSuccess);
      const index = this.taskInfo?.taskAttachments.findIndex(attachment => attachment.id === attachmentId);
      if (index !== undefined && index > -1) {
        this.taskInfo?.taskAttachments.splice(index, 1);
      }
    });
  }

  getFileName(fileUrl: string | undefined): string {
    if (!fileUrl) {
      return '';
    }
    return fileUrl.split('_')[1];
  }

  hideShowAttachmentsTab() {
    this.showAttachmentsTab = !this.taskInfo?.taskAttachments?.length ? false : true;
  }

  openContactModel(): void {
    this.showCreateContact = true
  }

  private fetchData<T>(
    loaderKey: string,
    serviceCall: Observable<Page<T>>,
    setContent: (content: T[]) => void
  ): Observable<T[]> {
    this.isLoading = true;
    this.loaders[loaderKey] = true;
    this.filterParams.orderBy = this.orderBy;

    return serviceCall.pipe(
      takeUntil(this.destroy$),
      map((res) => {
        setContent(res.content);
        this.setPaginationParamsFromPageResponse<T>(res);
        this.isLoading = false;
        this.loaders[loaderKey] = false;
        this.cdf.detectChanges();
        return res.content;
      }),
      catchError((error) => {
        this.isLoading = false;
        this.loaders[loaderKey] = false;
        this.cdf.detectChanges();
        return of([]);
      })
    );
  }

  private getAllCustomerLead(): Observable<CustomerLeadListItem[]> {
    return this.fetchData<CustomerLeadListItem>(
      'customerLead',
      this.crmService.getListWithFiltersWithPagination<CustomerLeadListFilter, CustomerLeadListItem>(
        this.filterParams,
        this.paginationConfig.page,
        this.paginationConfig.itemsPerPage,
        API_URL_UTIL.admin.crm.customerFilter
      ),
      (content) => { this.customerLeadList = content; }
    );
  }

  private getAllAssignee(): Observable<UserListItem[]> {
    return this.fetchData<UserListItem>(
      'shopUsers',
      this.userService.getListWithFiltersWithPagination<UserListFilter, UserListItem>(
        this.filterParams,
        this.paginationConfig.page,
        this.paginationConfig.itemsPerPage,
        API_URL_UTIL.admin.users.list
      ),
      (content) => {
        this.users = content;
        this.setActiveFlagForAll();
      }
    );
  }

  private setActiveFlagForAll() {
    this.users.forEach(user => user.status = this.isActiveTab);
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateContact = false;
    if (refreshList) {
      this.getContactInfo();
    }
  }

  onViewImage(fileUrl: string | undefined) {
    this.showEnlargedImage = true;
    if (fileUrl) {
      this.viewImageScr = fileUrl;
    }
  }

  isOptionDisabled(option: { archived: boolean; id: number }): boolean {
    const selectedValue = this.taskFormGroup.get('crmContactId')?.value;
    return option.archived && option.id !== selectedValue;
  }
}
