import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { ChangePasswordParam, UserListItem } from '@pages/administration/models';
import { takeUntil } from 'rxjs';
import { matchValidator } from 'src/app/@shared/validators';
import { UserService } from '../users.service';

@Component({
  selector: 'app-user-change-password',
  templateUrl: './user-change-password.component.html',
  styleUrls: ['./user-change-password.component.scss']
})
export class UserChangePasswordComponent extends BaseComponent implements OnInit {

  passwordFormGroup!: UntypedFormGroup

  @Input() userInfo!: UserListItem | null;
  @Output() onClose: EventEmitter<void> = new EventEmitter<void>();

  constructor(private readonly fb: UntypedFormBuilder,
    private readonly userService: UserService,
    private readonly toasterService: AppToasterService) {
    super();
  }

  ngOnInit(): void {
    this.initializeFormGroup();
  }

  initializeFormGroup(): void {
    this.passwordFormGroup = this.fb.group({
      newPassword: new UntypedFormControl('', [Validators.required, Validators.minLength(1), Validators.maxLength(100), matchValidator('confirmPassword', true)]),
      confirmPassword: new UntypedFormControl('', [Validators.required, matchValidator('newPassword')]),
    });
  }

  get passwordFinishParams(): ChangePasswordParam {
    return {
      newPassword: this.passwordFormGroup.controls['newPassword']?.value,
      userId: this.userInfo?.id as number
    }
  }

  onCancel(): void {
    this.onClose.emit();
  }

  onSubmit(): void {
    this.userService.changeUserPassword(this.passwordFinishParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.changePasswordSuccess);
      this.onCancel();
    });
  }

}
