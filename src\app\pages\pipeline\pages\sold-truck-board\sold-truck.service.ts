import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { progressColorList, returnPipelineStatusList, SoldStockPipelineStatusList } from '@constants/*';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { PipelineConfigListItem, PipelineGetDetail, SoldTruckBoardListItem } from '@pages/pipeline/models/sold-truck-board.model';
import { Observable } from 'rxjs';
import { IdNameModel, RoleNames } from 'src/app/@shared/models';

@Injectable({
  providedIn: 'root'
})
export class SoldTruckService extends BaseCrudService {

  constructor(protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  getBaseAPIPath(): string {
    return API_URL_UTIL.pipeline.root;
  }

  fromServerModel(json: SoldTruckBoardListItem): SoldTruckBoardListItem {
    if (!json) {
      return new SoldTruckBoardListItem();
    }
    return new SoldTruckBoardListItem(json);
  }

  getDealerByTemplate(): Observable<PipelineConfigListItem[]> {
    return this.httpClient.get<PipelineConfigListItem[]>(`template-pipelines`);
  }

  getPipelineDetailById(id: number): Observable<PipelineGetDetail> {
    return this.httpClient.get<PipelineGetDetail>(`${API_URL_UTIL.pipeline.pipelineDetail}/${id}`);
  }

  getPipelineOwnerList(): Observable<IdNameModel[]> {
    const endpoint = API_URL_UTIL.admin.users.role.replace(':roleName', RoleNames.ROLE_SALESPERSON);
    return this.httpClient.get<IdNameModel[]>(`${endpoint}`);
  }

  getStatusName(status: string): string | null {
    if (status === SoldStockPipelineStatusList.waiting_on_parts) {
      return returnPipelineStatusList.waiting_on_parts;
    }
    else if (status === SoldStockPipelineStatusList.completed) {
      return returnPipelineStatusList.completed;
    }
    else if (status === SoldStockPipelineStatusList.in_staging) {
      return returnPipelineStatusList.in_staging;
    }
    else if (status === SoldStockPipelineStatusList.schedule) {
      return returnPipelineStatusList.schedule;
    }
    else {
      return null;
    }
  }

  getTaskProgressClass(progress: number): string {
    if (progress > 100) {
      return progressColorList.red;
    } else if (progress >= 80) {
      return progressColorList.green;
    } else if (progress < 80 && progress >= 0) {
      return progressColorList.blue;
    }
    return progressColorList.placeholder;
  }
}
