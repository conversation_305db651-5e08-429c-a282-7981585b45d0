@import "src/assets/scss/variables";

.m-t-10 {
  margin-top: 10px;
}

.m-t-20 {
  margin-top: 20px;
}

.space-between {
  justify-content: space-between;
}

.m-r-10 {
  margin-right: 10px;
}

.add-driver-btn {
  height: 25px;
  padding: 0px 5px;
  float: right;
  margin: 3px;
}

.btn-driver {
  margin-top: -10px;
}

::ng-deep .driver-schedule-add {
  .p-disabled,
  .p-component:disabled,
  textarea:disabled {
    color: $disable-color;
    opacity: 1;
  }
  .p-multiselect {
    width: -webkit-fill-available;
  }
  .multi-select-dropdown {
    position: relative;
    .cross-icon {
      position: absolute;
      right: 51px;
      top: 50%;
      transform: translateY(-40%);
      font-size: 20px;
      color: #6c757d;
    }
  }

  .p-disabled .p-dropdown-label,
  .p-component:disabled .p-dropdown-label {
    color: $disable-color;
  }

  .drop-zone-disabled {
    color: $disable-color;
    background-color: $disable-background-color !important;
    opacity: 1;
  }
}

.title {
  color: var(--light-black-color);
  font-weight: 500 !important;
  font-size: 16px !important;
}

.m-l-10 {
  margin-left: 10px;
}

.f-s-12 {
  font-size: 12px;
}

.map-icon {
  button {
    display: flex;
    flex-direction: row-reverse;
    height: 100%;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    scroll-margin-bottom: 100px;

    img {
      margin-right: 10px;
    }
  }
}

.header-title {
  top: -8px;
  position: relative;
}

.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
  position: absolute;
  top: 30px;
}

.bold-text {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

.stock-title-rb {
  margin-right: 75px;
  margin-bottom: 8px;
}

.pb-8 {
  padding-top: 8px;
}

.files {
  width: 600px;
  padding: 12px 40px;
  margin-top: 10px;
  border: 1px solid #add2ff;
  background-color: #f6f9fd;
}

.file-progress {
  width: 95%;
  margin-left: 40px;
}

.file-box {
  .btn {
    height: auto;
    padding: 0;
  }

  img {
    height: 55px;
  }
}

.file-box-wrapper {
  display: flex;
  align-items: center;
}

.drop-zone__input {
  height: auto;
  width: 210px;
  z-index: 10;
}

.upload-button {
  padding: 0 10px;
  margin-bottom: 10px;

  img {
    height: 18px;
    margin: 0px 10px 3px;
  }
}

.view-icon {
  height: 15px;
  margin-left: 9px;
  width: 20px;
}

.cursor-pointer {
  cursor: pointer;
}

.driver-schedule {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

textarea {
  height: auto !important;
}

@media only screen and (max-width: 860px) {
  .upload-button {
    padding: 0 10px;
    margin-bottom: 10px;
    float: none;
    img {
      height: 20px;
      margin: 5px 16px;
    }
  }
}

.load-more {
  color: var(--text-color);
  text-decoration: underline;
  text-align: center;
  cursor: pointer;
}