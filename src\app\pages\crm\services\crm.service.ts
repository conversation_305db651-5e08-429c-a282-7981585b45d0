import { Injectable } from '@angular/core';
import { CustomerLeadStatusList, returnCustomerLeadStatusList } from '@constants/*';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';

@Injectable({
  providedIn: 'root'
})
export class CrmService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.crm.root;
  }

  getStatusName(status: string): string | null {
    if (status === CustomerLeadStatusList.open) {
      return returnCustomerLeadStatusList.open;
    }
    else if (status === CustomerLeadStatusList.closed) {
      return returnCustomerLeadStatusList.closed;
    }
    else if (status === CustomerLeadStatusList.deal) {
      return returnCustomerLeadStatusList.deal;
    }
    else {
      return null;
    }
  }

}
