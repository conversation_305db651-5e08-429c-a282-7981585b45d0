import { ChangeDetectorRef, Component, ElementRef, HostListener, OnInit, TemplateRef } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { NavigationService } from '@core/services';
import { BaseComponent, ROUTER_UTILS } from '@core/utils';
import { MenuConfig } from '@core/utils/menu-config';
import { AuthService } from '@pages/auth/services/auth.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Observable, Subscription, takeUntil } from 'rxjs';
import { DropdownRouteName, FirebaseRegistrationParams, UnreadNotificationCount } from 'src/app/@shared/models';
import { MessagingService } from 'src/app/@shared/services';
import { ModalService } from 'src/app/@shared/services/modal.service';

@Component({
  selector: 'app-mobile-header',
  templateUrl: './mobile-header.component.html',
  styleUrls: ['./../header/header.component.scss', './mobile-header.component.scss']
})

export class MobileHeaderComponent extends BaseComponent implements OnInit {
  dropdownOpts = DropdownRouteName;
  currentActiveParentRoute!: DropdownRouteName;
  isCollapsed = true;
  menuConfig = MenuConfig;
  // NOTE: The following modules are currently disabled per client's request.
  // rightMenuConfig = RightHeaderMenu;
  notificationModalRef!: BsModalRef;
  showModal = false;
  unreadNotificationCount!: number;
  isLoggedIn$!: Observable<boolean>;
  currentUserId!: number;
  subscription!: Subscription;
  isIOS = false;

  constructor(private readonly router: Router,
    private readonly authService: AuthService,
    private readonly cdf: ChangeDetectorRef,
    public readonly navigationService: NavigationService,
    public readonly messagingService: MessagingService,
    private readonly elementRef: ElementRef,
    public readonly modalService: ModalService) {
    super()
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.setActiveRoute();
    this.isIOS = this.isDeviceIOS();
  }

  private isDeviceIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  onClickLogOut(): void {
    if (this.currentUser?.id) {
      const tokenIdModel: FirebaseRegistrationParams = {
        recipientId: this.currentUser.id.toString(),
        token: localStorage.getItem(this.constants.firebase.registrationToken) as string
      }
      this.messagingService.unregisterToken(tokenIdModel).pipe(takeUntil(this.destroy$)).subscribe();
      this.authService.logOut();
      const { root, login } = ROUTER_UTILS.config.auth;
      this.router.navigate(['/', root, login]);
    }
  }

  async getCurrentUser(): Promise<void> {
    if (!this.authService.currentUser) {
      this.authService.getCurrentUser(true).pipe(takeUntil(this.destroy$)).subscribe(res => {
        this.currentUser = res;
        this.cdf.detectChanges();
      });
    }
    this.authService.getCurrentUser$().pipe(takeUntil(this.destroy$)).subscribe(res => {
      this.currentUser = res;
      this.cdf.detectChanges();
    });
  }

  setActiveRoute(): void {
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.currentActiveParentRoute = DropdownRouteName.NO_PARENT_ROUTE;
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.administration.root}${ROUTER_UTILS.config.base.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.ADMIN;
        }
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.pipeline.root}${ROUTER_UTILS.config.base.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.PIPELINE;
        }
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.transport.root}${ROUTER_UTILS.config.base.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.TRANSPORT;
        }
        if (event.url.includes(`${ROUTER_UTILS.config.base.root}${ROUTER_UTILS.config.crm.root}${ROUTER_UTILS.config.base.root}`)) {
          this.currentActiveParentRoute = DropdownRouteName.CRM;
        }
        this.cdf.detectChanges();
        // add other conditions here when more parent routes come.
      }
    });
  }

  openNotifications(template: TemplateRef<unknown>): void {
    this.showModal = true;
    this.notificationModalRef = this.modalService.showModal(template, { ...this.constants.globalModalConfig, ...this.constants.sidebarModalConfig });
  }

  refreshUnreadNotificationCount(): void {
    this.messagingService.unreadNotificationCount.asObservable().subscribe(() => {
      this.getNotificationCount();
    });
  }

  getNotificationCount(): void {
    this.messagingService.getNotificationCount(this.currentUserId).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: UnreadNotificationCount) => {
        this.unreadNotificationCount = res.unreadCount;
        this.cdf.detectChanges();
      }
    });
  }

  decrementUnreadCount(): void {
    if (this.unreadNotificationCount) {
      this.unreadNotificationCount -= 1;
    }
  }

  setNotificationUnreadCountToZero(): void {
    if (this.unreadNotificationCount) {
      this.unreadNotificationCount = 0;
    }
  }

  onCloseClick() {
    this.showModal = false;
  }

  @HostListener('document:click', ['$event', '$event.target'])
  onClick(event: MouseEvent, targetElement: HTMLElement): void {
    if (!targetElement) {
      return;
    }

    const clickedInside = this.elementRef.nativeElement.contains(targetElement);
    if (!clickedInside) {
      this.isCollapsed = true;
    }
  }

}
