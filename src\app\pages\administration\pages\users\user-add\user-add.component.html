<div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
  <h4 class="header-title">{{ title }}</h4>
  <span *ngIf="isEditMode" class="created-by">
    <span class="bold-text">#{{ userInfo?.firstName }}&nbsp;{{ userInfo?.lastName }}</span>
    Created By <span class="bold-text">{{ userInfo?.createdBy?.name }}</span> on
    {{ userInfo?.createdDate | date: constants.fullDateFormat }}
  </span>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>

<form (ngSubmit)="onSubmit()">
  <div class="content">
    <section [formGroup]="userFormGroup" class="card p-3">
      <div class="title">
        <h4>Basic Details</h4>
      </div>
      <div class="row content">
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Email</label>
          <input class="form-control" type="email" placeholder="Enter user email" formControlName="email" [readOnly]="isEditMode && isEmailDisabled" appEmailValidation />
          <app-error-messages [control]="userFormGroup.controls.email"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">First Name</label>
          <input class="form-control" type="text" placeholder="Enter user's first name" formControlName="firstName" />
          <app-error-messages [control]="userFormGroup.controls.firstName"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Last Name</label>
          <input class="form-control" type="text" placeholder="Enter user's last name" formControlName="lastName" />
          <app-error-messages [control]="userFormGroup.controls.lastName"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label>Phone number</label>
          <input class="form-control" type="text" placeholder="Enter phone number" [mask]="contactNoFormat" formControlName="phoneNumber" />
          <app-error-messages [control]="userFormGroup.controls.phoneNumber"></app-error-messages>
          <span class="text-danger f-s-12" *ngIf="userFormGroup.controls.phoneNumber.errors?.mask">Please enter valid phone number</span>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label>Employee ID</label>
          <input class="form-control" type="text" placeholder="Enter employee id" formControlName="employeeId" />
          <app-error-messages [control]="userFormGroup.controls.employeeId"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12 user-role" formGroupName="roleDto">
          <label class="required">Roles</label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="roles"
            formControlName="id"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select role"
          >
          </p-dropdown>
          <app-error-messages [control]="roleFormGroup.controls.id"> </app-error-messages>
        </div>
      </div>
    </section>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
    <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!isEditMode" appShowLoaderOnApiCall>Save & Add New</button>
  </div>
</form>
