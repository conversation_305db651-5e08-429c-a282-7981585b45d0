<div>
  <div class="row add-message" *appHasPermission="[permissionActions.CREATE_COMMUNICATION]">
    <form class="form" [formGroup]="commentFormGroup" [ngClass]="{ loading: isLoading }">
      <div class="row">
        <div class="col-11 m-t-30">
          <p-editor
            #editor
            appMentionPosition
            [mention]="availableMentionUsers"
            (itemSelected)="mentionSelected($event)"
            [mentionConfig]="mentionConfig"
            formControlName="message"
            (onTextChange)="textChange($event)"
            (onSelectionChange)="selectionChange($event)"
            (onInit)="selectionChange($event)"
            [readonly]="isViewMode"
          >
            <ng-template pTemplate="header">
              <span class="ql-formats">
                <button type="button" #bold class="ql-bold" aria-label="Bold"></button>
                <button type="button" class="ql-italic" aria-label="Italic"></button>
                <button type="button" class="ql-underline" aria-label="Underline"></button>
              </span>
            </ng-template>
          </p-editor>
          <app-error-messages [control]="commentFormGroup.controls.message"></app-error-messages>
        </div>
        <div class="col-1" *ngIf="!isViewMode">
          <button
            class="button button5"
            (click)="addNewParentCommentSubmit()"
            appShowLoaderOnApiCall
            *appHasPermission="[permissionActions.CREATE_COMMUNICATION]"
          >
            <em class="pi pi-reply"></em>
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<div>
  <form [formGroup]="addedCommentsForm">
    <ng-container *ngIf="!isFirstTime">
      <ng-container formArrayName="parent">
        <div *ngFor="let comments of commentsFormArray.controls; let parentIndex = index">
          <div class="row inventory-comments" [formGroupName]="parentIndex">
            <div class="col-1 initials-value">
              <p-avatar
                [label]="utils.getInitials(comments?.value.createdBy.firstName, comments?.value.createdBy.lastName) | uppercase"
                styleClass="p-mr-2"
                size="xlarge"
                shape="circle"
              >
              </p-avatar>
            </div>
            <div class="col-11 card-content">
              <p-card [ngClass]="{ 'highlight-comment': selectedCommentId === comments?.value?.id }">
                <div class="user-details">
                  <div>
                    <h5>{{ comments?.value.createdBy.firstName }} {{ comments?.value.createdBy.lastName }}</h5>
                  </div>
                  <div class="ml-0">
                    <span>{{ comments?.value.modifiedDate | date: constants.fullDateFormat }}</span>
                  </div>
                </div>
                <div class="d-flex space-between">
                  <div class="users-comment w-90">
                    <span *ngIf="!comments?.value?.editable" [innerHTML]="comments?.value.message"></span>{{ comments?.value?.editable }}
                    <div *ngIf="comments?.value?.editable">
                      <input
                        class="form-control"
                        type="text"
                        placeholder="Write an Update..."
                        [mention]="availableMentionUsers"
                        (itemSelected)="mentionSelected($event)"
                        [mentionConfig]="mentionConfig"
                        formControlName="message"
                      />
                    </div>
                  </div>
                  <div *ngIf="!isViewMode && currentUserId === comments?.value?.createdBy?.id">
                    <img
                      [src]="constants.staticImages.icons.deleteIcon"
                      (click)="onDelete(comments, $event)"
                      alt="delete-icon"
                      *appHasPermission="[permissionActions.DELETE_COMMUNICATION]"
                    />
                  </div>
                </div>
                {{ comments.value.showMultipleComments }}
                <div *ngIf="comments.value.childComments.length">
                  <p-divider></p-divider>
                </div>
                <ng-container formArrayName="childComments">
                  <div
                    *ngFor="
                      let child of getChildCommentsFormArrayByParentCommunicationId(parentIndex)?.controls;
                      let childCommentIndex = index;
                      trackBy: trackByFunction
                    "
                  >
                    <div
                      class="row inventory-child-comments"
                      [formGroupName]="childCommentIndex"
                      *ngIf="comments.value.childComments.length < 1 || comments?.value.isPrevieClicked"
                    >
                      <div class="col-1 initials-value">
                        <p-avatar
                          [label]="utils.getInitials(child?.value.createdBy.firstName, child?.value.createdBy.lastName) | uppercase"
                          styleClass="p-mr-2"
                          size="xlarge"
                          shape="circle"
                        >
                        </p-avatar>
                      </div>
                      <div class="col-11 card-content">
                        <div class="user-details">
                          <div>
                            <h5>{{ child?.value.createdBy.firstName }} {{ child?.value.createdBy.lastName }}</h5>
                          </div>
                          <div>
                            <span>{{ child?.value.modifiedDate | date: constants.fullDateFormat }}</span>
                          </div>
                        </div>
                        <div class="d-flex space-between">
                          <div class="users-comment">
                            <span [innerHTML]="child?.value.message"></span>
                          </div>
                          <div *ngIf="!isViewMode && currentUserId === child?.value?.createdBy?.id">
                            <img
                              [src]="constants.staticImages.icons.deleteIcon"
                              (click)="onDelete(child, $event)"
                              alt=""
                              *appHasPermission="[permissionActions.DELETE_COMMUNICATION]"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div *ngIf="!comments?.value.isPrevieClicked">
                      <div
                        *ngIf="
                          (comments.value.childComments.length - 1 === childCommentIndex && comments.value.childComments.length > 1) ||
                          comments.value.childComments.length === 1
                        "
                        class="row inventory-child-comments"
                        [formGroupName]="childCommentIndex"
                      >
                        <a
                          class="preview-more"
                          (click)="setisPrevieClickedValue(parentIndex)"
                          *ngIf="comments.value.childComments.length !== 1"
                          >View Previous Updates</a
                        >
                        <div class="col-1 initials-value">
                          <p-avatar
                            [label]="utils.getInitials(child?.value.createdBy.firstName, child?.value.createdBy.lastName) | uppercase"
                            styleClass="p-mr-2"
                            size="xlarge"
                            shape="circle"
                          >
                          </p-avatar>
                        </div>
                        <div class="col-11 card-content">
                          <div class="user-details">
                            <div>
                              <h5>{{ child?.value.createdBy.firstName }} {{ child?.value.createdBy.lastName }}</h5>
                            </div>
                            <div>
                              <span>{{ child?.value.modifiedDate | date: constants.fullDateFormat }}</span>
                            </div>
                          </div>
                          <div class="d-flex space-between">
                            <div class="users-comment">
                              <span *ngIf="!isNoteEditing" [innerHTML]="child?.value.message"></span>
                              <input
                                *ngIf="child?.value?.editable"
                                class="form-control"
                                type="text"
                                placeholder="Write an Update..."
                                [mention]="availableMentionUsers"
                                (itemSelected)="mentionSelected($event)"
                                [mentionConfig]="mentionConfig"
                                formControlName="childMessage"
                              />
                            </div>
                            <div *ngIf="!isViewMode && currentUserId === child?.value?.createdBy?.id">
                              <img
                                [src]="constants.staticImages.icons.deleteIcon"
                                (click)="onDelete(child, $event)"
                                alt=""
                                *appHasPermission="[permissionActions.DELETE_COMMUNICATION]"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
                <div class="row child-comments" *appHasPermission="[permissionActions.CREATE_COMMUNICATION]">
                  <div class="col-11">
                    <p-editor
                      #editorChild
                      appMentionPosition
                      id="{{ parentIndex }}"
                      [mention]="availableMentionUsers"
                      (itemSelected)="mentionSelected($event, parentIndex)"
                      [mentionConfig]="mentionConfig"
                      formControlName="childMessage"
                      (onTextChange)="childTextChange($event, parentIndex)"
                      (onSelectionChange)="selectionChildChange($event, parentIndex)"
                      (onInit)="selectionChildChange($event, parentIndex)"
                      [readonly]="isViewMode"
                    >
                      <ng-template pTemplate="header">
                        <span class="ql-formats">
                          <button type="button" #boldChild class="ql-bold" aria-label="Bold"></button>
                          <button type="button" class="ql-italic" aria-label="Italic"></button>
                          <button type="button" class="ql-underline" aria-label="Underline"></button>
                        </span>
                      </ng-template>
                    </p-editor>

                    <app-error-messages [control]="getChildCommentFormControl(parentIndex)"> </app-error-messages>
                  </div>
                  <div class="col-1" *ngIf="!isViewMode">
                    <button
                      class="button button5"
                      (click)="addNewChildCommentSubmit(parentIndex, comments.value.id)"
                      appShowLoaderOnApiCall
                    >
                      <em class="pi pi-reply"></em>
                    </button>
                  </div>
                </div>
              </p-card>
            </div>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </form>
</div>
