import { TitleCasePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ContactDetails } from '@pages/administration/models/crm.model';
import { CustomerCreateParams, CustomerStatusList } from '@pages/crm/models/customer-lead.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { InventorySpecification, InventorySpecificationResponse, ModelType } from '@pages/inventory/models';
import { InventorySpecificationService } from '@pages/inventory/services';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { CommonService } from 'src/app/@shared/services/common.service';

@Component({
  selector: 'app-crm-customer-add-extended',
  templateUrl: './crm-customer-add-extended.component.html',
  styleUrls: ['./crm-customer-add-extended.component.scss']
})
export class CrmContactAddExtendedComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Customer Lead';
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() crmId!: string | undefined;
  accordionTabs = {
    contactInfo: true,
    inventoryPreferenceInfo: true,
    salePersonInfo: true,
    specificationPreferenceInfo: true
  };

  loaders = {
    makes: false,
    models: false,
    categories: false,
    years: false,
    status: false,
    unitTypes: false,
    designations: false,
    vendors: false,
    previousOwnerName: false,
    lotLocation: false,
    currentLocation: false,
    internetGroup: false,
    ownedBy: false,
    categoryType: false
  };
  customerLeadFormGroup!: FormGroup;
  contactList: ContactDetails[] = [];
  displayContact!: ContactDetails | undefined;
  makes: IdNameModel[] = [];
  models: IdNameModel[] = [];
  years: IdNameModel[] = [];
  unitTypes: IdNameModel[] = [];
  designations: IdNameModel[] = [];
  categoryTypes: IdNameModel[] = [];
  modelPopups = {
    showCreateVendor: false,
    showCreateContact: false,
    showCreateModel: false,
    showCreateMake: false,
    showCreateUnitType: false,
    showPipelineDetails: false,
  };
  customerStatusList = CustomerStatusList;
  hasDataBeenModified = false;
  customerDetails!: any;
  showGoogleMapSideBar = false;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  fullAddress!: string;
  matchedCount!: number;
  categoryId!: number;
  selectedMakeId!: number;
  inventorySpecificationForm!: Array<InventorySpecification>;
  isSpecificationLoading = false;
  redirectUrl!: string;

  constructor(
    private readonly fb: FormBuilder,
    private readonly commonSharedService: CommonSharedService,
    private readonly crmService: CrmService,
    private readonly toasterService: AppToasterService,
    private readonly commonService: CommonService,
    private readonly inventoryService: InventoryService,
    private readonly cdf: ChangeDetectorRef,
    private readonly inventorySpecificationService: InventorySpecificationService,
    private readonly titlecasePipe: TitleCasePipe,
    private readonly activeRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    this.commonSharedService.setBlockUI$(true);
    this.initializeFormGroup();
    this.commonSharedService.setBlockUI$(false);
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.returnUrl) {
          this.redirectUrl = params?.returnUrl;
        }
      });
  }

  private getAll() {
    this.getContactInfo();
    this.getDesignationList();
    this.getCategoryTypes();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.crmId?.currentValue) {
      this.accordionTabs.contactInfo = this.accordionTabs.inventoryPreferenceInfo = this.accordionTabs.salePersonInfo = this.accordionTabs.specificationPreferenceInfo = false
      this.initializeFormGroup();
      this.getAll();
    }
  }


  getInventorySpecificationsForm(categoryId?: number): void {
    this.isSpecificationLoading = true;
    this.inventorySpecificationService.get<InventorySpecificationResponse>(
      categoryId ?? this.categoryId,
      `${API_URL_UTIL.specificationMasters.latest}`).pipe(takeUntil(this.destroy$)).subscribe(
        {
          next: (res: InventorySpecificationResponse) => {
            for (const specificationGroup of res?.masterData?.specification) {
              specificationGroup.fields = specificationGroup.fields.filter(field => field.includePreference)
            }
            this.inventorySpecificationForm = res?.masterData?.specification;
            this.isSpecificationLoading = false;
            this.cdf.detectChanges();
          }
        }
      )
  }

  initializeFormGroup(): void {
    this.customerLeadFormGroup = this.fb.group({
      crmContactId: new FormControl(this.crmId, [Validators.required]),
      unitTypeId: new FormControl(null, [Validators.required]),
      maxYear: new FormControl(null),
      minYear: new FormControl(null),
      makeId: new FormControl(null),
      unitModelId: new FormControl(null),
      designationId: new FormControl(null),
      status: new FormControl('OPEN', [Validators.required]),
      notes: new FormControl(null),
      categoryId: new FormControl(null, Validators.required)
    });
  }

  private getContactInfo(): void {
    this.loaders.previousOwnerName = true;
    this.crmService.getList<ContactDetails>(API_URL_UTIL.admin.crm.contact).pipe(takeUntil(this.destroy$)).subscribe({
      next: (contactList) => {
        this.contactList = contactList;
        this.loaders.previousOwnerName = false;
        if (this.customerLeadFormGroup.get('crmContactId')?.value) {
          this.displayContactDetails(this.customerLeadFormGroup.get('crmContactId')?.value);
        }

        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.previousOwnerName = false;
        this.cdf.detectChanges();
      }
    });
  }

  displayContactDetails(id: number): void {
    this.displayContact = this.contactList.find(contact => contact.id === id)
    this.cdf.detectChanges();
  }

  private getUnitTypes(categoryId: number | string) {
    this.loaders.unitTypes = true;
    const endpoint = `${API_URL_UTIL.inventory.unitTypesList}?categoryId=${categoryId}`;
    this.inventoryService.getList<IdNameModel>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (unitTypes) => {
        this.unitTypes = unitTypes;
        this.loaders.unitTypes = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.unitTypes = false;
        this.cdf.detectChanges();
      }
    });
  }
  private getMakeList(categoryId: number | string): void {
    this.loaders.makes = true;
    this.inventoryService.getCategoryByMakeList(categoryId).pipe(takeUntil(this.destroy$)).subscribe({
      next: (makeListing: IdNameModel[]) => {
        this.makes = makeListing;
        this.loaders.makes = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.makes = false;
        this.cdf.detectChanges();
      }
    });
  }

  private getModelList(makeId: number | string): void {
    this.loaders.models = true;
    const endpoint = API_URL_UTIL.inventory.makeBYModelList.replace(':makeId', makeId.toString())
    this.inventoryService.getList<IdNameModel>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (models) => {
        this.models = models;
        this.loaders.models = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.models = false;
        this.cdf.detectChanges();
      }
    })
  }

  private getDesignationList(): void {
    this.loaders.designations = true;
    this.commonService.getList(API_URL_UTIL.designations.root).pipe(takeUntil(this.destroy$)).subscribe({
      next: (designations: IdNameModel[]) => {
        this.designations = designations;
        this.loaders.designations = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.designations = false;
        this.cdf.detectChanges();
      }
    });
  }
  openModel(modelType: string): void {
    switch (modelType) {
      case ModelType.MAKE:
        const isCategoryIdPresent = this.customerLeadFormGroup.controls.categoryId?.value;
        if (isCategoryIdPresent) {
          this.modelPopups.showCreateMake = true;
        } else {
          this.toasterService.warning(MESSAGES.addCategoryBeforeAddingMake);
        }
        break;
      case ModelType.UNIT_TYPE:
        const isCategoryIdPresentForUnitType = this.customerLeadFormGroup.controls.categoryId?.value;
        if (isCategoryIdPresentForUnitType) {
          this.modelPopups.showCreateUnitType = true;
        } else {
          this.toasterService.warning(MESSAGES.addCategoryBeforeAddingUnitType);
        }
        break;
      case ModelType.MODEL:
        const isMakeIdPresent = this.customerLeadFormGroup.controls.makeId?.value;
        if (isMakeIdPresent) {
          this.modelPopups.showCreateModel = true;
        } else {
          this.toasterService.warning(MESSAGES.addMakeBeforeAddingModel);
        }
        break;
      case ModelType.VENDOR:
        this.modelPopups.showCreateVendor = true;
        break;
      case ModelType.CONTACT:
        this.modelPopups.showCreateContact = true;
        break;
      default:
        break;
    }
  }

  onAddEditPopupClose(modelType: string): void {
    this.modelPopups.showCreateMake = false;
    this.modelPopups.showCreateModel = false;
    this.modelPopups.showCreateVendor = false;
    this.modelPopups.showCreateContact = false;
    this.modelPopups.showPipelineDetails = false;
    this.modelPopups.showCreateUnitType = false;
    switch (modelType) {
      case ModelType.CONTACT:
        this.getContactInfo();
        break;
      case ModelType.UNIT_TYPE:
        this.getUnitTypes(this.categoryId);
        break;
      case ModelType.MAKE:
        this.getMakeList(this.categoryId);
        break;
      case ModelType.MODEL:
        this.getModelList(this.selectedMakeId);
        break;
      default:
        break;
    }
  }
  onSubmit(close = true): void {
    if (this.customerLeadFormGroup.invalid) {
      this.customerLeadFormGroup.markAllAsTouched();
      return;
    }
    this.saveCustomerLeadData(close);
  }

  get customerInfoCreateParams(): CustomerCreateParams {
    for (const [index, specification] of this.inventorySpecificationForm.entries()) {
      specification.id = index + 1;
      specification.order = index + 1;
      for (const field of specification.fields) {
        if (field.dataType === 'TextField' || field.dataType === 'TextBox') {
          field.value = this.titlecasePipe.transform(field.value)
        }
      }
    }
    return {
      ...this.customerLeadFormGroup.value,
      matchingCount: this.matchedCount,
      maxYear: this.customerLeadFormGroup.get('maxYear')?.value ? (new Date(this.customerLeadFormGroup.get('maxYear')?.value))?.getFullYear() : null,
      minYear: this.customerLeadFormGroup.get('minYear')?.value ? (new Date(this.customerLeadFormGroup.get('minYear')?.value))?.getFullYear() : null,
      specificationData: { specification: this.inventorySpecificationForm }
    };
  }

  saveCustomerLeadData(close = true): void {
    this.crmService.add(this.customerInfoCreateParams, API_URL_UTIL.admin.crm.customerLead).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.customerLeadAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
        this.goBack();
      } else {
        this.customerLeadFormGroup.reset({
          crmContactId: this.crmId,
          status: 'OPEN'
        });
      }
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.goBack();
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  toggleGoogleMapPopUp(contact: ContactDetails) {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getFullAddress(contact)
  }

  getFullAddress(contact: ContactDetails) {
    const rowAddress = []
    rowAddress.push(contact?.streetAddress ? contact.streetAddress : '')
    rowAddress.push(contact?.city ? contact.city : '')
    rowAddress.push(contact?.state ? contact.state : '')
    rowAddress.push(contact?.zipcode ? contact.zipcode : '')
    const cleanAddress = rowAddress.filter(str => {
      return str !== ""
    })
    this.fullAddress = cleanAddress.join(", ")
  }

  inventoryMatchedCount(event: number) {
    this.matchedCount = event
  }

  private getCategoryTypes() {
    this.loaders.categoryType = true;
    this.inventoryService.getCategoryType().pipe(takeUntil(this.destroy$)).subscribe({
      next: (categoryTypes: any) => {
        this.categoryTypes = categoryTypes;
        this.loaders.categoryType = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.categoryType = false;
        this.cdf.detectChanges();
      }
    });
  }

  changeCategory(id: number) {
    this.categoryId = id;
    this.getUnitTypes(id);
    this.getMakeList(id);
    this.getInventorySpecificationsForm();
  }

  changeMake(id: number) {
    this.selectedMakeId = id;
    this.getModelList(id)
  }

  getInventoryPerference(): string {
    const customerInfo = this.customerLeadFormGroup.getRawValue()
    const inventoryPreferenceInfo = [
      `catergory: ${this.categoryTypes.find(c => c.id === customerInfo.categoryId)?.name}`,
      `unitType: ${this.unitTypes.find(c => c.id === customerInfo.unitTypeId)?.name}`,
      `minYear: ${(new Date(customerInfo.minYear)).getFullYear()}`,
      `maxYear: ${(new Date(customerInfo.maxYear)).getFullYear()}`,
      `make: ${this.makes.find(c => c.id === customerInfo.makes)?.name}`,
      `model: ${this.models.find(c => c.id === customerInfo.unitModelId)?.name}`,
      `designations: ${this.designations.find(c => c.id === customerInfo.designationId)?.name}`,
      `status: ${this.customerStatusList.find(c => c.value === customerInfo.status)?.name}`,
      ` notes: ${customerInfo.notes}`
    ];

    return inventoryPreferenceInfo.join(', ');
  }

  getSpecificationPrefrecence(): string {
    const specification = []
    if (this.inventorySpecificationForm) {
      for (const section of this.inventorySpecificationForm) {
        for (const field of section.fields) {
          if (field.value) {
            if (field.dataType === 'DropDown') {
              specification.push(`${field.label}: ${field.options.find(option => option.id === Number(field.value))?.name}`)
            } else {
              specification.push(`${field.label}: ${field.value}`)
            }
          }
        }
      }
    }
    return specification.join(', ');
  }

  isOptionDisabled(option: ContactDetails): boolean {
    const selectedValue = this.customerLeadFormGroup?.get('crmContactId')?.value;
    return option.archived && option.id !== selectedValue;
  }
}
