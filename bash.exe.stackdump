Stack trace:
Frame         Function      Args
0007FFFFBBA0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBBA0, 0007FFFFAAA0) msys-2.0.dll+0x2118E
0007FFFFBBA0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBE78) msys-2.0.dll+0x69BA
0007FFFFBBA0  0002100469F2 (00021028DF99, 0007FFFFBA58, 0007FFFFBBA0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBBA0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBBA0  00021006A545 (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBE80  00021006B9A5 (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBFF450000 ntdll.dll
7FFBFF0A0000 KERNEL32.DLL
7FFBFCCF0000 KERNELBASE.dll
7FFBFD740000 USER32.dll
7FFBFD220000 win32u.dll
000210040000 msys-2.0.dll
7FFBFE0D0000 GDI32.dll
7FFBFD100000 gdi32full.dll
7FFBFCAF0000 msvcp_win.dll
7FFBFD250000 ucrtbase.dll
7FFBFD970000 advapi32.dll
7FFBFDA80000 msvcrt.dll
7FFBFDEF0000 sechost.dll
7FFBFE390000 RPCRT4.dll
7FFBFD350000 bcrypt.dll
7FFBFC3A0000 CRYPTBASE.DLL
7FFBFD070000 bcryptPrimitives.dll
7FFBFE100000 IMM32.DLL
