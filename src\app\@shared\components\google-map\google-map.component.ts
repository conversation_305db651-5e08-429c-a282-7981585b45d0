import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BaseComponent } from '@core/utils';
import { MESSAGES } from '../../constants/messages.constants';
declare const google: any;


@Component({
  selector: 'app-google-map',
  templateUrl: './google-map.component.html',
  styleUrls: ['./google-map.component.scss']
})
export class GoogleMapComponent extends BaseComponent implements OnInit {

  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() addressGroup: any;
  @Input() address!: string;
  options: any;
  overlays: any;
  closeSidebar = false;
  mapNotFoundMessage = '';
  zoom = 10;
  center: any = { lat: 0, lng: 0 };
  constructor(private readonly cdf: ChangeDetectorRef) {
    super();
  }

  ngOnInit(): void {
    if (this.addressGroup.latitude && this.addressGroup.longitude) {
      this.initializeMap(this.addressGroup.latitude, this.addressGroup.longitude, this.address);
    } else {
      this.getGeoLocation(this.address).then(
        (results: any) => {
          this.initializeMap(results[0].geometry.location.lat(), results[0].geometry.location.lng(), this.address);
          this.addMissingLatLongToAddress(results[0].geometry.location.lat(), results[0].geometry.location.lng());
        })
      if (this.mapNotFoundMessage !== '') {
        this.mapNotFoundMessage = MESSAGES.mapNotFound;
      }

    }
  }

  // used to add lat long details for the older/ previous addresses
  addMissingLatLongToAddress(lat: number, long: number) {
    // call api to update the lat long details in the current address
  }

  initializeMap(lat: number, long: number, address: string) {
    this.options = {
      center: { lat: lat, lng: long },
      zoom: 16
    };
    this.center = { lat, lng: long };
    this.overlays = [
      new google.maps.Marker({ position: { lat: lat, lng: long }, title: address })
    ];
    this.cdf.detectChanges();
  }

  onCancel() {
    this.onClose.emit(this.closeSidebar);
  }

  getGeoLocation(address: string) {
    const geocoder = new google.maps.Geocoder();
    return new Promise((resolve, reject) => {
      geocoder.geocode({ 'address': address }, (results: any, status: any) => { // status is empty
        if (status === google.maps.GeocoderStatus.OK) {
          resolve(results);
        }
        if (status === "ZERO_RESULTS") {
          this.mapNotFoundMessage = MESSAGES.mapNotFound;
        }
      });//end geocode
    });
  }
}
