import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormGroup, FormGroupDirective } from '@angular/forms';
import { MESSAGES, icons } from '@constants/*';
import { BaseComponent } from '@core/utils';
import { Account } from '@pages/auth/models';
import { AssociationsUnits } from '@pages/inventory/models';
import { InventoryAssociationsComponent } from '@pages/inventory/pages/inventory-add/inventory-associations/inventory-associations.component';
import { IncomingTruckDetails, IncomingTruckStatusList } from '@pages/transport/models/incoming-truck.model';
import { User } from '@pages/user/models';
import { ConfirmationService } from 'primeng/api';
import { IdNameModel } from 'src/app/@shared/models';

@Component({
  selector: 'app-payment-info',
  templateUrl: './payment-info.component.html',
  styleUrls: ['./payment-info.component.scss']
})
export class PaymentInfoComponent extends BaseComponent implements OnInit, OnChanges {
  @ViewChild(InventoryAssociationsComponent) inventoryAssociationsComponent!: InventoryAssociationsComponent;
  @Input() currentUser!: Account | null;
  @Input() users!: User[];
  @Input() paymentForm!: string;
  @Input() isEditMode !: boolean;
  @Input() generalTabStatus !: boolean;
  @Input() isViewMode !: boolean;
  @Input() isStatusError !: boolean;
  @Input() showAssociation !: boolean;
  @Input() categoryId!: number;
  @Input() incomingTruckDetails!: IncomingTruckDetails | null;
  @Output() checkedChange = new EventEmitter<boolean>();
  @Output() statusChanged = new EventEmitter<string>();
  @Output() changeParentCategory = new EventEmitter<number>();
  showIncomingCreateModal = false;
  @Input() unitTypes !: IdNameModel[];
  @Input() makes !: IdNameModel[];
  @Input() models !: IdNameModel[];
  categoriesToShow: IdNameModel[] = [];
  @Input() dealerOptions !: IdNameModel[];
  @Input() designations !: IdNameModel[];
  @Input() categoryTypes: IdNameModel[] = [];
  @Output() getUnitTypesEvent = new EventEmitter<number>();
  @Output() getMakeListEvent = new EventEmitter<number>();
  @Output() getModelListEvent = new EventEmitter<number>();
  loaders = {
    makes: false,
    models: false,
    status: false,
    unitTypes: false,
    designations: false,
    ownedBy: false,
    categoryType: false,
  };
  modelPopups = {
    showCreateModel: false,
    showCreateMake: false,
    showCreateUnitType: false
  };
  selectedUnits: Array<AssociationsUnits> = [];
  inventoryStatuses = IncomingTruckStatusList;
  @Input() status!: string | undefined;
  paymentFormGroup!: FormGroup;
  selectedMakeId!: number;
  enablePickupTab = false;

  constructor(
    private readonly rootFormGroup: FormGroupDirective,
    private readonly confirmationService: ConfirmationService,
  ) { super() }

  ngOnInit(): void {
    this.paymentFormGroup = this.rootFormGroup.control.get(this.paymentForm) as FormGroup;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.generalTabStatus?.currentValue === false) {
      this.enablePickupTab = false;
    }
  }

  onCheckedChangeGeneral() {
    this.checkedChange.emit(this.enablePickupTab);
  }

  assignToMe() {
    this.paymentFormGroup.patchValue({
      assigneeId: this.currentUser?.id
    });
  }

  changeSendEmailFlag(): void {
    if (this.incomingTruckDetails?.sentEmail) {
      this.confirmationService.confirm({
        target: event?.target as EventTarget,
        header: 'Confirmation',
        message: MESSAGES.incomingTruckResendFundingEmail,
        icon: icons.triangle,
        reject: () => {
          this.paymentFormGroup.patchValue({
            sendFundingEmail: false
          });
        }
      });
    }
  }
}
