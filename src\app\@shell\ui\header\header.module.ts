import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { UiSwitchConfig } from '@constants/*';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ConfirmationService } from 'primeng/api';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { MenuModule } from 'primeng/menu';
import { SidebarModule } from 'primeng/sidebar';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from 'src/app/@shared/pipes/pipes.module';
import { SharedLibsModule } from 'src/app/@shared/shared-libs.module';
import { HeaderComponent } from './header.component';
import { NotificationsComponent } from './notifications/notifications.component';


@NgModule({
  declarations: [
    HeaderComponent,
    NotificationsComponent
  ],
  exports: [HeaderComponent, NotificationsComponent],
  imports: [
    SharedLibsModule,
    RouterModule,
    BsDropdownModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    SharedComponentsModule,
    SidebarModule,
    InfiniteScrollModule,
    TabViewModule,
    MenuModule,
    PipesModule,
    TooltipModule,
    ConfirmPopupModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    DropdownModule
  ],
  providers: [
    ConfirmationService
  ]
})
export class HeaderModule { }
