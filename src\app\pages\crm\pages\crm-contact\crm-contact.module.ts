import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { ColumnDropdownModule } from '@pages/common-table-column/column-dropdown.module';
import { SoldTruckBoardModule } from '@pages/pipeline/pages/sold-truck-board/sold-truck-board.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { MenuModule } from 'primeng/menu';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from 'src/app/@shared/pipes/pipes.module';
import { CrmCustomerAddModule } from '../crm-customer/crm-customer-add/crm-customer-add.module';
import { CrmChangeAccountRepComponent } from './crm-change-account-rep/crm-change-account-rep.component';
import { CrmContactAddModule } from './crm-contact-add/crm-contact-add.module';
import { CrmContactListComponent } from './crm-contact-list/crm-contact-list.component';
import { CRMContactRoutingModule } from './crm-contact-routing.module';
import { CrmContactComponent } from './crm-contact.component';

@NgModule({
  declarations: [
    CrmContactComponent,
    CrmContactListComponent,
    CrmChangeAccountRepComponent
  ],
  imports: [
    CommonModule,
    CRMContactRoutingModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    FormsModule,
    ReactiveFormsModule,
    AccordionModule,
    ConfirmPopupModule,
    SidebarModule,
    CheckboxModule,
    TabViewModule,
    CrmCustomerAddModule,
    CrmContactAddModule,
    ColumnDropdownModule,
    SoldTruckBoardModule,
    TabsModule.forRoot(),
    PipesModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    MenuModule,
    ConfirmDialogModule,
    InputNumberModule
  ],

  exports: [],
  providers: [ConfirmationService, MessageService],
})

export class CrmContactModule { }
