import { TitleCasePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges, ViewChild } from '@angular/core';
import { FormGroup, NgForm } from '@angular/forms';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { DataTypeOptions } from '@pages/administration/pages/specification-configration/models/specification.model';
import { AddInventorySpecificationParams, InventoryListItem, InventorySpecification, InventorySpecificationFields, InventorySpecificationResponse, UnitType } from '@pages/inventory/models';
import { InventorySpecificationService } from '@pages/inventory/services';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-inventory-specification',
  templateUrl: './inventory-specification.component.html',
  styleUrls: ['./inventory-specification.component.scss']
})
export class InventorySpecificationComponent extends BaseComponent implements OnChanges {

  generalInfoFormGroup!: FormGroup;
  @Input() isViewMode!: boolean;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Input() selectedCategoryId!: number | null;
  @ViewChild(NgForm) inventoryForm!: NgForm;
  inventorySpecificationForm!: Array<InventorySpecification>;
  inventorySpecification!: InventorySpecificationResponse;
  unitDetails!: AddInventorySpecificationParams;
  displayAddOptionsToSpecificationFieldDialog = false;
  selectedSpecification!: InventorySpecification;
  selectedSpecificationField!: InventorySpecificationFields;
  originalMasterSpecification!: InventorySpecificationResponse

  constructor(
    private readonly inventorySpecificationService: InventorySpecificationService,
    private readonly inventoryService: InventoryService,
    private readonly cdf: ChangeDetectorRef,
    private readonly titleCasePipe: TitleCasePipe
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedCategoryId?.currentValue) {
      this.getInventorySpecifications();
    }
  }

  getInventorySpecifications(): void {
    this.getInventorySpecificationsForm();
  }

  getInventorySpecificationsForm(categoryId?: number): void {
    if (categoryId || this.selectedCategoryId) {
      this.inventorySpecificationService.get<InventorySpecificationResponse>(
        categoryId ?? this.selectedCategoryId!,
        `${API_URL_UTIL.specificationMasters.latest}`).pipe(takeUntil(this.destroy$)).subscribe(
          {
            next: (res: InventorySpecificationResponse) => {
              this.inventorySpecification = res;
              this.inventorySpecificationForm = res?.masterData?.specification;
              this.originalMasterSpecification = JSON.parse(JSON.stringify(res));
              if (this.inventoryInfo?.id || this.inventoryIncomingInfo?.id) {
                this.getInventorySpecificationsFormData();
              }
              this.cdf.detectChanges();
            }
          }
        )
    }
  }

  getInventorySpecificationsFormData(): void {
    if (this.inventoryInfo?.id) {
      this.inventoryService.get<AddInventorySpecificationParams>(this.inventoryInfo?.id as number,
        `${API_URL_UTIL.inventory.unitSpecifications}/${API_URL_UTIL.inventory.unit}`).subscribe(
          {
            next: (res: AddInventorySpecificationParams) => {
              this.unitDetails = res;
              if (res?.specificationData?.specification?.length) {
                for (const inventorySpecification of this.inventorySpecificationForm) {
                  const specificationGroupPreviousValue = res?.specificationData?.specification?.find(
                    specification => specification.sectionName === inventorySpecification.sectionName || (specification.sectionName === inventorySpecification.sectionName + ' - 1' && inventorySpecification.multiple)
                  );
                  if (inventorySpecification.multiple) {
                    inventorySpecification.sectionName = inventorySpecification.sectionName + (specificationGroupPreviousValue?.duplicateGeneratedSectionsCount ? ' - 1' : '');
                  }
                  if (specificationGroupPreviousValue) {
                    for (const inventorySpecificationField of inventorySpecification.fields) {
                      const specificationFieldsOfSelectedGroup = specificationGroupPreviousValue?.fields?.find(
                        field => field?.label === inventorySpecificationField?.label &&
                          field.dataType === inventorySpecificationField.dataType
                      );
                      if (specificationFieldsOfSelectedGroup) {
                        if (inventorySpecificationField.dataType === 'DropDown') {
                          const selectedNewOption = specificationFieldsOfSelectedGroup?.options.find(
                            option => option.id === Number(inventorySpecificationField.value)
                          );
                          const selectedPreviousOption = inventorySpecificationField?.options.find(
                            option => option.id === Number(inventorySpecificationField.value)
                          );
                          if (selectedNewOption?.name !== selectedPreviousOption?.name) {
                            inventorySpecificationField.value = null;
                          } else {
                            inventorySpecificationField.value = specificationFieldsOfSelectedGroup.value;
                          }
                        } else {
                          inventorySpecificationField.value = specificationFieldsOfSelectedGroup.value;
                        }

                      }
                    }
                    if (specificationGroupPreviousValue?.duplicateGeneratedSectionsCount) {
                      inventorySpecification.duplicateGeneratedSectionsCount = specificationGroupPreviousValue.duplicateGeneratedSectionsCount

                    }
                  }
                }
                for (const inventorySpecification of res?.specificationData?.specification) {
                  const duplicateSections = this.inventorySpecificationForm.find(
                    specification => inventorySpecification?.parentName === specification?.sectionName && specification.multiple
                  )
                  const selectedSectionIndex = this.inventorySpecificationForm.findIndex(
                    specification => inventorySpecification?.parentName === specification?.sectionName
                  )
                  if (duplicateSections) {
                    const indexOnWhichSpecificationShouldBeAdded = selectedSectionIndex + (
                      Number(Number(inventorySpecification?.sectionName?.split('-')[inventorySpecification?.sectionName?.split('-')?.length - 1]) - 1)
                    )
                    const fieldsInMasterGroup = duplicateSections.fields.map(field => field.label);
                    inventorySpecification.fields = inventorySpecification.fields.filter(field => fieldsInMasterGroup.includes(field.label));
                    this.inventorySpecificationForm.splice(indexOnWhichSpecificationShouldBeAdded, 0, { ...inventorySpecification });
                  }
                }
              } else {
                setTimeout(() => {
                  this.addInventorySpecification(this.inventoryInfo?.id)
                }, 0)
              }
              this.cdf.detectChanges();
            }
          }
        )
    }
    else if (this.inventoryIncomingInfo?.unitId) {
      this.inventoryService.get<AddInventorySpecificationParams>(this.inventoryIncomingInfo?.unitId as number,
        `${API_URL_UTIL.inventory.unitSpecifications}/${API_URL_UTIL.inventory.unit}`).subscribe(
          {
            next: (res: AddInventorySpecificationParams) => {
              this.unitDetails = res;
              if (!this.unitDetails?.specificationData?.specification) {
                this.selectedCategoryId === (this.inventoryIncomingInfo?.unit?.generalInformation?.unitType as UnitType).unitTypeCategoryId
                this.getInventorySpecificationsForm(this.selectedCategoryId!)
                setTimeout(() => {
                  this.addInventorySpecification(this.inventoryIncomingInfo?.unitId)
                }, 1000)
              }
              if (
                this.selectedCategoryId === (this.inventoryIncomingInfo?.unit?.generalInformation.unitType as UnitType).unitTypeCategoryId
              ) {
                this.inventorySpecificationForm = res?.specificationData?.specification;
              }
              this.cdf.detectChanges();
            }
          }
        )
    }
  }

  onSubmit(unitId?: number): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.inventoryForm.invalid) {
        return;
      }
  
      if (this.inventorySpecificationForm) {
        for (const specification of this.inventorySpecificationForm) {
          for (const [index, field] of specification.fields.entries()) {
            specification.fields[index] = { [field.label]: field.value, ...field };
          }
        }
        if (unitId) {
          this.addInventorySpecification(unitId).then(resolve).catch(reject);
        } else {
          this.updateInventorySpecification().then(resolve).catch(reject);
        }
      }
    });
  }
  
    addInventorySpecification(unitId: number | undefined): Promise<void> {
    return new Promise((resolve, reject) => {
      this.inventoryService.add(
        this.updateInventorySpecificationParams(unitId),
        API_URL_UTIL.inventory.unitSpecifications
      ).subscribe({
        next: () => resolve(),
        error: (err) => reject(err)
      });
    });
  }
  
  updateInventorySpecification(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.inventoryService.update(
        this.updateInventorySpecificationParams(),
        API_URL_UTIL.inventory.unitSpecifications
      ).subscribe({
        next: () => resolve(),
        error: (err) => reject(err)
      });
    });
  }

  updateInventorySpecificationParams(unitId?: number): AddInventorySpecificationParams {
    const masterSectionNames: string[] = [];
    this.originalMasterSpecification.masterData.specification.forEach(section => {
      masterSectionNames.push(section.sectionName);
      if (section.multiple) {
        masterSectionNames.push(`${section.sectionName} - 1`);
      }
    })
    const nonDuplicates: InventorySpecification[] = [];
    const duplicates: InventorySpecification[] = [];
    this.inventorySpecificationForm.forEach((specification, index) => {
      specification.id = index + 1;
      specification.order = index + 1;
      for (const field of specification.fields) {
        if (field.dataType === 'TextField' || field.dataType === 'TextBox') {
          field.value = this.titleCasePipe.transform(field.value)
        }
      }
      if (masterSectionNames.includes(specification.sectionName)) {
        nonDuplicates.push(specification)
      } else {
        duplicates.push(specification);
      }
    });
    this.inventorySpecificationForm = [...nonDuplicates, ...duplicates];
    return {
      id: unitId ? null : this.unitDetails?.id,
      specificationData: { specification: this.inventorySpecificationForm },
      unitId: unitId ? +unitId : this.unitDetails?.unitId,
      specificationMasterId: unitId ? this.inventorySpecification?.id : this.unitDetails?.specificationMasterId
    }
  }

  createDuplicationSection(event: Event, specificationToBeDuplicated: InventorySpecification): void {
    event.stopPropagation();
    const selectedSpecificationIndex = this.inventorySpecificationForm.findIndex(
      specification => specification.sectionName === specificationToBeDuplicated.sectionName
    )
    if (this.inventorySpecificationForm[selectedSpecificationIndex]?.duplicateGeneratedSectionsCount) {
      this.inventorySpecificationForm[selectedSpecificationIndex].duplicateGeneratedSectionsCount = Number(this.inventorySpecificationForm[selectedSpecificationIndex].duplicateGeneratedSectionsCount) + 1;
    } else {
      this.inventorySpecificationForm[selectedSpecificationIndex] = { ...this.inventorySpecificationForm[selectedSpecificationIndex], duplicateGeneratedSectionsCount: 1, sectionName: this.inventorySpecificationForm[selectedSpecificationIndex].sectionName + ' - 1' }
    }
    const newSpecification = {
      ...specificationToBeDuplicated,
      multiple: false,
      isShow: true,
      sectionName: specificationToBeDuplicated.sectionName.split('-')[0].trim() + ' - ' + (specificationToBeDuplicated?.duplicateGeneratedSectionsCount ? specificationToBeDuplicated?.duplicateGeneratedSectionsCount + 1 : 2),
      parentName: specificationToBeDuplicated?.duplicateGeneratedSectionsCount ? specificationToBeDuplicated.sectionName : specificationToBeDuplicated.sectionName + ' - 1',
      fields: specificationToBeDuplicated.fields.map(field => {
        return {
          ...field,
          for: (field.for + specificationToBeDuplicated.sectionName.split('-')[0].trim() + ' - ' + (specificationToBeDuplicated?.duplicateGeneratedSectionsCount ? specificationToBeDuplicated?.duplicateGeneratedSectionsCount + 1 : 2)),
          value: this.isTextField(field) ? '' : null
        }
      })
    }
    const indexOnWhichSpecificationShouldBeAdded = specificationToBeDuplicated?.duplicateGeneratedSectionsCount ?
      selectedSpecificationIndex + specificationToBeDuplicated?.duplicateGeneratedSectionsCount :
      selectedSpecificationIndex + 1
    this.inventorySpecificationForm.splice(indexOnWhichSpecificationShouldBeAdded, 0, { ...newSpecification });
    for (const [index, inventorySpecificationForm] of this.inventorySpecificationForm.entries()) {
      inventorySpecificationForm.id = index + 1;
      inventorySpecificationForm.order = index + 1;
    }
  }

  removeDuplicationSection(event: Event, specificationToBeRemoved: InventorySpecification): void {
    event.stopPropagation();
    const indexToRemove = this.inventorySpecificationForm.findIndex(
      specification => specification.sectionName === specificationToBeRemoved.sectionName
    );

    if (indexToRemove > -1) {
      this.inventorySpecificationForm.splice(indexToRemove, 1);

      const parentSection = this.inventorySpecificationForm.find(specification => specification.sectionName === specificationToBeRemoved.parentName);

      if (parentSection) {
        parentSection.duplicateGeneratedSectionsCount = (parentSection.duplicateGeneratedSectionsCount || 1) - 1;
      }

      this.inventorySpecificationForm.forEach((specification, index) => {
        specification.id = index + 1;
        specification.order = index + 1;

        if (specification.parentName === specificationToBeRemoved.parentName && specification.sectionName.includes(' - ')) {
          const parts = specification.sectionName.split(' - ');
          const newIndex = (parentSection?.duplicateGeneratedSectionsCount ? parentSection.duplicateGeneratedSectionsCount + 1 : 2);
          specification.sectionName = parts[0] + ' - ' + newIndex;
        }
      });

      const remainingSections = this.inventorySpecificationForm.filter(
        specification => specification.sectionName.split(' - ')[0] === specificationToBeRemoved.sectionName.split(' - ')[0]
      );

      if (remainingSections.length === 1 && !remainingSections[0].duplicateGeneratedSectionsCount) {
        const singleSection = remainingSections[0];
        singleSection.sectionName = singleSection.sectionName.split(' - ')[0];
      }
    }
  }


  private isTextField(field: InventorySpecificationFields): boolean {
    return field.dataType === DataTypeOptions[2] || field.dataType === DataTypeOptions[3]
  }

  openAddOptionsToSpecificationFieldsDialog(specificationDetails: InventorySpecification, fieldDetails: InventorySpecificationFields): void {
    this.selectedSpecification = specificationDetails;
    this.selectedSpecificationField = fieldDetails;
    this.displayAddOptionsToSpecificationFieldDialog = true;
  }

  updateSpecificationFieldOptions(fieldOptionsArray: Array<string>): void {
    const selectedSpecificationIndex = this.inventorySpecificationForm.findIndex(specification => this.selectedSpecification.sectionName === specification.sectionName)
    const selectedSpecificationFieldIndex = this.inventorySpecificationForm[selectedSpecificationIndex].fields.findIndex(field => field.id === this.selectedSpecificationField.id);
    const lastOptionIndex = this.inventorySpecificationForm[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].options.length - 1;
    const lastOptionId = this.inventorySpecificationForm[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].options[lastOptionIndex]?.id
    for (const [index, option] of fieldOptionsArray.entries()) {
      if (!this.inventorySpecificationForm[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].options.some(o => o.name === this.titleCasePipe.transform(option))) {
        this.inventorySpecificationForm[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].options.push({
          id: index + lastOptionId + 1,
          name: this.titleCasePipe.transform(option)
        })
      }
    }

    if (this.inventorySpecificationForm[selectedSpecificationIndex]?.multiple) {
      for (const specification of this.inventorySpecificationForm) {
        if (this.inventorySpecificationForm[selectedSpecificationIndex].sectionName === specification.parentName) {
          for (const field of specification.fields) {
            if (field.label === this.inventorySpecificationForm[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].label) {
              field.options = this.inventorySpecificationForm[selectedSpecificationIndex].fields[selectedSpecificationFieldIndex].options
            }
          }
        }
      }
    }

    this.selectedSpecification = {} as InventorySpecification;
    this.selectedSpecificationField = {} as InventorySpecificationFields;
  }

  onAddEditPopupClose() {
    this.displayAddOptionsToSpecificationFieldDialog = false;
  }
}