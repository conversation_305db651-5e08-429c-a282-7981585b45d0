<div class="card tabs task-list">
  <div class="tab-content">
    <p-table
      class="no-column-selection"
      [value]="tasks"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
      [scrollable]="true"
      scrollDirection="horizontal"
      styleClass="p-datatable-gridlines"
      [resizableColumns]="true"
      columnResizeMode="expand"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th pResizableColumn pSortableColumn="id" id="id" class="min-width-100" pFrozenColumn>#Id <p-sortIcon field="id"></p-sortIcon></th>
          <th pResizableColumn pSortableColumn="unit.generalInformation.stockNumber" id="stockNumber">
            #Stock <p-sortIcon field="unit.generalInformation.stockNumber"></p-sortIcon>
          </th>
          <th pResizableColumn pSortableColumn="summary" id="summary">Summary <p-sortIcon field="summary"></p-sortIcon></th>
          <th pResizableColumn pSortableColumn="taskType" id="taskType" class="min-width-100">Type <p-sortIcon field="taskType"></p-sortIcon></th>
          <th pResizableColumn class="timeline-header min-width-175" id="timelineHeader">Deadline</th>
          <th pResizableColumn id="reported">Reporter</th>
          <th pResizableColumn pSortableColumn="taskStatus" id="taskStatus" class="min-width-175">Status <p-sortIcon field="taskStatus"></p-sortIcon></th>
          <th pResizableColumn scope="col" class="small-col" *appHasPermission="[permissionActions.DELETE_TASK]">Active</th>
          <th pResizableColumn class="small-col text-center" id="actions" *appHasPermission="[permissionActions.DELETE_TASK, permissionActions.UPDATE_TASK]">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td (click)="onViewEdit(rowData, false)" class="view-task min-width-100" pFrozenColumn>{{ rowData?.id }}</td>
          <td>{{ rowData?.stockNumber }}</td>
          <td>{{ rowData?.summary }}</td>
          <td class="min-width-100">{{ rowData?.taskType?.name }}</td>
          <td class="timeline min-width-175">
            <div class="footer" *ngIf="rowData?.endDate">
              <span class="date"><em class="pi pi-clock"></em>{{ rowData?.endDate | date: constants.monthAndDateFormat }}</span>
            </div>
          </td>
          <td>{{ rowData?.reporter?.name }}</td>
          <td class="min-width-175">
            <p-dropdown
              class="w-150 task-list"
              appendTo="body"
              [options]="taskStatuses"
              (click)="findStatusIndex(rowIndex)"
              optionLabel="name"
              [(ngModel)]="rowData.taskStatus.id"
              optionValue="id"
              (onChange)="changeStatus(rowData, rowData?.taskStatus?.id, rowData?.id, $event)"
            >
              {{ rowData?.taskStatus?.name }}
            </p-dropdown>
          </td>
          <td *appHasPermission="[permissionActions.DELETE_TASK]">
            <ui-switch [(ngModel)]="rowData.isActive" [loading]="selectedTask?.id === rowData.id && isArchiveInProgress" (change)="onArchive(rowData, $event)">
              <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedTask?.id === rowData.id"></fa-icon>
            </ui-switch>
          </td>
          <td *appHasPermission="[permissionActions.UPDATE_TASK, permissionActions.DELETE_TASK]">
            <div class="content-between">
              <img [src]="constants.staticImages.icons.edit" (click)="onViewEdit(rowData, true)" alt="" *appHasPermission="[permissionActions.UPDATE_TASK]" />
              <ng-container *appHasPermission="[permissionActions.DELETE_TASK]">
                <em class="pi pi-trash text-danger" appShowLoaderOnApiCall (click)="onDelete(rowData, $event)" alt="" *ngIf="rowData?.taskType?.name === 'General'"></em>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="9" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </div>
</div>

<p-sidebar [(visible)]="showCreateModal" [fullScreen]="true" (onHide)="showCreateModal = false" [blockScroll]="true" [transitionOptions]="modalTransition" [showCloseIcon]="false">
  <app-task-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal" [taskId]="taskId || selectedTask!.id" [isViewMode]="isViewMode"></app-task-add>
</p-sidebar>

<p-confirmDialog header="Confirmation" styleClass="confirm-dialog" *ngIf="showConfirmationDialog" [breakpoints]="{ '960px': '60vw', '640px': '90vw' }" [style]="{ width: '30vw' }">
</p-confirmDialog>
