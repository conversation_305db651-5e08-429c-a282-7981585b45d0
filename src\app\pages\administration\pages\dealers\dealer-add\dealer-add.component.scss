@import '/src/assets/scss/theme/mixins';

.add-shop {
  width: 200px;

  button {
    display: flex;
    flex-direction: row-reverse;
    height: 100%;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    scroll-margin-bottom: 100px;

    img {
      margin-right: 10px;
    }
  }
}

.add-shops-btn {
  @include flex-end;
  margin-bottom: 5px;
}

.map-icon {
  margin-top: 23px;
  button {
    display: flex;
    flex-direction: row-reverse;
    height: 100%;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    scroll-margin-bottom: 100px;

    img {
      margin-right: 10px;
    }
  }
}

.shop-group {
  margin-bottom: 15px;
}

.delete-shop {
  &:hover {
    cursor: pointer;
  }
}

.pac-container {
  z-index: 9999999 !important;
}

@media only screen and (max-width: 500px) {
  .dealer-title {
    display: block !important;
    justify-content: space-between !important;
  }

  .dealer-form .content {
    padding-bottom: 50px;
  }
}

.dealer-title {
  display: flex;
  justify-content: space-between;
}

.f-s-12 {
  font-size: 12px;
}

.header-title {
  top: -8px;
  position: relative;
}
.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
  position: absolute;
  top: 30px;
}
.bold-text {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

.public-url {
  text-transform: none !important;
  a {
    text-transform: none !important;
  }
}
.email-col {
  width: 300px;
}
