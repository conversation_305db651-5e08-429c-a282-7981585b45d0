import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { SupplierReportsRoutingModule } from './supplier-reports-routing.module';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { TableModule } from 'primeng/table';
import { SupplierReportsComponent } from './supplier-reports.component';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';


@NgModule({
  declarations: [SupplierReportsComponent],
  imports: [
    CommonModule,
    SupplierReportsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    SharedComponentsModule,
    TableModule,
    DropdownModule,
    MultiSelectModule,
    FontAwesomeIconsModule,
    CalendarModule,
    DirectivesModule
  ]
})
export class SupplierReportsModule { }
