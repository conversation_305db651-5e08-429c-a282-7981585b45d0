import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmCustomerInventoryDetailsComponent } from './crm-customer-inventory-details/crm-customer-inventory-details.component';
import { CrmCustomerInventoryMatchedComponent } from './crm-customer-inventory-matched/crm-customer-inventory-matched.component';
import { CrmCustomerInventoryWrapperComponent } from './crm-customer-inventory-wrapper.component';
import { CrmCustomerQuotationComponent } from './crm-customer-quotation/crm-customer-quotation.component';
import { CrmCustomerRejectQuotationModule } from './crm-customer-reject-quotation/crm-customer-reject-quotation.module';
import { CrmCustomerTaskModule } from './crm-customer-task/crm-customer-task.module';

@NgModule({
  declarations: [CrmCustomerInventoryWrapperComponent,
    CrmCustomerInventoryMatchedComponent,
    CrmCustomerQuotationComponent,
    CrmCustomerInventoryDetailsComponent,
  ],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    SidebarModule,
    ReactiveFormsModule,
    FormsModule,
    CheckboxModule,
    DropdownModule,
    TabViewModule,
    CardModule,
    TableModule,
    ConfirmPopupModule,
    CrmCustomerTaskModule,
    CrmCustomerRejectQuotationModule
  ],
  exports: [
    CrmCustomerInventoryWrapperComponent,
    CrmCustomerInventoryMatchedComponent,
    CrmCustomerQuotationComponent,
    CrmCustomerInventoryDetailsComponent
  ],
  providers: [ConfirmationService, MessageService],
})

export class CrmCustomerInventoryWrapperModule { }
