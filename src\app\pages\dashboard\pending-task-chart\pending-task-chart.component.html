<div class="content">
  <div *ngIf="isFullViewPendingTask" class="title d-flex space-between">
    <div>
      <h6>Task</h6>
    </div>
    <div class="justify-content-end">
      <fa-icon [icon]="faIcons.faTimes" *ngIf="isFullViewPendingTask" (click)="onCancel()"></fa-icon>
    </div>
  </div>
  <div class="row">
    <div [ngClass]="{ 'd-flex': isFullViewPendingTask, 'col-lg-6 col-md-12': true }">
      <p-chart type="polarArea" [data]="data" [options]="chartOptions" class="w-70"></p-chart>
    </div>
    <div class="col-lg-6 col-md-12">
      <div>
        <span class="title-header f-s-25">Total Pending Task</span>
        <div class="font-bold">{{ taskStatusCount?.totalPendingTasks }}</div>
      </div>
      <div class="row">
        <div class="col-12">
          <div class="legend-items-container m-t-10">
            <div class="legend-items">
              <div class="legend-item ng-star-inserted">
                <div class="item-color blue-bar"></div>
                <div class="item-value ng-star-inserted">
                  <span class="text-color">To Do</span>
                  <div class="text-color">
                    {{ taskStatusCount?.toDoCount }}
                    <span class="m-l-10 low-box">{{ getStatus(taskStatusCount?.toDoCount) | number: "1.2-2" }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="legend-items-container m-t-10">
            <div class="legend-items">
              <div class="legend-item ng-star-inserted">
                <div class="item-color orange-bar"></div>
                <div class="item-value ng-star-inserted">
                  <span class="text-color">Issues</span>
                  <div class="text-color">
                    {{ taskStatusCount?.issueCount }}
                    <span class="m-l-10 high-box">{{ getStatus(taskStatusCount?.issueCount) | number: "1.2-2" }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="legend-items-container m-t-10">
            <div class="legend-items">
              <div class="legend-item ng-star-inserted">
                <div class="item-color yellow-bar"></div>
                <div class="item-value ng-star-inserted">
                  <span class="text-color">In Progress</span>
                  <div class="text-color">
                    {{ taskStatusCount?.inProgressCount }}
                    <span class="m-l-10 medium-box">{{ getStatus(taskStatusCount?.inProgressCount) | number: "1.2-2" }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
