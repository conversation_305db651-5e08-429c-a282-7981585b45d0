<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form [formGroup]="unitTypeFormGroup" (ngSubmit)="onSubmit()" [ngClass]="{ loading: isLoading }">
  <div class="content" *ngIf="!isLoading; else loaderTemplate">
    <section>
      <div class="row">
        <div class="col-12">
          <label class="required">Unit Type</label>
          <input class="form-control" type="text" placeholder="Enter Unit Type" formControlName="name" />
          <app-error-messages [control]="unitTypeFormGroup.controls.name"></app-error-messages>
        </div>
      </div>
    </section>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
  </div>
</form>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>
