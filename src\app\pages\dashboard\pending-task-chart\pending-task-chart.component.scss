@import "src/assets/scss/variables";

.legend-items-container .legend-items {
  width: 100%;
  white-space: nowrap;
  overflow: auto;
}

.legend-items-container .legend-items .legend-item {
  margin-right: 0;
  padding-right: 10px;
  width: 16.5%;
  display: inline-block;
  cursor: pointer;
}

.legend-items-container .legend-items .legend-item .item-color {
  max-height: -webkit-fill-available;
  min-height: 100%;
  border-left: 4px solid;
  width: 4px;
  height: 42px;
  float: left;
  margin-right: 7px;
}

.legend-items-container .legend-items .legend-item .item-value {
  margin-top: 0;
  font-size: inherit;
  margin-left: 11px;
}

.m-t-10 {
  margin-top: 10px;
}

.title-header {
  color: var(--content-head-color);
}

.font-bold {
  font-weight: 600;
  font-size: 30px;
  color: var(--text-color);
}

.high-priority {
  color: $high-priority-color;
}

.orange-bar {
  color: rgba(220, 93, 61, 0.6);
}

.yellow-bar {
  color: rgba(250, 177, 51, 0.6);
}

.blue-bar {
  color: rgba(25, 123, 222, 0.6);
}

.space-between {
  justify-content: space-between;
}

.f-s-25 {
  font-size: 25px;
}

.m-l-10 {
  margin-left: 10px;
}

.high-box {
  width: 300px;
  height: 100px;
  border: none;
  background: $high-box-background-color;
  color: $high-box-color;
}

.medium-box {
  width: 300px;
  height: 100px;
  border: none;
  background: $medium-box-background-color;
  color: $medium-box-color;
}

.low-box {
  width: 300px;
  height: 100px;
  border: none;
  background: $low-box-background-color;
  color: $low-box-color;
}

.w-70 {
  width: 70%;
}

.content {
  max-height: unset;
}
