.outer-box {
  border: 1px solid var(--table-border-color);
  margin-top: 40px !important;
}

.m-t-10 {
  margin-top: 10px;
}

.overview-title {
  margin-bottom: 10px !important;

  * {
    font-size: 16px !important;
    font-weight: 600 !important;
    letter-spacing: 0;
    line-height: 25px !important;
  }
}

.inner-box {
  padding: 10px;
  border: 1px solid #d0d7e1;
}

.m-20 {
  margin: 20px;
}

.w-100 {
  width: 100%;
}

.m-l-20 {
  margin-left: 20px;
}

.m-t-0 {
  margin-top: 0px;
}

.w-30 {
  width: 30%;
}

.amount-style {
  color: var(--text-color);
  font-weight: 500;
}

.m-l-29 {
  margin-left: 29%;
}

.m-l-10 {
  margin-left: 10px;
}

.m-t-20 {
  margin-top: 20px;
}

.space-between {
  justify-content: space-between;
}

.border-none {
  border: none !important;
}

.f-s-13 {
  font-size: 13px;
}

.download-icon {
  color: var(--active-color) !important;
}

.text-align-end {
  text-align: end;
}

.p-r-13 {
  padding-right: 13px;
}

::ng-deep .financial-parent-form {
  .p-disabled,
  .p-calendar-disabled {
    opacity: 1;
  }

  .p-disabled .p-dropdown-label,
  .task-add .p-component:disabled .p-dropdown-label,
  .p-calendar-disabled {
    color: #797979;
  }

  .p-dialog .p-dialog-header .p-dialog-title {
    font-weight: 500;
    font-size: 15px;
  }

  .p-dialog .p-dialog-content {
    padding: 0;
  }
}

.add-btn {
  height: 25px;
  padding: 0px 5px;
  float: right;
  margin: 3px;
}

.mt-23 {
  margin-top: 22px;
}
.m-t-30 {
  margin-top: 30px;
}

.m-t-8 {
  margin-top: 8px;
}

.group-4 {
  align-items: center;
  display: flex;
  height: 61px;
  margin-left: 20px;
  margin-top: 21px;
  min-width: 867px;
}

.flex-col {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 19px;
  min-height: 61px;
}

.text1 {
  letter-spacing: 0;
  line-height: normal;
  color: #1a1a1a;
  text-wrap: no-wrap;
}

.group-2 {
  align-items: flex-end;
  display: flex;
  gap: 110px;
  height: 50px;
  margin-bottom: 3px;
  margin-left: 72px;
  min-width: 308px;
  padding: 13px 11px;
}

.price-3 {
  color: var(--text-color);
  font-weight: 600px;
  letter-spacing: 0;
  line-height: normal;
}

.text3 {
  color: var(--text-color);
  font-weight: 600px;
  letter-spacing: 0;
  line-height: normal;
  margin-bottom: -10px;
  width: 214px;
  padding-bottom: 10px;
}

.price {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 19px;
}

.text2 {
  letter-spacing: 0;
  line-height: normal;
}

.content-between {
  align-items: center;

  img {
    height: 14px;
  }
}

@media only screen and (max-width: 500px) {
  .document-upload {
    flex-wrap: wrap;
    justify-content: center !important;
  }
}

.right-side-block {
  width: 500px;
  .inner-box {
    font-weight: 700;
  }
}
