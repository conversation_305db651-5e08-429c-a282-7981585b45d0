export interface InventorySpecification {
  sectionName: string;
  order: number;
  isShow: boolean;
  fields: Array<InventorySpecificationFields>;
  id: number;
  multiple: boolean;
  parentName?: string;
  duplicateGeneratedSectionsCount?: number;
}

export interface InventorySpecificationFields {
  for: string;
  label: string;
  value: string | null;
  dataType: string;
  order: number;
  isRequired: boolean;
  placeholder: string;
  options: Array<InventoryDropDownFieldOptions>;
  id: number;
  includePreference: boolean;
}

export interface InventoryDropDownFieldOptions {
  id: number;
  name: string;
}

export interface InventorySpecificationResponse {
  createdById: number;
  createdDate: string;
  id: number;
  masterData: InventorySpecificationMasterData;
  unitTypeCategoryId: number;
  fieldCount: number
}

export interface InventorySpecificationMasterData {
  specification: Array<InventorySpecification>
}

export interface AddInventorySpecificationParams {
  specificationData: SpecificationData;
  unitId: number;
  specificationMasterId: number;
  id?: number | null;
}

export interface SpecificationData {
  specification: Array<InventorySpecification>
}
