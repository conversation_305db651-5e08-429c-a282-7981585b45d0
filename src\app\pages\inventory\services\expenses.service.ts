import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { Observable } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { ExpensesListItem, VendorSupplierBasicInfo } from '../models';

@Injectable({
  providedIn: 'root'
})
export class ExpensesService extends BaseCrudService {

  constructor(protected readonly httpClient: HttpClient) { super(httpClient); }

  getBaseAPIPath(): string {
    return API_URL_UTIL.expenses.root;
  }

  fromServerModel(json: ExpensesListItem): ExpensesListItem {
    if (!json) {
      return new ExpensesListItem();
    }
    return new ExpensesListItem(json);
  }

  getVendorSupplierList(): Observable<VendorSupplierBasicInfo[]> {
    return this.httpClient.get<VendorSupplierBasicInfo[]>(`${API_URL_UTIL.expenses.vendorSupplier}`);
  }

  getExpensesList(): Observable<IdNameModel[]> {
    return this.httpClient.get<IdNameModel[]>(`${API_URL_UTIL.expenses.expense_type}`);
  }

  getAcquisitionList(): Observable<IdNameModel[]> {
    return this.httpClient.get<IdNameModel[]>(`${API_URL_UTIL.expenses.acquisitionMethods}`);
  }

  deleteExpenseAttachment(attachmentId: number): Observable<void> {
    return this.httpClient.delete<void>(`${API_URL_UTIL.expenses.attachments}/${attachmentId}`);
  }
}
