@import "../variables";

thead {
  tr {
    background-color: var(--table-even-color);

    & > * {
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 21px;
    }
  }
}

tbody {
  tr:nth-child(even) > * {
    background-color: var(--table-even-color) !important ;
    --bs-table-accent-bg: var(--table-even-color) !important;
  }

  tr:nth-child(odd) > * {
    background-color: var(--table-odd-color) !important;
    --bs-table-accent-bg: var(--table-odd-color) !important;
  }

  tr {
    & > * {
      font-size: 14px;
      letter-spacing: 0;
      line-height: 21px;
      color: var(--text-color);
    }
  }
}

.table > :not(caption) > * > * {
  padding: 0.8rem 0.5rem !important;
}

.table {
  margin-top: 0.5rem;
}

.p-datatable {
  min-height: 150px;
}

.p-datatable .p-datatable-tbody > tr > td {
  border: 1px solid var(--table-border-color);
  padding: 0.8rem 1rem;
  word-break: keep-all;
  white-space: normal !important;
}

.p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td {
  border-width: 0 0 1px 1px;
}

.p-datatable .p-datatable-header {
  border-top: none;
  padding: 0 0 1rem;
}

.p-datatable .p-sortable-column:focus {
  box-shadow: none;
}

.p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
  color: var(--active-color);
}

.p-datatable .p-sortable-column .p-sortable-column-icon {
  color: var(--text-color) !important;
  font-size: 12px;
}

.p-datatable .p-datatable-thead > tr > th {
  border: 1px solid var(--table-border-color);
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 21px;
  color: var(--text-color);
  white-space: normal !important;
}

.p-datatable .p-datatable-thead {
  tr {
    &:nth-child(1) > th {
      background-color: var(--table-header-bg-color);
      &:hover {
        background-color: var(--fields-focus-color);
        color: var(--text-color);
      }
    }
    &:nth-child(2) > th {
      background-color: var(--table-even-color);
    }
  }
}
body {
  &.LIGHT {
    .p-datatable .p-sortable-column.p-highlight {
      background-color: var(--app-background-color) !important;
      color: var(--active-color);
      &:hover {
        background-color: var(--app-background-color);
        color: var(--active-color);
        .p-sortable-column-icon {
          color: var(--active-color);
        }
      }
    }
  }
  &.DARK {
    .p-datatable .p-sortable-column.p-highlight {
      background-color: var(--fields-focus-color);
      color: var(--text-color);
      &:hover {
        background-color: var(--fields-focus-color);
      }
    }
  }
}

p-table {
  .p-multiselect .p-multiselect-panel {
    min-width: 300px;
  }

  .p-multiselect .p-multiselect-label {
    padding: 0.55rem 0.75rem;
    font-size: 14px;
    color: var(--text-color);
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
  }

  .p-multiselect-filter {
    padding: 0.3rem;
  }

  .p-multiselect-panel .p-multiselect-items .p-multiselect-item {
    padding: 0.6rem 1.25rem;
  }

  .p-multiselect-panel .p-multiselect-items:not(.p-multiselect-virtualscroll) {
    padding: 0.4rem 0;
  }

  .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
    color: var(--active-color);
    font-size: 14px;
  }

  .p-multiselect-panel .p-multiselect-header {
    padding-right: 10px;
  }
}

@media only screen and (max-width: 780px) {
  p-table {
    table {
      min-width: 50rem !important;
    }
  }
}

.small-col {
  width: 90px;
  text-align: center;
}

td img:hover {
  cursor: pointer;
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item {
  font-size: 14px;
}

.no-data {
  margin-top: 10px;
  padding-top: 10px;
  color: var(--text-color);
  text-align: center;
  font-size: 14;
}

tbody tr {
  border-bottom: none;
}

.p-datatable.p-datatable-hoverable-rows .p-datatable-tbody > tr:not(.p-highlight):hover {
  background-color: darken($table-row-color, 1%);
}

p-table.draggable {
  .p-datatable-tbody > tr > td {
    border: none;
    background-color: white;
  }

  .p-datatable {
    min-height: unset;
  }
}

.p-datatable-wrapper {
  max-height: calc(100vh - 270px);
}

.p-tabview-panels {
  background-color: var(--card-bg-color) !important;
  padding: 0.75rem !important;
  .p-datatable-wrapper {
    max-height: calc(100vh - 320px);
  }
}

.has-date-range-filter {
  .p-datatable-wrapper {
    max-height: calc(100vh - 350px);
  }
}

@media only screen and (max-width: 940px) {
  .p-datatable-wrapper {
    max-height: calc(100vh - 315px);
  }

  .p-tabview-panels {
    .p-datatable-wrapper {
      max-height: calc(100vh - 380px);
    }
  }

  .has-date-range-filter {
    .p-datatable-wrapper {
      max-height: calc(100vh - 530px);
    }
  }
}

.p-sidebar-left.p-sidebar-md,
.p-sidebar-right.p-sidebar-md {
  width: 44rem;
}

.p-sidebar-left.p-sidebar-lg,
.p-sidebar-right.p-sidebar-lg {
  width: 1000px !important;
}

.first-col {
  z-index: 1;
}

td > div > img {
  margin-right: 1.5rem;
}

th,
td {
  width: 140px;
}

@media only screen and (max-width: 700px) {
  .p-sidebar-left.p-sidebar-md,
  .p-sidebar-right.p-sidebar-md {
    width: 100% !important;
  }

  .p-sidebar-md {
    width: 100% !important;
  }
}
