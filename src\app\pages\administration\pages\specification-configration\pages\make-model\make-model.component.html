<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button class="btn btn-primary left" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAddEditMake()" *appHasPermission="[permissionActions.CREATE_MAKE_MODEL]">
      <span class="show-label">Add New Make/Model</span>
    </button>
  </div>
</app-page-header>

<div class="content">
  <section>
    <div class="row">
      <div class="d-flex justify-content-between block">
        <p-dropdown [options]="categories" [(ngModel)]="selectedCategory" optionLabel="name" optionValue="id" (onChange)="onCategoryChange($event.value)"></p-dropdown>
        <button class="btn btn-primary left" (click)="showDeleteModal(undefined, $event, true)" [disabled]="!selectedModelList?.length" *appHasPermission="[permissionActions.DELETE_MAKE_MODEL]">
          <span>Delete Make/Model ({{ getSelectedMakeModelCount() }})</span>
        </button>
      </div>
    </div>
  </section>
</div>

<ng-container *ngIf="!isLoading; else pageLoaderTemplate">
  <p-table
    class="no-column-selection make-model-table"
    [(selection)]="selectedModelList"
    [value]="modelList"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="true"
    [rowHover]="true"
    [loading]="isLoading"
    styleClass="p-datatable-gridlines"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <ng-container *appHasPermission="[permissionActions.DELETE_MAKE_MODEL]">
          <th pResizableColumn style="width: 4rem" *ngIf="getMakeModelCount()">
            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
          </th>
        </ng-container>
        <th pResizableColumn scope="col" class="make-column">Make</th>
        <th pResizableColumn scope="col">Model</th>
        <th pResizableColumn>Inventory</th>
        <th pResizableColumn scope="col" *appHasPermission="[permissionActions.DELETE_MAKE_MODEL, permissionActions.UPDATE_MAKE_MODEL]">Actions</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-make>
      <tr>
        <ng-container *appHasPermission="[permissionActions.DELETE_MAKE_MODEL]">
          <td *ngIf="getMakeModelCount()">
            <p-tableCheckbox *ngIf="!make?.inventoryCount" [value]="make"></p-tableCheckbox>
          </td>
        </ng-container>
        <td class="make-column">
          {{ make?.name }}
        </td>
        <td>
          <ng-container *ngFor="let model of make.models; let index = index">
            {{ model.name }}
            <span *ngIf="make?.models?.length - 1 !== index">,</span>
          </ng-container>
        </td>
        <td>
          {{ make?.inventoryCount }}
        </td>
        <td *appHasPermission="[permissionActions.DELETE_MAKE_MODEL, permissionActions.UPDATE_MAKE_MODEL]">
          <div>
            <img [src]="constants.staticImages.icons.edit" alt="" (click)="onAddEditMake(make)" *appHasPermission="[permissionActions.UPDATE_MAKE_MODEL]" />
            <img [src]="constants.staticImages.icons.deleteIcon" alt="" (click)="showDeleteModal(make, $event)" *appHasPermission="[permissionActions.DELETE_MAKE_MODEL]" />
          </div>
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="6" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
</ng-container>

<ng-template #pageLoaderTemplate>
  <div class="no-data mt-5">
    <em class="pi pi-spin pi-spinner"></em>
  </div>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showCreateModal"
  position="right"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-make
    [categories]="categories"
    *ngIf="showCreateModal"
    [categoryId]="selectedCategory"
    [isAddEdit]="true"
    [selectedMake]="selectedMake"
    (onClose)="closeModal()"
    (addNewMake)="addNewMake($event)"
    (editMake)="updateMake($event)"
  >
  </app-add-new-make>
</p-sidebar>

<p-confirmPopup></p-confirmPopup>
