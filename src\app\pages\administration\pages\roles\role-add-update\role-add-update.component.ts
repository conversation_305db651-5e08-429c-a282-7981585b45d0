import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Router } from '@angular/router';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { Assets, RoleModule, RoleModuleGroup } from '@pages/administration/models';
import { Role } from '@pages/auth/models';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { RoleService } from '../../users/roles.service';
import { AssetsService } from '../assets.service';
import { RoleModulesService } from '../role-modules.service';

@Component({
  selector: 'app-role-add-update',
  templateUrl: './role-add-update.component.html',
  styleUrls: ['./role-add-update.component.scss']
})
export class RoleAddUpdateComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedRole!: Role | null;
  @Input() redirectUrl!: string;
  @Input() isViewMode!: boolean;
  roles!: Array<RoleModuleGroup>;
  assets!: Array<Assets>;
  role: RoleModule = new RoleModule();
  pageTitle = 'Add Role & Permissions';
  @Output() onClose = new EventEmitter<void>();
  @Output() onAddRole = new EventEmitter<Role>();
  @Output() onUpdateRole = new EventEmitter<Role>();

  constructor(
    private readonly roleModulesService: RoleModulesService,
    private readonly roleService: RoleService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly assetsService: AssetsService,
    private readonly router: Router,
  ) {
    super();
  }

  ngOnInit(): void {
    this.getRoles();
    this.getAssets();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedRole?.currentValue) {
      if (this.selectedRole?.id) {
        this.pageTitle = this.isViewMode ? 'View Role & Permissions' : 'Edit Role & Permissions';
      } else {
        this.pageTitle = 'Add Role & Permissions';
      }
      this.setModulePermissions();
      this.setSubModulePermissions();
    }
  }

  setModulePermissions(): void {
    for (const module of this.roles) {
      const moduleActions = this.selectedRole?.privilegeActionResponseDTOs?.filter(res => {
        return res.module.id === module.id
      });
      const moduleActionIds = moduleActions?.map(({ actionDto }) => actionDto.id);
      const selectedActionIds = module.actions?.map(({ id }) => id).sort((a, b) => ((a as number) - (b as number)));

      if (moduleActionIds?.length) {
        if (this.areArraysEqual(moduleActionIds, selectedActionIds)) {
          module.checked = true;
        } else {
          module.checked = false;
        }
      } else {
        module.checked = false;
      }

      this.cdf.detectChanges();
    }
  }

  areArraysEqual(arr1: number[], arr2?: (number | undefined)[]): boolean {
    return JSON.stringify(arr1.sort((a, b) => a - b)) === JSON.stringify(arr2);
  }

  setSubModulePermissions() {
    this.role.name = this.selectedRole?.name;
    this.role.actions = [];
    this.role.actions = this.selectedRole?.privilegeActionResponseDTOs?.map(
      action => {
        return { id: action.actionDto.id, name: action.actionDto.name, assetId: action.actionDto.assetId }
      });
    this.cdf.detectChanges();
  }

  getRoles(): void {
    this.roleModulesService.getList<RoleModule>()
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: (res: Array<RoleModule>) => {
          this.roles = res;
          this.roles.forEach((module) => {
            module.checked = false;
          })
          this.isLoading = false;
          this.cdf.detectChanges()
        }, error: () => {
          this.isLoading = false;
        }
      })
  }

  getAssets(): void {
    this.assetsService.getList<Assets>()
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: (res: Array<Assets>) => {
          this.assets = res;
          this.cdf.detectChanges()
        }
      })
  }

  onModalClose() {
    this.onClose.emit();
    this.goBack()
  }

  onModalEdit() {
    this.isViewMode = false;
    this.pageTitle = 'Edit Role & Permissions';
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  onSubmit() {
    if (!this.role?.actions?.length) {
      this.toasterService.info(MESSAGES.roleShouldBeWithPermission);
      return;
    }
    this.isLoading = true;
    this.selectedRole?.id ? this.onUpdate() : this.onAdd();
  }

  onAdd(): void {
    this.roleService.add(this.addUpdateRoleParams).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Role) => {
        this.onClose.emit();
        this.onAddRole.emit(res);
        this.isLoading = false;
      }
    });
  }

  onUpdate(): void {
    this.roleService.update(this.addUpdateRoleParams).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Role) => {
        this.onClose.emit();
        this.onUpdateRole.emit(res);
        this.isLoading = false;
      }
    });
  }

  get addUpdateRoleParams() {
    return {
      id: this.selectedRole?.id,
      name: this.role.name,
      organizationId: 1,
      privilegeActionDtos: this.role.actions?.map(action => {
        return { actionId: action.id }
      }),
      privilegeAssetDtos: this.getPrivilegeRoleDtos()
    }
  }

  getPrivilegeRoleDtos() {
    for (const asset of this.assets) {
      const selectedAssets = this.role.actions?.filter((role) => {
        return role.assetId === asset.id
      })
      let assetAction = 'X';
      if (selectedAssets?.length) {
        for (const role of selectedAssets) {
          if (role.name) {
            assetAction = assetAction + this.getRoleAction(role.name)
          }
        }
      }
      asset.action = [...new Set(assetAction.split(''))].join('');
    }

    return this.assets.map((asset) => {
      return { assetId: asset.id, action: asset.action }
    });
  }

  getRoleAction(roleName: string): string {
    if (roleName.includes("Create")) {
      return 'C';
    }
    if (roleName.includes("Delete")) {
      return 'D';
    }
    if (roleName.includes("View")) {
      return 'R';
    }
    if (roleName.includes("Update")) {
      return 'U';
    }
    return ''
  }

  onToggleModule(event: any, roles: RoleModule): void {
    if (roles?.actions?.length) {
      if (event.checked) {
        this.role.actions = this.role.actions?.length
          ? [...this.role.actions, ...roles.actions]
          : roles.actions;
      } else {
        const actionIds = roles?.actions?.map(({ id }) => id);

        if (actionIds?.length) {
          this.role.actions = this.role?.actions?.filter(action => !actionIds.includes(action.id));
        }
      }
    }
    this.cdf.detectChanges();
  }


  onTogglePermission(selectedPermissions: IdNameModel[], permissionInfo: IdNameModel, roles: RoleModule): void {
    if (selectedPermissions.some(role => role.id === permissionInfo.id) && !permissionInfo?.name?.includes('View')) {
      const permissionName = this.removeFromString(['Create', 'Delete', 'Update'], permissionInfo.name ?? '')?.trim();
      const selectedActionViewPermission = roles?.actions?.find(r => r.name?.includes('View') && r.name.includes(permissionName));

      if (selectedActionViewPermission && !selectedPermissions.some(role => role.id === selectedActionViewPermission?.id)) {
        this.role.actions?.push(selectedActionViewPermission);
        this.toasterService.info(`View permissions for ${permissionName} is been enabled.`);
      }
    }

    roles.checked = roles?.actions?.every(action =>
      selectedPermissions.some(permission => permission.id === action.id)
    );
    this.cdf.detectChanges();
  }

  removeFromString(removeWords: string[], name: string) {
    return removeWords.reduce((result: string, word: string) => result.replace(word, ''), name)
  }

}
