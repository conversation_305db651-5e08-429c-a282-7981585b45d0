import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { TaskListFilter, TaskListItem } from '@pages/shops/models';
import { TaskService } from '@pages/shops/services/tasks.service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { DataType, IdNameModel, OperatorType, TreeOperatorType, ViewMode } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';

@Component({
  selector: 'app-task-list',
  templateUrl: './task-list.component.html',
  styleUrls: ['./task-list.component.scss']
})
export class TaskListComponent extends BaseComponent implements OnInit {

  tasks: TaskListItem[] = [];
  keyByTaskId = "taskType.id";
  @Input() filterParams: TaskListFilter = new TaskListFilter();
  @Output() updateTasks = new EventEmitter<void>();
  showCreateModal = false;
  isViewMode = false;
  showConfirmationDialog = false;
  selectedTask!: TaskListItem | null;
  taskStatuses: IdNameModel[] = [];
  isStatusList!: number | null;
  status!: string;
  taskId!: string;
  isActiveTab = true;
  isArchiveInProgress = false;

  constructor(private readonly taskService: TaskService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly router: Router) {
    super();
    this.paginationConfig.predicate = 'id';
    this.paginationConfig.itemsPerPageOptions = [5, 10, 25, 50, 100];
    this.paginationConfig.itemsPerPage = 5;
  }

  ngOnInit(): void {
    this.setFilterParams();
    this.getTaskStatuses();
  }

  setFilterParams(): void {
    this.filterParams.treeOperator = TreeOperatorType.OR;
    this.filterParams.values = [
      {
        dataType: DataType.LONG,
        key: this.keyByTaskId,
        operator: OperatorType.EQUAL,
        value: 1
      },
      {
        dataType: DataType.LONG,
        key: this.keyByTaskId,
        operator: OperatorType.EQUAL,
        value: 2
      },
      {
        dataType: DataType.LONG,
        key: this.keyByTaskId,
        operator: OperatorType.EQUAL,
        value: 3
      }
    ];
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.taskService.getListWithFiltersWithPagination<TaskListFilter, TaskListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.tasks.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.tasks = res.content;
          this.setActiveFlagForAll()
          this.setPaginationParamsFromPageResponse<TaskListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }
  private setActiveFlagForAll() {
    this.tasks.forEach(task => task.isActive = this.isActiveTab);
  }
  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.isViewMode = false;
    this.selectedTask = null;
    if (refreshList) {
      this.getAll();
    }
  }

  onViewEdit(task: TaskListItem, isEdit: boolean): void {
    this.isViewMode = isEdit ? false : true;
    this.router.navigate([], { queryParams: { id: task.id, mode: isEdit ? ViewMode.EDIT : ViewMode.READ } });
    this.selectedTask = task;
  }

  onDelete(task: TaskListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.taskDeleteWarning.replace('{record}', 'task'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(task);
      }
    });
  }

  onDeleteConfirmation(task: TaskListItem): void {
    this.taskService.delete(task.id, API_URL_UTIL.tasks.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.shopTaskDeleteSuccess);
          this.getAll();
        }
      });
  }

  private getTaskStatuses(): void {
    this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.statuses).pipe(takeUntil(this.destroy$)).subscribe({
      next: (taskStatuses) => {
        this.taskStatuses = taskStatuses;
      },
    });
  }

  findStatusIndex(index: number): void {
    this.isStatusList = index;
  }

  changeStatus(task: TaskListItem, status: number, id: number, isActive: any): void {
    const statusParam = {
      statusId: status
    }
    const endpoint = API_URL_UTIL.tasks.status.replace(':taskId', id.toString());
    this.taskService.update(statusParam, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.statusChangeSuccess);
      this.isStatusList = null;
      this.getAll();
    });
    if (status === 4) {
      const message = task.isActive
        ? MESSAGES.archiveWarning.replace('{record}', 'task')
        : MESSAGES.unArchiveWarning.replace('{record}', 'vendor');
      this.handleTaskConfirmation(task, isActive, message, true);
    }
  }

  onArchive(task: TaskListItem, isActive: boolean): void {
    this.selectedTask = task;
    const message = task.isActive
      ? MESSAGES.archiveWarning.replace('{record}', 'task')
      : MESSAGES.unArchiveWarning.replace('{record}', 'task');
    this.handleTaskConfirmation(task, isActive, message);
  }

  private handleTaskConfirmation(task: TaskListItem, isActive: boolean, message: string, isDialog = false): void {
    this.showConfirmationDialog = isDialog;
    this.cdf.detectChanges();
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: message,
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.isArchiveInProgress = true;
        this.showConfirmationDialog = false;
        this.onArchiveConfirmation(task);
        task.isActive = isActive;
      },
      reject: () => {
        task.isActive = !isActive;
        this.showConfirmationDialog = false;
        this.cdf.detectChanges();
      }
    });
  }

  private onArchiveConfirmation(task: TaskListItem): void {
    this.taskService.archivedTask((task.id).toString())
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.isActiveTab ? MESSAGES.taskArchiveSuccess : MESSAGES.taskUnArchiveSuccess);
          this.isArchiveInProgress = false;
          this.selectedTask = null;
          this.updateTasks.emit()
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedTask = null;
          this.getAll();
        }
      });
  }

  onBackdropClick(element: HTMLElement): void {
    Utils.handlePopoverBackdropClick(element, this.selectedTask, 'status', this.isActiveTab, this.cdf);
  }
}
