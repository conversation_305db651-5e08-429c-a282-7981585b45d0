<div class="card tabs crm-quotation">
  <div class="tab-content">
    <p-table
      class="no-column-selection"
      [value]="quatationListFormArray.controls"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      [rowHover]="true"
      [loading]="isLoading"
      [scrollable]="true"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th scope="col" pFrozenColumn>#Quote ID</th>
          <th scope="col">Lead Description</th>
          <th scope="col">#Stock</th>
          <th scope="col">Company</th>
          <th scope="col">Date Quoted</th>
          <th scope="col">Quoted By</th>
          <th scope="col">Quoted Price</th>
          <th scope="col">Retail Price</th>
          <th scope="col" class="min-width-300 justify-content-center">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td pFrozenColumn>
            {{ rowData?.value?.quotationId }}
          </td>
          <td>
            {{ rowData?.value?.leadDescription }}
          </td>
          <td>
            <button type="button" class="btn btn-link stock-btn" (click)="openInventoryDetailsModal(rowData.value)">{{ rowData?.value?.stock }}</button>
          </td>
          <td>
            {{ rowData?.value?.company }}
          </td>
          <td>
            {{ rowData?.value?.dateQuotated | date : constants.fullDateFormat }}
          </td>
          <td>
            {{ rowData?.value?.quotedBy }}
          </td>
          <td class="green-highlight"><span *ngIf="rowData?.value?.quotedPrice">$</span>{{ rowData?.value?.quotedPrice }}</td>
          <td><span>$</span>{{ rowData?.value?.retailPrice ? rowData?.value?.retailPrice : 0 }}</td>
          <td class="action min-width-300">
            <div class="actions-content align-items-center">
              <img
                [src]="constants.staticImages.icons.shareIcon"
                alt="Resend"
                pTooltip="Resend"
                tooltipPosition="top"
                class="resend-btn m-r-10"
                (click)="showQuotationDetails(rowData?.value)"
              />
              <div class="d-flex">
                <button
                  id="button-container"
                  type="button"
                  class="btn btn-primary"
                  (click)="onAccepted(rowData?.value?.quotationId)"
                  *ngIf="rowData?.value?.quotationStatus === null"
                >
                  Accept
                </button>
                <button
                  id="button-container"
                  type="button"
                  class="btn btn-secondary m-l-10"
                  (click)="onReject(rowData?.value?.quotationId)"
                  *ngIf="rowData?.value?.quotationStatus === null"
                >
                  Reject
                </button>
              </div>
              <span *ngIf="rowData?.value?.quotationStatus === quotationStatus.ACCEPTED" class="quotationAccepted">Accepted</span>
              <span *ngIf="rowData?.value?.quotationStatus === quotationStatus.REJECTED" class="quotationRejected">Rejected</span>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="6" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
  </div>
</div>
<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showRejectModel"
  position="right"
  (onHide)="showRejectModel = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-customer-reject-quotation (onClose)="onAddEditPopupClose($event)" [selectedQuotationId]="selectedQuotationId" *ngIf="showRejectModel">
  </app-crm-customer-reject-quotation>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-quotation-resend *ngIf="showCreateModal" [quotationId]="quotationId" [crmId]="customerLeadId" (saveQuotation)="onSaveQuotation()" (closeModal)="onCloseModal()">
  </app-crm-quotation-resend>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showInventoryDetails"
  (onHide)="showInventoryDetails = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  [fullScreen]="true"
  [baseZIndex]="10000"
  appendTo="body"
>
  <div class="modal-title sticky-top">
    <h4 class="header-title">Inventory Details</h4>
    <div>
      <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
    </div>
  </div>
  <app-inventory-details
    *ngIf="selectedInventoryId && selectedInventoryAbbreviation"
    [inventoryId]="selectedInventoryId"
    [dealerName]="selectedInventoryAbbreviation"
  ></app-inventory-details>
</p-sidebar>
