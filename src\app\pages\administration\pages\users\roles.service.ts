import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Role } from "@pages/administration/models";

@Injectable({ providedIn: 'root' })
export class RoleService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.roles.root;
  }

  fromServerModel(entity: Role): Role {
    return {
      ...entity,
      displayName: this.getFormattedRole(entity)
    }
  }

  private getFormattedRole(entity: Role): string {
    if (entity?.name?.includes('ROLE_')) {
      const roleName = entity.name.replace('ROLE_', '').replace('_', ' ');
      return roleName.charAt(0).toUpperCase() + roleName.slice(1)?.toLowerCase();
    }
    return '';
  }

}
