::ng-deep .add-wrapper {
  .p-tabview-nav {
    li {
      width: 20.66%;
      display: flex;
      justify-content: center;

      a {
        text-align: center;
        display: flex;
        justify-content: center;
      }
    }
  }

  .p-tabview-title {
    font-weight: 600;
  }

  .p-tabview .p-tabview-panels {
    padding: 0.5rem 0;
  }
}

::ng-deep .error {
  a {
    background-color: #ff1313 !important;
    color: white !important;
  }
}

.show-progress {
  width: 400px;
  height: 68px;
  padding: 15px;
  background: #e9e9ff;
  border: solid #696cff;
  border-width: 0 0 0 6px;
  color: #696cff;
  float: right;
}

@media only screen and (max-width: 500px) {
  ::ng-deep .add-wrapper {
    .p-tabview-nav {
      li {
        width: auto;
      }
    }
  }

  .document {
    ::ng-deep .p-tabview .p-tabview-panels {
      overflow: hidden;
    }
  }

  ::ng-deep .communication {
    z-index: 1;
  }

  ::ng-deep p-dialog {
    .p-dialog {
      width: unset !important;
    }
  }
}

@media only screen and (min-width: 500px) {
  ::ng-deep .financial-tab {
    .p-tabview-nav li.inventory-tab-headers:nth-last-child(2) {
      position: fixed;
      top: 0;
      right: 215px;
      height: 54px;
      width: 160px;
      z-index: 1;
    }

    .p-tabview-nav li.inventory-tab-headers:nth-last-child(3) {
      position: fixed;
      top: 0;
      right: 65px;
      height: 54px;
      width: 150px;
    }

    .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(2).p-highlight .p-tabview-nav-link,
    .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(3).p-highlight .p-tabview-nav-link {
      background-color: var(--card-bg-color);
    }

    .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(2) .p-tabview-nav-link,
    .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(3) .p-tabview-nav-link {
      background-color: var(--card-bg-color) !important;
    }

    .financial-tab .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(2):not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link,
    .financial-tab .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(3):not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link {
      background-color: var(--card-bg-color) !important;
    }
  }

  ::ng-deep .sale-tab {
    .p-tabview-nav li.inventory-tab-headers:nth-last-child(3) {
      position: fixed;
      top: 0;
      right: 364px;
      height: 54px;
      width: 130px;
    }

    .p-tabview-nav li.inventory-tab-headers:nth-last-child(4) {
      position: fixed;
      top: 0;
      right: 65px;
      height: 54px;
      width: 150px;
    }

    .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(3).p-highlight .p-tabview-nav-link,
    .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(4).p-highlight .p-tabview-nav-link {
      background-color: var(--card-bg-color);
    }

    .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(3) .p-tabview-nav-link,
    .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(4) .p-tabview-nav-link {
      background-color: var(--card-bg-color) !important;
    }

    .sale-tab .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(3):not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link,
    .sale-tab .p-tabview .p-tabview-nav li.inventory-tab-headers:nth-last-child(4):not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link {
      background-color: var(--card-bg-color) !important;
    }
  }
}
