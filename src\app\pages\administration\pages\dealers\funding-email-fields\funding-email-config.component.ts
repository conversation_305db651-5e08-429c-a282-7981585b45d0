import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { FundingEmailConfig } from '@pages/administration/models';
import { takeUntil } from 'rxjs';
import { FundingEmailConfigService } from './funding-email-fields.service';

@Component({
  selector: 'app-funding-email-config',
  templateUrl: './funding-email-config.component.html'
})
export class FundingEmailConfigComponent extends BaseComponent implements OnInit {
  @Output() onClose = new EventEmitter<void>();
  pageTitle = 'Funding Email Config'
  fundingFieldConfigList: FundingEmailConfig[] = [];
  selectedfields: number[] = [];
  isLoading = false;

  constructor(private readonly fundingEmailConfigService: FundingEmailConfigService,
              private readonly toasterService: AppToasterService
) {
    super();
  }

  ngOnInit(): void {
    this.getFundingEmailConfigs();
  }

  closePopup(): void {
    this.onClose.emit();
  }

  getFundingEmailConfigs(): void {
    this.fundingEmailConfigService.getList<FundingEmailConfig>().pipe(takeUntil(this.destroy$)).subscribe(res => {
      this.fundingFieldConfigList = res;
      this.selectedfields = res.filter(field => field.isIncluded === true).map(field => field.id);
    });
  }

  onSubmit(): void {
    this.isLoading = true;
    this.fundingEmailConfigService.update(this.updateFieldConfig).pipe(takeUntil(this.destroy$)).subscribe((res: FundingEmailConfig) => {
      this.isLoading = false;
      this.onClose.emit();
      this.toasterService.success(MESSAGES.updateFundingEmailConfigSuccess);
    });
  }

  private get updateFieldConfig(): FundingEmailConfig[] {
    return this.fundingFieldConfigList.map((field: any) => {
      if (this.selectedfields.includes(field.id)) {
        return { ...field, isIncluded: true };
      } else {
        return { ...field, isIncluded: false };
      }
    });
  }
}