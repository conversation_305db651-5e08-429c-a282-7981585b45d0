import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { CollapseModule } from 'ngx-bootstrap/collapse';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { SharedLibsModule } from 'src/app/@shared/shared-libs.module';
import { HeaderModule } from '../header/header.module';
import { MobileHeaderComponent } from './mobile-header.component';
@NgModule({
  declarations: [MobileHeaderComponent],
  imports: [
    SharedLibsModule, RouterModule, BsDropdownModule, DirectivesModule, FontAwesomeIconsModule, CollapseModule, BsDropdownModule, SidebarModule, HeaderModule
  ],
  exports: [MobileHeaderComponent]
})
export class MobileHeaderModule { }
