<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn class="top-header">
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="excelDownloadMenu.toggle($event)"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
    <p-menu #excelDownloadMenu [model]="excelDownloadMenuItems" [popup]="true"> </p-menu>
    <button class="btn btn-primary left" (click)="onAdd()" [appImageIconSrc]="constants.staticImages.icons.addNew" *appHasPermission="[permissionActions.CREATE_INCOMING_TRUCK]">
      <span class="m-l-15 show-label">Add New Incoming Truck</span>
    </button>
    <button class="btn btn-primary left column-btn show-label" (click)="toggleFilterSidebar()">
      <span class="show-label">Columns</span>
      <fa-icon [icon]="faIcons.faCaretDown"></fa-icon>
    </button>
  </div>
</app-page-header>

<div class="card incoming-truck-list">
  <div class="tab-content">
    <p-tabView (onChange)="onTabChanged($event)" [(activeIndex)]="activeIndex" [scrollable]="true" styleClass="dynamic-tabs">
      <p-tabPanel header="Pending">
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="incomingTruckTable"></ng-container>
        </ng-template>
      </p-tabPanel>
      <p-tabPanel header="Ready For Pickup">
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="incomingTruckTable"></ng-container>
        </ng-template>
      </p-tabPanel>
    </p-tabView>
  </div>
  <ng-template #incomingTruckTable>
    <p-table
      [columns]="selectedColumns"
      [value]="incomingTruckList"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="paginationConfig.predicate"
      [rowHover]="true"
      [loading]="isLoading"
      styleClass="p-datatable-gridlines"
      [resizableColumns]="true"
      columnResizeMode="expand"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <ng-container *ngFor="let col of columns">
            <th
              class="small-col"
              pResizableColumn
              pReorderableColumn
              [pReorderableColumnDisabled]="true"
              *ngIf="col.disable && col.name === 'VIN' && activeIndex === 0"
              [pSortableColumn]="col?.shortingKey"
              scope="col"
            >
              VIN <p-sortIcon [field]="col?.shortingKey"></p-sortIcon>
            </th>
            <th
              class="small-col"
              pResizableColumn
              pReorderableColumn
              [pReorderableColumnDisabled]="true"
              *ngIf="col.disable && col.name === 'Stock' && activeIndex === 1"
              [pSortableColumn]="col?.shortingKey"
              scope="col"
            >
              Stock <p-sortIcon [field]="col?.shortingKey"></p-sortIcon>
            </th>
            <th class="associate-col" pResizableColumn pReorderableColumn *ngIf="col.disable && col.name === 'Associated Stocks' && activeIndex === 1" scope="col">
              Associated Stocks
            </th>
            <th class="dynamic-col" pResizableColumn pReorderableColumn [pSortableColumn]="col?.shortingKey" [pSortableColumnDisabled]="!col.shorting" *ngIf="!col.disable">
              {{ col.name }}
              <p-sortIcon [field]="col.shortingKey || col.field" *ngIf="col.shorting"></p-sortIcon>
            </th>
            <th class="small-col cursor-default" pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" *ngIf="col.disable && col.name === 'Action'">
              {{ col.name }}
            </th>
          </ng-container>
        </tr>

        <tr class="inventory-search-tr">
          <ng-container *ngFor="let col of columns">
            <th class="small-col" pResizableColumn *ngIf="col.disable && col.name === 'VIN' && activeIndex === 0" scope="col">
              <span class="search-input"><input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" /> </span>
            </th>
            <th class="small-col" pResizableColumn *ngIf="col.disable && col.name === 'Stock' && activeIndex === 1" scope="col">
              <span class="search-input"><input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" /> </span>
            </th>
            <th pResizableColumn *ngIf="col.disable && col.name === 'Associated Stocks' && activeIndex === 1" scope="col"></th>
            <th pResizableColumn *ngIf="!col.disable" class="overflow-visible">
              <span class="search-input" *ngIf="col.type === 'DROP_DOWN'">
                <span *ngIf="col.shortingKey === 'status'">
                  <p-dropdown
                    [options]="inventoryStatusesCopy"
                    [disabled]="activeIndex === 0"
                    [(ngModel)]="col.value"
                    class="incoming-truck-list"
                    (onChange)="tableSearchByColumn($event, col)"
                    optionLabel="name"
                    optionValue="name"
                    [filter]="true"
                    filterBy="name"
                    [showClear]="col.value ? true : false"
                    placeholder="Select status"
                    appendTo="body"
                  >
                    <ng-template pTemplate="selectedItem">
                      <div class="country-item country-item-value" *ngIf="col.value">
                        <div>{{ col.value }}</div>
                      </div>
                    </ng-template>
                    <ng-template let-status pTemplate="item">
                      <div class="country-item">
                        <div>{{ status.name }}</div>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </span>
                <span *ngIf="col.shortingKey === 'unit.generalInformation.unitTypeCategory.id'">
                  <p-dropdown
                    appPreventClearFilter
                    [options]="categoryTypes"
                    [(ngModel)]="selectedCategoryId"
                    (onFocus)="getCategoryTypes()"
                    (onChange)="tableSearchByColumn($event, col)"
                    optionLabel="name"
                    optionValue="id"
                    [filter]="true"
                    filterBy="name"
                    [showClear]="selectedCategoryId ? true : false"
                    appendTo="body"
                    placeholder="Select Category"
                    (onClear)="onCategoryChange()"
                  >
                    <ng-template pTemplate="empty">
                      <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.category, data: categoryTypes }"></ng-container>
                    </ng-template>
                  </p-dropdown>
                </span>
              </span>
              <span class="search-input" *ngIf="col.shortingKey === 'unit.generalInformation.make.id'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="makes"
                  defaultLabel="Select a Make"
                  optionLabel="name"
                  optionValue="id"
                  (onFocus)="getMake()"
                  [maxSelectedLabels]="1"
                  selectedItemsLabel="{0} items selected"
                  [(ngModel)]="makeIds"
                  (onChange)="tableSearchByColumn($event, col)"
                  appendTo="body"
                  (onPanelHide)="getModels(makeIds, true)"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.make, data: makes }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="makeIds?.length" (click)="clearMakes(col)"></fa-icon>
              </span>

              <span class="search-input" *ngIf="col.shortingKey === 'unit.generalInformation.unitModel.id'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="models"
                  defaultLabel="Select a Model"
                  optionLabel="name"
                  optionValue="id"
                  (onFocus)="getModels()"
                  [maxSelectedLabels]="1"
                  selectedItemsLabel="{0} items selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [(ngModel)]="modelIds"
                  appendTo="body"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.model, data: models }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="modelIds?.length" (click)="clearModels(col)"></fa-icon>
              </span>

              <span class="search-input" *ngIf="col.shortingKey === 'unit.generalInformation.unitType.id'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="unitTypes"
                  defaultLabel="Select a Unit Type"
                  optionLabel="name"
                  optionValue="id"
                  (onFocus)="getUnitType()"
                  [maxSelectedLabels]="1"
                  [(ngModel)]="unitTypeIds"
                  selectedItemsLabel="{0} items selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  appendTo="body"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.unitType, data: unitTypes }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="unitTypeIds?.length" (click)="clearUnitTypes(col)"></fa-icon>
              </span>
              <span class="search-input" *ngIf="(col.type === 'STRING' || col.type === 'INTEGER') && col.key !== 'pickUpLocation'">
                <input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="col.type === 'DATE'">
                <p-calendar
                  appendTo="body"
                  [showIcon]="true"
                  [showButtonBar]="true"
                  [readonlyInput]="true"
                  inputId="startDateIcon"
                  (onSelect)="tableSearchByColumn($event, col)"
                  (onClearClick)="clearDate(col.key)"
                  [(ngModel)]="col.value"
                ></p-calendar>
              </span>
            </th>
            <th class="small-col" pResizableColumn *ngIf="col.disable && col.name === 'Action'">
              <button type="button" class="btn btn-primary btn-sm reset-btn" (click)="clearSearchInput()">Reset filters</button>
            </th>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-columns="columns" let-rowIndex="rowIndex">
        <tr>
          <ng-container *ngFor="let col of columns">
            <td *ngIf="col.name === 'VIN' && col.disable && activeIndex === 0" (click)="onViewEdit(rowData, false)" class="view-task">
              {{ getEvaluatedExpression(col.key, rowData) }}
            </td>
            <td *ngIf="col.name === 'Stock' && col.disable && activeIndex === 1" (click)="onViewEdit(rowData, false)" class="view-task">
              {{ rowData?.unit?.generalInformation?.stockNumber }}
            </td>
            <td *ngIf="col.name === 'Associated Stocks' && col.disable && activeIndex === 1">
              <span *ngFor="let associate of rowData?.unit?.unitAssociations; let index = index">
                <span class="view-task" (click)="onViewAssociation(associate.id)">
                  {{ associate?.stockNumber }}
                </span>
                <span *ngIf="index !== rowData?.unit?.unitAssociations?.length - 1"> , </span>
              </span>
            </td>
            <ng-container *ngIf="col.key && col.key !== null && !col.disable && col.type !== 'DROP_DOWN'">
              <td>
                <span *ngIf="col.type === 'DATE'">
                  {{ getEvaluatedExpression(col.key, rowData) | date : constants.dateFormat }}
                </span>
                <span *ngIf="col.type !== 'DATE'">
                  {{ getEvaluatedExpression(col.key, rowData) }}
                </span>
              </td>
            </ng-container>
            <td *ngIf="col.type === 'DROP_DOWN'" class="dynamic-col">
              <span *ngIf="col.name === 'Category'">
                {{ rowData?.unit?.generalInformation?.unitTypeCategory?.name }}
              </span>
              <span *ngIf="col.shortingKey === 'status'">
                <p-dropdown
                  class="w-150 incoming-truck-list"
                  appendTo="body"
                  [options]="inventoryStatuses"
                  (click)="storeCurrentValue(rowData?.status)"
                  optionLabel="name"
                  [(ngModel)]="rowData.status"
                  optionValue="value"
                  (onChange)="changeStatus(rowData?.status, rowData)"
                  [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_INCOMING_TRUCK]) || !rowData?.driverScheduleAddress?.state"
                >
                  {{ rowData?.status }}
                </p-dropdown>
              </span>
            </td>
            <td *ngIf="col.name === 'Action' && col.disable" class="actions">
              <div class="actions-content">
                <div *ngIf="activeIndex === 1">
                  <img
                    pTooltip="Add Specification"
                    *appHasPermission="[permissionActions.UPDATE_INCOMING_TRUCK]"
                    [src]="constants.staticImages.icons.share"
                    (click)="onViewEditClick(rowData)"
                    alt="share"
                  />
                </div>
                <div *appHasPermission="[permissionActions.UPDATE_INCOMING_TRUCK]">
                  <img pTooltip="Edit" [src]="constants.staticImages.icons.edit" alt="edit" (click)="onViewEdit(rowData, true)" />
                </div>
                <div *ngIf="activeIndex === 1">
                  <img pTooltip="Download PDF" [src]="constants.staticImages.icons.download" alt="download-pdf" (click)="selectedIncomingTruck = rowData; downloadPDF()" />
                </div>
                <div pTooltip="Delete" *appHasPermission="[permissionActions.DELETE_INCOMING_TRUCK]">
                  <img [src]="constants.staticImages.icons.deleteIcon" alt="delete" (click)="onDelete(rowData, $event)" />
                </div>
              </div>
            </td>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </ng-template>
</div>

<p-sidebar
  class="dealer"
  [(visible)]="showIncomingCreateModal"
  [fullScreen]="true"
  (onHide)="showIncomingCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-incoming-truck-add-wrapper
    (onClose)="onAddEditIncomingTruckPopupClose($event)"
    *ngIf="showIncomingCreateModal"
    [incomingTruckInfo]="selectedIncomingTruck"
    [isViewMode]="isIncomingTruckViewMode"
  >
  </app-incoming-truck-add-wrapper>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-inventory-add-wrapper
    (onClose)="onAddEditPopupClose($event)"
    *ngIf="showCreateModal"
    [inventoryIncomingInfo]="selectedInventory"
    [inventoryInfo]="inventoryInfo"
    [isEditMode]="isEditMode"
    [isViewMode]="isViewMode"
  >
  </app-inventory-add-wrapper>
</p-sidebar>

<p-sidebar
  [(visible)]="showColumnModal"
  position="right"
  (onHide)="showColumnModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-column-dropdown
    (onClose)="toggleColumnSidebar()"
    *ngIf="showColumnModal"
    [isModelVisible]="showColumnModal"
    [privateFilter]="defaultColumnsArray"
    [filterParams]="getFilterSaveParams()"
    [columnsList]="dropDownColumnList"
    (onSubmitCheck)="callFilterApiAgain()"
  >
  </app-column-dropdown>
</p-sidebar>

<p-confirmDialog *ngIf="!showConfirmationDialog"></p-confirmDialog>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <div class="d-flex justify-content-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
    <p *ngIf="!loader && !data?.length">No records found</p>
  </div>
</ng-template>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>

<p-confirmDialog
  acceptLabel="Proceed"
  rejectLabel="Cancel"
  styleClass="confirm-dialog"
  *ngIf="showConfirmationDialog"
  [breakpoints]="{ '960px': '60vw', '640px': '90vw' }"
  [style]="{ width: '30vw' }"
>
</p-confirmDialog>
