import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionForChildGuard } from '@core/guards';
import { PermissionActions } from 'src/app/@shared/models';
import { ShopsComponent } from './shops.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: ShopsComponent,
    title: 'Skeye - Shops',
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_SHOPS]
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ShopsRoutingModule { }
