import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, UntypedFormArray, Validators } from '@angular/forms';
import { RegExp } from '@constants/*';
import { BaseComponent } from '@core/utils';
import { Category } from '@pages/administration/pages/specification-configration/models/specification.model';
import { takeUntil } from 'rxjs';
import { CategoryService } from '../../category.service';

@Component({
  selector: 'app-category-add-update',
  templateUrl: './category-add-update.component.html',
  styleUrls: ['./category-add-update.component.scss']
})
export class CategoryAddUpdateComponent extends BaseComponent implements OnChanges, OnInit {

  @Input() selectedCategory!: Category | null;

  pageTitle!: string;
  blockSpecial: RegExp = RegExp.BlockSpecialCharAllowSpace;
  categoryEditFormGroup!: FormGroup;
  categoryAddFormGroup!: FormGroup;


  @Output() closeModal = new EventEmitter<void>();
  @Output() addNewCategory = new EventEmitter<Array<Category>>();
  @Output() updateCategoryDetails = new EventEmitter<Category>();

  constructor(
    private readonly categoryService: CategoryService,
    private readonly formBuilder: FormBuilder
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedCategory?.currentValue) {
      this.setPageConfigurations();
    }
  }

  ngOnInit(): void {
    this.initializeAddForm();
    this.pageTitle = this.selectedCategory ? 'Edit Category' : 'Add Category';
  }

  setPageConfigurations(): void {
    if (this.selectedCategory?.id) {
      this.pageTitle = 'Edit Category';
      this.initializeEditForm();
      this.categoryEditFormGroup.get('name')?.patchValue(this.selectedCategory?.name);
      return;
    }
    this.pageTitle = 'Add Category';
  }

  initializeEditForm(): void {
    this.categoryEditFormGroup = new FormGroup({
      name: new FormControl('', [Validators.required, Validators.maxLength(50)])
    });
  }

  initializeAddForm(): void {
    this.categoryAddFormGroup = this.formBuilder.group({
      category: this.newCategoryFormArray
    });
  }

  get newCategoryFormArray(): UntypedFormArray {
    return this.formBuilder.array([this.newCategoryFormGroup]);
  }

  get categoryFormArray(): UntypedFormArray {
    return this.categoryAddFormGroup.get('category') as UntypedFormArray;
  }

  get newCategoryFormGroup(): FormGroup {
    return this.formBuilder.group({
      name: new FormControl('', [Validators.required, Validators.maxLength(50)])
    });
  }

  onDeleteCategory(index: number): void {
    this.categoryFormArray.removeAt(index);
  }

  isAddNewCategoryVisible(index: number): boolean {
    return index === this.categoryFormArray.length - 1;
  }

  onAddNewCategory(): void {
    this.categoryFormArray.push(this.newCategoryFormGroup);
    document.getElementById('addModelBtn')?.scrollIntoView({
      behavior: 'smooth',
    });
  }

  onModalClose(): void {
    this.closeModal.emit();
    this.selectedCategory?.id ? this.categoryEditFormGroup?.reset() : this.clearAddCategoryForm();
  }

  clearAddCategoryForm(): void {
    this.categoryAddFormGroup?.reset();
    this.categoryFormArray?.clear();
    this.categoryFormArray.push(this.newCategoryFormGroup);
  }

  onUpdateCategory(): void {
    if (this.categoryEditFormGroup.invalid) {
      this.categoryEditFormGroup.markAllAsTouched();
      return;
    }
    this.updateCategory();
  }

  onAddCategory(): void {
    if (this.categoryAddFormGroup.invalid) {
      this.categoryAddFormGroup.markAllAsTouched();
      return;
    }
    this.addCategory();
  }

  addCategory(): void {
    this.categoryService.add<Array<Category>>(this.categoryFormArray.value).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Array<Category>) => {
        this.addNewCategory.emit(res);
        this.onModalClose();
      }
    });
  }

  updateCategory(): void {
    this.categoryService.update<Category>({ ...this.categoryEditFormGroup.getRawValue(), id: this.selectedCategory?.id }).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Category) => {
        this.updateCategoryDetails.emit(res);
        this.onModalClose();
      }
    });
  }

}
