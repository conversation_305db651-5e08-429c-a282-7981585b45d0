@import 'src/assets/scss/variables';

::ng-deep .stock-truck-list {
  .p-timeline-horizontal .p-timeline-event-connector {
    width: 100%;
    min-width: 100px;
    background-color: var(--active-color);
  }

  .p-timeline .p-timeline-event-marker {
    border-color: $placeholder-color;
    width: 1rem;
    height: 1rem;
  }

  .p-timeline-event-connector {
    flex-grow: unset;
  }

  .p-timeline-horizontal .p-timeline-event {
    flex: unset;
  }

  .p-timeline.p-timeline-horizontal .p-timeline-event-opposite,
  .p-timeline.p-timeline-horizontal .p-timeline-event-content {
    padding: 0;
  }

  .p-timeline-event-opposite {
    display: none;
  }

  .p-timeline-event-content {
    flex: unset;

    span {
      color: var(--text-color);
      font-size: 14px;
      letter-spacing: 0;
      line-height: 21px;
    }
  }

  .p-timeline.p-timeline-horizontal .p-timeline-event-opposite,
  .p-timeline.p-timeline-horizontal .p-timeline-event-content {
    width: max-content;
  }

  .p-timeline-event {
    min-height: unset;
  }

  .p-timeline-event-separator {
    span {
      border: 2px solid var(--active-color);
      background-color: var(--active-color);
      color: $white-color;
      border-radius: 50%;
      width: 1.5rem;
      height: 1.5rem;
      font-size: 0.8rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .p-datatable .p-datatable-tbody > tr > td {
    padding: 0.4rem 1rem;
  }
}

.timeline {
  .footer {
    display: flex;
    justify-content: space-between;
    margin-top: 2px;

    span {
      color: darken($placeholder-color, 5%);
      font-size: 11px;
      letter-spacing: 0;
      line-height: 18px;
    }

    em {
      font-size: 11px;
      margin-right: 2px;
    }

    .value {
      font-size: 12px;
      font-weight: 600;
    }
  }
}

.min-width-200 {
  min-width: 200px !important;
}

.initials {
  left: -10px;
  width: 40px;
  height: 25px;
  background-color: var(--app-background-color);
  color: var(--text-color);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  top: 25%;
  border: 1px solid $placeholder-color;
  margin-left: 5px;
  padding: 3px;
}

.w-190 {
  width: 190px !important;
}

::ng-deep .stock-truck p-dropdown {
  height: 22px;
}

::ng-deep .stock-truck {
  .p-dropdown .p-dropdown-label {
    background: var(--active-color) !important;
    color: $white-color !important;
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: 14px;
  }

  .p-dropdown .p-dropdown-trigger {
    background: var(--active-color) !important;
    color: $white-color !important;
  }

  .p-dropdown {
    background-color: var(--active-color) !important;
    height: inherit;
  }

  .pi-chevron-down:before {
    font-size: 14px;
  }

  .priority-icon {
    padding: 17px !important;
  }
}

@media only screen and (max-width: 860px) {
  .top-header {
    .column-btn {
      margin-top: 1rem;
    }
  }
}

.top-header {
  .column-btn {
    margin-left: 10px;
    color: #0b0b69;
    background-color: #fff;
    border-color: #0b0b69;
    font-weight: 600;
    border: 3px solid #0b0b69;
    padding: 0px 17px !important;

    fa-icon {
      margin-left: 5px;
      font-size: 16px;
      margin-right: 9px;
    }
  }
}

::ng-deep .inventory-search-tr th {
  .search-input {
    input {
      width: 4rem;
      height: 36px !important;
    }

    p-calendar.p-inputwrapper {
      span.p-calendar-w-btn {
        height: 36px !important;
      }
    }
  }

  .float-end img {
    cursor: pointer;
  }

  .reset-icon {
    fa-icon {
      color: var(--active-color) !important;
      font-size: 23px;
    }
  }
}
