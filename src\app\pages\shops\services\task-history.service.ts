import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Utils } from "src/app/@shared/services";
import { EventType, ParsedData, TaskHistoryListItem } from "../models";

@Injectable({ providedIn: 'root' })
export class TaskHistoryService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.taskHistory.root;
  }

  fromServerModel(apiRes: TaskHistoryListItem): TaskHistoryListItem {
    return {
      ...apiRes,
      parsedData: this.getParsedData(apiRes)
    }
  }

  getParsedData(apiRes: TaskHistoryListItem): ParsedData[] {
    let returnObject: ParsedData[] = []
    switch (apiRes.eventType) {
      case EventType.CREATED:
        returnObject.push({ fieldLabel: '', oldValue: '', newValue: '' });
        break;
      case EventType.MODIFIED:
        returnObject = (Utils.compareTwoObjects(JSON.parse(apiRes?.oldData), JSON.parse(apiRes?.newData)));
        break;
      case EventType.DELETED:
        returnObject.push({ fieldLabel: '', oldValue: '', newValue: '' });
        break;
      default:
        returnObject.push({ fieldLabel: '', oldValue: '', newValue: '' });
        break;
    }
    return returnObject;
  }
}
