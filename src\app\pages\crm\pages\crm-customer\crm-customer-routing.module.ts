import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { CrmCustomerListComponent } from "./crm-customer-list/crm-customer-list.component";
import { CrmCustomerComponent } from "./crm-customer.component";

const routes: Routes = [
  {
    path: '',
    component: CrmCustomerComponent,
    title: 'Skeye - Crm',
    children: [
      {
        path: '',
        component: CrmCustomerListComponent,
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class CRMCustomerRoutingModule { }
