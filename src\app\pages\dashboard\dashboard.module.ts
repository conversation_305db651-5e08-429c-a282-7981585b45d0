import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { ChartModule } from 'primeng/chart';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { DashboardComponent } from './dashboard.component';
import { InventoryAgingChartComponent } from './inventory-aging-chart/inventory-aging-chart.component';
import { InventoryPreferenceListComponent } from './inventory-preference-list/inventory-preference-list.component';
import { PendingTaskChartComponent } from './pending-task-chart/pending-task-chart.component';
import { RecentlyAddedInventoryComponent } from './recently-added-inventory/recently-added-inventory.component';
import { SalesByMonthChartComponent } from './sales-by-month-chart/sales-by-month-chart.component';
import { TaskAlertsListComponent } from './task-alerts-list/task-alerts-list.component';

@NgModule({
  declarations: [
    DashboardComponent,
    PendingTaskChartComponent,
    InventoryPreferenceListComponent,
    InventoryAgingChartComponent,
    SalesByMonthChartComponent,
    TaskAlertsListComponent,
    RecentlyAddedInventoryComponent
  ],

  imports: [
    CommonModule,
    SharedComponentsModule,
    FontAwesomeIconsModule,
    DirectivesModule,
    TableModule,
    ChartModule,
    SidebarModule,
    RouterModule.forChild([
      {
        path: '',
        component: DashboardComponent,
        data: {
          title: 'Dashboard',
          robots: 'noindex, nofollow',
        },
      },
    ]),
  ],
})
export class DashboardModule { }
