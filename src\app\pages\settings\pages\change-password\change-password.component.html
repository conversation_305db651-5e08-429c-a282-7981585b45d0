<div class="wrapper">
  <form [formGroup]="changePasswordFormGroup" (ngSubmit)="onChangePassword()">
    <div class="row">
      <div class="form-group col-lg-6 col-12">
        <label>Current password</label>
        <input formControlName="currentPassword" type="password" class="form-control password" appPasswordEye placeholder="Enter your current password" appPasswordValidation />
        <app-error-messages [control]="changePasswordFormGroup?.controls?.currentPassword"></app-error-messages>
      </div>
    </div>
    <div class="row">
      <div class="form-group col-lg-6 col-12">
        <label>New password</label>
        <input formControlName="newPassword" type="password" appPasswordEye class="form-control password" placeholder="Enter your new password" appPasswordValidation/>
        <app-error-messages [control]="changePasswordFormGroup.controls?.newPassword"></app-error-messages>
      </div>
      <div class="form-group col-lg-6 col-12">
        <label>Confirm new password</label>
        <input formControlName="confirmPassword" type="password" appPasswordEye class="form-control password" placeholder="Enter your new password again" appPasswordValidation/>
        <app-error-messages [control]="changePasswordFormGroup.controls?.confirmPassword"></app-error-messages>
      </div>
    </div>
    <div>
      <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Update Password</button>
    </div>
  </form>
</div>
