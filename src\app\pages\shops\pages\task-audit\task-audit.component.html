<div>
  <div *ngFor="let content of taskHistory">
    <div *ngIf="content?.eventType === EventType.CREATED">
      <div *ngIf="content?.taskEvent === taskEvent.TASK; else comment">
        <strong>{{content?.userName}}</strong>
        created task -
        {{(content?.eventDate | date :
        constants.fullDateFormat)}}
      </div>
      <ng-template #comment>
        <strong>{{content?.userName}}</strong>
        added comment - {{(content?.eventDate | date :
        constants.fullDateFormat)}}

        <div class="div-changed-data row font-weight-700">
          <div>Field</div>
          <div>Value</div>
        </div>
        <div class="div-changed-data row">
          <div>
            Comment
          </div>
          <div class="overflow-text">
            <span [innerHTML]="getCommentAdded(content)"> </span>
          </div>
        </div>
      </ng-template>
      <p-divider></p-divider>
    </div>
    <div *ngIf="content?.eventType === EventType.MODIFIED && content?.parsedData?.length">
      <strong>{{content?.userName}}</strong>
      made changes -
      {{(content?.eventDate | date :
      constants.fullDateFormat)}}
      <div class="div-changed-data row font-weight-700">
        <div>Field</div>
        <div>Old Value</div>
        <div>New Value</div>
      </div>
      <div *ngFor="let parsedData of content?.parsedData" class="div-changed-data row">
        <div class="overflow-text">
          {{parsedData?.fieldLabel}}
        </div>
        <div class="overflow-text">
          <span [innerHTML]="parsedData?.oldValue"> </span>
        </div>
        <div class="overflow-text">
          <span [innerHTML]="parsedData?.newValue"></span>
        </div>
      </div>
      <p-divider></p-divider>
    </div>
    <div *ngIf="content?.eventType === EventType.DELETED && content?.parsedData?.length">
      <strong>{{content?.userName}}</strong>
      deleted {{content?.taskEvent | lowercase}} -
      {{(content?.eventDate | date :
      constants.fullDateFormat)}}
      <div class="div-changed-data row">
        <div>Field</div>
        <div>Old Value</div>
        <div>New Value</div>
      </div>
      <div *ngFor="let parsedData of content?.parsedData" class="div-changed-data row">
        <div class="overflow-text">
          {{parsedData?.fieldLabel}}
        </div>
        <div class="overflow-text">
          <span [innerHTML]="parsedData?.oldValue"> </span>
        </div>
        <div class="overflow-text">
          <span [innerHTML]="parsedData?.newValue"></span>
        </div>
      </div>
      <p-divider></p-divider>
    </div>
    <div *ngIf="content?.eventType === EventType.MODIFIED && !content?.parsedData?.length">
      <strong>{{content?.userName}}</strong>
      made changes -
      {{(content?.eventDate | date :
      constants.fullDateFormat)}}
      <p-divider></p-divider>
    </div>
  </div>
</div>
