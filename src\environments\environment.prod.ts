
import { versions } from './versions';

export const environment = {
  versions,
  production: true,
  apiUrl: 'https://api.skeye.cloud/api',
  persistUserSession: true, // if the user login session needs to be persisted across browser tabs
  sentryKey: 'https://<EMAIL>/58',
  enableOtpBasedLogin: false, // true means user will be able to login with otp else will have to login with email & password only
  showButtonToAddDummyData: false,
  googleMapApiKey: 'AIzaSyC928Cd5M-PyrNF0nQeDzLWTv7gJjNrIeE',
  firebase: {
    apiKey: "AIzaSyCqOeMGHnMnpY55tD-OO51rmHF7tB2RyX8",
    authDomain: "skeyedeveloper-447ea.firebaseapp.com",
    projectId: "skeyedeveloper-447ea",
    storageBucket: "skeyedeveloper-447ea.appspot.com",
    messagingSenderId: "934556463127",
    appId: "1:934556463127:web:9af91145eb2420fc7cac52"
  },
  forntendUrl: 'https://app.skeye.cloud/'
};
