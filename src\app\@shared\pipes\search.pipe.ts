import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'searchFilter'
})

export class SearchPipe implements PipeTransform {
  transform(element: any[], searchInput: string): any[] {
    if (!element) {
      return [];
    }
    if (!searchInput) {
      return element;
    }
    searchInput = searchInput.toLowerCase();
    return element.filter(
      x => JSON.stringify(x).toLowerCase().includes(searchInput)
    )
  }
}
