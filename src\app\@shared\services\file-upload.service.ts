import { HttpClient, HttpEventType, HttpHeaders, HttpRequest, HttpResponse } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { API_URL_UTIL } from "@core/utils";
import { BehaviorSubject, Observable, map, skipWhile, tap } from "rxjs";
import { FileUploadProgress } from "../models";

@Injectable({ providedIn: 'root' })
export class FileUploadService {

  private readonly fileUploads$: BehaviorSubject<FileUploadProgress[]> = new BehaviorSubject([] as FileUploadProgress[]);
  fileUploadProgresses: FileUploadProgress[] = [];

  constructor(private readonly httpClient: HttpClient) { }

  setFileUploads(fileUploads: FileUploadProgress[]) {
    this.fileUploads$.next(fileUploads);
  }

  uploadFile(file: FormData, fileUploadIndex: number, filePath: string, folderName: string, fileUploadProgresses: FileUploadProgress[]): Observable<any> {
    const headers = new HttpHeaders({ 'ngsw-bypass': '' });
    const apiUrl = `${API_URL_UTIL.fileUpload.root}/${filePath}?type=${folderName}`;
    const req = new HttpRequest('POST', apiUrl, file, { reportProgress: true, headers });
    return this.httpClient.request<any>(req).pipe(
      tap((event: any) => {
        if (event.type === HttpEventType.UploadProgress) {
          if (event?.total) {
            const progress = Math.round(event.loaded / event?.total * 100);
            fileUploadProgresses[fileUploadIndex].progress$.next(progress);
            fileUploadProgresses[fileUploadIndex].isResolved = false;
          }
        }
        if (event.type === HttpEventType.Response) {
          fileUploadProgresses[fileUploadIndex].isResolved = true;
        }
      }),
      skipWhile(event => !(event instanceof HttpResponse)),
      map(response => response['body'])
    );
  }

  uploadFiles(file: FormData, filePath: string, folderName: string, fileUploadProgresses: FileUploadProgress[]): Observable<any> {
    const headers = new HttpHeaders({ 'ngsw-bypass': '' });
    const apiUrl = `${API_URL_UTIL.fileUpload.files}/${filePath}?type=${folderName}`;
    const req = new HttpRequest('POST', apiUrl, file, { reportProgress: true, headers });
    return this.httpClient.request<any>(req).pipe(
      tap((event: any) => {
        for (const [i] of fileUploadProgresses.entries()) {
          if (event.type === HttpEventType.UploadProgress) {
            if (event?.total) {
              const progress = Math.round(event.loaded / event?.total * 100);
              fileUploadProgresses[i].progress$.next(progress);
              fileUploadProgresses[i].isResolved = false;
            }
          }
          if (event.type === HttpEventType.Response) {
            fileUploadProgresses[i].isResolved = true;
          }
        }
      }),
      skipWhile(event => !(event instanceof HttpResponse)),
      map(response => response['body'])
    );
  }

  deleteFile(fileUrls: string[]): Observable<void> {
    return this.httpClient.delete<void>(API_URL_UTIL.fileUpload.delete, { body: fileUrls });
  }

  downloadFile(fileUrl: string, fileName: string): Observable<ArrayBuffer> {
    const params = {
      url: fileUrl,
      name: fileName
    };
    return this.httpClient.post<ArrayBuffer>(API_URL_UTIL.inventory.downloadDocuments, { ...params }, { responseType: 'arraybuffer' as any });
  }

}
