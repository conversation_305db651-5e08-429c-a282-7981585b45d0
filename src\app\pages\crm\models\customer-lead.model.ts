import { InventorySpecification } from "@pages/inventory/models";
import { GenericFilterParams, IdNameModel } from "src/app/@shared/models";

export class CustomerLeadListFilter extends GenericFilterParams {
  archived = false;
}

export class CustomerLeadListItem {
  id!: string;
  minYear!: number;
  maxYear!: number;
  unitType!: IdNameModel;
  matchingCount!: number;
  year!: number;
  makes!: Make[];
  unitModels!: UnitModel[];
  status!: string;
  contactName!: string;
  lastActivity!: number | string | null;
  deleted!: false;
  location!: boolean;
  reporter!: IdNameModel;
  specificationData!: Array<InventorySpecification>;
  category!: Category;
  createdDate!: Date;
  leadDescription!: string
}

export interface Make {
  id: number
  makeId: number
  makeName: string
  customerLeadId: string
}

export interface UnitModel {
  id: number
  unitModelId: number
  unitModelName: string
  customerLeadId: string
}

export interface Category {
  id: number;
  name: string;
  delete: boolean;
}

export const CustomerStatusList = [
  { value: 'OPEN', name: 'Open' },
  { value: 'CLOSE', name: 'Close' },
  { value: 'DEAL', name: 'Deal' }
]

export const CustomerCopyStatusList = [
  { value: 'ALL', name: 'All' },
  { value: 'OPEN', name: 'Open' },
  { value: 'CLOSE', name: 'Close' },
  { value: 'DEAL', name: 'Deal' }
]
export interface CustomerCreateParams {
  id: number,
  year: string,
  unitType: string,
  engineMake: string,
  engineModel: string,
  engineHp: string,
  transmissionType: string,
  notes: string,
  makes: CustomerLeadMakes[],
  unitModels: CustomerLeadUnitModels[],
  designationId: number,
  status: number,
  crmContactId: number,
  categoryId: number,
  specificationData: Array<InventorySpecification>
}

export interface CustomerLeadMakes {
  id?: number;
  makeId: number;
  makeName?: string;
  customerLeadId?: string | null;
}
export interface CustomerLeadUnitModels {
  id?: number;
  unitModelId: number;
  unitModelName?: string;
  customerLeadId?: string | null;
}

export interface CustomerDetails {
  id: string,
  year: string,
  unitType: IdNameModel,
  engineMake: IdNameModel,
  engineModel: IdNameModel,
  engineHp: IdNameModel,
  transmissionType: IdNameModel,
  notes: string,
  make: IdNameModel,
  unitModel: string,
  designation: IdNameModel,
  status: string,
  crmContact: CrmContact,
  deleted: boolean,
  createdDate: string,
  location: string,
  salesPerson: SalesPerson
}

export interface CrmContact {
  id: number,
  company: string,
  contactName: string,
  email: string,
  phoneWork: number,
  streetAddress: number,
  city: string,
  state: string,
  zipcode: string,
  country: string,
  creator: IdNameModel
}

export interface SalesPerson {
  id: number,
  name: string,
  phoneNumber: number,
  email: string
}
