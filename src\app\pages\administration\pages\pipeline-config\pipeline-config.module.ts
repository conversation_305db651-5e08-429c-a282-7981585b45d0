import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { ConfirmationService } from 'primeng/api';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TimelineModule } from 'primeng/timeline';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipelineAddComponent } from './pipeline-add/pipeline-add.component';
import { PipelineConfigRoutingModule } from './pipeline-config-routing.module';
import { PipelineConfigComponent } from './pipeline-config.component';
import { PipelineListComponent } from './pipeline-list/pipeline-list.component';

@NgModule({
  declarations: [
    PipelineConfigComponent,
    PipelineAddComponent,
    PipelineListComponent
  ],
  imports: [
    CommonModule,
    PipelineConfigRoutingModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    TimelineModule
  ],
  providers: [ConfirmationService],
  exports: [PipelineAddComponent]
})
export class PipelineConfigModule { }
