import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { PassedParamGroup, VendorExpensesCreateParam, VendorListItem } from '@pages/administration/models';
import { ExpensesListFilter, ExpensesListItem } from '@pages/inventory/models';
import { ExpensesService } from '@pages/inventory/services/expenses.service';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-vendor-expenses-list',
  templateUrl: './vendor-expenses-list.component.html',
  styleUrls: ['./vendor-expenses-list.component.scss']
})
export class VendorExpensesListComponent extends BaseComponent implements OnInit {
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() vendorExpenseInfo!: VendorListItem | null;
  @Input() filterParams: ExpensesListFilter = new ExpensesListFilter();
  @Input() filterParam: VendorExpensesCreateParam = new VendorExpensesCreateParam();
  expenses: ExpensesListItem[] | VendorExpensesCreateParam[] = [];
  vendorExpenseFormGroup!: FormGroup;
  constructor(private readonly expensesService: ExpensesService, private readonly fb: FormBuilder, private readonly cdf: ChangeDetectorRef) {
    super()
  }

  ngOnInit() {
    this.getAll();
    this.initializeFormGroup();
  }

  private initializeFormGroup(): void {
    this.vendorExpenseFormGroup = this.fb.group({
      startDateGroup: this.startDateFormGroup,
      endDateGroup: this.endDateFormGroup,
      treeOperator: new FormControl(null),
      values: new FormControl([])
    });
  }

  reset(): void {
    this.vendorExpenseFormGroup.reset();
    this.getAll();
    this.initializeFormGroup();
  }

  get vendorStartDateFormGroup(): FormGroup {
    return this.vendorExpenseFormGroup.get('startDateGroup') as FormGroup;
  }

  get vendorEndDateFormGroup(): FormGroup {
    return this.vendorExpenseFormGroup.get('endDateGroup') as FormGroup;
  }

  get startDateFormGroup(): FormGroup {
    return this.fb.group({
      value: new FormControl(null, Validators.required),
      key: new FormControl(PassedParamGroup.transDate),
      dataType: new FormControl(PassedParamGroup.date),
      operator: new FormControl(PassedParamGroup.greaterThanEqual),
    })
  }

  get endDateFormGroup(): FormGroup {
    return this.fb.group({
      value: new FormControl(null, Validators.required),
      key: new FormControl(PassedParamGroup.transDate),
      dataType: new FormControl(PassedParamGroup.date),
      operator: new FormControl(PassedParamGroup.lessThanEqual),
    })
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.vendorId = this.vendorExpenseInfo?.id;
    this.filterParams.orderBy = this.orderBy;
    this.expensesService.getListWithFiltersWithPagination<ExpensesListFilter, ExpensesListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.expenses.expensesList)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.expenses = res.content;
          this.setPaginationParamsFromPageResponse<ExpensesListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onCancel(): void {
    this.onClose.emit(true);
  }

  get createParams(): VendorExpensesCreateParam {
    return {
      ...this.vendorExpenseFormGroup.value,
      vendorId: this.vendorExpenseInfo?.id,
      treeOperator: this.vendorExpenseFormGroup.value.values.length === 1 ? PassedParamGroup.noop : PassedParamGroup.and,
    };
  }

  onSubmit(): void {
    this.vendorExpenseFormGroup.value.values = [];
    if (this.vendorExpenseFormGroup.value.startDateGroup.value) {
      const startDate = new Date(this.vendorExpenseFormGroup.value.startDateGroup.value).toISOString();
      this.setStartDate(startDate);
      this.vendorExpenseFormGroup.value.values.push(this.vendorExpenseFormGroup.value.startDateGroup);
    }
    if (this.vendorExpenseFormGroup.value.endDateGroup.value) {
      const endDate = new Date(this.vendorExpenseFormGroup.value.endDateGroup.value).toISOString();
      this.setEndDate(endDate);
      this.vendorExpenseFormGroup.value.values.push(this.vendorExpenseFormGroup.value.endDateGroup);
    }
    this.filterParam = this.createParams;
    this.expensesService.getListWithFiltersWithPagination<VendorExpensesCreateParam, VendorExpensesCreateParam>(this.filterParam, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.expenses.expensesList)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.expenses = res.content;
          this.setPaginationParamsFromPageResponse<VendorExpensesCreateParam>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  setEndDate(endDate: string): void {
    const setEndHours = new Date(endDate).setUTCHours(23);
    const setEndDate = new Date(setEndHours).toISOString();
    const setEndMin = new Date(setEndDate).setUTCMinutes(59);
    const setEndDateMin = new Date(setEndMin).toISOString();
    const setEndSec = new Date(setEndDateMin).setUTCSeconds(59);
    const finalEndDate = new Date(setEndSec).toISOString();
    this.vendorExpenseFormGroup.value.endDateGroup.value = finalEndDate;
  }

  setStartDate(startDate: string): void {
    const setStartHours = new Date(startDate).setUTCHours(0);
    const setStartDate = new Date(setStartHours).toISOString();
    const setStartMin = new Date(setStartDate).setUTCMinutes(0);
    const finalStartDate = new Date(setStartMin).toISOString();
    this.vendorExpenseFormGroup.value.startDateGroup.value = finalStartDate;
  }
}
