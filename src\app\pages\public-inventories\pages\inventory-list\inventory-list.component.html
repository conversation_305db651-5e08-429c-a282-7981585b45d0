<div class="page-wrapper public-page" *ngIf="!openModal">
  <header>
    <div class="logo row">
      <div class="col-md-3">
        <div class="dealer-name ms-3">
          <p-dropdown appPreventClearFilter appendTo="body" [options]="dealerOptions" optionLabel="name" optionValue="abbreviation"
            [filter]="true" filterBy="name" placeholder="Select Dealership" [(ngModel)]="selectedDealer"
            (onChange)="changeDealer()">
            <ng-template pTemplate="empty">
              <ng-container [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: dealerOptionsLoader, data: dealerOptions }"></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <ng-template #emptyMessage let-loader="loader" let-data="data">
            <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
            <p *ngIf="!loader && !data?.length">No records found</p>
          </ng-template>
        </div>
      </div>

      <div class="search-wrapper col-md-9">
        <input
          type="text"
          class="form-control header-search"
          pInputText
          [(ngModel)]="headerSearch"
          placeholder="Enter Stock Number"
          (keyup.enter)="onHeaderSearch()"
        />
        <button type="button" class="btn public-page-action-btn" (click)="onHeaderSearch()">Search</button>
      </div>
    </div>
  </header>

  <div class="row inventory-wrapper">
    <ng-container *ngIf="!noDealerExist; else noSuchDealerExist">
      <div class="col-md-3 col-sm-12 filters-wrapper">
        <div class="title-wrapper wrapper-section">
          <span>Filter</span>
          <span class="clear-all" (click)="clearAll()">Clear All</span>
        </div>
        <form>
          <div class="category wrapper-section" *ngIf="categories?.length">
            <ng-container *ngFor="let category of categories">
              <div class="p-field-radiobutton">
                <p-radioButton
                  name="category"
                  [value]="category?.id"
                  [inputId]="category.name"
                  (click)="onCategoryChange(category?.id)"
                  [(ngModel)]="selectedCategory"
                >
                </p-radioButton>
                <label for="truck">{{ category?.name }}</label>
              </div>
            </ng-container>
          </div>
          <div class="wrapper-section" *ngIf="unitTypes?.length">
            <input
              type="text"
              pInputText
              name="search"
              [(ngModel)]="searchUnitType"
              placeholder="Search Unit Type"
              class="form-control search-box"
            />
            <ng-container *ngIf="unitTypes?.length">
              <span>
                <ng-container *ngFor="let unitType of unitTypes | searchFilter : searchUnitType; let i = index; trackBy: trackByFunction">
                  <div class="checkbox" *ngIf="showAllUnits || i < 5">
                    <p-checkbox
                      name="unitType"
                      class="m-r-9"
                      [value]="unitType"
                      [label]="unitType.name"
                      (click)="onUnitTypeChange()"
                      [(ngModel)]="selectedUnitTypes"
                    ></p-checkbox>
                  </div>
                </ng-container>
                <div
                  class="more-records"
                  *ngIf="(unitTypes | searchFilter : searchUnitType).length > 5 && !showAllUnits"
                  (click)="showAllUnits = true"
                >
                  + {{ (unitTypes | searchFilter : searchUnitType).length - 5 }} More
                </div>
              </span>
            </ng-container>
          </div>
          <div class="wrapper-section" *ngIf="makes?.length">
            <input
              type="text"
              pInputText
              name="search"
              [(ngModel)]="searchMake"
              placeholder="Search Make"
              class="form-control search-box"
            />
            <ng-container *ngIf="makes?.length">
              <span>
                <ng-container *ngFor="let make of makes | searchFilter : searchMake; let i = index; trackBy: trackByFunction">
                  <div class="checkbox" *ngIf="showAllMakes || i < 5">
                    <p-checkbox
                      name="make"
                      class="m-r-9"
                      [value]="make"
                      [label]="make?.name"
                      (click)="onMakeChange()"
                      [(ngModel)]="selectedMakes"
                    >
                    </p-checkbox>
                  </div>
                </ng-container>
                <div
                  class="more-records"
                  *ngIf="(makes | searchFilter : searchMake).length > 5 && !showAllMakes"
                  (click)="showAllMakes = true"
                >
                  + {{ (makes | searchFilter : searchMake).length - 5 }} More
                </div>
              </span>
            </ng-container>
          </div>
          <div class="wrapper-section" *ngIf="models?.length && selectedMakes">
            <input
              type="text"
              pInputText
              name="search"
              [(ngModel)]="searchModel"
              placeholder="Search Model"
              class="form-control search-box"
            />
            <ng-container *ngIf="models?.length">
              <span>
                <ng-container *ngFor="let model of models | searchFilter : searchModel; let i = index; trackBy: trackByFunction">
                  <div class="checkbox" *ngIf="showAllModel || i < 5">
                    <p-checkbox
                      name="model"
                      class="m-r-9"
                      [value]="model"
                      [label]="model?.name"
                      (click)="onModelChange()"
                      [(ngModel)]="selectedModels"
                    ></p-checkbox>
                  </div>
                </ng-container>
                <div
                  class="more-records"
                  *ngIf="(models | searchFilter : searchModel).length > 5 && !showAllModel"
                  (click)="showAllModel = true"
                >
                  + {{ (models | searchFilter : searchModel).length - 5 }} More
                </div>
              </span>
            </ng-container>
          </div>
          <div class="wrapper-section">
            <div class="title">Year</div>
            <div class="date-picker-wrapper">
              <div class="from">
                <label>From</label>
                <p-calendar
                  appendTo="body"
                  name="selectedFromYear"
                  [showButtonBar]="true"
                  [readonlyInput]="true"
                  [(ngModel)]="selectedFromYear"
                  dataType="string"
                  view="year"
                  dateFormat="yy"
                  (onSelect)="onStartDateChange()"
                  (onClearClick)="onStartDateChange()"
                >
                </p-calendar>
              </div>
              <div class="dash">-</div>
              <div class="to">
                <label>To</label>
                <p-calendar
                  appendTo="body"
                  name="selectedToYear"
                  [showButtonBar]="true"
                  [readonlyInput]="true"
                  [(ngModel)]="selectedToYear"
                  dataType="string"
                  view="year"
                  dateFormat="yy"
                  (onSelect)="onEndDateChange()"
                  (onClearClick)="onEndDateChange()"
                >
                </p-calendar>
              </div>
            </div>
          </div>
          <div class="wrapper-section">
            <div class="title">Price</div>
            <div class="price-wrapper">
              <div class="min">
                <label>Min Price</label>
                <div class="d-flex align-items-center mr-1">
                  <span>$</span>
                  <input
                    min="0"
                    max="100"
                    type="number"
                    name="minPrice"
                    [(ngModel)]="minPrice"
                    pInputText
                    class="form-control"
                    (input)="onMinPriceChange()"
                  />
                </div>
              </div>
              <div class="dash">-</div>
              <div class="max">
                <label>Max Price</label>
                <div class="d-flex align-items-center mr-1">
                  <span>$</span>
                  <input
                    [max]="maxValue"
                    type="number"
                    name="maxPrice"
                    [(ngModel)]="maxPrice"
                    pInputText
                    class="form-control"
                    (input)="onMaxPriceChange()"
                  />
                </div>
              </div>
            </div>
            <div>
              <small class="text-danger" *ngIf="isMaxValue">Max range {{ maxValue }}</small>
            </div>
          </div>
        </form>
        <!-- PrimeNG Slider -->
        <div class="slider-container">
          <div>
            <label class="left-number mt-2">{{ minValue }}</label> <label class="right-number mt-2">{{ maxValue }}</label>
          </div>
          <div>
            <p-slider
              [(ngModel)]="sliderValue"
              [min]="minValue"
              [max]="maxValue"
              [step]="1000"
              [range]="true"
              [animate]="true"
              (onChange)="onSliderChange($event)"
            >
            </p-slider>
          </div>
        </div>
      </div>

      <div class="col-md-9 col-sm-12 inventory-item-wrapper">
        <div class="sort-wrapper">
          <div class="total-inventories">
            <span *ngIf="paginationConfig?.totalElements"> {{ paginationConfig?.totalElements }} Vehicles </span>
          </div>
          <div class="dropdown-wrapper">
            <p-dropdown
              class="sorting-dropdown"
              appendTo="body"
              [options]="sortByOptions"
              optionLabel="label"
              optionValue="id"
              placeholder="Sort by"
              [showClear]="true"
              (onChange)="sortBy($event)"
              [(ngModel)]="selectedSortBy"
            >
            </p-dropdown>
          </div>
        </div>

        <div class="selected-filter-wrapper">
          <div class="item" *ngIf="selectedCategory">
            <span class="name">{{ getSelectedCategoryName(selectedCategory) }}</span>
            <span class="close" (click)="clearCatergotyFilter()">
              <fa-icon [icon]="faIcons.faTimes"></fa-icon>
            </span>
          </div>
          <div class="item" *ngIf="selectedUnitTypes?.length">
            <span class="name">{{ getSelectedUnitTypeName() }}</span>
            <span class="close" (click)="clearUnitTypes()">
              <fa-icon [icon]="faIcons.faTimes"></fa-icon>
            </span>
          </div>
          <div class="item" *ngIf="selectedMakes?.length">
            <span class="name">{{ getSelectedMakeName() }}</span>
            <span class="close" (click)="clearMakes()">
              <fa-icon [icon]="faIcons.faTimes"></fa-icon>
            </span>
          </div>
          <div class="item" *ngIf="selectedModels?.length">
            <span class="name">{{ getSelectedModelName() }}</span>
            <span class="close" (click)="clearModels()">
              <fa-icon [icon]="faIcons.faTimes"></fa-icon>
            </span>
          </div>
          <div class="item" *ngIf="selectedFromYear?.length">
            <span class="name">From year: {{ selectedFromYear }}</span>
            <span class="close" (click)="clearFromYear()">
              <fa-icon [icon]="faIcons.faTimes"></fa-icon>
            </span>
          </div>
          <div class="item" *ngIf="selectedToYear?.length">
            <span class="name">To year: {{ selectedToYear }}</span>
            <span class="close" (click)="clearToYear()">
              <fa-icon [icon]="faIcons.faTimes"></fa-icon>
            </span>
          </div>
          <div class="item" *ngIf="minPrice">
            <span class="name">Minimum Price: {{ minPrice }}</span>
            <span class="close" (click)="clearMinPrice()">
              <fa-icon [icon]="faIcons.faTimes"></fa-icon>
            </span>
          </div>
          <div class="item" *ngIf="maxPrice">
            <span class="name">Maximum Price: {{ maxPrice }}</span>
            <span class="close" (click)="clearMaxPrice()">
              <fa-icon [icon]="faIcons.faTimes"></fa-icon>
            </span>
          </div>
        </div>

        <div class="row" *ngIf="!isLoading; else showLoader">
          <ng-container *ngIf="inventories?.length; else noDataFound">
            <div class="col-md-6 col-lg-4 col-sm-12 invetory-content d-flex justify-content-center" *ngFor="let inventory of inventories">
              <div class="cursor-pointer" (click)="goToDetailPage(inventory.id)">
                <img [src]="inventory.unitImages?.fullUrl ? inventory.unitImages?.fullUrl : constants.staticImages.noImages" alt="" />
                <div class="card details-parent">
                  <div
                    class="advertising-banner"
                    *ngIf="inventory.generalInformation.advertising.id !== NO_BANNER"
                    [ngStyle]="{
                      color: inventory.generalInformation?.advertising?.fontColor ?? 'white',
                      'background-color': inventory.generalInformation?.advertising?.bgColor ?? 'blue'
                    }"
                  >
                    <span> {{ inventory.generalInformation?.advertising?.name }}</span>
                  </div>
                  <div class="card-body">
                    <div class="basic-details">
                      <div class="model-name">
                        {{ inventory?.generalInformation?.year }}
                        {{ inventory?.generalInformation?.make?.name }}
                        {{ inventory?.generalInformation?.unitModel?.name }}
                      </div>
                      <div class="model-description">
                        {{ inventory?.generalInformation?.make?.name }}
                        {{ inventory?.generalInformation?.unitModel?.name }}
                      </div>
                      <div class="model-conditional-details">
                        <div class="miles">
                          <img [src]="constants.staticImages.icons.truckBlack" alt="" />
                          <span>
                            {{ inventory?.odometer?.odometerReading ? (inventory?.odometer?.odometerReading | number : '1.0-0') : 0 }}
                            {{ inventory?.odometer?.unitOfDistance?.name }}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="model-details">
                      <div>
                        <ng-container
                          [ngTemplateOutlet]="modelDetails"
                          [ngTemplateOutletContext]="{ generalInfo: inventory?.generalInformation?.stockNumber, label: 'Unit' }"
                        >
                        </ng-container>
                      </div>
                      <div>
                        <ng-container
                          [ngTemplateOutlet]="modelDetails"
                          [ngTemplateOutletContext]="{ generalInfo: inventory?.generalInformation?.year, label: 'Year' }"
                        >
                        </ng-container>
                      </div>
                      <div>
                        <ng-container
                          [ngTemplateOutlet]="modelDetails"
                          [ngTemplateOutletContext]="{ generalInfo: inventory?.generalInformation?.make?.name, label: 'Make' }"
                        >
                        </ng-container>
                      </div>
                      <div>
                        <ng-container
                          [ngTemplateOutlet]="modelDetails"
                          [ngTemplateOutletContext]="{ generalInfo: inventory?.generalInformation?.unitModel?.name, label: 'Model' }"
                        >
                        </ng-container>
                      </div>
                      <div>
                        <ng-container
                          [ngTemplateOutlet]="modelDetails"
                          [ngTemplateOutletContext]="{
                            generalInfo: inventory?.retailAskingPrice
                              ? (inventory?.retailAskingPrice | currency : 'USD' : true : '1.0-0')
                              : null,
                            label: 'Retail Price'
                          }"
                        >
                        </ng-container>
                      </div>
                    </div>
                    <div class="d-flex justify-content-center w-100 mt-2">
                      <button type="button" class="btn public-page-action-btn">View Details</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
          <ng-template #noDataFound>
            <div class="text-center">No data found</div>
          </ng-template>
        </div>
        <div class="text-center" *ngIf="paginationConfig.totalElements >= paginationConfig.itemsPerPage">
          <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
        </div>
      </div>
    </ng-container>
    <ng-template #noSuchDealerExist>
      <div class="text-center mt-4">No Dealer Found</div>
    </ng-template>
  </div>

  <app-footer></app-footer>
</div>

<ng-template #modelDetails let-generalInfo="generalInfo" let-label="label">
  <span class="model-detail-label">
    {{ label }}
  </span>
  <span class="model-detail-info">
    {{ generalInfo ? generalInfo : 'NA' }}
  </span>
</ng-template>
<ng-template #showLoader>
  <div class="listing-loader" style="scale: 3">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
  </div>
</ng-template>

<app-inventory-details (onClose)="closeModal()" *ngIf="openModal"></app-inventory-details>
