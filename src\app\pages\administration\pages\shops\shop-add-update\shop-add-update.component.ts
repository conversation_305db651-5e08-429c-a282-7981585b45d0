import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, UntypedFormArray, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ShopParam, Shops } from '@pages/administration/models';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { User } from '@sentry/angular';
import { takeUntil } from 'rxjs';
import { ShopService } from '../../pipeline-config/shop.service';

@Component({
  selector: 'app-shop-add-update',
  templateUrl: './shop-add-update.component.html',
  styleUrls: ['./shop-add-update.component.scss']
})
export class ShopAddUpdateComponent extends BaseComponent implements OnInit {

  @Input() selectedShop!: Shops | null;
  redirectUrl!: string;
  pageTitle!: string;
  shopEditFormGroup!: FormGroup;
  shopAddFormGroup!: FormGroup;
  loaders = {
    shopUsers: false
  }
  users: User[] = [];
  @Output() closeModal = new EventEmitter<void>();
  @Output() addNewShop = new EventEmitter<Array<Shops>>();
  @Output() updateShop = new EventEmitter<Shops>();
  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly shopService: ShopService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly toasterService: AppToasterService,
    private readonly router: Router,
    private readonly activeRoute: ActivatedRoute,
    private readonly commonSharedService: CommonSharedService
  ) {
    super();
  }

  ngOnInit(): void {
    this.commonSharedService.setBlockUI$(true);
    this.initializeAddForm();
    this.getUsers();
    this.pageTitle = this.selectedShop?.id ? 'Edit Shop' : 'Add Shop';
    this.activeRoute.queryParams.subscribe(params => {
      if (params?.returnUrl) {
        this.redirectUrl = params?.returnUrl;
      }
    });
    if (this.selectedShop?.id) {
      this.setPageConfigurations();
    }
    this.commonSharedService.setBlockUI$(false);
  }

  setPageConfigurations(): void {
    this.initializeEditForm();
    this.shopEditFormGroup.get('name')?.setValue(this.selectedShop?.name);
    this.shopEditFormGroup.get('defaultUserId')?.setValue(this.selectedShop?.defaultUserId);
    return;
  }

  initializeEditForm(): void {
    this.shopEditFormGroup = new FormGroup({
      name: new FormControl('', [Validators.required, Validators.maxLength(50)]),
      defaultUserId: new FormControl(null, [Validators.required]),
    });
  }

  initializeAddForm(): void {
    this.shopAddFormGroup = this.formBuilder.group({
      shop: this.newShopFormArray
    });
  }

  get newShopFormArray(): UntypedFormArray {
    return this.formBuilder.array([this.newShopFormGroup]);
  }

  get shopFormArray(): UntypedFormArray {
    return this.shopAddFormGroup.get('shop') as UntypedFormArray;
  }

  get newShopFormGroup(): FormGroup {
    return this.formBuilder.group({
      name: new FormControl('', [Validators.required, Validators.maxLength(50)]),
      defaultUserId: new FormControl(null, [Validators.required]),
    });
  }

  onDeleteShop(index: number): void {
    this.shopFormArray.removeAt(index);
  }

  isAddNewShopVisible(index: number): boolean {
    return index === this.shopFormArray.length - 1;
  }

  onAddNewShop(): void {
    this.shopFormArray.push(this.newShopFormGroup);
    document.getElementById('addModelBtn')?.scrollIntoView({
      behavior: 'smooth',
    });
  }

  onModalClose(): void {
    this.closeModal.emit();
    this.selectedShop?.id ? this.shopEditFormGroup?.reset() : this.clearAddShopForm();
    this.goBack();
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  getUsers(): void {
    this.loaders.shopUsers = true;
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.users = res;
          this.loaders.shopUsers = false;
        },
        error: () => {
          this.loaders.shopUsers = false;
        }
      });
  }

  clearAddShopForm(): void {
    this.shopAddFormGroup?.reset();
    this.shopFormArray?.clear();
    this.shopFormArray.push(this.newShopFormGroup);
  }

  onUpdateShop(): void {
    if (this.shopEditFormGroup.invalid) {
      this.shopEditFormGroup.markAllAsTouched();
      return;
    }
    this.updateShopData();
  }

  onAddShop(): void {
    if (this.shopAddFormGroup.invalid) {
      this.shopAddFormGroup.markAllAsTouched();
      return;
    }
    else if (this.hasDuplicates(this.shopFormArray.value)) {
      this.toasterService.error(MESSAGES.duplicatesNotAllowed)
      return;
    }
    this.addShop();
  }

  hasDuplicates(objects: ShopParam[]): boolean {
    const lowerCaseNames = objects.map(obj => obj.name.toLowerCase().trim());
    const uniqueLowerCaseNames = new Set(lowerCaseNames);
    return lowerCaseNames.length !== uniqueLowerCaseNames.size;
  }

  addShop(): void {
    this.shopService.add<Array<Shops>>(this.shopFormArray.value).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Array<Shops>) => {
        this.addNewShop.emit(res);
        this.onModalClose();
      }
    });
  }

  updateShopData(): void {
    this.shopService.update<Shops>({ ...this.shopEditFormGroup.getRawValue(), id: this.selectedShop?.id }).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: Shops) => {
        this.updateShop.emit(res);
        this.onModalClose();
      }
    });
  }

}
