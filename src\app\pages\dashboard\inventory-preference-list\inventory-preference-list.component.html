<div class="content">
  <div class="tab-content">
    <div *ngIf="isFullView" class="title d-flex space-between">
      <div>
        <h6>Inventory Preference Section</h6>
      </div>
      <div class="justify-content-end">
        <fa-icon [icon]="faIcons.faTimes" *ngIf="isFullView" (click)="onCancel()"></fa-icon>
      </div>
    </div>
    <p-table
      class="no-column-selection"
      [value]="inventoryPreference"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAllInventoryPreference.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th scope="col">Customer</th>
          <th scope="col">Unit Type</th>
          <th scope="col">Requested On</th>
          <th scope="col">Make</th>
          <th scope="col">Year</th>
          <th scope="col">Model</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td>{{ rowData?.contactName }}</td>
          <td>{{ rowData?.unitType?.name }}</td>
          <td>{{ rowData?.createdDate | date: constants.monthDateAndYearFormat }}</td>
          <td>{{ rowData?.makes?.length ? getMakeNames(rowData?.makes) : null }}</td>
          <td>{{ rowData?.minYear }} <span *ngIf="rowData?.minYear && rowData?.maxYear">-</span> {{ rowData?.maxYear }}</td>
          <td>{{ rowData?.unitModels?.length ? getUnitModelsNames(rowData?.unitModels) : null }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="9" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
  </div>
</div>
