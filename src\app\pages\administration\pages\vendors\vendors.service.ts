import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Utils } from "src/app/@shared/services";
import { VendorListItem } from '../../models';

@Injectable({ providedIn: 'root' })
export class VendorService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.vendors.root;
  }

  fromServerModel(json: VendorListItem): VendorListItem {
    if (!json) {
      return new VendorListItem();
    }
    return {
      ...json,
      contactPersonName: Utils.getFullName(json.contactPerson?.firstName, json.contactPerson?.lastName),
      addressCity: json.address?.city,
      addressState: json.address?.state,
    };
  }
}
