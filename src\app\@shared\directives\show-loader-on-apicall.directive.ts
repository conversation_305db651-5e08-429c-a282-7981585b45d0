import { ComponentFactoryResolver, ComponentRef, Directive, ElementRef, HostListener, Inject, OnDestroy, OnInit, Renderer2, ViewContainerRef } from '@angular/core';
import { CommonService } from '@core/services';
import { faIcons } from '@core/utils';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { Subject, takeUntil } from 'rxjs';
import { Subscription } from 'rxjs/internal/Subscription';

/**
 * @whatitdoes it injects the spinner on buttons based on API call status.
 * @howtouse <button appShowLoaderOnApiCall></div>
 */
@Directive({
  selector: '[appShowLoaderOnApiCall]'
})
export class ShowLoaderOnApiCallDirective implements OnInit, OnDestroy {
  visible = false;
  loadingSubscription: Subscription = new Subscription();
  destroy$: Subject<void> = new Subject();
  faIconElement!: ComponentRef<FaIconComponent>;
  loaderInitialized = false;
  constructor(
    private readonly elem: ElementRef,
    private readonly viewContainerRef: ViewContainerRef,
    private readonly renderer: Renderer2,
    private readonly commonService: CommonService,
    @Inject(ComponentFactoryResolver) private readonly factoryResolver: ComponentFactoryResolver,) {
  }

  ngOnInit() {
    this.renderer.addClass(this.elem.nativeElement, 'btn-with-loader');

    this.commonService.isApiCallInProgress$.pipe(takeUntil(this.destroy$)).subscribe(isApiCallInProgress => {
      if (!isApiCallInProgress && this.elem.nativeElement && this.faIconElement?.location?.nativeElement) {
        this.renderer.removeClass(this.elem.nativeElement, 'disabled');
        this.renderer.removeAttribute(this.elem.nativeElement, 'disabled');
        this.renderer.removeChild(this.elem.nativeElement, this.faIconElement?.location?.nativeElement);
        this.faIconElement.destroy();
        this.destroySubscription();
      }
    });
  }

  initializeSpinnerComponentElement() {
    const factory = this.factoryResolver.resolveComponentFactory(FaIconComponent);
    const faIconElement = this.viewContainerRef.createComponent(factory);
    faIconElement.instance.icon = faIcons.faSpinner;
    faIconElement.instance.spin = true;
    this.renderer.addClass(faIconElement.location.nativeElement, 'btn-loader-icon');
    faIconElement.instance.render();
    this.faIconElement = faIconElement;
  }
  
  removeLoader() {
    // Remove the loader and reset the flag when API call is completed or fails
    if (this.faIconElement) {
      this.renderer.removeChild(this.elem.nativeElement, this.faIconElement.location.nativeElement);
      this.faIconElement.destroy();  // Clean up the component
      this.loaderInitialized = false; // Reset the flag
    }
    this.renderer.removeClass(this.elem.nativeElement, 'disabled');
    this.renderer.setAttribute(this.elem.nativeElement, 'disabled', 'false');
  }

  ngOnDestroy() {
    this.destroySubscription();
  }

  destroySubscription() {
    if (this.destroy$.observed) {
      this.destroy$.next();
      this.destroy$.unsubscribe();
    }
  }

  @HostListener('click', ['$event'])
  onClick(): void {
    this.destroy$ = new Subject();
    const parentFormElement = this.getParentFormElement(this.elem.nativeElement);
    if (parentFormElement) {
      if (!parentFormElement?.classList.contains('ng-invalid')) {
        this.applyLoaderOnClickedButton();
      }
    } else {
      this.applyLoaderOnClickedButton();
    }
  }

  private applyLoaderOnClickedButton(): void {
    this.commonService.isApiCallInProgress$.pipe(takeUntil(this.destroy$)).subscribe(isApiCallInProgress => {
      if (isApiCallInProgress) {
        if (!this.loaderInitialized) { // Only initialize if loader hasn't been added yet
          this.initializeSpinnerComponentElement();
          this.renderer.addClass(this.elem.nativeElement, 'disabled');
          setTimeout(() => {
            this.renderer.setAttribute(this.elem.nativeElement, 'disabled', 'true');
          }, 0);
          this.renderer.appendChild(this.elem.nativeElement, this.faIconElement?.location?.nativeElement);
          this.loaderInitialized = true;  // Set flag to true once loader is added
        }
      } else {
        // Optional: Handle when the API call is complete, if you need to remove loader or reset
        this.removeLoader();
      }
    });
  }

  private getParentFormElement(element: any): HTMLFormElement | undefined | void {
    const parentElement = element?.parentElement;
    if (parentElement instanceof HTMLFormElement) {
      return parentElement;
    } else {
      return this.getParentFormElement(parentElement);
    }
  }

}
