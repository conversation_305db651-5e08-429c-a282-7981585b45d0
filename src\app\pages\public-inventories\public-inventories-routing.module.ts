import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { InventoryListComponent } from "./pages/inventory-list/inventory-list.component";
import { PublicInventoriesComponent } from './public-inventories.component';

const routes: Routes = [
  {
    path: '',
    component: PublicInventoriesComponent,
    children: [
      {
        path: '',
        component: InventoryListComponent,
        pathMatch: 'full'
      }
    ]
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PublicInventories { }
