<div class="content">
  <div class="tab-content">
    <div *ngIf="isFullViewTaskAlerts" class="title d-flex space-between">
      <div>
        <h6>TASK NEW ALERTS</h6>
      </div>
      <div class="justify-content-end">
        <fa-icon [icon]="faIcons.faTimes" *ngIf="isFullViewTaskAlerts" (click)="onCancel()"></fa-icon>
      </div>
    </div>
    <p-table
      [resizableColumns]="true"
      class="no-column-selection"
      styleClass="p-datatable-gridlines"
      [value]="taskAlerts"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAllTaskAlert.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      scrollDirection="horizontal"
      columnResizeMode="expand"
      [loading]="isLoading"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th pResizableColumn id="id">#Id</th>
          <th pResizableColumn id="stockNumber">#Stock</th>
          <th pResizableColumn id="summary">Summary</th>
          <th pResizableColumn id="taskType">Type</th>
          <th pResizableColumn class="timeline-header" id="timelineHeader">Deadline</th>
          <th pResizableColumn id="assignee">Assignee</th>
          <th pResizableColumn id="reported">Created By</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
        <tr>
          <td>{{ rowData?.id }}</td>
          <td>{{ rowData?.stockNumber }}</td>
          <td>{{ rowData?.summary }}</td>
          <td>{{ rowData?.taskType?.name }}</td>
          <td class="timeline">
              <span>{{ rowData?.endDate | date: constants.dateFormat }}</span>
          </td>
          <td>{{ rowData?.assignee?.name }}</td>
          <td>{{ rowData?.reporter?.name }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="9" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
  </div>
</div>
