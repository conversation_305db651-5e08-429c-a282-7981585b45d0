import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionForChildGuard } from '@core/guards';
import { ROUTER_UTILS } from '@core/utils';
import { PermissionActions } from 'src/app/@shared/models';

const routes: Routes = [
  {
    path: ROUTER_UTILS.config.administration.users.root,
    loadChildren: async () => (await import('./pages/users/users.module')).UsersModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_USERS]
    }
  },
  {
    path: ROUTER_UTILS.config.administration.dealers.root,
    loadChildren: async () => (await import('./pages/dealers/dealers.module')).DealersModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_DEALERS]
    }
  },
  // NOTE: The following modules are currently disabled per client's request.
  // {
  //   path: ROUTER_UTILS.config.administration.pipelineConfig.root,
  //   loadChildren: async () => (await import('./pages/pipeline-config/pipeline-config.module')).PipelineConfigModule,
  //   canActivate: [PermissionForChildGuard],
  //   data: {
  //     permission: [PermissionActions.VIEW_PIPELINE_CONFIG]
  //   }
  // },
  {
    path: ROUTER_UTILS.config.administration.vendors.root,
    loadChildren: async () => (await import('./pages/vendors/vendors.module')).VendorsModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_VENDORS]
    }
  },
  {
    path: ROUTER_UTILS.config.administration.suppliers.root,
    loadChildren: async () => (await import('./pages/suppliers/suppliers.module')).SuppliersModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_VENDORS]
    }
  },
  {
    path: ROUTER_UTILS.config.administration.specificationConfig.root,
    loadChildren: async () => (await import('./pages/specification-configration/specification-configration.module')).SpecificationConfigrationModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [
        PermissionActions.VIEW_SPECIFICATION_MASTER,
        PermissionActions.VIEW_CATEGORY,
        PermissionActions.VIEW_MAKE_MODEL,
        PermissionActions.VIEW_UNIT_TYPE
      ]
    }
  },
  // NOTE: The following modules are currently disabled per client's request.
  // {
  //   path: ROUTER_UTILS.config.administration.shops,
  //   loadChildren: async () => (await import('./pages/shops/shops.module')).ShopsModule,
  //   canActivate: [PermissionForChildGuard],
  //   data: {
  //     permission: [PermissionActions.VIEW_SHOPS]
  //   }
  // },
  {
    path: ROUTER_UTILS.config.administration.roles,
    loadChildren: async () => (await import('./pages/roles/roles.module')).RolesModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_ROLE]
    }
  },
  {
    path: ROUTER_UTILS.config.administration.publicPageConfig.root,
    loadChildren: async () => (await import('./pages/public-page-config/public-page-config.module')).PublicPageConfigModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [
        PermissionActions.VIEW_ADVERTISE,
        PermissionActions.VIEW_QUOTE_FORM,
      ]
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdministrationRoutingModule { }
