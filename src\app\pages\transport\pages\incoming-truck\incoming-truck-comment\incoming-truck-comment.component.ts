import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES, VALIDATION_CONSTANTS, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { User } from '@pages/calendar/event-calendar/models/evnet-calendar';
import { AllUserListFilter } from '@pages/inventory/models/inventory-communication';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { IncomingTruckBoardListItem, IncomingTruckCommentCreateParam, IncomingTruckCommentListItem } from '@pages/transport/models/incoming-truck.model';
import { IncomingTruckCommentService } from '@pages/transport/services/incoming-truck-comment.service';
import { IncomingTruckService } from '@pages/transport/services/incoming-truck.service';
import { MentionConfig } from 'angular-mentions';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { MENTION_CONFIG } from 'src/app/@shared/constants/common.constant';
import { MentionParam } from 'src/app/@shared/models';

@Component({
  selector: 'app-incoming-truck-comment',
  templateUrl: './incoming-truck-comment.component.html',
  styleUrls: ['./incoming-truck-comment.component.scss']
})
export class IncomingTruckCommentComponent extends BaseComponent implements OnInit {

  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() filterParams: any = new AllUserListFilter();
  @ViewChild('commentEditor') set content(content: any) {
    if (content) {
      this.commentEditor = content;
    }
  }
  @ViewChild('boldText') boldText!: ElementRef;
  isFirstTime = true;
  commentEditor: any;
  title = 'Add Comment';
  commentFormGroup!: FormGroup;
  @Input() data!: IncomingTruckBoardListItem | null;
  @Input() isViewMode!: boolean | null;
  @Input() selectedCommentId!: number;
  comments: IncomingTruckCommentListItem[] = [];
  isAddingComment = false;
  mentionedUsers: any[] = [];
  availableMentionUsers: MentionParam[] = [];
  currentMentionUser!: number;
  mentionConfig: MentionConfig = {
    ...MENTION_CONFIG,
    items: this.availableMentionUsers
  };
  constructor(private readonly fb: FormBuilder,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly incomingTruckCommentService: IncomingTruckCommentService,
    private readonly incomingTruckService: IncomingTruckService,
    private readonly cdf: ChangeDetectorRef) { super() }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getAll();
    if (this.data?.id) {
      this.incomingTruckComments();
    }
  }

  private initializeFormGroup(): void {
    this.commentFormGroup = this.fb.group({
      comment: new FormControl(null, [Validators.required, Validators.maxLength(VALIDATION_CONSTANTS.textAreaMaxLength)]),
      incomingTruckId: new FormControl(this.data?.id),
      id: new FormControl(null),
      mentionUserIds: new FormControl([])
    });
  }

  get incomingTruckCommentCreateParams(): IncomingTruckCommentCreateParam {
    return {
      ...this.commentFormGroup.value,
    };
  }

  incomingTruckComments(): void {
    this.isLoading = true;
    const id = this.data?.id.toString() || '';
    this.incomingTruckService.getIncomingTruckComments(id).pipe(takeUntil(this.destroy$)).subscribe({
      next: comments => {
        this.isLoading = false;
        this.comments = comments;
        if (!this.comments?.length) {
          this.isAddingComment = true;
        }
      },
      error: () => {
        this.isLoading = false;
      }
    })
  }

  onAddNewCommentSubmit(): void {
    if (this.commentFormGroup.invalid) {
      this.commentFormGroup.markAllAsTouched();
      return;
    }
    this.commentFormGroup.markAsUntouched();

    if (this.incomingTruckCommentCreateParams?.id) {
      this.edit();
    } else {
      this.add();
    }
  }

  removeErasedUser(comment: string, ids: number[]): number[] {
    return ids.filter(id => {
      const label = this.availableMentionUsers.find(obj => obj.id === id)?.label;
      return label && comment.toLowerCase().includes(label.toLowerCase());
    });
  }

  add(): void {
    const params = this.incomingTruckCommentCreateParams
    params.mentionUserIds = this.mentionedUsers.map(i => i.id);
    if (this.commentFormGroup.controls['comment'].value.includes('&lt;strong')) {
      let val = this.commentFormGroup.controls['comment'].value.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['comment'].setValue(`${val}`);
      params.comment = this.commentFormGroup.controls['comment'].value
    }
    params.mentionUserIds = this.removeErasedUser(params.comment, params.mentionUserIds);
    this.incomingTruckCommentService.add<IncomingTruckCommentCreateParam>(params).pipe(takeUntil(this.destroy$)).subscribe(savedComment => {
      this.toasterService.success(MESSAGES.commentAddSuccess);
      this.comments.push(savedComment);
      this.commentFormGroup.reset();
      this.mentionedUsers = [];
      this.isAddingComment = false;
    });
  }

  edit(): void {
    const params = this.incomingTruckCommentCreateParams
    params.mentionUserIds = [...new Set([...params.mentionUserIds, ...this.mentionedUsers.map(i => i.id)])];
    if (this.commentFormGroup.controls['comment'].value.includes('&lt;strong')) {
      let val = this.commentFormGroup.controls['comment'].value.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['comment'].setValue(`${val}`);
      params.comment = this.commentFormGroup.controls['comment'].value
    }
    params.mentionUserIds = this.removeErasedUser(params.comment, params.mentionUserIds);
    this.incomingTruckCommentService.update<IncomingTruckCommentCreateParam>(params).pipe(takeUntil(this.destroy$)).subscribe(savedComment => {
      this.toasterService.success(MESSAGES.commentUpdateSuccess);
      const index = this.comments.findIndex(comment => comment.id === (savedComment as IncomingTruckCommentCreateParam)?.id);
      if (index > -1) {
        this.comments[index] = savedComment as IncomingTruckCommentListItem;
      }
      this.commentFormGroup.reset();
      this.mentionedUsers = [];
      this.isAddingComment = false;
    });
  }

  onAddComment(): void {
    this.commentFormGroup.patchValue({
      id: null,
      comment: null,
      incomingTruckId: this.data?.id,
    });
    this.isAddingComment = true;
  }

  onCancel(): void {
    this.isAddingComment = false;
    this.mentionedUsers = [];
  }

  onEdit(taskComment: IncomingTruckCommentListItem): void {
    this.mentionedUsers = [];
    this.commentFormGroup.patchValue({
      id: taskComment.id,
      comment: taskComment.comment,
      incomingTruckId: taskComment.incomingTruckId,
      mentionUserIds: taskComment.mentionUserIds
    })
    this.isAddingComment = true;
  }

  onDelete(comment: IncomingTruckCommentListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'comment'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(comment);
      }
    });
  }

  onDeleteConfirmation(comment: IncomingTruckCommentListItem): void {
    this.incomingTruckCommentService.delete(comment.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.commentDeleteSuccess);
          const index = this.comments.findIndex(c => c.id === comment.id);
          if (index > -1) {
            this.comments.splice(index, 1);
          }
        }
      });
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.availableMentionUsers = res.map((user) => ({ id: user.id, label: user.name }));
          this.mentionConfig.items = this.availableMentionUsers;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: (err: any) => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  selectionChange(e: any) {
    if (e && e.range && e.range.index > 0 && e.source === 'api') {
      this.boldText.nativeElement.click();
    }
  }

  mentionSelected(mention: any, childIndex = -1) {
    let count = 0;
    for (const i of this.mentionedUsers) {
      if (i.id === mention.id) {
        count++;
      }
    }
    if (count === 0) {
      this.mentionedUsers.push(mention);
    }
    setTimeout(() => {
      this.commentEditor.getQuill().updateContents();
    }, 0);
  }

  textChange(e: any) {
    if (e.htmlValue.includes('&lt;strong')) {
      let val = e.htmlValue.split('&lt;strong');
      const index = val.findIndex((x: any) => x.includes('/strong&gt;'));
      let d = val[index].split('/strong&gt;');
      d[0] = this.decodeEntities(`&lt;strong${d[0]}/strong&gt;`);
      d = d.join(' ');
      val[index] = d;
      val = val.join(' ');
      this.commentFormGroup.controls['comment'].setValue(`${val}`);
      setTimeout(() => {
        this.commentEditor.el.nativeElement.childNodes[0].lastChild.childNodes[0].focus();
        this.commentEditor.el.nativeElement.childNodes[0].lastChild.childNodes[0].click();
        this.boldText.nativeElement.click();
        this.commentEditor.getQuill().setSelection(val.length + 1);
      }, 0);
    }
  }

  decodeEntities(str: any) {
    // this prevents any overhead from creating the object each time
    const element = document.createElement('div');
    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
    element.innerHTML = str;

    return element.textContent;
  }

}

