<div class="modal-title close" [ngClass]="isEditMode ? 'edit-header' : ''">
  <div>
    <h4 class="header-title">{{ title }}</h4>
    <span *ngIf="isEditMode" class="created-by">
      <span class="bold-text">#{{ incomingTruckInfo?.unit?.generalInformation?.stockNumber }}</span>
      Created By <span class="bold-text">{{ incomingTruckInfo?.unit?.createdBy?.name }}</span> on
      {{ incomingTruckInfo?.unit?.createdDate | date: constants.monthDateAndYearFormat }} at
      {{ incomingTruckInfo?.unit?.createdDate | date: constants.time }}
    </span>
  </div>
  <div>
    <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
  </div>
</div>

<form (ngSubmit)="onSubmit()">
  <div>
    <ng-container [ngSwitch]="currentStep">
      <div *ngSwitchCase="addUnitSteps.SEARCH_INCOMING">
        <app-incoming-truck-search (onVehicleSelect)="onVehicleSelect($event)"></app-incoming-truck-search>
      </div>
      <div *ngSwitchCase="addUnitSteps.ADD_INCOMING_TRUCK">
        <app-incoming-truck-add
          (onClose)="onCancel()"
          (hasBeenModified)="hasBeenModified($event)"
          (isEditTitle)="onEditTitle($event)"
          [isViewMode]="isViewMode"
          (isPrevious)="isPreviousTab($event)"
          [incomingTruckInfo]="incomingTruckInfo"
          [categoriesToDisplay]="categoriesToDisplay"
        ></app-incoming-truck-add>
      </div>
    </ng-container>
  </div>
</form>

<ng-template #searchUnitActionsTemplate>
  <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
  <button class="btn btn-primary" type="submit" (click)="onStepChange(addUnitSteps.ADD_INCOMING_TRUCK)">Add information manually</button>
  <button class="btn btn-primary" type="submit" (click)="onStepChange(addUnitSteps.ADD_INCOMING_TRUCK)" *ngIf="selectedVehicle">Continue with selected vehicle</button>
</ng-template>

<ng-template #addUnitActionsTemplate>
  <div class="step-actions" [ngClass]="{ 'justify-content-end': isEditMode }">
    <button class="btn btn-secondary" type="button" (click)="onStepChange(addUnitSteps.SEARCH_INCOMING)" *ngIf="!isEditMode && isPrevious">Previous</button>
    <div>
      <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
      <button class="btn btn-primary mx-2" type="submit" appShowLoaderOnApiCall>Save</button>
      <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!isEditMode" appShowLoaderOnApiCall>Save & Add New</button>
    </div>
  </div>
</ng-template>
