import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Constants } from '@constants/*';
import { CommonService, RefreshTokenService } from '@core/services';
import { StorageItem, getItem, removeItem } from '@core/utils';
import { API_URL_UTIL } from '@core/utils/api-url.utils';
import { ROUTER_UTILS } from '@core/utils/router.utils';
import { BehaviorSubject, Observable, catchError, map, of } from 'rxjs';
import { ServerMessage } from 'src/app/@shared/models';
import { Account, Authority, ForgotPasswordFinishRequestParams, ForgotPasswordInitRequestParams, LoginEmailPasswordParams, LoginResponse, Role } from '../models';

@Injectable({
  providedIn: 'root',
})
export class AuthService {

  isLoggedIn$ = new BehaviorSubject<boolean>(!!getItem(StorageItem.AuthToken));
  currentUser!: Account;
  isCurrentUserAdmin = false;
  private readonly currentUserSubject: BehaviorSubject<Account> = new BehaviorSubject(this.currentUser);

  constructor(
    private readonly httpClient: HttpClient,
    private readonly router: Router,
    private readonly refreshTokenService: RefreshTokenService,
    private readonly commonService: CommonService
    ) { }

  get isLoggedIn(): boolean {
    return this.isLoggedIn$.getValue();
  }

  loginWithEmailPassword(loginEmailPasswordParams: LoginEmailPasswordParams): Observable<LoginResponse> {
    return this.httpClient.post<LoginResponse>(API_URL_UTIL.account.loginWithEmailPassword, loginEmailPasswordParams).pipe(map(res => {
      this.refreshTokenService.storeAuthTokens(res);
      this.refreshTokenService.startRefreshTokenTimer(res.access_token);
      this.isLoggedIn$.next(true);
      return res;
    }));
  }

  onForgotPasswordInit(params: ForgotPasswordInitRequestParams): Observable<ServerMessage> {
    return this.httpClient.post<ServerMessage>(API_URL_UTIL.account.forgotPasswordInit, params);
  }

  onForgotPasswordFinish(params: ForgotPasswordFinishRequestParams): Observable<void> {
    return this.httpClient.post<void>(API_URL_UTIL.account.forgotPasswordFinish, params);
  }

  /**
   * @param fromDb if fromDb is true, it will always fetch user info from the API request, else it will return current user from cache
   * @returns current logged in user
   */
  getCurrentUser(fromDb = false): Observable<Account | null> {
    if (fromDb || !this.currentUser?.id) {
      return this.httpClient.get<Account>(API_URL_UTIL.account.root).pipe(map(res => {
        this.currentUser = res;
        this.isCurrentUserAdmin = this.currentUser?.role?.name === Authority.ROLE_ADMIN;
        this.setCurrentUser$(res);
        localStorage.setItem(Constants.localStorageConstants.roleInfo, JSON.stringify(this.currentUser.role));
        return res;
      }), catchError(err => {
        this.logOut();
        return of(null);
      }));
    }
    localStorage.setItem(Constants.localStorageConstants.roleInfo, JSON.stringify(this.currentUser.role));
    return of(this.currentUser);
  }

  getCurrentUser$(): Observable<Account> {
    return this.currentUserSubject.asObservable();
  }

  setCurrentUser$(value: Account) {
    this.currentUserSubject.next(value);
  }

  getRoleInfo(): Role {
    return JSON.parse(localStorage.getItem(Constants.localStorageConstants.roleInfo) as string);
  }

  logOut(): void {
    removeItem(StorageItem.AuthToken);
    localStorage.removeItem(Constants.localStorageConstants.roleInfo);
    this.isLoggedIn$.next(false);
    this.currentUser = null as unknown as Account;
    this.refreshTokenService.stopRefreshTokenTimer();
    this.commonService.setBlockUI$(false);
    this.router.navigate([ROUTER_UTILS.config.auth.root]);
  }
}
