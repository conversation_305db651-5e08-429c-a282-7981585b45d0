import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { DocumentListItem, SharingType } from '@pages/inventory/models/documents.model';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-inventory-document-details',
  templateUrl: './inventory-document-details.component.html',
  styleUrls: ['./inventory-document-details.component.scss']
})
export class InventoryDocumentDetailsComponent extends BaseComponent implements OnInit, OnChanges {

  title = 'Document Details';
  documentDetailsFormGroup!: FormGroup;
  hasDataBeenModified = false;
  isDataExist = false;
  SharingType = SharingType;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() documentDetails!: DocumentListItem | null;
  @Input() isViewMode!: boolean;

  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly fileUploadService: FileUploadService,
    private readonly toasterService: AppToasterService,
    private readonly inventoryService: InventoryService,
    private readonly confirmationService: ConfirmationService,
  ) { super(); }

  ngOnInit(): void {
    this.initializeFormGroup();
    if (this.isDataExist) {
      this.setFormGroupValue();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.documentDetails?.currentValue) {
      this.isDataExist = true
    }
  }

  setFormGroupValue() {
    this.documentDetailsFormGroup.get('title')?.setValue(this.documentDetails?.title);
    this.documentDetailsFormGroup.get('description')?.setValue(this.documentDetails?.description);
    this.documentDetailsFormGroup.get('sharing')?.setValue(this.documentDetails?.sharing);
    this.documentDetailsFormGroup.get('documentType')?.setValue(this.documentDetails?.documentType);
  }

  private initializeFormGroup(): void {
    this.documentDetailsFormGroup = this.formBuilder.group({
      title: new FormControl(null),
      description: new FormControl(null),
      documentType: new FormControl(null),
      sharing: new FormControl(null)
    });
    if (this.isViewMode) {
      this.documentDetailsFormGroup.disable();
    }
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  onSubmit(): void {
    this.updateDocuments();
  }

  get documentUpdateParams(): DocumentListItem[] {
    return [{
      id: this.documentDetails?.id,
      title: this.documentDetailsFormGroup.get('title')?.value,
      url: this.documentDetails?.url ? this.documentDetails?.url : null,
      description: this.documentDetailsFormGroup.get('description')?.value,
      sharing: this.documentDetailsFormGroup.get('sharing')?.value,
      unitId: this.documentDetails?.unitId ? this.documentDetails?.unitId : null,
      fileName: this.documentDetailsFormGroup.get('title')?.value,
      fileSize: this.documentDetails?.fileSize ? this.documentDetails?.fileSize : null,
      createdDate: this.documentDetails?.createdDate ? this.documentDetails?.createdDate : null,
      folderName: null
    }]
  }

  private updateDocuments(): void {
    const documentUpdateParams = this.documentUpdateParams;
    this.inventoryService.update(documentUpdateParams, API_URL_UTIL.inventory.document).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.documentUpdateSuccess);
        this.onClose.emit(true);
      }
    })
  }

}
