import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { takeUntil } from 'rxjs';
import { TaskAlertListItem } from '../dashboard.model';
import { DashboardService } from '../dashboard.service';

@Component({
  selector: 'app-task-alerts-list',
  templateUrl: './task-alerts-list.component.html',
  styleUrls: ['./task-alerts-list.component.scss']
})
export class TaskAlertsListComponent extends BaseComponent implements OnInit {
  @Input() isFullViewTaskAlerts!: boolean;
  taskAlerts: TaskAlertListItem[] = [];
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(
    private readonly dashboardService: DashboardService,
    private readonly cdf: ChangeDetectorRef
  ) { super() }

  ngOnInit(): void {
    this.getAllTaskAlert();
  }
  getAllTaskAlert(): void {
    this.isLoading = true;
    const endpoint = `${API_URL_UTIL.dashboard.taskNewAlert}`;
    this.dashboardService.getList<TaskAlertListItem>(endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.taskAlerts = res;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  onCancel(): void {
    this.onClose.emit(true);
  }
}
