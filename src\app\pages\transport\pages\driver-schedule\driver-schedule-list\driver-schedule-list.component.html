<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn class="top-header">
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="excelDownloadMenu.toggle($event)"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
    <p-menu #excelDownloadMenu [model]="excelDownloadMenuItems" [popup]="true"> </p-menu>
    <button class="btn btn-primary left" (click)="onAdd()" [appImageIconSrc]="constants.staticImages.icons.addNew" *appHasPermission="[permissionActions.CREATE_DRIVER_SCHEDULE]">
      <span class="m-l-15 show-label">Add New Driver Schedule</span>
    </button>
    <button class="btn btn-primary left column-btn show-label" (click)="toggleFilterSidebar()">
      <span class="show-label">Columns</span>
      <fa-icon [icon]="faIcons.faCaretDown"></fa-icon>
    </button>
  </div>
</app-page-header>

<div class="card">
  <div class="tabs">
    <ng-container *ngIf="defaultTabs">
      <p-tabView (onChange)="onTabChanged($event)" [scrollable]="true" styleClass="dynamic-tabs">
        <ng-container *ngFor="let tab of defaultTabs">
          <p-tabPanel *ngIf="tab.filterName" [header]="tab.filterName">
            <ng-template pTemplate="content">
              <ng-container [ngTemplateOutlet]="activeTabTemplate" [ngTemplateOutletContext]="{ heading: tab.filterName }"></ng-container>
            </ng-template>
          </p-tabPanel>
        </ng-container>
      </p-tabView>
    </ng-container>
  </div>
</div>
<ng-template #activeTabTemplate let-header>
  <p-table
    [columns]="selectedColumns"
    [value]="driverScheduleBoardList"
    styleClass="p-datatable-gridlines"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="true"
    (onLazyLoad)="onSortChange($event, getAll.bind(this))"
    [sortField]="paginationConfig.predicate"
    [rowHover]="true"
    [loading]="isLoading"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <!-- heading row -->
      <tr>
        <ng-container *ngFor="let col of columns">
          <th
            pResizableColumn
            class="small-col"
            *ngIf="col.disable && col.name === 'Item'"
            [pSortableColumn]="col?.shortingKey"
            scope="col"
            pReorderableColumn
            [pReorderableColumnDisabled]="true"
          >
            Item <p-sortIcon [field]="col?.key"> </p-sortIcon>
          </th>
          <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="associate-col cursor-default" *ngIf="col.disable && col.name === 'Associated Stocks'" scope="col">
            Associated Stocks
          </th>
          <th pResizableColumn [pSortableColumn]="col?.shortingKey" pReorderableColumn *ngIf="!col.disable">
            {{ col.name }}
            <p-sortIcon [field]="col.shortingKey" *ngIf="col.shorting"></p-sortIcon>
          </th>
          <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="small-col cursor-default" *ngIf="col.disable && col.name === 'Action'">
            {{ col.name }}
          </th>
        </ng-container>
      </tr>

      <!-- Column search row -->
      <tr class="inventory-search-tr">
        <ng-container *ngFor="let col of columns">
          <th pResizableColumn class="small-col" *ngIf="col.disable && col.name === 'Item'" scope="col">
            <span class="search-input"><input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" /> </span>
          </th>
          <th pResizableColumn *ngIf="col.disable && col.name === 'Associated Stocks'" scope="col"></th>
          <th pResizableColumn *ngIf="!col.disable">
            <span
              class="search-input"
              *ngIf="
                col.type !== 'IMAGE' &&
                col.type !== 'BUTTON' &&
                col.type !== 'DATE' &&
                col.key !== 'pickUpLocation' &&
                col.key !== 'destinationLocation' &&
                col.key !== 'driverScheduleStatus'
              "
            >
              <input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
            </span>
            <span class="search-input" *ngIf="col.type === 'DATE'">
              <p-calendar
                appendTo="body"
                [showIcon]="true"
                [showButtonBar]="true"
                [readonlyInput]="true"
                inputId="startDateIcon"
                (onSelect)="tableSearchByColumn($event, col)"
                (onClearClick)="clearDate(col.key)"
                [(ngModel)]="col.value"
              ></p-calendar>
            </span>
          </th>

          <th pResizableColumn class="small-col" *ngIf="col.disable && col.name === 'Action'">
            <button type="button" class="btn btn-primary btn-sm reset-btn" (click)="clearSearchInput()">Reset filters</button>
          </th>
        </ng-container>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-rowData let-columns="columns" let-rowIndex="rowIndex">
      <tr>
        <ng-container *ngFor="let col of columns">
          <td *ngIf="col.name === 'Item' && col.disable" (click)="onViewEdit(rowData, false)" class="view-task">
            {{ getEvaluatedExpression(col.key, rowData) }}
          </td>
          <td *ngIf="col.name === 'Associated Stocks' && col.disable">
            <span *ngFor="let associate of rowData.unitAssociations; let index = index">
              <span>
                {{ associate?.stockNumber }}
              </span>
              <span *ngIf="index !== rowData.unitAssociations.length - 1"> , </span>
            </span>
          </td>
          <td *ngIf="col.name === 'Drivers' && !col.disable">
            <span *ngFor="let associate of rowData.drivers; let index = index">
              <span>
                {{ associate?.driverName }}
              </span>
              <span *ngIf="index !== rowData.drivers.length - 1"> , </span>
            </span>
          </td>
          <ng-container *ngIf="col.key && col.key !== null && !col.disable && col.name !== 'Drivers'">
            <td *ngIf="col.type !== 'ENUM'">
              <span *ngIf="col.type === 'DATE'">
                {{ getEvaluatedExpression(col.key, rowData) | date: constants.dateFormat }}
              </span>
              <span *ngIf="col.type !== 'DATE'">
                {{ getEvaluatedExpression(col.key, rowData) }}
              </span>
            </td>
            <td *ngIf="col.type === 'ENUM' && col.name === 'Status'">
              <p-dropdown
                class="w-145 border-less driverSchedulingList"
                appendTo="body"
                [options]="driverScheduleStatuses"
                optionLabel="name"
                [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_DRIVER_SCHEDULE])"
                [(ngModel)]="rowData.driverScheduleStatus"
                optionValue="value"
                (onChange)="changeStatus(rowData?.id, rowData?.driverScheduleStatus)"
              >
              </p-dropdown>
            </td>
          </ng-container>

          <td *ngIf="col.name === 'Action' && col.disable">
            <div class="actions-content flex justify-content-center">
              <img [src]="constants.staticImages.icons.edit" alt="" (click)="onViewEdit(rowData, true)" *appHasPermission="[permissionActions.UPDATE_DRIVER_SCHEDULE]" />
              <img
                [src]="constants.staticImages.icons.deleteIcon"
                appShowLoaderOnApiCall
                alt=""
                (click)="onDelete(rowData, $event)"
                *appHasPermission="[permissionActions.DELETE_DRIVER_SCHEDULE]"
              />
              <p-menu #act [model]="actionMenu" [popup]="true" appendTo="body"> </p-menu>
              <fa-icon
                [icon]="faIcons.faEllipsisV"
                class="action-btn px-2"
                *appHasPermission="[permissionActions.UPDATE_DRIVER_SCHEDULE]"
                (click)="act.toggle($event); selectedDriverScheduleBoard = rowData"
              ></fa-icon>
            </div>
          </td>
        </ng-container>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
    </ng-template>
  </p-table>

  <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-driver-schedule-add (onClose)="onAddEditPopupClose($event)" [isViewMode]="isViewMode" *ngIf="showCreateModal" [driverScheduleInfo]="selectedDriverScheduleBoard">
  </app-driver-schedule-add>
</p-sidebar>

<p-sidebar
  [(visible)]="showColumnModal"
  position="right"
  (onHide)="showColumnModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-column-dropdown
    (onClose)="toggleColumnSidebar()"
    *ngIf="showColumnModal"
    [isModelVisible]="showColumnModal"
    [privateFilter]="defaultColumnsArray"
    [filterParams]="getFilterSaveParams()"
    [columnsList]="dropDownColumnList"
    (onSubmitCheck)="callFilterApiAgain()"
  >
  </app-column-dropdown>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>
<p-confirmPopup *ngIf="!showCreateModal"></p-confirmPopup>
