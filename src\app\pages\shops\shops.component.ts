import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { HistoryModuleName, TASK_TYPES } from '@constants/*';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { takeUntil } from 'rxjs';
import { IdNameModel, ViewMode } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { TaskListFilter, TaskListItem, TaskUserGroupListItem } from './models';
import { TaskService } from './services/tasks.service';

@Component({
  selector: 'app-shops',
  templateUrl: './shops.component.html',
  styleUrls: ['./shops.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default
})
export class ShopsComponent extends BaseComponent implements OnInit {

  taskUserGroups: TaskUserGroupListItem[] = [];
  showCreateModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.SHOPS;
  selectedTaskUserGroup!: TaskUserGroupListItem | null;
  isButtonExpanded = true;
  currentUser!: Account | null;
  expandCollapseButtonLabel = "Expand All";
  isViewMode = false;
  taskId!: string;
  isRedirectUrl = false;
  tasks!: Array<TaskListItem>
  constructor(
    private readonly taskService: TaskService,
    private readonly cdf: ChangeDetectorRef,
    private readonly authService: AuthService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super();
    this.pageTitle = 'Shops';
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.listenToRouteParams();
    this.getAllShopsData();
  }

  getAllShopsData(): void {
    this.taskService.getAllTask()
      .pipe(takeUntil(this.destroy$)).subscribe((res: Array<TaskListItem>) => {
        this.tasks = res.filter(task => task.taskType.id !== TASK_TYPES.SALES);
      });
  }

  private listenToRouteParams(): void {
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      if (params.id) {
        this.isViewMode = ViewMode[(params.mode as ViewMode)] !== ViewMode[ViewMode.EDIT]; // if mode is edit, isViewMode is false
        this.showCreateModal = true;
        this.taskId = params.id;
      }
    });
  }


  getTaskUserGroups(): void {
    this.isLoading = true;
    if (this.currentUser) {
      this.taskService.getList<TaskUserGroupListItem>(API_URL_UTIL.tasks.userGroups).pipe(takeUntil(this.destroy$)).subscribe(userGroups => {
        this.taskUserGroups = userGroups;
        if (this.taskUserGroups) {
          for (const taskUserGroups of this.taskUserGroups) {
            if (taskUserGroups.assignees) {
              for (const assignees of taskUserGroups.assignees) {
                if (this.currentUser?.id === assignees?.id) {
                  taskUserGroups.isTabOpen = true;
                  assignees.isTabOpen = true;
                }
              }
            }
          }
        }
        this.isLoading = false;
        this.cdf.detectChanges();
      });
    }
  }

  onAddTask() {
    this.showCreateModal = true;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.backToCalendar();
    this.showCreateModal = false;
    this.selectedTaskUserGroup = null;
    if (refreshList) {
      this.getTaskUserGroups();
      this.getAllShopsData();
    }
    if (!this.isRedirectUrl) {
      this.router.navigate([], { queryParams: {} });
    }
  }

  getTaskListFilter(assignee: IdNameModel, shopId: number): TaskListFilter {
    return new TaskListFilter(shopId, assignee.id as number);
  }

  onExpandCollapse(isExpand: boolean): void {
    this.isButtonExpanded = isExpand ? false : true;
    this.expandCollapseButtonLabel = isExpand ? "Collapse All" : "Expand All";
    if (this.taskUserGroups) {
      for (const taskUserGroups of this.taskUserGroups) {
        taskUserGroups.isTabOpen = isExpand;
        if (taskUserGroups.assignees) {
          for (const assignees of taskUserGroups.assignees) {
            assignees.isTabOpen = isExpand;
          }
        }
      }
    }
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user) {
        this.currentUser = user;
        this.getTaskUserGroups();
      }
    });
  }

  backToCalendar() {
    this.activatedRoute.queryParams
      .subscribe(params => {
        if (params['redirectUrl']) {
          this.isRedirectUrl = true;
          this.router.navigateByUrl(`${API_URL_UTIL.calendar.root}`)
        }
      })
  }

  exportUsersToExcel(): void {
    this.isExporting = true;
    import("xlsx").then(xlsx => {
      const workbook = { Sheets: this.getSheetTabs(this.tasks, xlsx), SheetNames: this.getSheetTabNames(this.tasks) };
      const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
      Utils.saveAsExcelFile(excelBuffer, "Shops");
      this.isExporting = false;
    });
  }

  getSheetTabs(tasks: Array<TaskListItem>, xlsx: any) {
    let tabs: any = {};
    for (const tab of this.getSheetTabNames(tasks)) {
      tabs = { ...tabs, [tab]: this.getSheet(tasks, tab, xlsx) }
    }
    return tabs;
  }

  getSheetTabNames(tasks: Array<TaskListItem>): Array<string> {
    const shopNames: Array<string> = [];
    for (const tab of this.taskUserGroups) {
      if (this.getExcelData(tasks, tab.shopName)?.length) {
        shopNames.push(tab.shopName);
      }
    }
    return shopNames;
  }

  getSheet(res: Array<TaskListItem>, tabName: string, xlsx: any) {
    const inventory = this.getExcelData(res, tabName)
    return xlsx.utils.json_to_sheet(inventory);
  }

  getExcelData(tasks: Array<TaskListItem>, tabName: string) {
    const taskData = [];
    for (const task of tasks) {
      if (task.shop.name === tabName) {
        taskData.push({
          'Shop': task.shop.name,
          'Assignee': task.assignee.name,
          'Priority': task.taskPriority,
          'ID': task.id,
          'Stock': task.stockNumber,
          'Summary': task.summary,
          'Task Type': task.taskType.name,
          'Deadline': `${Utils.dateIntoUserReadableFormat(task?.endDate ?? '') ?? ''}`,
          'Reporter': task.reporter.name,
          'Status': task.taskStatus.name
        })
      }
    }
    return taskData;
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
