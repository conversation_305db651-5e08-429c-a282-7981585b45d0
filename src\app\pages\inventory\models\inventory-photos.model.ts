import { IdNameModel } from "src/app/@shared/models";

export interface InventoryPhotoItem {
  id: number | null;
  unitId: number;
  url: string;
  photoType: InventoryPhotoType;
  isSelected?: boolean;
  detail?: string | null;
  displayPicture?: boolean;
  fullUrl?: string;
  fileUrl?: string;
  imageOrder?: number;
  tempId?: number;
  binary?: string;
}

export enum InventoryPhotoType {
  PUBLIC_PHOTO = 'PUBLIC',
  INTERNAL_PHOTO = 'INTERNAL'
}

export interface InventoryPhotoAttachment {
  fullTaskAttachmentUrl?: string;
  id?: number;
  taskId?: number;
  url: string;
  unitPhotoType?: InventoryPhotoType;
}


export interface InventoryPhotoParameter {
  url: string;
  photoType: InventoryPhotoType,
  unitId: number
}

export interface UnitImages {
  photoType: PhotoType;
  unitId?: number | null;
  url: string;
  fileUrl?: string | null;
}

export enum PhotoType {
  COMPONENT_CONDITION = 'COMPONENT_CONDITION'
}

export interface InventoryConditionsItem {
  id?: number | null;
  condition: string | null;
  unitComponentId?: string | number | undefined;
  unitId?: number | null;
  unitImages: UnitImages[] | null
}


export interface InventoryComponentResponse {
  id: number | null,
  condition: string,
  unitImages: UnitImagesResponse[],
  unitId: number | null,
  unitComponent: IdNameModel
}

export interface UnitImagesResponse {
  id: number | null,
  url: string,
  detail: string | null,
  photoType: string,
  unitId: number | null | undefined,
  fullUrl: string | undefined,
  displayPicture: boolean,
  isNewUpload?: boolean,
  imageOrder?: number
}

