@import "src/assets/scss/variables";

footer {
  padding: 10px;
  background-color: var(--card-bg-color);
  color: var(--text-color);

  span {
    filter: opacity(1);
  }

  .version-info {
    font-size: 12px;
    color: var(--text-color);
    filter: opacity(0.7);
  }
}
body {
  &.LIGHT {
    footer {
      box-shadow: 0 0px 5px 2px lighten($placeholder-color, 30%);
    }
  }
}

@media only screen and (max-width: 700px) {
  .version-info {
    display: none;
  }
}
