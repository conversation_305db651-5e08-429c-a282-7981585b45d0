import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { FilterList } from '@pages/common-table-column/models/common-table.column.model';
import { ConfirmationService } from 'primeng/api';
import { finalize, takeUntil } from 'rxjs';

@Component({
  selector: 'app-preference-list',
  templateUrl: './preference-list.component.html',
  styleUrls: ['./preference-list.component.scss']
})
export class PreferenceListComponent extends BaseComponent implements OnInit {
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onApplyPreference = new EventEmitter<FilterList>();
  @Output() onDeletePreference = new EventEmitter<FilterList>();
  @Input() currentUserId!: number | null;
  title = 'My Preferences';
  clonedColumnItem: { [s: string]: FilterList } = {};
  preferenceList: FilterList[] = [];
  preferenceListMaster: FilterList[] = [];
  searchedTerm = '';
  constructor(
    private readonly columnDropdownService: ColumnDropdownService,
    private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef,
    private readonly confirmationService: ConfirmationService,
  ) { super(); }

  ngOnInit(): void {
    this.getAll();
  }
  tableSearchByColumn(event: any) {
    this.preferenceList = this.preferenceListMaster.filter(pref => pref.filterName?.toLowerCase().includes(event.target.value.toLowerCase()));
  }

  getAll(): void {
    this.isLoading = true;
    const module = `${this.currentUserId}_PREFERENCES`;
    this.columnDropdownService.get<FilterList[]>(`user/${this.currentUserId}/module?module=${module}`)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoading = false;
          this.cdf.detectChanges();
        })
      )
      .subscribe({
        next: (res) => {
          this.preferenceList = this.preferenceListMaster = res;
        }
      });
  }


  onRowEditInit(filter: FilterList) {
    if (filter?.id) {
      this.clonedColumnItem[filter.id] = { ...filter };
      this.cdf.detectChanges();
    }
  }

  onRowEditSave(filter: FilterList) {
    this.updatePreference(filter);
  }

  onRowEditCancel(filter: FilterList, index: number) {
    if (filter?.id) {
      this.preferenceList[index] = this.clonedColumnItem[filter.id];
      delete this.clonedColumnItem[filter.id];
      this.cdf.detectChanges();
    }
  }

  private updatePreference(filter: FilterList): void {
    this.columnDropdownService.update(filter).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.preferenceMessage.replace("{action}", "updated"));
        this.cdf.detectChanges();
      }
    });
  }

  onApplyFilters(filter: FilterList) {
    this.onApplyPreference.emit(filter);
  }

  onDelete(filter: FilterList, index: number): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'preference'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.columnDropdownService.delete(filter?.id ?? '').pipe(takeUntil(this.destroy$)).subscribe({
          next: () => {
            this.preferenceList.splice(index, 1);
            this.toasterService.success(MESSAGES.preferenceMessage.replace("{action}", "deleted"))
            this.onDeletePreference.emit(filter);
            this.cdf.detectChanges();
          }
        });
      },
      reject: () => {
        return;
      }
    });
  }

  onCancel(): void {
    this.onClose.emit(false);
  }
}
