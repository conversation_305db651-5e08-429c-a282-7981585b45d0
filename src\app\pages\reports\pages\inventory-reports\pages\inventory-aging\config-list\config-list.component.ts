import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { BaseComponent } from '@core/utils';
import { AgingConfigService } from '@pages/reports/services/aging-config.service';
import { takeUntil } from 'rxjs';
import { AgingConfigDto } from '../../../models/inventory-report-model';

@Component({
  selector: 'app-config-list',
  templateUrl: './config-list.component.html',
  styleUrls: ['./config-list.component.scss']
})
export class ConfigListComponent extends BaseComponent implements OnInit {
  @Output() onClose = new EventEmitter<void>();
  agingConfigList: AgingConfigDto[] = [];
  selectedData!: AgingConfigDto | null;
  showEditModal = false;
  constructor(private readonly agingEmailService: AgingConfigService) {
    super();
  }

  ngOnInit(): void {
    this.getAll();
  }

  getAll(): void {
    this.isLoading = true;
    this.agingEmailService.getList<AgingConfigDto>().pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.agingConfigList = res;
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    })
  }

  onEdit(data: AgingConfigDto): void {
    this.selectedData = data;
    this.showEditModal = true;
  }

  closePopup(): void {
    this.onClose.emit();
  }

  closeEditModal(bool: boolean) {
    this.showEditModal = false;
    this.selectedData = null;
    if (bool) {
      this.getAll();
    }
  }
}
