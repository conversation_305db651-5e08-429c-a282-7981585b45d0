<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      (click)="onAddEditBanner()"
      *appHasPermission="[permissionActions.CREATE_ADVERTISE]"
    >
      <span class="role-label">Add New Banner</span>
    </button>
  </div>
</app-page-header>
<ng-container *ngIf="!isLoading; else pageLoaderTemplate">
  <p-table
    class="no-column-selection"
    [scrollable]="true"
    [value]="banners"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="false"
    [rowHover]="true"
    [loading]="isLoading"
    styleClass="p-datatable-gridlines"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <th pResizableColumn scope="col">Name</th>
        <th pResizableColumn scope="col">Background Color</th>
        <th pResizableColumn scope="col">Text Color</th>
        <th pResizableColumn scope="col" class="actions">Action</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-banner let-rowIndex="rowIndex">
      <tr>
        <td>
          {{ banner.name }} &nbsp; <span *ngIf="banner?.clearAfter">(Clears after {{ banner?.clearAfter }} days)</span>
        </td>
        <td>
          <div tooltipPosition="bottom" pTooltip="{{ banner.bgColor }}" class="color-bar w-100 h-100 p-2" style="background-color:{{ banner.bgColor }}"></div>
        </td>
        <td><div tooltipPosition="bottom" pTooltip="{{ banner.fontColor }}" class="color-bar w-100 h-100 p-2" style="background-color:{{ banner.fontColor }}"></div></td>
        <td class="actions">
          <ng-container *ngIf="banner.id !== 1">
            <img [src]="constants.staticImages.icons.edit" class="me-3" alt="edit" (click)="onAddEditBanner(banner)" *appHasPermission="[permissionActions.UPDATE_ADVERTISE]" />
            <ng-container *ngIf="banner.id !== 2">
              <img [src]="constants.staticImages.icons.deleteIcon" alt="delete" (click)="onDelete(banner)" *appHasPermission="[permissionActions.DELETE_ADVERTISE]" />
            </ng-container>
          </ng-container>
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="6" class="no-data">No data to display</td>
    </ng-template>
  </p-table></ng-container
>

<ng-template #pageLoaderTemplate>
  <div class="no-data mt-5">
    <em class="pi pi-spin pi-spinner"></em>
  </div>
</ng-template>
<p-sidebar
  class="dealer"
  [(visible)]="openModal"
  position="right"
  (onHide)="openModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-advertising-add-edit *ngIf="openModal" (onClose)="closeModal($event)" [banner]="selectedBanner" [isEditMode]="isEditMode"></app-advertising-add-edit>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>

<p-confirmPopup></p-confirmPopup>
