import { Injectable } from '@angular/core';
import { Route, Router, UrlSegment } from '@angular/router';
import { AuthService } from '@pages/auth/services/auth.service';
import { ROUTER_UTILS } from '../utils/router.utils';

@Injectable({
  providedIn: 'root',
})
export class AuthLoadGuard  {

  constructor(private readonly router: Router, private readonly authService: AuthService) { }

  canLoad(route: Route, segments: UrlSegment[]): boolean {
    const isLoggedIn = this.authService.isLoggedIn;

    if (isLoggedIn) {
      return true;
    }

    const returnUrl = segments.map((s) => s.path).join('/');

    const { root, login } = ROUTER_UTILS.config.auth;

    this.router.navigate(['/', root, login], {
      queryParams: { returnUrl },
    });

    return false;
  }
}
