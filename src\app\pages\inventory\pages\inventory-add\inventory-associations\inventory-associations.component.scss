.primary-inventories {
  img {
    height: 185px;
    width: 100%;
  }

  .model-name {
    margin-top: 10px;
  }

  .model-details {
    margin-top: 10px;
  }
}

.model-details {
  font-weight: 500;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 15px;

  div {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 9px;

    .model-detail-label {
      color: #939393;
      margin-right: 2px;
    }
  }
}

.model-name {
  font-weight: 500;
  margin-bottom: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.associated-units {
  margin-top: 10px;

  .content {
    display: flex;
    justify-content: space-between;
  }

  img {
    height: 50px;
  }

  .link-off {
    height: 20px;
    cursor: pointer;
  }

  .associated-unit {
    width: 100%;
  }
}

.stockNumber {
  font-weight: 500;
  font-size: 14px;
  color: #939393;
}

.title {
  h4 {
    margin: 12px;
  }
}

.history-main-wrapper {
  margin-top: 15px;

  .history-content-wrapper {
    border-bottom: 1px solid #e3e3e3;
    margin-bottom: 10px;
    margin-top: 15px;
    font-size: 14px;

    .user-info {
      margin-bottom: 10px;
    }

    .history-details {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      .history-detail-content {
        .history-title {
          margin-bottom: 5px;
        }
      }
    }
  }

  .history-content-wrapper:last-child {
    border: none;
  }

  .history-content-wrapper:first-child {
    margin-top: 0px;
  }

  .history-bold-text {
    font-weight: 600;
  }
}

.association-card {
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn {
      span {
        color: white !important;
        font-size: 15px !important;
        font-weight: 400 !important;
        text-transform: none;
      }
    }
  }
}

.load-more {
  color: var(--text-color);
  text-decoration: underline;
  text-align: center;
  cursor: pointer;
}

@media only screen and (max-width: 500px) {
  .model-name {
    width: inherit !important;
  }
}

@media only screen and (max-width: 900px) and (min-width: 500px) {
  .primary-inventories {
    img {
      height: 300px;
    }
  }
}

.card {
  &.units,
  &.primary-inventories {
    background-color: var(--app-background-color);
  }
}
