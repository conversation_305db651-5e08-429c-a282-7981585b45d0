{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}, "version": 1, "newProjectRoot": "projects", "projects": {"skeye": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:application": {"strict": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/manifest.json", "src/firebase-messaging-sw.js"], "styles": ["node_modules/primeng/resources/themes/lara-light-blue/theme.css", "node_modules/primeng/resources/primeng.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeicons/primeicons.css", "src/styles.scss", "node_modules/quill/dist/quill.core.css", "node_modules/quill/dist/quill.snow.css", {"input": "src/lara-light-purple.scss", "bundleName": "lara-light-purple", "inject": false}, {"input": "src/lara-dark-purple.scss", "bundleName": "lara-dark-purple", "inject": false}], "scripts": ["node_modules/quill/dist/quill.js"], "allowedCommonJsDependencies": ["ngx-google-places-autocomplete", "ngx-gp-autocomplete"]}, "configurations": {"prod": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "500kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}, {"replace": "src/manifest.json", "with": "src/manifest-prod.json"}], "outputHashing": "all"}, "stage": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "500kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}, {"replace": "src/firebase-messaging-sw.js", "with": "src/firebase-messaging-sw-stage.js"}, {"replace": "src/manifest.json", "with": "src/manifest-stage.json"}], "outputHashing": "all"}, "dev": {"outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}, {"replace": "src/manifest.json", "with": "src/manifest-dev.json"}]}, "qa": {"outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}]}, "local": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "prod"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"prod": {"browserTarget": "skeye:build:prod"}, "stage": {"browserTarget": "skeye:build:stage"}, "dev": {"browserTarget": "skeye:build:dev"}, "qa": {"browserTarget": "skeye:build:qa"}, "local": {"browserTarget": "skeye:build:local"}}, "defaultConfiguration": "local"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "skeye:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [], "allowedCommonJsDependencies": ["ngx-google-places-autocomplete", "ngx-gp-autocomplete"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}}