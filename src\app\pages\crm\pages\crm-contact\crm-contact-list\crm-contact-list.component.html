<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn class="top-header">
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      *ngIf="!getSelectedContactsCount()"
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="excelDownloadMenu.toggle($event)"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
    <p-menu #excelDownloadMenu [model]="excelDownloadMenuItems" [popup]="true"> </p-menu>
    <button class="btn btn-primary left" (click)="onAdd()" [appImageIconSrc]="constants.staticImages.icons.addNew" *appHasPermission="[permissionActions.CREATE_CONTACTS]">
      <span class="show-label">Add New Contact</span>
    </button>
    <button *ngIf="!getSelectedContactsCount()" class="btn btn-primary left column-btn show-label" (click)="toggleFilterSidebar()">
      <span class="show-label">Columns</span>
      <fa-icon [icon]="faIcons.faCaretDown"></fa-icon>
    </button>
    <ng-container *appHasPermission="[permissionActions.UPDATE_CONTACTS]">
      <button
        *ngIf="getSelectedContactsCount() && activeTabDetail?.filterName.toLowerCase() !== 'unassigned'"
        class="btn btn-primary left ms-3 light-edit-image"
        (click)="unAssignReporterForSelectedContact($event)"
      >
        <img [src]="constants.staticImages.icons.userXmark" alt="x" width="20px" />
        <span class="show-label ms-3"
          >Unassign Account Rep<span *ngIf="getSelectedContactsCount()">({{ getSelectedContactsCount() }})</span></span
        >
      </button>
    </ng-container>
    <ng-container *appHasPermission="[permissionActions.UPDATE_CONTACTS]">
      <button
        *ngIf="getSelectedContactsCount()"
        class="btn btn-primary left ms-3 light-edit-image"
        (click)="showChangeAccountRepModal = true"
        [appImageIconSrc]="constants.staticImages.icons.edit"
      >
      <span class="show-label">
        <span *ngIf="activeTabDetail?.filterName.toLowerCase() === 'unassigned'">Add Account Rep</span>
        <span *ngIf="activeTabDetail?.filterName.toLowerCase() !== 'unassigned'">Change Account Rep</span>
        <span *ngIf="getSelectedContactsCount()">({{ getSelectedContactsCount() }})</span>
      </span>
      </button>
    </ng-container>
  </div>
</app-page-header>
<div class="card">
  <div class="tabs">
    <ng-container *ngIf="defaultTabs">
      <p-tabView (onChange)="onTabChanged($event)" [scrollable]="true" styleClass="dynamic-tabs" [(activeIndex)]="activeIndex">
        <ng-container *ngFor="let tab of defaultTabs">
          <p-tabPanel *ngIf="tab.filterName" [header]="tab.filterName">
            <ng-template pTemplate="content">
              <ng-container [ngTemplateOutlet]="activeTabTemplate" [ngTemplateOutletContext]="{ heading: tab.filterName }"></ng-container>
            </ng-template>
          </p-tabPanel>
        </ng-container>
      </p-tabView>
    </ng-container>
  </div>
</div>
<ng-template #activeTabTemplate let-heading="heading">
  <p-table
    [columns]="selectedColumns"
    [value]="crmContactList"
    [(selection)]="selectedContacts"
    responsiveLayout="scroll"
    sortMode="single"
    styleClass="p-datatable-gridlines"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="true"
    (onLazyLoad)="onSortChange($event, getAll.bind(this))"
    [sortField]="paginationConfig.predicate"
    [rowHover]="true"
    [loading]="isLoading"
    [resizableColumns]="true"
    columnResizeMode="expand"
    [rowSelectable]="isRowSelectable"
  >
    <ng-template pTemplate="header" let-columns let-rowIndex="rowIndex">
      <!-- heading row -->
      <tr>
        <ng-container *ngIf="isSuperAdminOrManager()">
          <ng-container *appHasPermission="[permissionActions.UPDATE_CONTACTS]">
          <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" *ngIf="getCrmContactsCount()" class="checkbox-col cursor-default" scope="col">
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
            </th>
          </ng-container>
        </ng-container>
        <ng-container *ngFor="let col of columns">
          <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" *ngIf="col.disable && col.key === 'firstName'" [pSortableColumn]="col?.shortingKey" scope="col">
            First Name <p-sortIcon [field]="col?.shortingKey"></p-sortIcon>
          </th>
          <th pResizableColumn  *ngIf="col.disable && col.key === 'prospect' && heading === 'Prospect'" scope="col" class="cursor-default">Prospect</th>
          <th pResizableColumn pReorderableColumn [pSortableColumn]="col?.shortingKey" [pSortableColumnDisabled]="!col.shorting" *ngIf="!col.disable">
            {{ col.name }}
            <p-sortIcon [field]="col.shortingKey || col.field" *ngIf="col.shorting"></p-sortIcon>
          </th>
          <ng-container *appHasPermission="[permissionActions.UPDATE_CONTACTS]">
          <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="small-col cursor-default" *ngIf="col.disable && col.name === 'Active'">
            {{ col.name }}
          </th>
        </ng-container>
          <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="small-col cursor-default" *ngIf="col.disable && col.name === 'Action'">
            {{ col.name }}
          </th>
        </ng-container>
      </tr>
      <!-- Column search row -->
      <tr class="inventory-search-tr">
        <ng-container *ngIf="isSuperAdminOrManager()">
          <ng-container *appHasPermission="[permissionActions.UPDATE_CONTACTS]">
            <th class="checkbox-col" *ngIf="getCrmContactsCount()"></th>
          </ng-container>
        </ng-container>
        <ng-container *ngFor="let col of columns">
          <th pResizableColumn *ngIf="col.disable && col.key === 'firstName'" scope="col">
            <span class="search-input"><input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" /> </span>
          </th>
          <th pResizableColumn *ngIf="col.disable && col.key === 'prospect' && heading === 'Prospect'" scope="col"></th>
          <th pResizableColumn *ngIf="!col.disable && col.type === 'LONG'">
            <span class="search-input">
              <p-inputNumber
                pInputNumber
                type="number"
                mode="decimal"
                [useGrouping]="false"
                styleClass="w-100"
                inputStyleClass="form-control"
                (onInput)="tableSearchByColumn($event, col)"
                [(ngModel)]="col.value"
              ></p-inputNumber>
            </span>
          </th>
          <th pResizableColumn *ngIf="!col.disable && col.type !== 'LONG'">
            <span class="search-input">
              <input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
            </span>
          </th>
          <th pResizableColumn class="small-col" *ngIf="col.disable && col.name === 'Action'">
            <button type="button" class="btn btn-primary btn-sm reset-btn" (click)="clearSearchInput()">Reset filters</button>
          </th>
        </ng-container>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-rowData let-columns="columns" let-rowIndex="rowIndex">
      <tr>
        <ng-container *ngIf="isSuperAdminOrManager()">
          <ng-container *appHasPermission="[permissionActions.UPDATE_CONTACTS]">
            <td class="checkbox-col" *ngIf="getCrmContactsCount()">
              <p-tableCheckbox [value]="rowData" [disabled]="!allowActions(rowData)"></p-tableCheckbox>
            </td>
          </ng-container>
        </ng-container>
        <ng-container *ngFor="let col of columns">
          <td *ngIf="col.key === 'firstName' && col.disable && col.key !== 'primaryPhone' && col.key !== 'secondaryPhone'" (click)="onViewEdit(rowData, false)" class="view-task">
            {{ getEvaluatedExpression(col.key, rowData) }}
          </td>
          <td *ngIf="col.key === 'prospect' && col.disable && col.key !== 'primaryPhone' && col.key !== 'secondaryPhone' && heading === 'Prospect'">
            <ui-switch
              [(ngModel)]="rowData.prospect"
              [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_CONTACTS]) || !allowActions(rowData)"
              [loading]="selectedCrmContactList?.id === rowData?.id && isArchiveInProgress"
              (change)="onProspectToCustomerChange(rowData, $event)"
            >
              <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedCrmContactList?.id === rowData.id"></fa-icon>
            </ui-switch>
          </td>
          <ng-container *ngIf="col.key && col.key !== null && !col.disable && col.type !== 'DROP_DOWN' && col.key !== 'primaryPhone' && col.key !== 'secondaryPhone' && col.key !== 'archived'">
            <td>
              <span>
                {{ getEvaluatedExpression(col.key, rowData) }}
              </span>
            </td>
          </ng-container>
          <td *ngIf="col.key === 'primaryPhone' && col.key !== 'secondaryPhone'">
            {{ rowData?.primaryPhone | phone }}
          </td>
          <td *ngIf="col.key === 'secondaryPhone' && col.key !== 'primaryPhone'">
            {{ rowData?.secondaryPhone | phone }}
          </td>
          <td *ngIf="col.key === 'archived'">
            <ng-container *appHasPermission="[permissionActions.UPDATE_CONTACTS]">
          <td *ngIf="col.key === 'archived'" >
            <ui-switch 
              [(ngModel)]="rowData.isActive" 
                [loading]="selectedCrmContactList?.id === rowData.id && isArchiveInProgress"
                (change)="onArchive(rowData, $event)">
              <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedCrmContactList?.id === rowData.id"></fa-icon>
              </ui-switch>
            </ng-container>
          </td>
          <td *ngIf="col.name === 'Action' && col.disable" class="actions">
            <div class="actions-content">
              <ng-container *appHasPermission="[permissionActions.UPDATE_CONTACTS]">
                <img
                  [src]="constants.staticImages.icons.userXmark"
                  *ngIf="isSuperAdminOrManager() && activeTabDetail?.filterName.toLowerCase() !== 'unassigned' && allowActions(rowData)"
                  (click)="unAssignReporter(rowData, $event)"
                  alt="x"
                />
                <img [src]="constants.staticImages.icons.edit" *ngIf="allowActions(rowData)" (click)="onViewEdit(rowData, true)" alt="edit" />
              </ng-container>
              <!-- <ng-container *appHasPermission="[permissionActions.DELETE_CONTACTS]">
                <img [src]="constants.staticImages.icons.deleteIcon" *ngIf="allowActions(rowData)" (click)="onDelete(rowData, $event)" alt="delete" />
              </ng-container> -->
            </div>
          </td>
        </ng-container>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
  <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
</ng-template>
<p-sidebar
  class="pipeline-config"
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-crm-contact-add
    (onClose)="onAddEditPopupClose($event)"
    *ngIf="showCreateModal"
    [crmContactInfo]="selectedCrmContactList"
    [currentUser]="currentUser"
    [isViewMode]="isCrmContactViewMode"
  ></app-crm-contact-add>
</p-sidebar>
<p-sidebar
  [(visible)]="showColumnModal"
  position="right"
  (onHide)="showColumnModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-column-dropdown
    (onClose)="toggleColumnSidebar()"
    *ngIf="showColumnModal"
    [isModelVisible]="showColumnModal"
    [privateFilter]="defaultColumnsArray"
    [filterParams]="getFilterSaveParams()"
    [columnsList]="dropDownColumnList"
    (onSubmitCheck)="callFilterApiAgain()"
  >
  </app-column-dropdown>
</p-sidebar>
<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>
<p-sidebar
  class="dealer"
  [(visible)]="showChangeAccountRepModal"
  position="right"
  (onHide)="showChangeAccountRepModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-crm-change-account-rep *ngIf="showChangeAccountRepModal" (onClose)="toggleChangeAccountRepSidebar()" [selectedContacts]="selectedContacts"></app-crm-change-account-rep>
</p-sidebar>
<p-confirmPopup *ngIf="!showConfirmationDialog && showConfirmationPopup" appClickOutside (clickOutside)="onBackdropClick($event)"></p-confirmPopup>
<p-confirmDialog styleClass="confirm-dialog" *ngIf="showConfirmationDialog" [breakpoints]="{ '960px': '60vw', '640px': '90vw' }" [style]="{ width: '30vw' }"> </p-confirmDialog>
