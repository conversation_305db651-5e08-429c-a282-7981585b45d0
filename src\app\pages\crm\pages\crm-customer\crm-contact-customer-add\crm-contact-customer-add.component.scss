.m-t-20 {
  margin-top: 20px;
}

.add-btn {
  height: 25px;
  padding: 0px 5px;
  float: right;
  margin: 3px;
}

.mt-23 {
  margin-top: 23px;
}

.m-t-0 {
  margin-top: 0px;
}

.displayContactDetails.company {
  font-weight: 500;
}

.displayContactIcons {
  border: none;
  background-color: transparent;
  float: right;
  margin-right: 5px;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

.bold {
  font-weight: 600;
  font-size: 20px;
}

::ng-deep .customer-lead-div {
  .p-disabled {
    opacity: 0.6;
  }

  .p-disabled .p-dropdown-label,
  .task-add .p-component:disabled .p-dropdown-label {
    color: #797979;
  }
}

.p-disabled,
.p-component:disabled,
textarea:disabled {
  color: #797979;
  opacity: 1;
}

::ng-deep .display-content .p-card .p-card-content {
  padding: 0;
}

.m-0 {
  margin: 0px;
}

.sales-detail {
  margin-top: -10px;
}

.map-icon {
  margin-top: 23px;
  button {
    display: flex;
    flex-direction: row-reverse;
    height: 100%;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    scroll-margin-bottom: 100px;

    img {
      margin-right: 10px;
    }
  }
}

.f-s-12 {
  font-size: 12px;
}

@media only screen and (max-width: 500px) {
  .map-icon {
    margin-top: 32px;
  }
}
