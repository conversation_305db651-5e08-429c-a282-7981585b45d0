@import "/src/assets/scss/theme/mixins";

.m-t-20 {
  margin-top: 20px;
}

.add-btn {
  height: 24px;
  padding: 0px 5px;
  float: right;
  margin: 0px 0px 3px 0px;
}

.mt-23 {
  margin-top: 23px;
}

.m-t-0 {
  margin-top: 0px;
}

.displayContactDetails.company {
  font-weight: 500;
}

.displayContactIcons {
  border: none;
  background-color: transparent;
  float: right;
  margin-right: 5px;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

.bold {
  font-weight: 600;
  font-size: 20px;
}

::ng-deep .customer-lead-div {
  .p-disabled {
    opacity: 0.6;
  }

  .p-disabled .p-dropdown-label,
  .task-add .p-component:disabled .p-dropdown-label {
    color: #797979;
  }
}

.p-disabled,
.p-component:disabled,
textarea:disabled {
  opacity: 0.6;
}

::ng-deep .display-content .p-card .p-card-content {
  padding: 0;
}

.m-0 {
  margin: 0px;
}

.sales-detail {
  margin-top: -10px;
}

.location-dot {
  color: var(--active-color);
}

.header-title {
  top: -8px;
  position: relative;
}

.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
  position: absolute;
  top: 30px;
}

.bold-text {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

.year-wrapper {
  display: flex;

  .year-content {
    @include flex-center;
    color: var(--text-color);
    span {
      margin-right: 5px;
      font-size: 14px;
    }
  }

  .from {
    margin-right: 5px;
  }
}

::ng-deep .picker-dropdown {
  .p-datepicker {
    width: 200px !important;
  }
  .p-datepicker-buttonbar .p-button {
    color: white;
  }
}

p-inputNumber {
  display: block;
  padding: 0px !important;
}

.add-btn {
  height: 25px;
  padding: 0px 5px;
  margin: 0px 0px 3px 0px;
}

.accordion-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

td.empty {
  display: flex;
  width: 100%;
  justify-content: center;
}
::ng-deep .customer-lead-div {
  .p-multiselect {
    width: -webkit-fill-available;
  }

  .multi-select-dropdown {
    position: relative;
    .cross-icon {
      position: absolute;
      right: 51px;
      top: 50%;
      transform: translateY(-40%);
      font-size: 20px;
      color: #6c757d;
    }
  }
}
