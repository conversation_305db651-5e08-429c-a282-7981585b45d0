::ng-deep .driver-schedule-list {
  .p-dropdown .p-dropdown-label {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
    padding: 3px 0px 3px 10px !important;
    font-size: 14px;
  }

  .p-dropdown .p-dropdown-trigger {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
  }

  .p-dropdown {
    background-color: var(--active-color) !important;
    height: inherit;
  }

  .pi-chevron-down:before {
    font-size: 14px;
  }
}

.w-150 {
  width: 150px !important;
}

.view-task {
  text-decoration: underline;
  color:  var(--link-color);
  cursor: pointer;
}

.m-l-15 {
  margin-left: 15px;
}

.m-r-10 {
  margin-right: 15px;
}
.tabs {
  position: relative;
}

.top-header {
  .column-btn {
    margin-left: 10px;
    color: #0b0b69;
    background-color: #fff;
    border-color: #0b0b69;
    font-weight: 600;
    border: 3px solid #0b0b69;
    padding: 0px 17px !important;

    fa-icon {
      margin-left: 5px;
      font-size: 16px;
      margin-right: 9px;
    }
  }
}

.small-col {
  width: 100px;
  text-align: center !important;
}

::ng-deep .inventory-search-tr th {
  .search-input {
    input {
      width: 4rem;
      height: 36px !important;
    }

    p-calendar.p-inputwrapper {
      span.p-calendar-w-btn {
        height: 36px !important;
      }
    }
  }
  .float-end img {
    cursor: pointer;
  }
}

.top-header {
  .p-menu-overlay {
    top: unset !important;
    left: unset !important;
  }
}

::ng-deep .content-between {
  .p-menu-overlay {
    top: unset !important;
    left: unset !important;
  }
}

.action-btn {
  font-size: 19px;
  margin-top: 3px;
}

.w-145 {
  width: 145px !important;
}

::ng-deep .driverSchedulingList {
  .normal-dropdowns {
    .p-dropdown {
      height: 42px;
    }
  }
  .p-dropdown .p-dropdown-label {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
    font-size: 14px;
    padding: 3px 0px 3px 10px !important;
  }

  .p-dropdown .p-dropdown-trigger {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
  }

  .p-dropdown {
    background-color: var(--active-color) !important;
    height: inherit;
  }

  .pi-chevron-down:before {
    font-size: 14px;
  }

  .priority-icon {
    padding: 17px !important;
  }
}

@media only screen and (max-width: 860px) {
  .top-header {
    .column-btn {
      margin-top: 1rem;
    }
  }
}
