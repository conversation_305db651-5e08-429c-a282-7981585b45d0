import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { SplashScreenService } from '@core/services';
import { BaseComponent } from '@core/utils';

@Component({
  selector: 'app-splash-screen',
  templateUrl: './splash-screen.component.html',
  styleUrls: ['./splash-screen.component.scss']
})
export class SplashScreenComponent extends BaseComponent implements OnInit {

  @ViewChild('splashScreen', { static: true }) splashScreen!: ElementRef;

  constructor(private readonly splashScreenService: SplashScreenService) {
    super();
  }

  ngOnInit(): void {
    this.splashScreenService.init(this.splashScreen);
  }

}
