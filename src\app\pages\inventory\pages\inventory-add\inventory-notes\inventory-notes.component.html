<form (ngSubmit)="onSubmit()" class="inventory-notes">
  <section>
    <p-accordion class="nested-accordion" [multiple]="true">
      <div class="row">
        <div class="col-md-6 col-12">
          <p-accordionTab [(selected)]="accordionTabs.externalNotes">
            <ng-template pTemplate="header">
              <div class="accordion-header">
                <span>External Notes</span>
                <em class="pi" [ngClass]="accordionTabs.externalNotes ? 'pi-angle-up' : 'pi-angle-down'"></em>
              </div>
            </ng-template>
            <ng-template pTemplate="content">
              <ng-container [ngTemplateOutlet]="externalNotes"></ng-container>
            </ng-template>
          </p-accordionTab>
        </div>
        <div class="col-md-6 col-12">
          <p-accordionTab [(selected)]="accordionTabs.internalNotes">
            <ng-template pTemplate="header">
              <div class="accordion-header">
                <span>Internal Notes</span>
                <em class="pi" [ngClass]="accordionTabs.internalNotes ? 'pi-angle-up' : 'pi-angle-down'"></em>
              </div>
            </ng-template>
            <ng-template pTemplate="content">
              <ng-container [ngTemplateOutlet]="internalNotes"></ng-container>
            </ng-template>
          </p-accordionTab>
        </div>
      </div>
    </p-accordion>
  </section>
</form>

<ng-template #externalNotes>
  <p-message severity="info" text="{{ externalNotesMsg }}" styleClass="p-mr-2"></p-message>
  <p-editor [(ngModel)]="externalNotesDetails" [style]="{ height: '320px' }" [readonly]="isViewMode">
    <ng-template pTemplate="header">
      <span class="ql-formats">
        <span class="ql-formats">
          <select class="ql-font"></select>
          <select class="ql-size"></select>
        </span>
        <span class="ql-formats">
          <button type="button" class="ql-bold" aria-label="Bold"></button>
          <button type="button" class="ql-italic" aria-label="Italic"></button>
          <button type="button" class="ql-underline" aria-label="Underline"></button>
        </span>
        <span class="ql-formats">
          <button class="ql-align" value=""></button>
          <button class="ql-align" value="center"></button>
          <button class="ql-align" value="right"></button>
          <button class="ql-align" value="justify"></button>
        </span>
        <span class="ql-formats">
          <button type="button" class="ql-list" value="bullet" aria-label="Unordered List"></button>
        </span>
        <span class="ql-formats">
          <button type="button" class="ql-image" aria-label="Underline"></button>
          <button type="button" class="ql-link" value="bullet" aria-label="Insert Link"></button>
        </span>
        <span role="button" tabindex="0" class="ql-picker-item"></span>
      </span>
    </ng-template>
  </p-editor>
</ng-template>

<ng-template #internalNotes>
  <p-message severity="info" text="{{ internalNotesMsg }}" styleClass="p-mr-2"></p-message>
  <p-editor [(ngModel)]="internalNotesDetails" [style]="{ height: '320px' }" [readonly]="isViewMode">
    <ng-template pTemplate="header">
      <span class="ql-formats">
        <span class="ql-formats">
          <select class="ql-font"></select>
          <select class="ql-size"></select>
        </span>
        <span class="ql-formats">
          <button type="button" class="ql-bold" aria-label="Bold"></button>
          <button type="button" class="ql-italic" aria-label="Italic"></button>
          <button type="button" class="ql-underline" aria-label="Underline"></button>
        </span>
        <span class="ql-formats">
          <button class="ql-align" value=""></button>
          <button class="ql-align" value="center"></button>
          <button class="ql-align" value="right"></button>
          <button class="ql-align" value="justify"></button>
        </span>
        <span class="ql-formats">
          <button type="button" class="ql-list" value="bullet" aria-label="Unordered List"></button>
        </span>
        <span class="ql-formats">
          <button type="button" class="ql-image" aria-label="Underline"></button>
          <button type="button" class="ql-link" value="bullet" aria-label="Insert Link"></button>
        </span>
        <span role="button" tabindex="0" class="ql-picker-item"></span>
      </span>
    </ng-template>
  </p-editor>
</ng-template>
