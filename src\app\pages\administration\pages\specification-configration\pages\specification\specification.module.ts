import { CommonModule, TitleCasePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { SpecificationGroupAddUpdateComponent } from './pages/specification-group-add-update/specification-group-add-update.component';
import { SpecificationGroupFieldAddUpdateComponent } from './pages/specification-group-field-add-update/specification-group-field-add-update.component';
import { SpecificationRoutingModule } from './specification-routing.module';
import { SpecificationComponent } from './specification.component';


@NgModule({
  declarations: [
    SpecificationComponent,
    SpecificationGroupAddUpdateComponent,
    SpecificationGroupFieldAddUpdateComponent
  ],
  imports: [
    CommonModule,
    SpecificationRoutingModule,
    DirectivesModule,
    SharedComponentsModule,
    SidebarModule,
    TableModule,
    FontAwesomeModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    FormsModule,
    DropdownModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    CheckboxModule
  ],
  providers: [
    ConfirmationService,
    MessageService,
    TitleCasePipe
  ],
})
export class SpecificationModule { }
