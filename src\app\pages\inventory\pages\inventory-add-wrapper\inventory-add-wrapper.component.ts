import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseComponent } from '@core/utils';
import { InventoryListItem, Tabs } from '@pages/inventory/models';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { IdNameModel } from 'src/app/@shared/models';
import { InventoryAddComponent } from '../inventory-add/inventory-add.component';

enum AddUnitStep {
  SEARCH_UNIT = 1,
  ADD_UNIT
}

@Component({
  selector: 'app-inventory-add-wrapper',
  templateUrl: './inventory-add-wrapper.component.html',
  styleUrls: ['./inventory-add-wrapper.component.scss']
})
export class InventoryAddWrapperComponent extends BaseComponent implements OnChanges {
  title = 'Add Unit';
  addUnitSteps = AddUnitStep;
  currentStep = AddUnitStep.ADD_UNIT;
  hasDataBeenModified = false;
  selectedVehicle: any;
  index = 1;
  currentStepIndex = Tabs.GENERAL;
  isPrevious = true;
  activeIndex = 0;
  tabs = Tabs;
  showLoader = false;
  redirectUrl!: string;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Input() isEditMode!: boolean;
  @Input() isViewMode!: boolean;
  @Input() activeIndexes!: number;
  @Input() showSoldTabs!: boolean;
  @Input() showHoldTabs!: boolean;
  @Input() showAssociation = true;
  @Input() categoriesToShow!: Array<IdNameModel>;
  @Output() addedUnitId = new EventEmitter<number>();
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  isFlagEditButton!: boolean
  @ViewChild(InventoryAddComponent) inventoryAddComponent!: InventoryAddComponent;
  constructor(
    private readonly activeRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.inventoryInfo?.currentValue || changes.inventoryIncomingInfo?.currentValue) {
      if (this.isViewMode) {
        this.title = 'View Unit';
      }
      else {
        this.title = 'Edit Unit';
      }
      this.isEditMode = true;
      this.currentStep = AddUnitStep.ADD_UNIT;
    }
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.returnUrl) {
          this.redirectUrl = params?.returnUrl;
        }
      });
  }

  onSubmit(): void {
    this.inventoryAddComponent.onSubmit();
  }

  onCancel(hasDataModified = false): void {
    this.hasDataBeenModified = hasDataModified;
    this.onClose.emit(this.hasDataBeenModified);
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  isPreviousTab(event: any): void {
    this.isPrevious = event;
  }

  onDataModified(isModified: boolean): void {
    this.hasDataBeenModified = isModified;
  }

  onStepChange(nextStep: AddUnitStep): void {
    this.currentStep = nextStep;
  }

  onVehicleSelect(vehicle: any): void {
    this.selectedVehicle = vehicle;
  }

  onTabChange(activeIndex: number): void {
    this.activeIndex = activeIndex;
  }

  get nextStepName(): string {
    switch (this.activeIndex) {
      case Tabs.GENERAL: return 'Photos';
      case Tabs.PHOTOS: return 'Condition';
      case Tabs.CONDITION: return 'Notes';
      case Tabs.NOTES: return 'Documents';
      default: return 'General';
    }
  }

  onAddedUnitId(unitId: number): void {
    this.addedUnitId.emit(unitId);
  }

  setLoaderStatus(bool: boolean) {
    this.showLoader = bool;
  }

  onEditTitle(event: boolean) {
    if (event) {
      this.isFlagEditButton = event
      this.title = 'Edit Unit'
    }
  }
}
