import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionForChildGuard } from '@core/guards';
import { ROUTER_UTILS } from '@core/utils';
import { PermissionActions } from 'src/app/@shared/models';

const routes: Routes = [
  {
    path: ROUTER_UTILS.config.transport.driverScheduleBoard.root,
    loadChildren: async () => (await import('./pages/driver-schedule/driver-schedule.module')).DriverScheduleModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_DRIVER_SCHEDULE]
    }
  },
  {
    path: ROUTER_UTILS.config.transport.incomingTruckBoard.root,
    loadChildren: async () => (await import('./pages/incoming-truck/incoming-truck.module')).IncomingTruckModule,
    canActivate: [PermissionForChildGuard],
    data: {
      permission: [PermissionActions.VIEW_INCOMING_TRUCK]
    }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TransportRoutingModule { }
