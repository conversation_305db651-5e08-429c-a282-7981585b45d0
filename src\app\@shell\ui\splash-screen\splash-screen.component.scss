:host {
  .splash-screen {
    background-color: var(--card-bg-color);
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    z-index: 1000;

    img {
      margin-left: calc(100vw - 100%);
      width: 200px;
      margin-bottom: 30px;
    }

    span {
      margin-left: calc(100vw - 100%);
      margin-bottom: 30px;
    }

    ::ng-deep {
      [role="progressbar"] {
        margin-left: calc(100vw - 100%);
      }

      .mat-progress-spinner circle,
      .mat-spinner circle {
        // brand color
        stroke: var(--primary-color);
      }
    }
  }
}
