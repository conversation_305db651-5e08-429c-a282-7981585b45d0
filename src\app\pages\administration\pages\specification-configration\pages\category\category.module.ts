import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { KeyFilterModule } from 'primeng/keyfilter';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CategoryRoutingModule } from './category-routing.module';
import { CategoryComponent } from './category.component';
import { CategoryAddUpdateComponent } from './pages/category-add-update/category-add-update.component';


@NgModule({
  declarations: [
    CategoryComponent,
    CategoryAddUpdateComponent
  ],
  imports: [
    CommonModule,
    CategoryRoutingModule,
    DirectivesModule,
    SharedComponentsModule,
    SidebarModule,
    TableModule,
    FontAwesomeModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    KeyFilterModule
  ],
  providers: [
    ConfirmationService,
    MessageService
  ],
  exports: [
  ]
})
export class CategoryModule { }
