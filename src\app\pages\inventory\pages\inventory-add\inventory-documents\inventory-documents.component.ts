import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Constants, icons, MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { allowExtensions, getRefactorFileName } from '@core/utils/fileUpaloadName.util';
import { AuthService } from '@pages/auth/services/auth.service';
import { InventoryListItem } from '@pages/inventory/models';
import { DocumentListFilter, DocumentListItem, SharingType } from '@pages/inventory/models/documents.model';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import * as saveAs from 'file-saver';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { FileProperties, FileUploadProgress, TableColumn } from 'src/app/@shared/models';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-inventory-documents',
  templateUrl: './inventory-documents.component.html',
  styleUrls: ['./inventory-documents.component.scss']
})
export class InventoryDocumentsComponent extends BaseComponent implements OnInit, OnChanges {

  cols: TableColumn[] = [];
  _selectedColumns: any[] = [];
  filterParams: DocumentListFilter = new DocumentListFilter();
  inventoryDocumentList: DocumentListItem[] = [];
  fileUploadProgresses: FileUploadProgress[] = [];
  unitId!: number;
  isUploadFlag = true;
  taskFileUploadPath = 'Inventory Document Files';
  documentDetails!: DocumentListItem;
  showDocumentDetails = false;
  @Input() isViewMode!: boolean;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() isEditMode = false;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  excelDownloadMenuItems: MenuItem[] = [
    {
      label: "Private Document",
      command: () => this.triggerFileInput('privateDocument')
    },
    {
      label: "Public Document",
      command: () => this.triggerFileInput('publicDocument')
    }
  ];
  accordionTabs = {
    documentation: true
  }
  constructor(private readonly inventoryService: InventoryService,
    private readonly cdf: ChangeDetectorRef,
    private readonly fileUploadService: FileUploadService,
    private readonly toasterService: AppToasterService,
    private readonly authService: AuthService,
    private readonly datePipe: DatePipe,
    private readonly commonSharedService: CommonSharedService,
    private readonly confirmationService: ConfirmationService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.setTableColumns();
    this.getUnitId();
    this.getCurrentUser();
    if (this.isEditMode && (this.inventoryInfo || this.inventoryIncomingInfo)) {
      this.inventoryDocumentList = [];
      this.getPublicDocumentListByUnitId(SharingType.PRIVATE);
      this.getPublicDocumentListByUnitId(SharingType.PUBLIC);
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.inventoryInfo?.currentValue || changes.inventoryIncomingInfo?.currentValue) {
      this.isEditMode = true;
    }
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  getPublicDocumentListByUnitId(_sharingType: SharingType) {
    this.unitId = Number(this.inventoryIncomingInfo ? this.inventoryIncomingInfo?.unitId : this.inventoryInfo?.id);
    const unitId = this.inventoryIncomingInfo ? this.inventoryIncomingInfo?.unitId : this.inventoryInfo?.id;
    if (unitId && _sharingType) {
      const endpoint = API_URL_UTIL.inventory.documentDetails.replace(':unitId', String(unitId));
      const fullEndpoint = `${endpoint}?sharingType=${_sharingType}`;
      this.inventoryService.get<DocumentListItem[]>(fullEndpoint).pipe(takeUntil(this.destroy$)).subscribe({
        next: (notesDetails) => {
          for (const notesDetail of notesDetails) {
            this.inventoryDocumentList.push({ ...notesDetail, isEditable: true, uploadDate: this.datePipe.transform((notesDetail.createdDate), Constants.dateFormat) })
          }
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      })
    }
  }

  setTableColumns() {
    this.cols = [
      { field: 'description', sortKey: 'description', sortable: true, header: 'Description', reorderable: true, evaluationExpression: 'description' },
      { field: 'uploadDate', header: 'Upload Date', sortable: true, sortKey: 'uploadDate', reorderable: true, evaluationExpression: 'uploadDate' },
      { field: 'sharing', header: 'Sharing', sortable: true, sortKey: 'sharing', reorderable: true, evaluationExpression: 'sharing' },
    ];
    this._selectedColumns = this.cols;
  }

  onFileSelect(event: any, type: string) {
    if (event.target?.files?.length) {
      for (const file of event.target.files) {
        if (file.size > this.constants.fileSize) {
          this.toasterService.warning(MESSAGES.fileUploadMessage)
          return
        }
        const modifiedFileName = getRefactorFileName(file.name);
        const isExtensionSupported = allowExtensions(modifiedFileName, Constants.allowedPdfFormats)
        if (isExtensionSupported) {
          const modifiedFile = new File([file], modifiedFileName, { type: file.type });
          this.uploadDocument(modifiedFile, type);
        } else {
          this.toasterService.error(MESSAGES.fileTypeSupportedPDF);
          return
        }
        
      }
    }
  }

  triggerFileInput(elementId: string): void {
    const fileInput = document.getElementById(elementId) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  uploadDocument(file: File, fileType: string): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, this.taskFileUploadPath, this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          const date = new Date();
          this.inventoryDocumentList.push({
            title: file.name,
            url: fileUrl.url,
            description: null,
            sharing: fileType === SharingType.PRIVATE ? SharingType.PRIVATE : SharingType.PUBLIC,
            unitId: this.unitId,
            fileName: file.name,
            fileSize: (file.size / 1000).toFixed(2),
            uploadDate: ` ${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`,
            isEditable: false,
            file: file,
          })
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.documentUploadSuccess);
          if (this.fileUploadProgresses.length === 1) {
            this.isUploadFlag = false;
            this.cdf.detectChanges();
          }
          this.cdf.detectChanges();
        },
        error: err => {
          this.toasterService.error(MESSAGES.fileUploadError);
          this.cdf.detectChanges();
        }
      })
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showDocumentDetails = false;
    if (refreshList) {
      this.inventoryDocumentList = [];
      this.getPublicDocumentListByUnitId(SharingType.PRIVATE);
      this.getPublicDocumentListByUnitId(SharingType.PUBLIC);
    }
  }

  openDocumentDetails(documentDetails: DocumentListItem): void {
    this.documentDetails = documentDetails;
    this.showDocumentDetails = true;
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user) {
        this.currentUser = user;
      }
    });
  }

  private getUnitId() {
    if (!this.isEditMode) {
      this.inventoryService.getUnitId$().pipe(takeUntil(this.destroy$)).subscribe(res => {
        this.unitId = res;
      });
    }
  }

  downloadServerPDF(file: DocumentListItem | null): void {
    if (file?.url) {
      this.fileUploadService.downloadFile(file.url, file.fileName).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/pdf" }), file.fileName);
        }
      });
    }
  }

  onDeleteDocument(document: any, event: any): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'document'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        if (document.id) {
          this.onDeleteConfirmation(document?.id);
        } else {
          this.removeDocumentFromList(document);
        }
      }
    });
  }

  removeDocumentFromList(document: any): void {
    const index = this.inventoryDocumentList.findIndex(item => item === document);
    if (index > -1) {
      this.inventoryDocumentList.splice(index, 1);
    }

  }


  onDeleteConfirmation(documentId: number | null | undefined): void {
    if (documentId) {
      const id = String(documentId);
      const endpoint = API_URL_UTIL.inventory.documentDelete.replace(':documentId', id)
      this.inventoryService.delete(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          this.inventoryDocumentList = [];
          this.getPublicDocumentListByUnitId(SharingType.PRIVATE);
          this.getPublicDocumentListByUnitId(SharingType.PUBLIC);
          this.toasterService.success(MESSAGES.documentDeleteSuccess);
        }
      })
    }
  }

  get documentCreateParams(): any {
    const params = [];
    for (const documentList of this.inventoryDocumentList) {
      if (!documentList.isEditable) {
        params.push({
          title: documentList.title,
          url: documentList.url,
          description: documentList.description,
          unitId: this.unitId,
          sharing: documentList.sharing,
          fileName: documentList.fileName,
          fileSize: documentList.fileSize
        });
      }
    }
    return params;
  }


  private saveDocuments(): void {
    if (this.documentCreateParams.length) {
      this.commonSharedService.setMessage$(MESSAGES.documentsSave);
      this.commonSharedService.setBlockUI$(true);
      this.inventoryService.add(this.documentCreateParams, API_URL_UTIL.inventory.document).pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.toasterService.success(MESSAGES.documentAddSuccess);
        this.commonSharedService.setBlockUI$(false);
      })
    }
  }

  onSubmit(): void {
    this.saveDocuments();
  }
}
