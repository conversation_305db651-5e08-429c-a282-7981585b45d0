import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { MESSAGES, icons } from '@constants/*';
import { BaseComponent } from '@core/utils';
import { AssociationsUnits, InventoryListItem } from '@pages/inventory/models';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-inventory-sold',
  templateUrl: './inventory-sold.component.html',
  styleUrls: ['./inventory-sold.component.scss']
})
export class InventorySoldComponent extends BaseComponent implements OnInit, OnChanges {

  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() selectedStatus!: number | null;
  @Input() isGeneralInfo!: boolean;

  inventoryQuotationFormGroup!: FormGroup;

  @Output() closeModal = new EventEmitter<boolean>();
  @Output() saveQuotation = new EventEmitter<void>();
  @Output() soldInventoriesId = new EventEmitter<Array<number>>();

  constructor(
    private readonly fb: FormBuilder,
    private readonly inventoryService: InventoryService,
    private readonly confirmationService: ConfirmationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeQuotationFormGroup();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.inventoryInfo?.currentValue) {
      this.initializeQuotationFormGroup();
      this.addPrimaryInventory()
      this.addAssociatedinventories();
    }
  }

  initializeQuotationFormGroup(): void {
    this.inventoryQuotationFormGroup = this.fb.group({
      content: this.fb.array([])
    });
  }

  get quotationDetailsFormArray(): FormArray {
    return this.inventoryQuotationFormGroup.get('content') as FormArray;
  }

  addPrimaryInventory(): void {
    if (this.inventoryInfo) {
      this.quotationDetailsFormArray.push(this.fb.group({
        fullUrl: this.inventoryInfo?.unitImages?.fullUrl,
        unitId: this.inventoryInfo?.generalInformation?.id,
        yearMakeModel: `${this.inventoryInfo?.generalInformation?.year} ${this.inventoryInfo?.generalInformation?.make?.name} ${this.inventoryInfo?.generalInformation?.unitModel?.name}`,
        stockNumber: this.inventoryInfo?.generalInformation?.stockNumber,
        vin: this.inventoryInfo?.generalInformation?.vin,
        isSelected: true
      }));
    }
  }

  addAssociatedinventories(): void {
    if (this.inventoryInfo?.associations) {
      for (const unitQuotationAssociation of this.inventoryInfo?.associations) {
        this.quotationDetailsFormArray.push(this.fb.group({
          fullUrl: unitQuotationAssociation.unitImages?.fullUrl,
          unitId: unitQuotationAssociation.id,
          yearMakeModel: unitQuotationAssociation.yearMakeModel,
          stockNumber: unitQuotationAssociation.stockNumber,
          retailAskingPrice: unitQuotationAssociation.retailCost,
          vin: unitQuotationAssociation.vin,
          totalProjectedInvestment: unitQuotationAssociation.investmentCost,
          isSelected: false,
        }));
      }
    }
  }

  onModalClose(shouldClose = false): void {
    this.closeModal.emit(shouldClose);
    this.quotationDetailsFormArray?.clear();
  }

  onSubmit(event: MouseEvent): void {
    if (this.allInventoriesSelected) {
      this.soldSelectedInventories();
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.soldSelectedItems,
      header: "Confirmation",
      icon: icons.triangle,
      accept: () => {
        this.soldSelectedInventories();
      }
    });
  }

  get allInventoriesSelected(): boolean {
    return this.quotationDetailsFormArray.value.every((inventory: AssociationsUnits) => {
      return inventory?.isSelected;
    });
  }

  soldSelectedInventories(): void {
    if (this.isGeneralInfo) {
      this.soldInventoriesId.emit(this.createQuotationParams());
      this.onModalClose(true);
      return;
    }
    const endpoint = `status/${this.selectedStatus}`;
    const queryParams = `${endpoint}?associationId=${this.inventoryInfo?.unitAssociationId ?? ''}`
    this.inventoryService.update(this.createQuotationParams(), queryParams).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.onModalClose(true);
      }
    });
  }

  createQuotationParams(): Array<number> {
    const soldInvenntoryIds = [];
    for (const quote of this.quotationDetailsFormArray.value) {
      if (quote.isSelected) {
        soldInvenntoryIds.push(quote.unitId);
      }
    }
    return soldInvenntoryIds;
  }

}
