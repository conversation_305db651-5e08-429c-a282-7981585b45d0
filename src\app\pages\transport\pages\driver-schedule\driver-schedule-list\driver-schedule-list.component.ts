import { DatePipe, TitleCasePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HistoryModuleName, MESSAGES, dateFormat, driverAddress, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { PrivilegeActionResponseDTOs } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { ColumnItem, FilterList, FilterModuleName } from '@pages/common-table-column/models/common-table.column.model';
import { Address, DriverScheduleBoardListItem, DriverScheduleStatus } from '@pages/transport/models/driver-schedule.model';
import { DriverScheduleService } from '@pages/transport/services/driver-schedule.service';
import * as saveAs from 'file-saver';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { TabsetComponent } from 'ngx-bootstrap/tabs';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, GenericFilterParams, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';
@Component({
  selector: 'app-driver-schedule-list',
  templateUrl: './driver-schedule-list.component.html',
  styleUrls: ['./driver-schedule-list.component.scss'],
  providers: [TitleCasePipe]
})
export class DriverScheduleListComponent extends BaseComponent implements OnInit {
  @ViewChild('profileTabs') profileTabs!: TabsetComponent
  driverScheduleBoardList: DriverScheduleBoardListItem[] = [];
  filterParams: GenericFilterParams = new GenericFilterParams();
  showCreateModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.DRIVER_SCHEDULE;
  selectedDriverScheduleBoard!: DriverScheduleBoardListItem | null;
  driverSchedules: DriverScheduleBoardListItem[] = [];
  statues: IdNameModel[] = [];
  statusCopy: IdNameModel[] = [];
  isStatusList!: number | null;
  isViewMode = false;
  pickUpLocation: Address[] = [];
  destinationLocation: Address[] = [];
  cols: any[] = [];
  _selectedColumns: any[] = [];
  showColumnModal = false;
  defaultTabs!: FilterList[];
  tableColumn!: FilterList | undefined;
  defaultColumnsArray: ColumnItem[] = [];
  dropDownColumnList: ColumnItem[] = [];
  filterValue: FilterValue[] = [];
  globalSearch = new Subject<FilterValue[]>();
  initialFilterParams: any;
  showCalenderModel: any;
  modifiedDriverScheduleBoardList: any[] = [];
  activeTabDetail: any;
  hideFieldForSearch: string | null = '';
  redirectToCalender!: string;
  userPermissions!: PrivilegeActionResponseDTOs[];
  driverScheduleStatuses = [
    {
      name: 'Requested',
      value: DriverScheduleStatus.REQUESTED
    },
    {
      name: 'Scheduled',
      value: DriverScheduleStatus.SCHEDULED
    },
    {
      name: 'Issue',
      value: DriverScheduleStatus.ISSUE
    },
    {
      name: 'Completed',
      value: DriverScheduleStatus.COMPLETED
    }
  ];
  excelDownloadMenuItems: MenuItem[] = [
    {
      label: "Selected Columns",
      command: () => this.exportUsersToExcel()
    },
    {
      label: "All Columnns",
      command: () => this.exportUsersToExcel(true)
    }
  ];
  actionMenu: MenuItem[] = [
    {
      label: "Send an email",
      command: () => this.sendEmail()
    }, {
      label: "Download PDF",
      command: () => this.downloadPDF()
    }
  ];

  constructor(
    private readonly DriverScheduleService: DriverScheduleService,
    private readonly cdf: ChangeDetectorRef,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService,
    readonly columnDropDownService: ColumnDropdownService,
    private readonly commonService: CommonService,
    private readonly commonSharedService: CommonSharedService,
    private readonly authService: AuthService,
    private readonly datePipe: DatePipe,
    private readonly titleCasePipe: TitleCasePipe,
    private readonly activeRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super()
    this.pageTitle = 'Driver Scheduling Board';
  }

  async ngOnInit(): Promise<void> {
    this.getDriverScheduleStatuses();
    await this.getCurrentUser();
    this.getFilterDetail();
    this.getFilterSaveParams();
    this.displaySearchResult();
    this.filterParams.values = this.driverScheduleInfoParams;
    this.filterParams.treeOperator = TreeOperatorType.NOOP;
    this.initialFilterParams = Object.assign([], this.filterParams.values)
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getDriverScheduleDetails(params.id);
        }
      })
    this.userPermissions = this.authService.getRoleInfo()?.privilegeActionResponseDTOs;
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  private getCurrentUser(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe({
        next: (user) => {
          if (user) {
            this.currentUser = user;
          }
          resolve();
        },
        error: () => {
          reject();
        }
      });
    });
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  onTabChanged(tabChangeEvent: any): void {
    const tab = this.defaultTabs[tabChangeEvent.index]
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });

    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    if (tab?.data) {
      const tabData = JSON.parse(tab?.data);
      this.filterParams = tabData;
      this.initialFilterParams = Object.assign([], this.filterParams.values);
      this.hideFieldForSearch = tab.hideField ?? null;
      this.activeTabDetail = tab;
    }

    this.getAll();
  }

  get driverScheduleInfoParams() {
    return [
      {
        dataType: DataType.ENUM,
        key: 'driverScheduleStatus',
        operator: OperatorType.EQUAL,
        value: 'REQUESTED',
        enumName: 'DriverScheduleStatusEnum'
      }
    ]
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.DriverScheduleService.getListWithFiltersWithPagination<GenericFilterParams, DriverScheduleBoardListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.inventory.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.driverScheduleBoardList = res.content;
          for (const address of this.driverScheduleBoardList) {
            for (const addresses of address.driverScheduleAddresses) {
              if (addresses.location === driverAddress.pickUp) {
                this.pickUpLocation.push(addresses);
              }
              if (addresses.location === driverAddress.destination) {
                this.destinationLocation.push(addresses);
              }
            }
          }
          this.driverScheduleViewFromCalender()
          this.setPaginationParamsFromPageResponse<DriverScheduleBoardListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  onAdd(): void {
    this.showCreateModal = true;
    this.isViewMode = false;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    if (this.redirectToCalender) {
      this.router.navigateByUrl(`${API_URL_UTIL.calendar.root}`);
    }
    this.showCreateModal = false;
    this.selectedDriverScheduleBoard = null;
    if (refreshList) {
      this.getAll();
    }

  }

  getDriverScheduleDetails(id: string): void {
    this.DriverScheduleService.get<DriverScheduleBoardListItem>(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: DriverScheduleBoardListItem) => {
          this.onViewEdit(res, false);
        }
      });
  }

  onEdit(driverList: DriverScheduleBoardListItem): void {
    this.showCreateModal = true;
    this.selectedDriverScheduleBoard = driverList;
    this.cdf.detectChanges();
  }

  onDelete(driverData: DriverScheduleBoardListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'driver schedule'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(driverData);
      }
    });
  }

  onDeleteConfirmation(driverData: DriverScheduleBoardListItem): void {
    this.DriverScheduleService.delete(driverData.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.driverScheduleDataDeleted);
          this.getAll();
        }
      });
  }

  changeStatus(id: number, status: string): void {
    this.commonSharedService.setBlockUI$(true);
    this.DriverScheduleService.updateDriverScheduleStatus(id, status).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.commonSharedService.setBlockUI$(false);
        this.toasterService.success(MESSAGES.statusChangeSuccess);
        this.getAll();
      }
    });
  }

  private getDriverScheduleStatuses(): void {
    this.DriverScheduleService.getDriverStatus().pipe(takeUntil(this.destroy$)).subscribe({
      next: (statusMap) => {
        for (const status in statusMap) {
          this.statues.push({ id: status, name: statusMap[status as keyof typeof statusMap] });
        }

        this.setAllInStatus(this.statues)
      }
    });
  }

  setAllInStatus(status: any) {
    this.statusCopy = Object.assign([], status);
    this.statusCopy.push({ 'id': 'ALL', 'name': 'All' })
    this.statusCopy.reverse();
  }

  findStatusIndex(index: number): void {
    this.isStatusList = index;
  }

  onViewEdit(task: DriverScheduleBoardListItem, isEdit: boolean): void {
    this.showCreateModal = true;
    this.isViewMode = isEdit ? false : true;
    this.selectedDriverScheduleBoard = task;
  }

  toggleFilterSidebar() {
    this.showColumnModal = true;
  }

  toggleColumnSidebar() {
    this.showColumnModal = !this.showColumnModal;
  }

  sortColumnList(columns: ColumnItem[]) {
    const tempData: ColumnItem[] = columns.filter(x => x.name === 'Item' || x.name === 'Associated Stocks');
    const tempData1: ColumnItem[] = columns.filter(x => x.name !== 'Item' && x.name !== 'Action' && x.name !== 'Associated Stocks');
    const tempData2: ColumnItem[] = columns.filter(x => x.name === 'Action');
    return tempData.concat(tempData1, tempData2);
  }

  sortSelectedColumns() {
    let temp: any[] = [];
    const ids = this.defaultColumnsArray.map(a => a.id);
    this.defaultColumnsArray = this.sortColumnList(this.defaultColumnsArray)
    this._selectedColumns = this.cols = this.defaultColumnsArray;

    if (this.tableColumn && this.tableColumn?.data) {
      temp = JSON.parse(this.tableColumn?.data);
    }

    this.dropDownColumnList.forEach((column: any) => {
      if (!ids.includes(column.id)) {
        temp.push(column);
      }
    });

    this.dropDownColumnList = temp;
    this.dropDownColumnList = this.sortColumnList(this.dropDownColumnList);
  }

  getFilterSaveParams(): FilterList {
    return {
      module: FilterModuleName.DRIVER_SCHEDULE_MODULE.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      id: this.tableColumn?.id,
      hideField: ''
    }
  }

  getFilterDetail(): void {
    this.defaultColumnsArray = [];
    const userId = this.currentUser?.id ?? null
    const endpoint = `${API_URL_UTIL.filter.filterDataByUserIdAndModule}`.replace(':userId', String(userId)).concat(`?module=${FilterModuleName.DRIVER_SCHEDULE_MODULE}`)
    this.columnDropDownService.getList<FilterList>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (filters) => {
        this.defaultTabs = filters.filter((item: any) => item.filterType === "TAB");
        this.tableColumn = filters.find((item: any) => item.filterType === "COLUMN");
        if (this.tableColumn && this.tableColumn?.data) {
          this.defaultColumnsArray = JSON.parse(this.tableColumn?.data);
          this.getDropDownColumnList();
          this.cdf.detectChanges();
        } else {
          this.getDropDownColumnList(true);
        }
      }
    });

  }

  getDropDownColumnList(shouldCreateDefaultColumn = false) {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${FilterModuleName.DRIVER_SCHEDULE_MODULE.toUpperCase()}`);
    this.commonService.getListFromObject<ColumnItem>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        if (shouldCreateDefaultColumn) {
          this.createDefaultColumns();
        } else {
          this.sortSelectedColumns();
        }
        this.cdf.detectChanges();
      });
  }

  callFilterApiAgain() {
    this.getFilterDetail();
  }

  get filterInfoParams() {
    return {
      id: this.tableColumn?.id,
      module: FilterModuleName.DRIVER_SCHEDULE_MODULE.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      isDefault: false,
      deleted: false,
      isPrivate: true,
      filterName: 'Column filter',
      filterType: 'COLUMN',
      data: JSON.stringify(this.selectedColumns),
      hideField: null
    };
  }

  createDefaultColumns(): void {
    const defaultFields = this.dropDownColumnList.filter(a => a.default ? a.name : null)
    if (this.tableColumn === undefined && defaultFields?.length) {
      this._selectedColumns = defaultFields;
      this.columnDropDownService.add<FilterList>(this.filterInfoParams, '').pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          if (res.filterType === 'COLUMN') {
            this.tableColumn = res;
            this.defaultColumnsArray = JSON.parse(res.data);
            this.sortSelectedColumns()
            this.cdf.detectChanges();
          }
        }
      });
    }
  }


  assignDataType(type: string): string {
    let stringDataType = '';
    switch (type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER
        break;
      case 'DROP_DOWN':
        stringDataType = DataType.ENUM
        break;
      case 'DATE':
        stringDataType = DataType.DATE
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE
        break;
      default:
        stringDataType = DataType.STRING
        break;
    }
    return stringDataType
  }

  assignOperator(type: string) {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DOUBLE':
        operatorType = OperatorType.EQUAL
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }


  getEventValue(event: any, col: any) {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = event?.value?.split(" ")?.join("_")?.toUpperCase();
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format)
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  getFilterInfo(inputValue: any, col: any) {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.shortingKey)
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col.type) as DataType,
        key: col.shortingKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        if (inputValue?.length) {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
    if (col.type === 'DROP_DOWN' && (inputValue === 'ALL' || inputValue === '')) {
      const rm = this.filterParams.values.find(d => d.key === 'driverScheduleStatus')
      if (rm) {
        this.filterParams.values.splice(this.filterParams.values.indexOf(rm), 1)
      }

    }
  }

  tableSearchByColumn(event: any, col: any) {
    this.isLoading = true;
    this.driverScheduleBoardList = [];
    const searchInput = this.getEventValue(event, col)
    this.getFilterInfo(searchInput, col)
    this.setSearchEndDate(event, col);
    this.globalSearch.next(this.filterParams.values);
    this.setValueForReset(searchInput, col);
  }

  setValueForReset(input: string, col: any) {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);

    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    } else if (col.key === 'driverScheduleStatus') {
      temp.value = this.titleCasePipe.transform(input.split('_').join(' '));
    } else {
      temp.value = input;
    }
    if (temp1) {
      temp1.value = temp.value;
    }
  }

  setSearchEndDate(event: any, col: any) {
    const existingDate = this.filterParams.values.find(d => d.key === col.shortingKey && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col.type) as DataType,
        key: col.shortingKey,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  displaySearchResult() {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
      this.getAll();
    });
  }

  setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 23, 59, 50).toISOString();
  }

  setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0).toISOString();
  }

  clearSearchInput() {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.filterParams.values = Object.assign([], this.initialFilterParams);
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  clearDate(type: string) {
    if (type === 'createdDate') {
      this.filterParams.values = this.filterParams.values.filter((param: any) => param.key !== "createdDate");
    } else {
      this.filterParams.values = this.filterParams.values.filter((param: any) => param.key !== "endDate");
    }
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  driverScheduleViewFromCalender() {
    this.activeRoute.queryParams
      .subscribe(params => {
        if (Number(params['id'])) {
          const status = this.titleCasePipe.transform(params['status'])
          const tab = this.profileTabs.tabs.find(h => h.heading === status)
          if (tab) {
            tab.active = true
          }
          const activeTab = this.defaultTabs.find(t => t.filterName === status)
          if (activeTab?.data) {
            this.filterParams = JSON.parse(activeTab.data)
            this.initialFilterParams = Object.assign([], this.filterParams.values);
            this.hideFieldForSearch = activeTab.hideField ?? null;
            this.activeTabDetail = activeTab;
          }
          this.getAll();
          const rowData = this.driverScheduleBoardList.find(d => d.id === Number(params['id']))
          if (rowData) {
            this.redirectToCalender = params['redirectUrl']
            this.onViewEdit(rowData, true)
          }
        }
      });
  }

  exportUsersToExcel(downloadAll = false): void {
    this.isExporting = true;
    this.DriverScheduleService.getListWithFiltersWithPagination<GenericFilterParams, DriverScheduleBoardListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, API_URL_UTIL.inventory.list)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content, downloadAll)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "Driver-schedule");
          this.isExporting = false;
        });
      });
  }

  getExcelData(incomingTruck: Array<DriverScheduleBoardListItem>, downloadAll = false) {
    const excelData = incomingTruck.map(res => ({
      'Item': res?.item,
      'Associated stock': res.unitAssociations[0].stockNumber,
      'Requested By': res?.createdBy?.name,
      'Requested Date': Utils.dateIntoUserReadableFormat(res?.createdDate ?? ''),
      'Deadline': res?.endDate,
      'Day of Delivery': res?.weekDay,
      'Driver': res?.driver?.name,
      'Where Is The Unit Located?': res?.pickUpLocation,
      'Where Is The Unit Going?': res?.destinationLocation,
      'Dispatcher': res?.dispatcher?.name,
      'Status': res?.driverScheduleStatus
    }));
    if (!downloadAll) {
      const selectedKeysToBeDisplayedInExcel = this.defaultColumnsArray.map(({ name }) => name);
      for (const [index, data] of excelData.entries()) {
        for (const key in data) {
          if (!selectedKeysToBeDisplayedInExcel.includes(key)) {
            delete (excelData as any)[index][key]
          }
        }
      }
    }
    return excelData;
  }

  sendEmail(): void {
    this.DriverScheduleService.get(`${API_URL_UTIL.driverSchedule.send}/${API_URL_UTIL.driverSchedule.email}/${API_URL_UTIL.driverSchedule.driver}/${this.selectedDriverScheduleBoard?.id}?email=<EMAIL>`).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.selectedDriverScheduleBoard = null;
        this.toasterService.success(MESSAGES.inventoryMatchedEmailSuccess);
      }
    });
  }

  downloadPDF(): void {
    this.DriverScheduleService.downloadPdf(`${API_URL_UTIL.driverSchedule.pdf}/${API_URL_UTIL.driverSchedule.generator}/${this.selectedDriverScheduleBoard?.id}`).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: ArrayBuffer) => {
        saveAs(new Blob([res], { type: "application/pdf" }), `Driver_Schedule_${this.selectedDriverScheduleBoard?.item}`);
        this.selectedDriverScheduleBoard = null;
      }
    });
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
