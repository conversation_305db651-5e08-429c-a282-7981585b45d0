import { IdNameModel } from 'src/app/@shared/models';

export interface ProfitabilityListItem {
  id: number;
  stockNumber: string;
  category: IdNameModel;
  initialInvestment: number;
  actualInvestment: number;
  retailAskingPrice: number;
  totalProjectedInvestment: number;
  netProfitAmount: number;
  sellPrice: number;
  invoiceDate: string;
}


export interface InventoryAgingListItem {
  id: number;
  stockNumber: string;
  amount: number;
  inventoryAge: number;
  contactAndVendorAndSupplierType: string;
  crmContact: IdNameModel | null;
  vendor: IdNameModel | null;
  supplier: IdNameModel | null;
  purchasingAgent: IdNameModel;
  acquisitionMethod: IdNameModel;
  transDate: string;
}

export const colorCodes = {
  LESS_THAN_90: 'age-90',
  LESS_THAN_180: 'age-180',
  LESS_THAN_270: 'age-270',
  LESS_THAN_365: 'age-365',
  GREATER_THAN_365: 'age-365',
}

export class AgingConfigDto {
  id!: number;
  days!: number;
  color!: string;
  subject!: string;
  body!: string;
  toEmailEmployee!: IdNameModel;
}

export class AgingConfigUpdateDto {
  id!: number;
  days!: number;
  color!: string;
  toEmpId!: number;
  subject!: string;
  body!: string;
}