import { DatePipe, TitleCasePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { HistoryModuleName, MESSAGES, dateFormat, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { CrmContactListItem } from '@pages/administration/models/crm.model';
import { PrivilegeActionResponseDTOs } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { ColumnItem, FilterList, FilterModuleName } from '@pages/common-table-column/models/common-table.column.model';
import { CrmTaskListFilter } from '@pages/crm/models/crm-task.model';
import { TaskListItem } from '@pages/shops/models';
import { TaskService } from '@pages/shops/services/tasks.service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, IdNameModel, OperatorType, TreeOperatorType, ViewMode } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';

@Component({
  selector: 'app-crm-task-list',
  templateUrl: './crm-task-list.component.html',
  styleUrls: ['./crm-task-list.component.scss'],
  providers: [TitleCasePipe]
})
export class CrmTaskListComponent extends BaseComponent implements OnInit {
  tasks: TaskListItem[] = [];
  filterParams: CrmTaskListFilter = new CrmTaskListFilter();
  showCreateModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.TASK_SALES;
  isViewMode = false;
  selectedTask!: TaskListItem | null;
  taskStatuses: IdNameModel[] = [];
  taskStatusesCopy: IdNameModel[] = [];
  isStatusList!: number | null;
  status!: string;
  taskId!: string;
  keyByTaskId = "taskType.id";
  assigneeId = "assignee.id";
  isActiveTab = true;
  isArchiveInProgress = false;
  cols: any[] = [];
  _selectedColumns: any[] = [];
  showColumnModal = false;
  defaultTabs!: FilterList[];
  tableColumn!: FilterList | undefined;
  showConfirmationDialog = false;
  defaultColumnsArray: ColumnItem[] = [];
  dropDownColumnList: ColumnItem[] = [];
  filterValue: FilterValue[] = [];
  globalSearch = new Subject<FilterValue[]>();
  priorities: IdNameModel[] = [];
  rangeDates!: Date | null;
  createdDate!: Date | null;
  activeIndex = 0;
  isRedirectUrl = false;
  userPermissions!: Array<PrivilegeActionResponseDTOs>;
  @ViewChild('calendar') private calendar: any;
  excelDownloadMenuItems: MenuItem[] = [
    {
      label: "Selected Columns",
      command: () => this.exportUsersToExcel()
    },
    {
      label: "All Columnns",
      command: () => this.exportUsersToExcel(true)
    }
  ];

  constructor(private readonly taskService: TaskService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    readonly columnDropDownService: ColumnDropdownService,
    private readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly datePipe: DatePipe) {
    super()
    this.pageTitle = 'Task Management'
  }


  async ngOnInit(): Promise<void> {
    this.setFilters(this.activeIndex);
    this.getTaskStatuses();
    await this.getCurrentUser();
    this.getFilterDetail();
    this.getFilterSaveParams();
    this.displaySearchResult();
    this.listenToRouteParams();
    this.userPermissions = this.authService.getRoleInfo()?.privilegeActionResponseDTOs;
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  private getCurrentUser(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe({
        next: (user) => {
          if (user) {
            this.currentUser = user;
          }
          resolve();
        },
        error: () => {
          reject();
        }
      });
    });
  }

  private listenToRouteParams(): void {
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      if (params.id) {
        this.isViewMode = ViewMode[(params.mode as ViewMode)] !== ViewMode[ViewMode.EDIT]; // if mode is edit, isViewMode is false
        this.showCreateModal = true;
        this.taskId = params.id;
      }
    });
  }

  get taskInfoParams(): CrmTaskListFilter {
    return {
      archived: false,
      treeOperator: TreeOperatorType.NOOP,
      values: [
        {
          dataType: DataType.LONG,
          key: this.keyByTaskId,
          operator: OperatorType.EQUAL,
          value: 4
        }
      ]
    }
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.taskService.getListWithFiltersWithPagination<CrmTaskListFilter, TaskListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.tasks.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.tasks = res.content;
          this.setActiveFlagForAll()
          this.setPaginationParamsFromPageResponse<TaskListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  private setActiveFlagForAll() {
    this.tasks.forEach(task => task.isActive = this.isActiveTab);
  }

  onAdd(): void {
    this.showCreateModal = true;
    this.isViewMode = false;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.backToCalendar();
    this.showCreateModal = false;
    this.selectedTask = null;
    if (refreshList) {
      this.getAll();
    }
    if (!this.isRedirectUrl) {
      this.router.navigate([], { queryParams: {} });
    }
  }

  onEdit(crmContact: TaskListItem): void {
    this.showCreateModal = true;
    this.selectedTask = crmContact;
    this.cdf.detectChanges();
  }

  onDelete(pipelineConfig: CrmContactListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'crm task data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(pipelineConfig);
      }
    });
  }

  onDeleteConfirmation(task: CrmContactListItem): void {
    this.taskService.delete(task.id, API_URL_UTIL.tasks.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.shopTaskDeleteSuccess);
          this.getAll();
        }
      });
  }

  onViewEdit(task: TaskListItem, isEdit: boolean): void {
    this.isViewMode = isEdit ? false : true;
    this.router.navigate([], { queryParams: { id: task.id, mode: isEdit ? ViewMode.EDIT : ViewMode.READ } });
    this.selectedTask = task;
  }

  clearDate(key: string) {
    if (key === "startDate/endDate") {
      this.filterParams.values = this.filterParams.values.filter((param: any) => param.key !== "startDate");
    } else {
      this.filterParams.values = this.filterParams.values.filter((param: any) => param.key !== "createdDate");
    }
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  private getTaskStatuses(): void {
    this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.statuses).pipe(takeUntil(this.destroy$)).subscribe({
      next: (taskStatuses) => {
        this.taskStatuses = taskStatuses;
        this.setAllInStatus();
      },
    });
  }

  setAllInStatus() {
    this.taskStatusesCopy = Object.assign([], this.taskStatuses);
    this.taskStatusesCopy.push({ 'id': '0', 'name': 'All' });
    this.taskStatusesCopy.reverse();
  }

  findStatusIndex(index: number): void {
    this.isStatusList = index;
  }

  changeStatus(task: TaskListItem, status: number, id: number, isActive: any): void {
    const statusParam = {
      statusId: status
    }
    const endpoint = API_URL_UTIL.tasks.status.replace(':taskId', id.toString());
    this.taskService.update(statusParam, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.statusChangeSuccess);
      this.isStatusList = null;
      this.getAll();
    });
    if (status === 4) {
      const message = task.isActive
        ? MESSAGES.archiveWarning.replace('{record}', 'task')
        : MESSAGES.unArchiveWarning.replace('{record}', 'vendor');
      this.handleTaskConfirmation(task, isActive, message, true);
    }
  }

  onArchive(task: TaskListItem, isActive: boolean): void {
    this.selectedTask = task;
    const message = task.isActive
      ? MESSAGES.archiveWarning.replace('{record}', 'task')
      : MESSAGES.unArchiveWarning.replace('{record}', 'task');
    this.handleTaskConfirmation(task, isActive, message);
  }

  private handleTaskConfirmation(task: TaskListItem, isActive: boolean, message: string, isDialog = false): void {
    if (isDialog) {
      this.showConfirmationDialog = true;
      this.cdf.detectChanges();
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: message,
      icon: icons.triangle,
      accept: () => {
        this.isArchiveInProgress = true;
        if (isDialog) {
          this.showConfirmationDialog = false;
        }
        this.onArchiveConfirmation(task);
        task.isActive = isActive;
      },
      reject: () => {
        task.isActive = !isActive;
        if (isDialog) {
          this.showConfirmationDialog = false;
        }
        this.cdf.detectChanges();
      }
    });
  }

  private onArchiveConfirmation(task: TaskListItem): void {
    this.taskService.archivedTask((task.id).toString())
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.isActiveTab ? MESSAGES.taskArchiveSuccess : MESSAGES.taskUnArchiveSuccess);
          this.isArchiveInProgress = false;
          this.selectedTask = null;
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedTask = null;
          this.getAll();
        }
      });
  }

  onBackdropClick(element: HTMLElement): void {
    Utils.handlePopoverBackdropClick(element, this.selectedTask, 'isActive', this.isActiveTab, this.cdf);
  }

  toggleFilterSidebar() {
    this.showColumnModal = true;
  }

  toggleColumnSidebar() {
    this.showColumnModal = !this.showColumnModal;
  }

  createDefaultColumns(): void {
    const defaultFields = this.dropDownColumnList.filter(a => a.default ? a.name : null)
    if (this.tableColumn === undefined && defaultFields?.length) {
      this._selectedColumns = defaultFields;
      this.columnDropDownService.add<FilterList>(this.filterInfoParams, '').pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          if (res.filterType === 'COLUMN') {
            this.tableColumn = res;
            this.defaultColumnsArray = JSON.parse(res.data);
            this.sortSelectedColumns()
            this.cdf.detectChanges();
          }
        }
      });
    }
  }
  get filterInfoParams() {
    return {
      id: this.tableColumn?.id,
      module: FilterModuleName.CRM_TASK_MODULE.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      isDefault: false,
      deleted: false,
      isPrivate: true,
      filterName: 'Column filter',
      filterType: 'COLUMN',
      data: JSON.stringify(this.selectedColumns),
      hideField: null
    };
  }

  callFilterApiAgain() {
    this.getFilterDetail();
  }

  getDropDownColumnList(shouldCreateDefaultColumn = false) {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${FilterModuleName.CRM_TASK_MODULE}`);
    this.commonService.getListFromObject<ColumnItem>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        if (shouldCreateDefaultColumn) {
          this.createDefaultColumns();
        } else {
          this.sortSelectedColumns()
        }
        this.cdf.detectChanges();
      });
  }

  getFilterSaveParams(): FilterList {
    return {
      module: FilterModuleName.CRM_TASK_MODULE.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      id: this.tableColumn?.id,
      hideField: ''
    }
  }

  getFilterDetail(): void {
    this.defaultColumnsArray = [];
    const userId = this.currentUser?.id ?? null
    const endpoint = `${API_URL_UTIL.filter.filterDataByUserIdAndModule}`.replace(':userId', String(userId)).concat(`?module=${FilterModuleName.CRM_TASK_MODULE}`)
    this.columnDropDownService.getList<FilterList>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (filters) => {
        this.defaultTabs = filters.filter((item: any) => item.filterType === "TAB");
        this.tableColumn = filters.find((item: any) => item.filterType === "COLUMN")
        if (this.tableColumn?.data) {
          this.defaultColumnsArray = JSON.parse(this.tableColumn?.data)
          this.getDropDownColumnList();
          this.cdf.detectChanges();
        } else {
          this.getDropDownColumnList(true);
        }
      }
    });
  }

  sortColumnList(columns: ColumnItem[]) {
    const tempData: ColumnItem[] = columns.filter(x => x.name === 'p');
    const tempData1: ColumnItem[] = columns.filter(x => x.name !== 'p' && x.name !== 'Action');
    const tempData2: ColumnItem[] = columns.filter(x => x.name === 'Action');
    return tempData.concat(tempData1, tempData2);
  }

  sortSelectedColumns() {
    let temp: any[] = [];
    const ids = this.defaultColumnsArray.map(a => a.id);
    this.defaultColumnsArray = this.sortColumnList(this.defaultColumnsArray)
    this._selectedColumns = this.cols = this.defaultColumnsArray;
    if (this.tableColumn?.data) {
      temp = JSON.parse(this.tableColumn?.data);
    }
    this.dropDownColumnList.forEach((column: any) => {
      if (!ids.includes(column.id)) {
        temp.push(column);
      }
    });
    this.dropDownColumnList = temp;
    this.dropDownColumnList = this.sortColumnList(this.dropDownColumnList);
  }

  tableSearchByColumn(event: any, col: any) {
    this.isLoading = true;
    this.tasks = [];
    const searchInput = this.getEventValue(event, col)
    this.getFilterInfo(searchInput, col)
    if (this.rangeDates) {
      this.calendar.overlayVisible = false;
    }
    if (col.key === 'startDate/endDate' && this.rangeDates !== null) {
      this.setSearchEndDate(this.rangeDates, col)
    } else {
      this.setSearchEndDate(event, col)
    }

    this.globalSearch.next(this.filterParams.values);
    // Reset input code
    this.setValueForReset(searchInput, col);
  }

  setValueForReset(input: string, col: any) {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);

    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    }
    else {
      temp.value = input;
    }
    if (temp1) {
      temp1.value = temp.value;
    }

  }

  getEventValue(event: any, col: any) {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        if (col.key === 'taskStatus.name') {
          temp = event?.value;
        } else {
          temp = event?.value?.split(" ")?.join("_")?.toUpperCase();
        }
        break;
      case 'DATE':
        let endDate: any
        if (col.key === 'startDate/endDate') {
          const tmpEvent = this.rangeDates;
          endDate = this.datePipe.transform(tmpEvent, dateFormat.format);
        } else {
          endDate = this.datePipe.transform(event, dateFormat.format);
        }
        temp = this.setStartDate(new Date(endDate).toISOString());
        break;
      default:
        temp = event.value;
        break;
    }
    return temp
  }

  getFilterInfo(inputValue: any, col: any) {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.shortingKey)
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.shortingKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        if (inputValue?.length) {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
    if (col.type === 'DROP_DOWN' && (inputValue === 'All' || inputValue === '')) {
      const rm = this.filterParams.values.find((d: any) => d.key === 'taskStatus.name')
      if (rm) {
        this.filterParams.values.splice(this.filterParams.values.indexOf(rm), 1)
      }
    }
  }

  assignDataType(col: any): string {
    const type = col.type;
    let stringDataType = '';
    switch (type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER
        break;
      case 'DROP_DOWN':
        if (col.key === 'taskStatus.name') {
          stringDataType = DataType.STRING
        } else {
          stringDataType = DataType.ENUM
        }
        break;
      case 'DATE':
        stringDataType = DataType.DATE
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE
        break;
      default:
        stringDataType = DataType.STRING
        break;
    }
    return stringDataType
  }

  assignOperator(type: string) {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DOUBLE':
        operatorType = OperatorType.EQUAL
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }

  displaySearchResult() {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
      this.getAll();
    });
  }

  setSearchEndDate(event: any, col: any) {
    const existingDate = this.filterParams.values.find((d: any) => d.key === col.shortingKey && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.shortingKey,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 23, 59, 50).toISOString();
  }

  setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0).toISOString();
  }

  clearSearchInput() {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.rangeDates = null;
    this.createdDate = null;
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.setFilters(this.activeIndex);
    this.getAll();
  }

  backToCalendar() {
    this.activatedRoute.queryParams
      .subscribe(params => {
        if (params['redirectUrl']) {
          this.isRedirectUrl = true;
          this.router.navigateByUrl(`${API_URL_UTIL.calendar.root}`);
        }
      })
  }


  exportUsersToExcel(downloadAll = false): void {
    this.isExporting = true;
    this.taskService.getListWithFiltersWithPagination<CrmTaskListFilter, TaskListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, API_URL_UTIL.tasks.list)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content, downloadAll)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "crm-task");
          this.isExporting = false;
        });
      });
  }

  getExcelData(crmContact: Array<TaskListItem>, downloadAll = false) {
    const excelData = crmContact.map(res => ({
      'ID': res.id,
      'Lead ID': res.customerLeadId,
      'Stock': res.stockNumber,
      'Summary': res.summary,
      'Creation Date': Utils.dateIntoUserReadableFormat(res?.createdDate ?? ''),
      'Deadline': `${Utils.dateIntoUserReadableFormat(res?.endDate ?? '')}`,
      'Assignee': res?.assignee?.name,
      'Customer': res?.crmContact?.name,
      'Created By': res?.reporter?.name,
      'Status': res?.taskStatus?.name,
      'Task Type': res?.taskType?.name,
      'Active': res.archived ? 'Archived' : 'Active'
    }))
    if (!downloadAll) {
      const selectedKeysToBeDisplayedInExcel = this.defaultColumnsArray.map(({ name }) => name);
      for (const [index, data] of excelData.entries()) {
        for (const key in data) {
          if (!selectedKeysToBeDisplayedInExcel.includes(key)) {
            delete (excelData as any)[index][key]
          }
        }
      }
    }
    return excelData;
  }

  myTaskParams(): FilterValue | void {
    if (this.currentUser?.id) {
      return {
        dataType: DataType.LONG,
        key: this.assigneeId,
        operator: OperatorType.EQUAL,
        value: this.currentUser.id
      }
    }
  }

  setFilters(tabIndex: number): void {
    this.filterParams.archived = false;
    this.filterParams.values = this.taskInfoParams.values;
    switch (tabIndex) {
      case 0:
        break;
      case 1:
        this.filterParams.values.push(this.myTaskParams());
        break;
      case 2:
        this.filterParams.archived = true;
        break;
    }
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
  }

  onTabChanged(e: any): void {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.setFilters(e.index);
    this.getAll();
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
