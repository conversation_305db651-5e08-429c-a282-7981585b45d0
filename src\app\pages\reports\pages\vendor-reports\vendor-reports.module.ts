import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { VendorReportsRoutingModule } from './vendor-reports-routing.module';
import { VendorReportsComponent } from './vendor-reports.component';


@NgModule({
  declarations: [VendorReportsComponent],
  imports: [
    CommonModule,
    VendorReportsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    SharedComponentsModule,
    TableModule,
    DropdownModule,
    MultiSelectModule,
    FontAwesomeIconsModule,
    CalendarModule,
    DirectivesModule
  ]
})
export class VendorReportsModule { }
