import { Directive, ElementRef, OnInit, Renderer2 } from '@angular/core';

@Directive({
    selector: '[appToggleMoreLinksButton]',
})
export class ToggleMoreLinksButtonDirective implements OnInit {
    constructor(private elementRef: ElementRef, private renderer: Renderer2) { }

    ngOnInit() {
        const hostElement = this.elementRef.nativeElement;
        setTimeout(() => {
            this.updateButtonVisibility(hostElement);
        }, 0);

        this.renderer.listen('window', 'resize', () => {
            this.updateButtonVisibility(hostElement);
        });
    }

    private updateButtonVisibility(hostElement: HTMLElement) {
        const tabNavContainer = hostElement.querySelector('.p-tabview-nav-container'),
            moreLinksButton = tabNavContainer?.querySelector('.p-tabview-nav-btn'),
            navContentContainer = tabNavContainer?.querySelector('.p-tabview-nav-content'),
            navLinkItems = navContentContainer?.querySelectorAll('.p-tabview-nav-link');

        if (!tabNavContainer || !moreLinksButton || !navContentContainer || !navLinkItems) {
            return;
        }

        const navContentWidth = navContentContainer.clientWidth;
        let totalNavItemsWidth = 0;

        navLinkItems.forEach(navLinkItem => {
            totalNavItemsWidth += navLinkItem.clientWidth;
        });

        if (totalNavItemsWidth > navContentWidth) {
            this.renderer.setStyle(moreLinksButton, 'display', 'block');
        } else {
            this.renderer.setStyle(moreLinksButton, 'display', 'none');
        }
    }
}