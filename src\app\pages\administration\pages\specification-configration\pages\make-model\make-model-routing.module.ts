import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { MakeModelComponent } from "./make-model.component";

const routes: Routes = [
    {
      path: '',
      component: MakeModelComponent,
      title: 'Skeye - Specification Model',
      children: [
        {
          path: '',
          component: MakeModelComponent,
          pathMatch: 'full'
        }
      ]
    }
  ];

  @NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
  })

  export class MakeModelRoutingModule { }
