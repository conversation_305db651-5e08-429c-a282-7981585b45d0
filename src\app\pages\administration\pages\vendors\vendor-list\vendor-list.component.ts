import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { VendorListFilter, VendorListItem } from '@pages/administration/models';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { TabDirective } from 'ngx-bootstrap/tabs';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { DataType, TableColumn } from 'src/app/@shared/models';
import { PatchParam } from 'src/app/@shared/models/patch-param.model';
import { Utils } from 'src/app/@shared/services';
import { VendorService } from '../vendors.service';

@Component({
  selector: 'app-vendor-list',
  templateUrl: './vendor-list.component.html',
  styleUrls: ['./vendor-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VendorListComponent extends BaseComponent implements OnInit {

  cols: TableColumn[] = [];
  _selectedColumns: any[] = [];
  vendors: VendorListItem[] = [];
  filterParams: VendorListFilter = new VendorListFilter();
  showCreateModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.VENDORS;
  selectedVendor!: VendorListItem | null;
  selectedVendorExpenses!: VendorListItem | null;
  isActiveTab = true;
  isArchiveInProgress = false;
  isViewMode = false;
  showExpensesCreateModal = false;
  constructor(
    private readonly vendorService: VendorService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly activeRoute: ActivatedRoute
  ) {
    super();
    this.pageTitle = 'Vendors';
    this.paginationConfig.predicate = 'name';
  }

  ngOnInit(): void {
    this.setTableColumns();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getVendorById(Number(params.id));
        }
      });
  }

  getVendorById(id: number) {
    this.vendorService.get<VendorListItem>(id).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.onEdit(res, res?.archived ? true : false);
      }
    });
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  onTabChanged(tabChangeEvent: TabDirective): void {
    if (tabChangeEvent.heading === 'Archived') {
      this.isActiveTab = false;
      this.filterParams.archived = true;
    } else {
      this.isActiveTab = true;
      this.filterParams.archived = false;
    }
    this.getAll();
  }

  setTableColumns() {
    this.cols = [
      { field: 'name', sortKey: 'name', header: 'Name', sortable: true, reorderable: true },
      { field: 'contactPersonName', header: 'Contact Person', reorderable: true },
      { field: 'email', header: 'Email', sortable: true, reorderable: true },
      { field: 'phoneNumber', header: 'Phone Number', reorderable: true, isATemplate: true },
      { field: 'addressCity', header: 'City', reorderable: true },
      { field: 'addressState', header: 'State', reorderable: true },
    ];
    this._selectedColumns = this.cols;
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.vendorService.getListWithFiltersWithPagination<VendorListFilter, VendorListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.vendors.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.vendors = res.content;
          this.setActiveFlagForAllVendors()
          this.setPaginationParamsFromPageResponse<VendorListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  private setActiveFlagForAllVendors() {
    this.vendors.forEach(vendor => vendor.isActive = this.isActiveTab);
  }
  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedVendor = null;
    this.isViewMode = false;
    if (refreshList) {
      this.getAll();
    }
  }

  onEdit(vendor: VendorListItem, isActiveTab: boolean): void {
    this.showCreateModal = true;
    this.selectedVendor = vendor;
    this.isViewMode = isActiveTab ? isActiveTab : false;
    this.cdf.detectChanges();
  }

  onArchive(vendor: VendorListItem, isActive: boolean): void {
    this.selectedVendor = vendor;
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: vendor.isActive ? MESSAGES.archiveWarning.replace('{record}', 'vendor') : MESSAGES.unArchiveWarning.replace('{record}', 'vendor'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.isArchiveInProgress = true;
        this.onArchiveConfirmation(vendor);
        vendor.isActive = isActive;
      },
      reject: () => {
        vendor.isActive = !isActive;
        this.cdf.detectChanges();
      }
    });
  }

  private onArchiveConfirmation(vendor: VendorListItem): void {
    this.vendorService.patch(this.getPatchRequestParams(vendor))
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.isActiveTab ? MESSAGES.vendorArchiveSuccess : MESSAGES.vendorUnArchiveSuccess);
          this.isArchiveInProgress = false;
          this.selectedVendor = null;
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedVendor = null;
          this.getAll();
        }
      });
  }

  private getPatchRequestParams(vendor: VendorListItem): PatchParam {
    return {
      id: vendor.id,
      values: [{
        key: 'archived',
        value: `${this.isActiveTab}`,
        dataType: DataType.BOOLEAN
      }]
    }
  }

  onViewExpensesList(vendor: VendorListItem): void {
    this.showExpensesCreateModal = true;
    this.selectedVendorExpenses = vendor;
  }

  onAddEditExpensePopupClose(refreshList: boolean): void {
    this.showExpensesCreateModal = false;
    this.selectedVendorExpenses = null;
    if (refreshList) {
      this.getAll();
    }
  }

  onBackdropClick(element: HTMLElement): void {
    Utils.handlePopoverBackdropClick(element, this.selectedVendor, 'isActive', this.isActiveTab, this.cdf);
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
