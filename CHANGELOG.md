# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [1.3.1](https://gitlab.com/sunflowerlab/skeye/skeye_client/compare/v1.3.0...v1.3.1) (2022-08-30)

## [1.3.0](https://gitlab.com/sunflowerlab/skeye/skeye_client/compare/v1.2.0...v1.3.0) (2022-08-03)


### Features

* added UTC timezone on build date time ([9372d47](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/9372d4740a52a21721a8b14c8abe96d0db032af2))

## [1.2.0](https://gitlab.com/sunflowerlab/skeye/skeye_client/compare/v1.1.2...v1.2.0) (2022-08-02)


### Features

* SKY-147 design financial module ([7c243ab](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/7c243ab5014f61cd3d819478de86d5b440f285bd))
* SKY-147 financial ([65bb38e](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/65bb38e28420c2181f9cf571ed6016cc5d243199))
* SKY-83 client feedback changes ([5b9dc83](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/5b9dc83d63c09814ccd739bdab6d54002efe1aa8))
* SKY-95 client feedback and some bug fix ([08bd989](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/08bd9895cf8606928da0a08bf6181501c790455d))


### Bug Fixes

* added parent route active handling ([f3071a1](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/f3071a1e04d6d67bc1578a9e8a924d86bea7630c))

### [1.1.2](https://gitlab.com/sunflowerlab/skeye/skeye_client/compare/v1.1.1...v1.1.2) (2022-06-15)

### [1.1.1](https://gitlab.com/sunflowerlab/skeye/skeye_client/compare/v1.1.0...v1.1.1) (2022-06-15)

## 1.1.0 (2022-06-13)


### Features

* added git versioning ([7a760e8](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/7a760e8f2043c86067df505846cd9ce677e55215))
* added modules and components for administration feature ([e5740b5](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/e5740b5a9545c10355eae0c5015119d942294b37))
* added refresh token feature ([7bf5f15](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/7bf5f15e65aed60532872e2c9640d3790bc1f34b))
* added routes for user listing ([45d7383](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/45d73835672d8756a763e6c885cedf076020da77))
* added vs code settings ([72632d3](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/72632d3f71168d729f50d6b80f74ab3628e12515))
* fetch current logged in user info and show dynamic name on header ([ccaaaec](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/ccaaaec427c1c7c422baae034014d3353379f50a))
* login and forgot password integration ([47f0451](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/47f04518943833107621f00fb060cd394085ff48))
* navbar integrated ([2b92f75](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/2b92f75e5c24ed83fd959ef61315c651d3c6759f))
* Shop listing, add/edit shop task details, comments ([ff1c491](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/ff1c491b6053102a6d6f4b5d6f02b13f59bed299))
* shop task activity logs add/edit ([2c948c2](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/2c948c24061c8fda8266e5e7ec08dbd1f5c7aeea))
* SKY-22 dealer listing, add/edit dealer, archive/unarchive dealer, add shops ([61a33f8](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/61a33f879f2908aaf745213090562e0c33a45328))
* SKY-23 edit user feature done ([a80066b](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/a80066ba5cdd406828c310e8706eaac0483b1ae5))
* SKY-34 added template pipeline configuration, listing, add/edit/delete ([18baad1](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/18baad10cb7dc35e744b8f65db102f92ab80298a))
* SKY-34 added timeline in add/edit pipeline config ([f58d498](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/f58d498ad388d4549d9f70e4d920e809d399ba66))
* SKY-35 pipeline configuration listing ([d7bc1ee](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/d7bc1eea6822420f02d2cdd91b51b7bb2748c502))
* user listing active and archive design done ([2f55320](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/2f55320ab23c9e0becab090db6291902a529eb47))
* User listing, user change password, user add ([8123db8](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/8123db8b9a6216ceab044242e8f5bdabb0309974))
* user profile and change password integrated ([051c848](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/051c84812e78b11ab3d913fed622fd1950710452))


### Bug Fixes

* app initializer blocking application if refresh token has expired ([94fce02](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/94fce0232c62ff87fab890402fb3ca34c7c31cb1))
* fixed some sonar issues ([d7ac30f](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/d7ac30f5bc7c9a2982ce24a1a15f362310880128))
* header icon going out of screen ([4b19b40](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/4b19b405b93a4b480625fb8e0d36135547854d8a))
* header position fixed. scroll issues fixed ([6eac701](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/6eac701720f838555a6d95a10ae6fdb1a59b0eaa))
* header responsive in 786 size devices ([91a58a7](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/91a58a7935eed30f4316029be0b0b0e3fde8eb38))
* SKY-24 archived user listing not loading ([9fbeea5](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/9fbeea5b3601538ad82f78587b11bb4efed45c7d))
* SKY-24 show unique dealer names in user listing ([14edfe4](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/14edfe48e1e32c8e7d06510199ce30a4639f5d8a))
* solar issue fixes ([d4bcb61](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/d4bcb617d8414cf80b218ecbeaa070dd979728ba))
* updated gitlab cicd configuration to push new changes to remote branch ([e6e5fb4](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/e6e5fb4e89c89e29ac96487182c9084c82f28c19))
* updated semantic versioning configuration file ([b3293d1](https://gitlab.com/sunflowerlab/skeye/skeye_client/commit/b3293d1697cdc1121aa51698b839a4eee809667b))

## 1.1.0 (2022-04-07)


### Features

* added git versioning ([7a760e8](https://gitlab.com/sunflowerlab/Repositories/BaseProject/angular/angularbaseproject_v2/commit/7a760e8f2043c86067df505846cd9ce677e55215))
* added vs code settings ([72632d3](https://gitlab.com/sunflowerlab/Repositories/BaseProject/angular/angularbaseproject_v2/commit/72632d3f71168d729f50d6b80f74ab3628e12515))
