import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { AssociationsUnits } from '@pages/inventory/models';
import { Observable } from 'rxjs';
import { IncomingTruckBoardListItem, IncomingTruckCommentListItem } from '../models/incoming-truck.model';

@Injectable({
  providedIn: 'root'
})
export class IncomingTruckService extends BaseCrudService {

  constructor(protected readonly httpClient: HttpClient) {
    super(httpClient);
  }

  getBaseAPIPath(): string {
    return API_URL_UTIL.incomingTruck.root;
  }

  fromServerModel(json: IncomingTruckBoardListItem): IncomingTruckBoardListItem {
    if (!json) {
      return new IncomingTruckBoardListItem();
    }
    return new IncomingTruckBoardListItem(json);
  }

  getDriverStatus(): Observable<{}> {
    return this.httpClient.get<{}>(`${this.getFullAPIUrl()}/${API_URL_UTIL.driverSchedule.status}`);
  }

  getIncomingUnits(): Observable<Array<AssociationsUnits>> {
    return this.httpClient.get<Array<AssociationsUnits>>(`${this.getFullAPIUrl()}/${API_URL_UTIL.inventory.unit}`);
  }

  downloadPdf(endpoint: string) {
    return this.httpClient.get(`${this.getFullAPIUrl()}/${endpoint}`,
      {
        responseType: "arraybuffer"
      });
  }

  getIncomingTruckComments(unitId: string): Observable<IncomingTruckCommentListItem[]> {
    return this.httpClient.get<IncomingTruckCommentListItem[]>(`${API_URL_UTIL.incomingTruck.incomingTruckComment}/${unitId}/comments`);
  }
}
