import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Utils } from "src/app/@shared/services";
import { DealerListItem } from '../../models';

@Injectable({ providedIn: 'root' })
export class DealerService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.dealers.root;
  }

  fromServerModel(json: DealerListItem): DealerListItem {
    if (!json) {
      return new DealerListItem();
    }
    return {
      ...json,
      addressState: json.address?.state,
      contactPersonName: Utils.getFullName(json.contactPerson?.firstName, json.contactPerson?.lastName),
    };
  }
}
